package main

import (
	"bot/infrastructure/repository/ai-bot-common/llm/gdmap"
	"bot/infrastructure/repository/ai-bot-common/mcpclien"
	aibotnlpimpl "bot/infrastructure/repository/ai-bot-nlp-impl/system"
	"context"
	"encoding/json"
	"fmt"
	"testing"
)

func TestBaseNluIntent_MatchSlots(t *testing.T) {
	intent := aibotnlpimpl.NewBaseNluIntent()
	text := "现在几点了"
	r := intent.Intent(context.Background(), text)
	fmt.Printf("Domain %s, Intent: %s\n", r.Domain, r.Intent)
	indent, err := json.MarshalIndent(r, "", "  ")
	if err != nil {
		return
	}
	fmt.Println(string(indent))
}

func TestWeatherIntent(t *testing.T) {
	intent := aibotnlpimpl.NewBaseNluIntent()
	text := "我在哪里"
	r := intent.Intent(context.Background(), text)
	fmt.Printf("Domain %s, Intent: %s\n", r.Domain, r.Intent)
	indent, err := json.MarshalIndent(r, "", "  ")
	if err != nil {
		return
	}
	fmt.Println(string(indent))
}

func TestJson(t *testing.T) {
	imap := gdmap.NewGDMap("05f0304a9f5ad3e34d522457fbd8ae7b")
	//baseCalling := system_fun.NewBaseCalling(imap)
	//positionFunc, err := baseCalling.QueryLocalPositionFunc(context.Background())
	//if err != nil {
	//	return
	//}
	//t.Log(positionFunc)

	location, err := imap.IpLocation(context.Background())
	if err != nil {
		return
	}
	t.Log(location)
}

func TestName(t *testing.T) {
	v := `{
  "dd6843fa-1e07-44e0-a328-bb7f1823e509" : {
    "name" : "maps_text_search",
    "arguments" : {
      "keywords" : "深圳必游景点",
      "city" : "深圳"
    }
  }
}`
	var data map[string]mcpclien.McpCall
	json.Unmarshal([]byte(v), &data)
	t.Log(data)
}
