package bot

import (
	"bot/application/dto"
	"bot/infrastructure/common/resp"
	"bot/infrastructure/po"
	"github.com/gin-gonic/gin"
)

// SendWriter
// @Summary
// @Description  聊天发送消息，发送消息成功返回会 发送的消息本身
// @Tags         聊天
// @Accept       json
// @Param        args body  dto.ChatDTO true "聊天实体"
// @Produce      json
// @Success      200  {object}  resp.Response{code=int,data=any,msg=string}
// @Failure      500  {object}  resp.Response{code=int,data=any,msg=string}
// @Router       /api/bot/chat/send [post]
func (control *Control) SendWriter(c *gin.Context) {
	var data *dto.ChatDTO
	var message *po.MessagePO
	var err error
	if err = c.ShouldBind(&data); err != nil {
		resp.ERROR(c, err)
		return
	}

	if message, err = control.ChatService.Send(c, data, c.Request, c.Writer); err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, message)
}
