package bot

import (
	"bot/application/dto"
	"bot/infrastructure/common/resp"
	"bot/infrastructure/po"

	"github.com/gin-gonic/gin"
)

// SendPainting
// @Summary
// @Description  绘画发送消息，发送消息成功返回会 发送的消息本身
// @Tags         绘画
// @Accept       json
// @Param        args body  dto.PaintingDTO true "绘画实体"
// @Produce      json
// @Success      200  {object}  resp.Response{code=int,data=any,msg=string}
// @Failure      500  {object}  resp.Response{code=int,data=any,msg=string}
// @Router       /api/bot/painting/send [post]
func (control *Control) SendPainting(c *gin.Context) {
	var data *dto.PaintingDTO
	var message *po.PaintingPO
	var err error
	if err = c.ShouldBind(&data); err != nil {
		resp.ERROR(c, err)
		return
	}

	if message, err = control.PaintingService.Send(c, data, c.Request, c.Writer); err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, message)
}

func (control *Control) SavePainting(c *gin.Context) {
	var data *dto.PaintingDTO
	var err error
	if err = c.ShouldBind(&data); err != nil {
		resp.ERROR(c, err)
		return
	}
}

func (control *Control) PaintingStream(c *gin.Context) {
	var data *dto.PaintingDTO
	var err error
	if err = c.ShouldBind(&data); err != nil {
		resp.ERROR(c, err)
		return
	}
	control.PaintingService.PaintingStream(c, data, c.Request, c.Writer)
}
