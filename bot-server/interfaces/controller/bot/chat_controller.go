package bot

import (
	"bot/application/dto"
	"bot/infrastructure/common/resp"
	"bot/infrastructure/po"

	"github.com/gin-gonic/gin"
)

// Send
// @Summary
// @Description  聊天发送消息，发送消息成功返回会 发送的消息本身
// @Tags         聊天
// @Accept       json
// @Param        args body  dto.ChatDTO true "聊天实体"
// @Produce      json
// @Success      200  {object}  resp.Response{code=int,data=any,msg=string}
// @Failure      500  {object}  resp.Response{code=int,data=any,msg=string}
// @Router       /api/bot/chat/send [post]
func (control *Control) Send(c *gin.Context) {
	var data *dto.ChatDTO
	var message *po.MessagePO
	var err error
	if err = c.ShouldBind(&data); err != nil {
		resp.ERROR(c, err)
		return
	}

	if message, err = control.ChatService.Send(c, data, c.Request, c.Writer); err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, message)
}

// SaveAgent
// @Summary
// @Description  保存代理消息
// @Tags         聊天
// @Accept       json
// @Param        args body  dto.ChatDTO true "聊天实体"
// @Produce      json
// @Success      200  {object}  resp.Response{code=int,data=any,msg=string}
// @Failure      500  {object}  resp.Response{code=int,data=any,msg=string}
// @Router       /api/bot/chat/saveAgent [post]
func (control *Control) SaveAgent(c *gin.Context) {
	var data *dto.SaveAgentDTO
	var err error
	if err = c.ShouldBind(&data); err != nil {
		resp.ERROR(c, err)
		return
	}
	// 更新消息内容
	control.ManageService.ChatDB.Model(&po.MessagePO{}).Where("id = ?", data.ID).Update("content", data.Content)
	message := new(po.MessagePO)
	// 获取消息
	control.ManageService.ChatDB.Where("id = ?", data.ID).First(message)
	resp.SUCCESS(c, message)
}

// SendClear
// @Summary
// @Description  发送清空消息上下文
// @Tags         聊天
// @Accept       json
// @Param        args body  dto.ChatDTO true "聊天实体"
// @Produce      json
// @Success      200  {object}  resp.Response{code=int,data=any,msg=string}
// @Failure      500  {object}  resp.Response{code=int,data=any,msg=string}
// @Router       /api/bot/chat/clear [post]
func (control *Control) SendClear(c *gin.Context) {
	var data *dto.ChatDTO

	var err error
	if err = c.ShouldBind(&data); err != nil {
		resp.ERROR(c, err)
		return
	}

	var message = new(po.MessagePO)
	message.ID = control.ManageService.Node.String()
	message.MessageType = data.MessageType
	message.ConversationID = data.ConversationID
	message.Role = data.Role
	if err = control.ManageService.ChatDB.Create(message).Error; err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, message)
}

// ChatStream
// @Summary
// @Description  根据消息获取消息的流式响应
// @Tags         聊天流式
// @Accept       json
// @Param        args body  dto.ChatDTO true "聊天实体"
// @Produce      json
// @Success      200  {object}  resp.Response{code=int,data=any,msg=string}
// @Failure      500  {object}  resp.Response{code=int,data=any,msg=string}
// @Router       /api/bot/chat/stream [post]
func (control *Control) ChatStream(c *gin.Context) {
	var data *dto.ChatDTO
	var err error
	if err = c.ShouldBind(&data); err != nil {
		// todo 优化直接下发错误数据 存储为一个完整的消息
		resp.ERROR(c, err)
		return
	}
	control.ChatService.ChatStream(c, data, c.Request, c.Writer)
}

// ChatStream
// @Summary
// @Description  根据消息获取消息的流式响应
// @Tags         聊天流式
// @Accept       json
// @Param        args body  dto.ChatDTO true "聊天实体"
// @Produce      json
// @Success      200  {object}  resp.Response{code=int,data=any,msg=string}
// @Failure      500  {object}  resp.Response{code=int,data=any,msg=string}
// @Router       /api/bot/chat/stream [post]
func (control *Control) AgentStream(c *gin.Context) {
	var data *dto.ChatDTO
	var err error
	if err = c.ShouldBind(&data); err != nil {
		// todo 优化直接下发错误数据 存储为一个完整的消息
		resp.ERROR(c, err)
		return
	}
	control.ChatService.ChatStream(c, data, c.Request, c.Writer)
}
