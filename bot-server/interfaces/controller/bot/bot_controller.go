package bot

import (
	aibot "bot/application/service/ai-bot"
	"bot/conf"
	"bot/infrastructure/common/logs"
)

type Control struct {
	*logs.AppLog
	*conf.Configuration
	*aibot.ChatService
	*aibot.PaintingService
	ManageService *aibot.BotManageService
}

func NewControl(
	log *logs.AppLog,
	configuration *conf.Configuration,
	service *aibot.ChatService,
	paintingService *aibot.PaintingService,
	manageService *aibot.BotManageService,
) *Control {
	return &Control{
		AppLog:          log,
		Configuration:   configuration,
		ChatService:     service,
		PaintingService: paintingService,
		ManageService:   manageService,
	}
}
