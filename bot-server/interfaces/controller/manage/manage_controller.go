package manage

import (
	aibot "bot/application/service/ai-bot"
	"bot/conf"
	"bot/infrastructure/common/logs"
	"fmt"
	"os"
	"strings"

	"github.com/gin-gonic/gin"
)

type Control struct {
	*logs.AppLog
	*conf.Configuration
	ManageService *aibot.BotManageService
}

func NewControl(
	log *logs.AppLog,
	configuration *conf.Configuration,
	manageService *aibot.BotManageService,
) *Control {
	return &Control{
		AppLog:        log,
		Configuration: configuration,
		ManageService: manageService,
	}
}

func (control *Control) Resource(c *gin.Context) {
	requestURI := c.Request.RequestURI
	p := "/api/resource/"
	index := strings.Index(requestURI, p)
	path := requestURI[index+len(p):]
	fmt.Println(path)

	file, err := os.ReadFile(fmt.Sprintf("./static/%s", path))
	if err != nil {
		return
	}
	c.Writer.Header().Set("Content-Type", "image/png")
	c.Writer.Write(file)
}
