package manage

import (
	"bot/application/dto"
	"bot/infrastructure/common/resp"
	"github.com/gin-gonic/gin"
)

// QueryPaintingNegativePrompts
// @Summary 查询负面提示词列表
// @Description 获取所有负面提示词列表
// @Tags 负面提示词管理
// @Accept json
// @Produce json
// @Success 200 {object} resp.Response{code=int,data=[]po.PaintingNegativePromptPO,msg=string}
// @Failure 500 {object} resp.Response{code=int,data=any,msg=string}
// @Router /api/bot/painting-negative-prompts [get]
func (control *Control) QueryPaintingNegativePrompts(c *gin.Context) {
	prompts, err := control.ManageService.QueryPaintingNegativePrompts(c)
	if err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, prompts)
}

// QueryPaintingNegativePromptsByCondition
// @Summary 根据条件查询负面提示词列表
// @Description 根据查询条件获取负面提示词列表
// @Tags 负面提示词管理
// @Accept json
// @Param args body dto.PaintingNegativePromptDTO false "查询参数"
// @Produce json
// @Success 200 {object} resp.Response{code=int,data=[]po.PaintingNegativePromptPO,msg=string}
// @Failure 500 {object} resp.Response{code=int,data=any,msg=string}
// @Router /api/bot/painting-negative-prompts/query [post]
func (control *Control) QueryPaintingNegativePromptsByCondition(c *gin.Context) {
	var data *dto.PaintingNegativePromptDTO
	if err := c.ShouldBindJSON(&data); err != nil {
		resp.ERROR(c, err)
		return
	}

	prompts, err := control.ManageService.QueryPaintingNegativePromptsByCondition(c, data)
	if err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, prompts)
}

// CreatePaintingNegativePrompt
// @Summary 创建负面提示词
// @Description 创建新的负面提示词
// @Tags 负面提示词管理
// @Accept json
// @Param args body dto.PaintingNegativePromptDTO true "负面提示词参数"
// @Produce json
// @Success 200 {object} resp.Response{code=int,data=po.PaintingNegativePromptPO,msg=string}
// @Failure 500 {object} resp.Response{code=int,data=any,msg=string}
// @Router /api/bot/painting-negative-prompts/create [post]
func (control *Control) CreatePaintingNegativePrompt(c *gin.Context) {
	var data *dto.PaintingNegativePromptDTO
	if err := c.ShouldBindJSON(&data); err != nil {
		resp.ERROR(c, err)
		return
	}

	prompt, err := control.ManageService.CreatePaintingNegativePrompt(c, data)
	if err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, prompt)
}

// UpdatePaintingNegativePrompt
// @Summary 更新负面提示词
// @Description 更新指定的负面提示词
// @Tags 负面提示词管理
// @Accept json
// @Param args body dto.PaintingNegativePromptDTO true "负面提示词参数"
// @Produce json
// @Success 200 {object} resp.Response{code=int,data=po.PaintingNegativePromptPO,msg=string}
// @Failure 500 {object} resp.Response{code=int,data=any,msg=string}
// @Router /api/bot/painting-negative-prompts/update [post]
func (control *Control) UpdatePaintingNegativePrompt(c *gin.Context) {
	var data *dto.PaintingNegativePromptDTO
	if err := c.ShouldBindJSON(&data); err != nil {
		resp.ERROR(c, err)
		return
	}

	prompt, err := control.ManageService.UpdatePaintingNegativePrompt(c, data)
	if err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, prompt)
}

// DeletePaintingNegativePrompt
// @Summary 删除负面提示词
// @Description 删除指定的负面提示词
// @Tags 负面提示词管理
// @Accept json
// @Param args body dto.PaintingNegativePromptDTO true "负面提示词参数"
// @Produce json
// @Success 200 {object} resp.Response{code=int,data=any,msg=string}
// @Failure 500 {object} resp.Response{code=int,data=any,msg=string}
// @Router /api/bot/painting-negative-prompts/delete [post]
func (control *Control) DeletePaintingNegativePrompt(c *gin.Context) {
	var data *dto.PaintingNegativePromptDTO
	if err := c.ShouldBindJSON(&data); err != nil {
		resp.ERROR(c, err)
		return
	}

	err := control.ManageService.DeletePaintingNegativePrompt(c, data)
	if err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, nil)
}

// GetPaintingNegativePrompt
// @Summary 获取单个负面提示词
// @Description 根据ID获取指定的负面提示词
// @Tags 负面提示词管理
// @Accept json
// @Param args body dto.PaintingNegativePromptDTO true "负面提示词参数"
// @Produce json
// @Success 200 {object} resp.Response{code=int,data=po.PaintingNegativePromptPO,msg=string}
// @Failure 500 {object} resp.Response{code=int,data=any,msg=string}
// @Router /api/bot/painting-negative-prompts/get [post]
func (control *Control) GetPaintingNegativePrompt(c *gin.Context) {
	var data *dto.PaintingNegativePromptDTO
	if err := c.ShouldBindJSON(&data); err != nil {
		resp.ERROR(c, err)
		return
	}

	prompt, err := control.ManageService.GetPaintingNegativePrompt(c, data)
	if err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, prompt)
} 