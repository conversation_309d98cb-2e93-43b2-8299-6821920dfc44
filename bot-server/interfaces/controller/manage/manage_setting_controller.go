package manage

import (
	"bot/application/dto"
	"bot/infrastructure/common/resp"
	"github.com/gin-gonic/gin"
)

func (control *Control) Setting(c *gin.Context) {
	manageService := control.ManageService
	setting, err := manageService.SystemSetting(c)
	if err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, setting)
}

func (control *Control) UpdateSetting(c *gin.Context) {
	var err error
	var setting *dto.SettingDTO
	if err = c.ShouldBindJSON(&setting); err != nil {
		resp.ERROR(c, err)
		return
	}
	if err = control.ManageService.UpdateSystemSetting(c, setting); err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, nil)
}

func (control *Control) Clear(c *gin.Context) {
	manageService := control.ManageService
	if err := manageService.Clear(c); err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, nil)
}
