package manage

import (
	"bot/application/dto"
	"bot/infrastructure/common/resp"
	"github.com/gin-gonic/gin"
)

// Platforms
// @Summary
// @Description  获取系统内的所有平台信息
// @Tags         平台
// @Accept       json
// @Param        args body  dto.ConversationDTO true "会话实体"
// @Produce      json
// @Success      200  {object}  resp.Response{code=int,data=any,msg=string}
// @Failure      500  {object}  resp.Response{code=int,data=any,msg=string}
// @Router       /api/bot/platforms [get]
func (control *Control) Platforms(c *gin.Context) {
	service := control.ManageService
	platforms, err := service.Platforms(c)
	if err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, platforms)
}

// CreatePlatform
// @Summary
// @Description  创建添加平台
// @Tags         平台
// @Accept       json
// @Param        args body  dto.PlatformDTO true "平台实体"
// @Produce      json
// @Success      200  {object}  resp.Response{code=int,data=any,msg=string}
// @Failure      500  {object}  resp.Response{code=int,data=any,msg=string}
// @Router       /api/bot/platforms/create [post]
func (control *Control) CreatePlatform(c *gin.Context) {
	service := control.ManageService
	var data *dto.PlatformDTO
	if err := c.BindJSON(&data); err != nil {
		resp.ERROR(c, err)
		return
	}
	if err := service.CreatePlatform(c, data); err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, nil)
}

// UpdatePlatform
// @Summary
// @Description  修改平台配置
// @Tags         平台
// @Accept       json
// @Param        args body  dto.PlatformDTO true "会话实体"
// @Produce      json
// @Success      200  {object}  resp.Response{code=int,data=any,msg=string}
// @Failure      500  {object}  resp.Response{code=int,data=any,msg=string}
// @Router       /api/bot/platforms/update [post]
func (control *Control) UpdatePlatform(c *gin.Context) {
	service := control.ManageService
	var data *dto.PlatformDTO
	if err := c.BindJSON(&data); err != nil {
		resp.ERROR(c, err)
		return
	}
	if err := service.UpdatePlatform(c, data); err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, nil)
}

// DeletePlatforms
// @Summary
// @Description  删除平台
// @Tags         平台
// @Accept       json
// @Param        args body  dto.ConversationDTO true "会话实体"
// @Produce      json
// @Success      200  {object}  resp.Response{code=int,data=any,msg=string}
// @Failure      500  {object}  resp.Response{code=int,data=any,msg=string}
// @Router       /api/bot/platforms/delete [post]
func (control *Control) DeletePlatforms(c *gin.Context) {
	service := control.ManageService
	var data *dto.PlatformDTO
	if err := c.BindJSON(&data); err != nil {
		resp.ERROR(c, err)
		return
	}
	if err := service.DeletePlatforms(c, data); err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, nil)
}
