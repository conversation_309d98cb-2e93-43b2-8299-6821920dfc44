package manage

import (
	"bot/application/dto"
	"bot/infrastructure/common/resp"
	"errors"

	"github.com/gin-gonic/gin"
)

// Files
// @Summary 查询文件列表
// @Description 根据查询条件获取文件列表
// @Tags 文件管理
// @Accept json
// @Param args body dto.FileQueryDTO false "查询参数"
// @Produce json
// @Success 200 {object} resp.Response{code=int,data=any,msg=string}
// @Failure 500 {object} resp.Response{code=int,data=any,msg=string}
// @Router /api/bot/files [post]
func (control *Control) Files(c *gin.Context) {
	var data *dto.FileQueryDTO
	if err := c.ShouldBindJSON(&data); err != nil {
		resp.ERROR(c, err)
		return
	}

	files, err := control.ManageService.QueryFiles(c, data)
	if err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, files)
}

// FilesByPID
// @Summary 根据父级ID查询文件列表
// @Description 获取指定目录下的文件列表
// @Tags 文件管理
// @Accept json
// @Param pid query string false "父级ID"
// @Produce json
// @Success 200 {object} resp.Response{code=int,data=any,msg=string}
// @Failure 500 {object} resp.Response{code=int,data=any,msg=string}
// @Router /api/bot/files/by-pid [get]
func (control *Control) FilesByPID(c *gin.Context) {
	pid := c.Query("pid")

	files, err := control.ManageService.QueryFilesByPID(c, pid)
	if err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, files)
}

// FileTree
// @Summary 获取文件树结构
// @Description 获取指定根目录的文件树结构
// @Tags 文件管理
// @Accept json
// @Param root_pid query string false "根目录ID"
// @Produce json
// @Success 200 {object} resp.Response{code=int,data=any,msg=string}
// @Failure 500 {object} resp.Response{code=int,data=any,msg=string}
// @Router /api/bot/files/tree [get]
func (control *Control) FileTree(c *gin.Context) {
	rootPID := c.Query("root_pid")

	files, err := control.ManageService.GetFileTree(c, rootPID)
	if err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, files)
}

// CreateFile
// @Summary 创建文件或目录
// @Description 在指定目录下创建新文件或目录
// @Tags 文件管理
// @Accept json
// @Param args body dto.FileCreateDTO true "文件创建参数"
// @Produce json
// @Success 200 {object} resp.Response{code=int,data=any,msg=string}
// @Failure 500 {object} resp.Response{code=int,data=any,msg=string}
// @Router /api/bot/files/create [post]
func (control *Control) CreateFile(c *gin.Context) {
	var data *dto.FileCreateDTO
	if err := c.ShouldBindJSON(&data); err != nil {
		resp.ERROR(c, err)
		return
	}

	file, err := control.ManageService.CreateFile(c, data)
	if err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, file)
}

// UpdateFile
// @Summary 更新文件信息
// @Description 更新文件名称或移动文件位置
// @Tags 文件管理
// @Accept json
// @Param args body dto.FileUpdateDTO true "文件更新参数"
// @Produce json
// @Success 200 {object} resp.Response{code=int,data=any,msg=string}
// @Failure 500 {object} resp.Response{code=int,data=any,msg=string}
// @Router /api/bot/files/update [post]
func (control *Control) UpdateFile(c *gin.Context) {
	var data *dto.FileUpdateDTO
	if err := c.ShouldBindJSON(&data); err != nil {
		resp.ERROR(c, err)
		return
	}

	file, err := control.ManageService.UpdateFile(c, data)
	if err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, file)
}

// DeleteFile
// @Summary 删除文件或目录
// @Description 删除指定的文件或空目录
// @Tags 文件管理
// @Accept json
// @Param args body dto.FileDeleteDTO true "文件删除参数"
// @Produce json
// @Success 200 {object} resp.Response{code=int,data=any,msg=string}
// @Failure 500 {object} resp.Response{code=int,data=any,msg=string}
// @Router /api/bot/files/delete [post]
func (control *Control) DeleteFile(c *gin.Context) {
	var data *dto.FileDeleteDTO
	if err := c.ShouldBindJSON(&data); err != nil {
		resp.ERROR(c, err)
		return
	}

	if err := control.ManageService.DeleteFile(c, data); err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, nil)
}

// MoveFile
// @Summary 移动文件或目录
// @Description 将文件或目录移动到新的父目录
// @Tags 文件管理
// @Accept json
// @Param file_id query string true "文件ID"
// @Param new_pid query string true "新父级目录ID"
// @Produce json
// @Success 200 {object} resp.Response{code=int,data=any,msg=string}
// @Failure 500 {object} resp.Response{code=int,data=any,msg=string}
// @Router /api/bot/files/move [post]
func (control *Control) MoveFile(c *gin.Context) {
	fileID := c.Query("file_id")
	newPID := c.Query("new_pid")

	if fileID == "" {
		resp.ERROR(c, errors.New("文件ID不能为空"))
		return
	}

	file, err := control.ManageService.MoveFile(c, fileID, newPID)
	if err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, file)
}

// GetFileInfo
// @Summary 获取文件信息
// @Description 根据文件ID获取详细信息
// @Tags 文件管理
// @Accept json
// @Param id query string true "文件ID"
// @Produce json
// @Success 200 {object} resp.Response{code=int,data=any,msg=string}
// @Failure 500 {object} resp.Response{code=int,data=any,msg=string}
// @Router /api/bot/files/info [get]
func (control *Control) GetFileInfo(c *gin.Context) {
	fileID := c.Query("id")
	if fileID == "" {
		resp.ERROR(c, errors.New("文件ID不能为空"))
		return
	}

	data := &dto.FileQueryDTO{ID: fileID}
	file, err := control.ManageService.QueryFile(c, data)
	if err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, file)
}
