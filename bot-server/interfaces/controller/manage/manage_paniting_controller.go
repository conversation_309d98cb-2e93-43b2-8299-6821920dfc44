package manage

import (
	"bot/application/dto"
	"bot/infrastructure/common/resp"

	"github.com/gin-gonic/gin"
)

// QueryPaintingConfigs
// @Summary 查询绘画配置列表
// @Description 获取所有绘画配置列表
// @Tags 绘画配置管理
// @Accept json
// @Produce json
// @Success 200 {object} resp.Response{code=int,data=[]po.PaintingConfigPO,msg=string}
// @Failure 500 {object} resp.Response{code=int,data=any,msg=string}
// @Router /api/bot/painting-configs [get]
func (control *Control) QueryPaintingConfigs(c *gin.Context) {
	configs, err := control.ManageService.QueryPaintingConfigs(c)
	if err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, configs)
}

// QueryPaintingConfigsByCondition
// @Summary 根据条件查询绘画配置列表
// @Description 根据查询条件获取绘画配置列表
// @Tags 绘画配置管理
// @Accept json
// @Param args body dto.PaintingConfigQueryDTO false "查询参数"
// @Produce json
// @Success 200 {object} resp.Response{code=int,data=[]po.PaintingConfigPO,msg=string}
// @Failure 500 {object} resp.Response{code=int,data=any,msg=string}
// @Router /api/bot/painting-configs/query [post]
func (control *Control) QueryPaintingConfigsByCondition(c *gin.Context) {
	var data *dto.PaintingConfigQueryDTO
	if err := c.ShouldBindJSON(&data); err != nil {
		resp.ERROR(c, err)
		return
	}

	configs, err := control.ManageService.QueryPaintingConfigsByCondition(c, data)
	if err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, configs)
}

// CreatePaintingConfig
// @Summary 创建绘画配置
// @Description 创建新的绘画配置
// @Tags 绘画配置管理
// @Accept json
// @Param args body dto.PaintingConfigDTO true "绘画配置参数"
// @Produce json
// @Success 200 {object} resp.Response{code=int,data=po.PaintingConfigPO,msg=string}
// @Failure 500 {object} resp.Response{code=int,data=any,msg=string}
// @Router /api/bot/painting-configs/create [post]
func (control *Control) CreatePaintingConfig(c *gin.Context) {
	var data *dto.PaintingConfigDTO
	if err := c.ShouldBindJSON(&data); err != nil {
		resp.ERROR(c, err)
		return
	}

	config, err := control.ManageService.CreatePaintingConfig(c, data)
	if err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, config)
}

// UpdatePaintingConfig
// @Summary 更新绘画配置
// @Description 更新指定的绘画配置
// @Tags 绘画配置管理
// @Accept json
// @Param args body dto.PaintingConfigDTO true "绘画配置参数"
// @Produce json
// @Success 200 {object} resp.Response{code=int,data=po.PaintingConfigPO,msg=string}
// @Failure 500 {object} resp.Response{code=int,data=any,msg=string}
// @Router /api/bot/painting-configs/update [post]
func (control *Control) UpdatePaintingConfig(c *gin.Context) {
	var data *dto.PaintingConfigDTO
	if err := c.ShouldBindJSON(&data); err != nil {
		resp.ERROR(c, err)
		return
	}

	config, err := control.ManageService.UpdatePaintingConfig(c, data)
	if err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, config)
}

// DeletePaintingConfig
// @Summary 删除绘画配置
// @Description 删除指定的绘画配置
// @Tags 绘画配置管理
// @Accept json
// @Param args body dto.PaintingConfigDTO true "绘画配置参数"
// @Produce json
// @Success 200 {object} resp.Response{code=int,data=any,msg=string}
// @Failure 500 {object} resp.Response{code=int,data=any,msg=string}
// @Router /api/bot/painting-configs/delete [post]
func (control *Control) DeletePaintingConfig(c *gin.Context) {
	var data *dto.PaintingConfigDTO
	if err := c.ShouldBindJSON(&data); err != nil {
		resp.ERROR(c, err)
		return
	}

	err := control.ManageService.DeletePaintingConfig(c, data)
	if err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, nil)
}

// GetPaintingConfig
// @Summary 获取单个绘画配置
// @Description 根据ID获取指定的绘画配置
// @Tags 绘画配置管理
// @Accept json
// @Param args body dto.PaintingConfigDTO true "绘画配置参数"
// @Produce json
// @Success 200 {object} resp.Response{code=int,data=po.PaintingConfigPO,msg=string}
// @Failure 500 {object} resp.Response{code=int,data=any,msg=string}
// @Router /api/bot/painting-configs/get [post]
func (control *Control) GetPaintingConfig(c *gin.Context) {
	var data *dto.PaintingConfigDTO
	if err := c.ShouldBindJSON(&data); err != nil {
		resp.ERROR(c, err)
		return
	}

	config, err := control.ManageService.GetPaintingConfig(c, data)
	if err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, config)
}

// QueryPaintingRecords
// @Summary 查询绘画记录列表
// @Description 获取所有绘画记录列表
// @Tags 绘画记录管理
// @Accept json
// @Produce json
// @Success 200 {object} resp.Response{code=int,data=[]po.PaintingPO,msg=string}
// @Failure 500 {object} resp.Response{code=int,data=any,msg=string}
// @Router /api/bot/painting-records [get]
func (control *Control) QueryPaintingRecords(c *gin.Context) {
	records, err := control.ManageService.QueryPaintingRecords(c)
	if err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, records)
}

// QueryPaintingRecordsByCondition
// @Summary 根据条件查询绘画记录列表
// @Description 根据查询条件获取绘画记录列表
// @Tags 绘画记录管理
// @Accept json
// @Param args body dto.PaintingRecordQueryDTO false "查询参数"
// @Produce json
// @Success 200 {object} resp.Response{code=int,data=[]po.PaintingPO,msg=string}
// @Failure 500 {object} resp.Response{code=int,data=any,msg=string}
// @Router /api/bot/painting-records/query [post]
func (control *Control) QueryPaintingRecordsByCondition(c *gin.Context) {
	var data *dto.PaintingRecordQueryDTO
	if err := c.ShouldBindJSON(&data); err != nil {
		resp.ERROR(c, err)
		return
	}

	records, err := control.ManageService.QueryPaintingRecordsByCondition(c, data)
	if err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, records)
}

// CreatePaintingRecord
// @Summary 创建绘画记录
// @Description 创建新的绘画记录
// @Tags 绘画记录管理
// @Accept json
// @Param args body dto.PaintingRecordDTO true "绘画记录参数"
// @Produce json
// @Success 200 {object} resp.Response{code=int,data=po.PaintingPO,msg=string}
// @Failure 500 {object} resp.Response{code=int,data=any,msg=string}
// @Router /api/bot/painting-records/create [post]
func (control *Control) CreatePaintingRecord(c *gin.Context) {
	var data *dto.PaintingRecordDTO
	if err := c.ShouldBindJSON(&data); err != nil {
		resp.ERROR(c, err)
		return
	}

	record, err := control.ManageService.CreatePaintingRecord(c, data)
	if err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, record)
}

// UpdatePaintingRecord
// @Summary 更新绘画记录
// @Description 更新指定的绘画记录
// @Tags 绘画记录管理
// @Accept json
// @Param args body dto.PaintingRecordDTO true "绘画记录参数"
// @Produce json
// @Success 200 {object} resp.Response{code=int,data=po.PaintingPO,msg=string}
// @Failure 500 {object} resp.Response{code=int,data=any,msg=string}
// @Router /api/bot/painting-records/update [post]
func (control *Control) UpdatePaintingRecord(c *gin.Context) {
	var data *dto.PaintingRecordDTO
	if err := c.ShouldBindJSON(&data); err != nil {
		resp.ERROR(c, err)
		return
	}

	record, err := control.ManageService.UpdatePaintingRecord(c, data)
	if err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, record)
}

// DeletePaintingRecord
// @Summary 删除绘画记录
// @Description 删除指定的绘画记录
// @Tags 绘画记录管理
// @Accept json
// @Param args body dto.PaintingRecordDTO true "绘画记录参数"
// @Produce json
// @Success 200 {object} resp.Response{code=int,data=any,msg=string}
// @Failure 500 {object} resp.Response{code=int,data=any,msg=string}
// @Router /api/bot/painting-records/delete [post]
func (control *Control) DeletePaintingRecord(c *gin.Context) {
	var data *dto.PaintingRecordDTO
	if err := c.ShouldBindJSON(&data); err != nil {
		resp.ERROR(c, err)
		return
	}

	err := control.ManageService.DeletePaintingRecord(c, data)
	if err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, nil)
}

// GetPaintingRecord
// @Summary 获取单个绘画记录
// @Description 根据ID获取指定的绘画记录
// @Tags 绘画记录管理
// @Accept json
// @Param args body dto.PaintingRecordDTO true "绘画记录参数"
// @Produce json
// @Success 200 {object} resp.Response{code=int,data=po.PaintingPO,msg=string}
// @Failure 500 {object} resp.Response{code=int,data=any,msg=string}
// @Router /api/bot/painting-records/get [post]
func (control *Control) GetPaintingRecord(c *gin.Context) {
	var data *dto.PaintingRecordDTO
	if err := c.ShouldBindJSON(&data); err != nil {
		resp.ERROR(c, err)
		return
	}

	record, err := control.ManageService.GetPaintingRecord(c, data)
	if err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, record)
}
