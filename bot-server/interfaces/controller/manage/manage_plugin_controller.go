package manage

import (
	"bot/application/dto"
	"bot/infrastructure/common/resp"
	"github.com/gin-gonic/gin"
)

func (control *Control) Plugins(c *gin.Context) {
	service := control.ManageService
	plugins, err := service.Plugins(c)
	if err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, plugins)
}

func (control *Control) UpdatePlugin(c *gin.Context) {
	var err error
	var plugin *dto.PluginDTO
	if err = c.ShouldBindJSON(&plugin); err != nil {
		resp.ERROR(c, err)
		return
	}
	if err = control.ManageService.UpdatePlugin(c, plugin); err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, nil)
}

func (control *Control) UpdateAllPluginPlatform(c *gin.Context) {
	var err error
	var plugin *dto.PlatformDTO
	if err = c.ShouldBindJSON(&plugin); err != nil {
		resp.ERROR(c, err)
		return
	}
	if err = control.ManageService.UpdateAllPluginPlatform(c, plugin); err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, nil)
}
