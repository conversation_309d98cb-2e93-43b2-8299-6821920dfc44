package manage

import (
	"bot/application/dto"
	"bot/domain/entity"
	"bot/infrastructure/common/resp"
	"context"
	"time"

	"github.com/chromedp/cdproto/network"
	"github.com/chromedp/chromedp"
	"github.com/gin-gonic/gin"
)

func (control *Control) OllamaModels(c *gin.Context) {
	manageService := control.ManageService
	var err error
	var data any
	var param *dto.PlatformDTO
	if err = c.ShouldBind(&param); err != nil {
		resp.ERROR(c, err)
		return
	}
	if data, err = manageService.OllamaModels(c, param); err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, data)
}

func (control *Control) DeepSeekModels(c *gin.Context) {
	manageService := control.ManageService
	var err error
	var data any
	var param *dto.PlatformDTO
	if err = c.ShouldBind(&param); err != nil {
		resp.ERROR(c, err)
		return
	}
	if data, err = manageService.DeepSeekModels(c, param); err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, data)
}

func (control *Control) GiteeAIModels(c *gin.Context) {
	manageService := control.ManageService
	var err error
	var data any
	var param *dto.PlatformModelQueryDTO
	if err = c.ShouldBind(&param); err != nil {
		resp.ERROR(c, err)
		return
	}
	if data, err = manageService.GiteeAIModels(c, param.PlatformDTO, param.Model); err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, data)
}

func (control *Control) GiteeAIFreeModels(c *gin.Context) {
	var err error
	var htmlContent string

	// 创建一个新的Chrome上下文
	ctx, cancel := chromedp.NewContext(context.Background())
	defer cancel()

	// 设置超时时间
	ctx, cancel = context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	// 使用chromedp请求页面并等待API数据加载
	err = chromedp.Run(ctx,
		network.Enable(), // 启用网络监控
		chromedp.Navigate("https://ai.gitee.com/serverless-api"),
		chromedp.ActionFunc(func(ctx context.Context) error {
			// 等待网页完整加载
			time.Sleep(5 * time.Second)
			return nil
		}),
		chromedp.OuterHTML("html", &htmlContent),
	)

	if err != nil {
		resp.ERROR(c, err)
		return
	}

	c.Writer.Write([]byte(htmlContent))
}

func (control *Control) VolcengineModels(c *gin.Context) {
	data := []entity.ModelEntity{
		{
			Name:        "Qwen2.5-72B-Instruct",
			Description: "聊天机器人",
			Type:        "QA",
		},
	}
	resp.SUCCESS(c, data)
}
