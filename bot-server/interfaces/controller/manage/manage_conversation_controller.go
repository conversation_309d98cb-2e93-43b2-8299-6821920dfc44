package manage

import (
	"bot/application/dto"
	"bot/infrastructure/common/resp"
	"bot/infrastructure/po"

	"github.com/gin-gonic/gin"
)

// Conversations
// @Summary
// @Description
// @Tags         会话
// @Accept       json
// @Produce      json
// @Success      200  {object}  resp.Response{code=int,data=any,msg=string}
// @Failure      500  {object}  resp.Response{code=int,data=any,msg=string}
// @Router       /api/bot/conversations [get]
func (control *Control) Conversations(c *gin.Context) {

	service := control.ManageService
	conversations, err := service.Conversations(c)
	if err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, conversations)
}

// DelConversation
// @Summary
// @Description
// @Tags         会话
// @Accept       json
// @Param        args body  dto.CrudDTO true "会话实体"
// @Produce      json
// @Success      200  {object}  resp.Response{code=int,data=any,msg=string}
// @Failure      500  {object}  resp.Response{code=int,data=any,msg=string}
// @Router       /api/bot/conversations/delete [post]
func (control *Control) DelConversation(c *gin.Context) {
	var err error
	var data *dto.ConversationDTO
	if err = c.BindJSON(&data); err != nil {
		resp.ERROR(c, err)
		return
	}
	service := control.ManageService
	if err = service.DeleteConversations(c, data); err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, nil)
}

// UpdateConversation
// @Summary
// @Description
// @Tags         会话
// @Accept       json
// @Param        args body  dto.CrudDTO true "会话实体"
// @Produce      json
// @Success      200  {object}  resp.Response{code=int,data=any,msg=string}
// @Failure      500  {object}  resp.Response{code=int,data=any,msg=string}
// @Router       /api/bot/conversations/update [post]
func (control *Control) UpdateConversation(c *gin.Context) {
	var err error
	var data *dto.ConversationDTO
	var result *po.ConversationPO
	if err = c.BindJSON(&data); err != nil {
		resp.ERROR(c, err)
	}
	service := control.ManageService
	if result, err = service.UpdateConversation(c, data); err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, result)
}

// CreateConversation
// @Summary
// @Description
// @Tags         会话
// @Accept       json
// @Param        args body  dto.ConversationDTO true "会话实体"
// @Produce      json
// @Success      200  {object}  resp.Response{code=int,data=any,msg=string}
// @Failure      500  {object}  resp.Response{code=int,data=any,msg=string}
// @Router       /api/bot/conversations/create [post]
func (control *Control) CreateConversation(c *gin.Context) {
	var id string
	var err error
	var data *dto.ConversationDTO
	if err = c.BindJSON(&data); err != nil {
		resp.ERROR(c, err)
		return
	}
	service := control.ManageService
	if id, err = service.CreateConversation(c, data); err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, id)
}

func (control *Control) ConversationMessages(c *gin.Context) {
	service := control.ManageService
	var data *dto.ConversationDTO
	var list []*po.MessagePO
	var err error
	if err = c.ShouldBind(&data); err != nil {
		resp.ERROR(c, err)
		return
	}
	if list, err = service.Messages(c, data); err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, list)
}

func (control *Control) ConversationMessage(c *gin.Context) {
	service := control.ManageService
	var data *dto.MessageDTO
	var message *po.MessagePO
	var err error
	if err = c.ShouldBind(&data); err != nil {
		resp.ERROR(c, err)
		return
	}
	if message, err = service.Message(c, data); err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, message)
}

func (control *Control) DelConversationMessages(c *gin.Context) {
	service := control.ManageService
	var data *dto.ConversationDTO
	if err := c.BindJSON(&data); err != nil {
		resp.ERROR(c, err)
		return
	}
	if err := service.DeleteMessages(c, data); err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, nil)
}

func (control *Control) DelConversationMessage(c *gin.Context) {
	service := control.ManageService
	var data *dto.MessageDTO
	if err := c.BindJSON(&data); err != nil {
		resp.ERROR(c, err)
		return
	}
	if err := service.DeleteMessage(c, data); err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, nil)
}

func (control *Control) UUID(c *gin.Context) {
	resp.SUCCESS(c, control.ManageService.UUID())
}
