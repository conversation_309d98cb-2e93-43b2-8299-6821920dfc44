package mcp

import (
	"bot/application/dto"
	aibot "bot/application/service/ai-bot"
	"bot/conf"
	"bot/infrastructure/common/logs"
	"bot/infrastructure/common/resp"
	"fmt"

	"github.com/gin-gonic/gin"
)

type Control struct {
	*logs.AppLog
	*conf.Configuration
	ManageService *aibot.BotMcpService
}

func NewControl(
	log *logs.AppLog,
	configuration *conf.Configuration,
	manageService *aibot.BotMcpService,
) *Control {
	return &Control{
		AppLog:        log,
		Configuration: configuration,
		ManageService: manageService,
	}
}

// List 获取MCP列表
func (control *Control) List(c *gin.Context) {

	mcpList, err := control.ManageService.GetMcpService()
	if err != nil {
		resp.ERROR(c, err)
		return
	}
	resp.SUCCESS(c, mcpList)
}

// Info 获取MCP详情
func (control *Control) Info(c *gin.Context) {

	id := c.Query("id")
	if id == "" {
		resp.ERROR(c, fmt.Errorf("ID不能为空"))
		return
	}

	mcpInfo, err := control.ManageService.GetMcpInfoService(id)
	if err != nil {
		resp.ERROR(c, err)
		return
	}

	resp.SUCCESS(c, mcpInfo)
}

func (control *Control) Tools(c *gin.Context) {

	id := c.Query("id")
	if id == "" {
		resp.ERROR(c, fmt.Errorf("ID不能为空"))
		return
	}

	mcpInfo, err := control.ManageService.GetMcpTools(id)
	if err != nil {
		resp.ERROR(c, err)
		return
	}

	resp.SUCCESS(c, mcpInfo)
}

func (control *Control) Reload(c *gin.Context) {

	id := c.Query("id")
	if id == "" {
		resp.ERROR(c, fmt.Errorf("ID不能为空"))
		return
	}

	mcpInfo, err := control.ManageService.GetMcpInfoService(id)
	if err != nil {
		resp.ERROR(c, err)
		return
	}

	resp.SUCCESS(c, mcpInfo)
}

// Add 添加MCP
func (control *Control) Add(c *gin.Context) {

	var mcpDTO dto.McpDTO
	if err := c.ShouldBindJSON(&mcpDTO); err != nil {
		resp.ERROR(c, err)
		return
	}

	err := control.ManageService.AddMcpService(&mcpDTO)
	if err != nil {
		resp.ERROR(c, err)
		return
	}

	resp.SUCCESS(c, nil)
}

// Edit 编辑MCP
func (control *Control) Edit(c *gin.Context) {

	var mcpDTO dto.McpDTO
	if err := c.ShouldBindJSON(&mcpDTO); err != nil {
		resp.ERROR(c, err)
		return
	}

	if mcpDTO.ID == "" {
		resp.ERROR(c, fmt.Errorf("ID不能为空"))
		return
	}

	err := control.ManageService.UpdateMcpService(&mcpDTO)
	if err != nil {
		resp.ERROR(c, err)
		return
	}

	resp.SUCCESS(c, nil)
}

// Delete 删除MCP
func (control *Control) Delete(c *gin.Context) {

	var params struct {
		ID string `json:"id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&params); err != nil {
		resp.ERROR(c, err)
		return
	}

	err := control.ManageService.DeleteMcpService(params.ID)
	if err != nil {
		resp.ERROR(c, err)
		return
	}

	resp.SUCCESS(c, nil)
}
