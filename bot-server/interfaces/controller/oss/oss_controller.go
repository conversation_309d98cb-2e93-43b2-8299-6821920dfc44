package oss

import (
	aibot "bot/application/service/ai-bot"
	"bot/infrastructure/common/resp"
	"bot/infrastructure/common/util/imageutil"
	"encoding/base64"
	"fmt"
	"github.com/gin-gonic/gin"
	"golang.org/x/text/encoding/simplifiedchinese"
	"golang.org/x/text/transform"
	"io"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"strings"
)

const (
	defaultServerURL = "http://localhost:2081" // 默认服务器地址
)

// Base64UploadRequest base64图片上传请求
type Base64UploadRequest struct {
	Base64Data string `json:"base64Data"` // base64编码的图片数据
	Filename   string `json:"filename"`   // 文件名（可选）
}

// CopyFromLocalRequest 从本地路径复制文件的请求结构
type CopyFromLocalRequest struct {
	LocalPath string `json:"localPath"` // 本地文件路径
}

type Control struct {
	*aibot.BotOssService
}

func NewControl(service *aibot.BotOssService) *Control {
	return &Control{
		BotOssService: service,
	}
}

// UploadBase64 处理base64图片上传
func (control *Control) UploadImageBase64(c *gin.Context) {
	var req Base64UploadRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		resp.ERROR(c, fmt.Errorf("无效的请求参数: %v", err))
		return
	}

	// 检查base64数据是否为空
	if req.Base64Data == "" {
		resp.ERROR(c, fmt.Errorf("base64数据不能为空"))
		return
	}

	// 处理上传
	result := imageutil.HandleBase64Upload(c, req.Base64Data, req.Filename)
	if result.Error != nil {
		resp.ERROR(c, result.Error)
		return
	}

	// 返回成功信息
	resp.SUCCESS(c, gin.H{
		"url": fmt.Sprintf("%s/api/resource/%s", defaultServerURL, result.Path),
	})
}

// UploadImage  处理文件上传
func (control *Control) UploadImage(c *gin.Context) {
	// 单文件上传
	file, err := c.FormFile("file")
	if err != nil {
		resp.ERROR(c, fmt.Errorf("上传文件错误: %v", err))
		return
	}

	// 处理上传
	result := imageutil.HandleFileUpload(c, file)
	if result.Error != nil {
		resp.ERROR(c, result.Error)
		return
	}
	resp.SUCCESS(c, gin.H{
		"url": fmt.Sprintf("%s/api/resource/%s", defaultServerURL, result.Path),
	})
}

// CKEditorUploadImage  提供ck编辑器文件上传
func (control *Control) CKEditorUploadImage(c *gin.Context) {
	// 单文件上传
	file, err := c.FormFile("upload")
	if err != nil {
		c.JSON(500, gin.H{
			"error": map[string]string{
				"message": err.Error(),
			},
		})
		return
	}
	// 处理上传
	result := imageutil.HandleFileUpload(c, file)
	if result.Error != nil {
		c.JSON(500, gin.H{
			"error": map[string]string{
				"message": result.Error.Error(),
			},
		})
		return
	}

	// 返回成功信息 返回 url 属性 适配 ckeditor 协议
	c.JSON(200, gin.H{
		"url": fmt.Sprintf("%s/api/resource/%s", defaultServerURL, result.Path),
	})
}

func (control *Control) Get(c *gin.Context) {
	requestURI := c.Request.RequestURI
	p := "/api/resource/"
	index := strings.Index(requestURI, p)
	if index == -1 {
		resp.ERROR(c, fmt.Errorf("无效的请求路径"))
		return
	}

	// 获取文件路径并进行 URL 解码
	encodedPath := requestURI[index+len(p):]
	path, err := url.QueryUnescape(encodedPath)
	if err != nil {
		resp.ERROR(c, fmt.Errorf("无效的文件路径编码: %v", err))
		return
	}

	// 清理路径，防止目录遍历攻击
	path = filepath.Clean(path)
	if strings.Contains(path, "..") {
		resp.ERROR(c, fmt.Errorf("非法的文件路径"))
		return
	}

	// 构建完整的文件路径
	fullPath := filepath.Join("var", path)

	// 获取文件扩展名并设置正确的 Content-Type
	ext := strings.ToLower(filepath.Ext(path))
	contentType := "application/octet-stream" // 默认二进制流
	switch ext {
	case ".jpg", ".jpeg":
		contentType = "image/jpeg"
	case ".png":
		contentType = "image/png"
	case ".gif":
		contentType = "image/gif"
	case ".webp":
		contentType = "image/webp"
	case ".bmp":
		contentType = "image/bmp"
	}

	// 设置响应头
	c.Header("Content-Type", contentType)

	// 使用 ServeFile 提供文件服务
	http.ServeFile(c.Writer, c.Request, fullPath)
}

// GetBase64 获取图片资源的base64格式数据
func (control *Control) GetBase64(c *gin.Context) {
	requestURI := c.Request.RequestURI
	p := "/api/base64/"
	index := strings.Index(requestURI, p)
	path := requestURI[index+len(p):]

	// 读取文件
	file, err := os.ReadFile(fmt.Sprintf("./var/%s", path))
	if err != nil {
		resp.ERROR(c, fmt.Errorf("读取文件失败: %v", err))
		return
	}

	// 转换为base64
	base64Data := base64.StdEncoding.EncodeToString(file)

	// 返回base64数据
	resp.SUCCESS(c, gin.H{
		"base64": base64Data,
	})
}

// CopyFromLocal 从本地路径复制文件到上传目录
func (control *Control) CopyFromLocal(c *gin.Context) {
	var req CopyFromLocalRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		resp.ERROR(c, fmt.Errorf("无效的请求参数: %v", err))
		return
	}

	// 检查本地路径是否为空
	if req.LocalPath == "" {
		resp.ERROR(c, fmt.Errorf("本地文件路径不能为空"))
		return
	}

	// 处理 Windows 路径分隔符
	localPath := strings.ReplaceAll(req.LocalPath, "\\", "/")

	// 处理相对路径
	if !filepath.IsAbs(localPath) {
		// 如果是相对路径，则相对于当前工作目录
		pwd, err := os.Getwd()
		if err != nil {
			resp.ERROR(c, fmt.Errorf("获取当前工作目录失败: %v", err))
			return
		}
		localPath = filepath.Join(pwd, localPath)
	}

	// 规范化路径并转换为系统路径格式
	localPath = filepath.FromSlash(filepath.Clean(localPath))

	// 输出日志，帮助调试
	fmt.Printf("原始路径: %s\n", req.LocalPath)
	fmt.Printf("处理后路径: %s\n", localPath)

	// 检查文件是否存在和权限
	fileInfo, err := os.Stat(localPath)
	if err != nil {
		if os.IsNotExist(err) {
			// 尝试使用 GBK 编码路径（针对 Windows 中文路径）
			if gbkPath, err := utf8ToGBK(localPath); err == nil {
				fileInfo, err = os.Stat(gbkPath)
				if err == nil {
					localPath = gbkPath
				} else {
					resp.ERROR(c, fmt.Errorf("本地文件不存在，完整路径: %s (尝试GBK编码后仍失败)", localPath))
					return
				}
			} else {
				resp.ERROR(c, fmt.Errorf("本地文件不存在，完整路径: %s", localPath))
				return
			}
		} else if os.IsPermission(err) {
			resp.ERROR(c, fmt.Errorf("没有权限访问文件: %s", localPath))
			return
		} else {
			resp.ERROR(c, fmt.Errorf("检查文件失败: %v", err))
			return
		}
	}

	// 检查是否为常规文件
	if !fileInfo.Mode().IsRegular() {
		resp.ERROR(c, fmt.Errorf("不是有效的文件: %s", localPath))
		return
	}

	// 检查文件大小
	if fileInfo.Size() > 10*1024*1024 { // 10MB 限制
		resp.ERROR(c, fmt.Errorf("文件大小超过限制（最大10MB）：当前大小 %.2fMB", float64(fileInfo.Size())/1024/1024))
		return
	}

	// 检查文件扩展名
	ext := strings.ToLower(filepath.Ext(localPath))
	if !imageutil.IsImageFile(ext) {
		resp.ERROR(c, fmt.Errorf("不支持的文件格式: %s", ext))
		return
	}

	// 读取源文件
	sourceFile, err := os.ReadFile(localPath)
	if err != nil {
		resp.ERROR(c, fmt.Errorf("读取本地文件失败: %v, 路径: %s", err, localPath))
		return
	}

	// 生成目标文件路径
	fileName := imageutil.GenerateFileName(filepath.Base(localPath))
	targetPath := filepath.Join("var", fileName)

	// 确保目标目录存在
	targetDir := filepath.Dir(targetPath)
	if err := os.MkdirAll(targetDir, 0755); err != nil {
		resp.ERROR(c, fmt.Errorf("创建目标目录失败: %v", err))
		return
	}

	// 写入目标文件
	err = os.WriteFile(targetPath, sourceFile, 0644)
	if err != nil {
		resp.ERROR(c, fmt.Errorf("保存文件失败: %v", err))
		return
	}
	// 返回成功信息
	resp.SUCCESS(c, fmt.Sprintf("%s/api/resource/%s", defaultServerURL, fileName))
}

// utf8ToGBK 将 UTF-8 编码的字符串转换为 GBK 编码
func utf8ToGBK(text string) (string, error) {
	// 这里需要添加 golang.org/x/text 包的依赖
	reader := transform.NewReader(strings.NewReader(text),
		simplifiedchinese.GBK.NewEncoder())
	data, err := io.ReadAll(reader)
	if err != nil {
		return "", err
	}
	return string(data), nil
}
