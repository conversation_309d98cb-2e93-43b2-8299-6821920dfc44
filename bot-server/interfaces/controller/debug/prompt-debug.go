package debug

import (
	"bot/application/dto"
	"bot/domain/entity"
	"bot/domain/service/bot/sdk"
	"bot/domain/service/bot/sdk/agent"
	"bot/domain/service/bot/sdk/arg"
	"bot/domain/service/bot/sdk/sdkutil"
	"bot/domain/service/bot/sdk/sse"
	"bot/domain/vo"
	"bot/infrastructure/common/resp"
	"bot/infrastructure/common/util/apputil"
	"bot/infrastructure/repository/ai-bot-common/mcpclien"
	ai_bot_platform "bot/infrastructure/repository/ai-bot-platform"
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"os"
	"strings"
	"text/template"

	"github.com/gin-gonic/gin"
	"github.com/mark3labs/mcp-go/mcp"
)

func McpPromptDebug(c *gin.Context) {

	var req dto.McpPromptDebugDto
	if err := c.ShouldBindJSON(&req); err != nil {
		resp.ERROR(c, err)
		return
	}

	// 初始化 mcp 客户端
	client := mcpclien.NewMCPClient(&entity.McpEntity{
		Command: "npx",
		Args:    []string{"-y", "@amap/amap-maps-mcp-server"},
		Env: map[string]string{
			"AMAP_MAPS_API_KEY": "05f0304a9f5ad3e34d522457fbd8ae7b",
		},
		Timeout: 10,
	})
	defer client.Close()

	request := mcp.InitializeRequest{}
	request.Params.ProtocolVersion = "1.0"
	request.Params.ClientInfo = mcp.Implementation{
		Name:    "test-client",
		Version: "1.0.0",
	}
	request.Params.Capabilities = mcp.ClientCapabilities{
		Roots: &struct {
			ListChanged bool `json:"listChanged,omitempty"`
		}{
			ListChanged: true,
		},
	}

	r, err := client.Initialize(context.Background(), request)
	if err != nil {
		resp.ERROR(c, err)
		return
	}

	requestList := mcp.ListToolsRequest{}
	mcpMap := make(map[string][]mcp.Tool)
	callMCP, err := client.ListTools(context.Background(), requestList)
	if err != nil {
		resp.ERROR(c, err)
		return
	}

	mcpInfo := map[string]string{
		"test-id": r.ServerInfo.Name,
	}
	mcpMap["test-id"] = callMCP.Tools

	data := make(map[string]interface{})
	data["McpTool"] = mcpInfo
	data["Tools"] = mcpMap

	open, err := os.Open(apputil.AbsPath("template/debug/deepseek-call.txt"))
	if err != nil {
		resp.ERROR(c, err)
		return
	}
	all, err := io.ReadAll(open)
	if err != nil {
		resp.ERROR(c, err)
		return
	}
	buffer := bytes.NewBuffer(all)
	mcpTemp := template.New("mcp")
	mcpTemp = mcpTemp.Funcs(template.FuncMap{
		"json": ai_bot_platform.Json,
	})
	parse, err := mcpTemp.Parse(buffer.String())
	if err != nil {
		resp.ERROR(c, err)
		return
	}

	var buf bytes.Buffer
	if err = parse.Execute(&buf, data); err != nil {
		resp.ERROR(c, err)
		return
	}

	//fmt.Println(buf.String())

	// 调用 gitee 的 api
	chatRequest := arg.NewChatRequest(&entity.PlatformConfig{
		Api: "https://ai.gitee.com",
		// 测试 key
		Key:          "IFMRT50L3XUISCC1X9SVXJOZAYRAPPVMGSACHUG2",
		CurrentModel: "DeepSeek-R1",
	})
	chatRequest.Url = "/v1/chat/completions"
	chatRequest.Model = "DeepSeek-R1"
	chatRequest.Messages = []*entity.HistoryEntity{
		{
			Role:    vo.SystemRoleType,
			Content: buf.String(),
		},
		{
			Role:    vo.UserRoleType,
			Content: req.Query,
		},
	}

	build, err := chatRequest.Build()
	if err != nil {
		resp.ERROR(c, err)
		return
	}

	response, err := http.DefaultClient.Do(build)
	if err != nil {
		resp.ERROR(c, err)
		return
	}
	defer response.Body.Close()

	scanner := bufio.NewScanner(response.Body)

	push := sse.New(c.Writer)

	agentTagHandler := agent.NewAgentTagHandler()
	agentTagHandler.AddTag(&agent.AgentTag{
		TagType:  "mcp",
		StartTag: "```mcp",
		EndTag:   "```",
	})

	agentTagHandler.AddTag(&agent.AgentTag{
		TagType:  "action",
		StartTag: "```action",
		EndTag:   "```",
	})

	fmt.Printf("%s", "\n\n\n\n\n")
	for scanner.Scan() {
		buf := scanner.Bytes()
		buf = sdkutil.ExtractJSON(buf)
		var value arg.ChatCompletion
		_ = json.Unmarshal(buf, &value)
		content := value.GetContent()
		if content == "" {
			continue
		}
		fmt.Print(content)
		origin, tag, hasTag := agentTagHandler.Feed(content)
		if hasTag {
			if tag == "mcp" {
				origin = strings.ReplaceAll(origin, "\n", "")
				origin = strings.TrimLeft(origin, "```mcp")
				origin = strings.TrimRight(origin, "```")
				origin = strings.TrimSpace(origin)
				// 生成随机数
				id := rand.Intn(1000000)
				agentItem := agent.NewMcpCallAgentItemByStatus(fmt.Sprintf("test-%d", id), origin, "test-id")
				llmContent := sdk.NewLLMContent(sse.Data, agentItem.Json(), nil)
				push.Writer(string(llmContent.Byte()) + sse.Segmentation)
			}
		} else {
			if origin != "" {
				agentItem := agent.NewMarkdownAgentItem(origin)
				llmContent := sdk.NewLLMContent(sse.Data, agentItem.Json(), nil)
				push.Writer(string(llmContent.Byte()) + sse.Segmentation)
			}
		}
	}

}
