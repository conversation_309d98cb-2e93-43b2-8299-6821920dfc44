package interfaces

import (
	"bot/conf"
	"bot/infrastructure/common/db"
	"bot/infrastructure/common/logs"
	"bot/interfaces/controller/bot"
	"bot/interfaces/controller/debug"
	"bot/interfaces/controller/manage"
	"bot/interfaces/controller/mcp"
	"bot/interfaces/controller/oss"

	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

type Api struct {
	*conf.Configuration
	*logs.AppLog
	*gin.Engine
	*db.ChatDB

	Bot    *bot.Control
	Manage *manage.Control
	Oss    *oss.Control
	Mcp    *mcp.Control
}

func NewApi(
	c *conf.Configuration,
	log *logs.AppLog,
	engine *gin.Engine,
	chatDB *db.ChatDB,
	control *bot.Control,
	ossControl *oss.Control,
	manageControl *manage.Control,
	mcpControl *mcp.Control,
) *Api {
	return &Api{
		Configuration: c,
		AppLog:        log,
		Engine:        engine,
		ChatDB:        chatDB,
		Bot:           control,
		Manage:        manageControl,
		Oss:           ossControl,
		Mcp:           mcpControl,
	}
}

func (api *Api) BindJson(ctx *gin.Context, v interface{}) error {
	if err := ctx.BindJSON(&v); err != nil {
		return err
	}
	return nil
}

func (api *Api) ShouldBind(ctx *gin.Context, v interface{}) error {
	if err := ctx.ShouldBind(&v); err != nil {
		return err
	}
	return nil
}

func (api *Api) ShouldBindUri(ctx *gin.Context, v interface{}) error {
	if err := ctx.ShouldBindUri(&v); err != nil {
		return err
	}
	return nil
}

func (api *Api) Init() {
	api.Engine.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	group := api.Engine.Group("/api")

	testApi := group.Group("/test")

	testApi.POST("/prompt-mcp", debug.McpPromptDebug)

	group.GET("/giteeAI/freeModel", api.Manage.GiteeAIFreeModels) // 资源获取

	group.POST("/uploadImage", api.Oss.UploadImage)             // 资源上传
	group.POST("/ckUploadImage", api.Oss.CKEditorUploadImage)   // 资源上传
	group.POST("/uploadImageBase64", api.Oss.UploadImageBase64) // 资源上传

	group.GET("/base64/*path", api.Oss.GetBase64)   // 资源获取
	group.POST("/localFile", api.Oss.CopyFromLocal) // 资源获取

	// 将单个路由改为静态文件服务
	group.Static("/resource", "./var") // 使用静态文件服务提供资源访问

	set := group.Group("/setting")
	set.GET("info", api.Manage.Setting)          // 获取配置信息
	set.POST("update", api.Manage.UpdateSetting) // 更新配置信息
	set.POST("clear", api.Manage.Clear)          // 清空消息记录，缓存的消息记录

	botApi := group.Group("/bot")

	botApi.GET("/uuid", api.Manage.UUID) // 获取UUID

	botApi.GET("/plugins", api.Manage.Plugins)                            // 获取插件
	botApi.POST("/plugins/update", api.Manage.UpdatePlugin)               // 更新插件
	botApi.POST("/plugins/updateAll", api.Manage.UpdateAllPluginPlatform) // 更新插件

	botApi.GET("/platforms", api.Manage.Platforms)               // 获取平台
	botApi.POST("/platforms/create", api.Manage.CreatePlatform)  // 创建平台
	botApi.POST("/platforms/update", api.Manage.UpdatePlatform)  // 更新平台
	botApi.POST("/platforms/delete", api.Manage.DeletePlatforms) // 删除平台

	botApi.GET("/conversations", api.Manage.Conversations)                             // 获取会话
	botApi.POST("/conversations/create", api.Manage.CreateConversation)                // 创建会话
	botApi.POST("/conversations/update", api.Manage.UpdateConversation)                // 更新会话
	botApi.POST("/conversations/delete", api.Manage.DelConversation)                   // 删除会话
	botApi.GET("/conversations/message", api.Manage.ConversationMessage)               // 获取会话消息
	botApi.GET("/conversations/messages", api.Manage.ConversationMessages)             // 获取会话消息列表
	botApi.POST("/conversations/messages/deletes", api.Manage.DelConversationMessages) // 删除会话消息
	botApi.POST("/conversations/messages/delete", api.Manage.DelConversationMessage)   // 删除会话指定消息

	// bot 默认提供的聊天功能 ，其他插件需要实现自己的 消息发送和流式数据接口 插件之间不得复用
	botApi.POST("/chat/send", api.Bot.Send)
	botApi.POST("/chat/saveAgent", api.Bot.SaveAgent)
	botApi.POST("/chat/clear", api.Bot.SendClear)
	botApi.POST("/chat/stream", api.Bot.ChatStream)
	botApi.POST("/chat/agent", api.Bot.ChatStream)

	// ai 写作
	botApi.POST("/writer/send", api.Bot.SendWriter)

	botApi.GET("/ollama/models", api.Manage.OllamaModels)     // 获取OLLama模型
	botApi.GET("/deepSeek/models", api.Manage.DeepSeekModels) // 获取DeepSeek模型
	botApi.GET("/giteeAI/models", api.Manage.GiteeAIModels)   // 获取GiteeAI模型

	// ai 绘画
	botApi.POST("/painting/send", api.Bot.SendPainting)
	botApi.POST("/painting/save", api.Bot.SavePainting)
	botApi.POST("/painting/stream", api.Bot.PaintingStream)

	// 文件管理接口
	filesApi := botApi.Group("/files")
	filesApi.GET("/by-pid", api.Manage.FilesByPID)  // 根据父级ID查询文件列表
	filesApi.GET("/tree", api.Manage.FileTree)      // 获取文件树结构
	filesApi.GET("/info", api.Manage.GetFileInfo)   // 获取文件信息
	filesApi.POST("", api.Manage.Files)             // 查询文件列表
	filesApi.POST("/create", api.Manage.CreateFile) // 创建文件或目录
	filesApi.POST("/update", api.Manage.UpdateFile) // 更新文件信息
	filesApi.POST("/delete", api.Manage.DeleteFile) // 删除文件或目录
	filesApi.POST("/move", api.Manage.MoveFile)     // 移动文件或目录

	// 绘画配置管理接口
	paintingConfigApi := botApi.Group("/painting-configs")
	paintingConfigApi.GET("", api.Manage.QueryPaintingConfigs)                   // 查询绘画配置列表
	paintingConfigApi.POST("/query", api.Manage.QueryPaintingConfigsByCondition) // 根据条件查询绘画配置
	paintingConfigApi.POST("/create", api.Manage.CreatePaintingConfig)           // 创建绘画配置
	paintingConfigApi.POST("/update", api.Manage.UpdatePaintingConfig)           // 更新绘画配置
	paintingConfigApi.POST("/delete", api.Manage.DeletePaintingConfig)           // 删除绘画配置
	paintingConfigApi.POST("/get", api.Manage.GetPaintingConfig)                 // 获取单个绘画配置

	// 负面提示词管理接口
	paintingNegativePromptApi := botApi.Group("/painting-negative-prompts")
	paintingNegativePromptApi.GET("", api.Manage.QueryPaintingNegativePrompts)                   // 查询负面提示词列表
	paintingNegativePromptApi.POST("/query", api.Manage.QueryPaintingNegativePromptsByCondition) // 根据条件查询负面提示词
	paintingNegativePromptApi.POST("/create", api.Manage.CreatePaintingNegativePrompt)           // 创建负面提示词
	paintingNegativePromptApi.POST("/update", api.Manage.UpdatePaintingNegativePrompt)           // 更新负面提示词
	paintingNegativePromptApi.POST("/delete", api.Manage.DeletePaintingNegativePrompt)           // 删除负面提示词
	paintingNegativePromptApi.POST("/get", api.Manage.GetPaintingNegativePrompt)                 // 获取单个负面提示词

	// 绘画记录管理接口
	paintingRecordApi := botApi.Group("/painting-records")
	paintingRecordApi.GET("", api.Manage.QueryPaintingRecords)                   // 查询绘画记录列表
	paintingRecordApi.POST("/query", api.Manage.QueryPaintingRecordsByCondition) // 根据条件查询绘画记录
	paintingRecordApi.POST("/create", api.Manage.CreatePaintingRecord)           // 创建绘画记录
	paintingRecordApi.POST("/update", api.Manage.UpdatePaintingRecord)           // 更新绘画记录
	paintingRecordApi.POST("/delete", api.Manage.DeletePaintingRecord)           // 删除绘画记录
	paintingRecordApi.POST("/get", api.Manage.GetPaintingRecord)                 // 获取单个绘画记录

	mcpApi := botApi.Group("/mcp")

	mcpApi.GET("/list", api.Mcp.List)
	mcpApi.GET("/info", api.Mcp.Info)
	mcpApi.GET("/tools", api.Mcp.Tools)
	mcpApi.GET("/reload", api.Mcp.Reload)
	mcpApi.POST("/add", api.Mcp.Add)
	mcpApi.POST("/edit", api.Mcp.Edit)
	mcpApi.POST("/delete", api.Mcp.Delete)

	marketplaceApi := mcpApi.Group("/marketplace")
	marketplaceApi.GET("/list", api.Mcp.List)
	marketplaceApi.GET("/info", api.Mcp.Info)
	marketplaceApi.GET("/install", api.Mcp.Info)

}
