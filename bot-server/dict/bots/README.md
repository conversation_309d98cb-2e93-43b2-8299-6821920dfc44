# Intent 
## 词性定义
`dict/bots/system/template.toml` 文件定义了系统内的词槽模板
## 意图模板定义
```toml
[[templates.draw]]
score = 0.6
# 问生图 意图匹配 模版
template = "gen[1,1];unit[2,0];object[4,0];gen[0,1];number[3,0]"

```
- [[templates.xxx]] 表示定义了一个xxx 意图模板
- score = 0.6 表示意图匹配的权重，当多个意图匹配的权重相同时，按照匹配程度进行排序
- template 表示意图匹配的模版，模版中包含多个词槽，词槽的格式为 `word:[position,is_must]`

### 词槽模板
组要由3个部分组合
```text
gen:[0,1];count:[1,0];gen_draw:[3,1]
```
`gen:[0,1]` gen 为词槽名，第一个元素0表示词槽位置 ，第二个元素1表示词槽是否必须出现，
0作为词槽位置有特殊意义表示不强制词槽出现位置，所以一般带位置的词槽需要从1开始
词槽定义 来源于 `dict/bots/system/slot.toml` 文件定义的分词列表

### 意图评分
意图评分的定义来源于 词槽模板 中必须出现的词槽数量占比整体词槽数量的比值，一个例子，当
上述例子中所有的必须出现的词槽都对应匹配上了，计算出来的评分必然大于等于 0.66 当非必须出现的词槽也命中了
就是 1 

# Intent Match 意图匹配
```toml
[intents.draw]
patterns = [
    "^.*[{{gen}}].*"
]
score = 1.0
```
#### 意图名
`[intents.xxx]` 独占一行定义 例如 `[intents.draw]` **draw** 就是对应的意图名，意图名下面更着的`patterns`就是意图匹配表达式，一个意图可以有多个意图匹配表达式
#### 匹配表达式
匹配表达式中 有参数占位符 格式为 `{{xxx}}` 形式和 vue 内的模版表达式类似，在运行对应的匹配表达式之前回把模版参数对应词槽参数一一替换，获得正确的正则表达式。
</br>

