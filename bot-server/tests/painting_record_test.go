package tests

import (
	"bot/application/dto"
	"bot/domain/entity"
	"bot/infrastructure/po"
	"testing"
	"time"
)

// TestPaintingRecordDTO 测试绘画记录DTO的基本功能
func TestPaintingRecordDTO(t *testing.T) {
	// 创建测试数据
	dto := &dto.PaintingRecordDTO{
		ID:        "test-painting-123",
		Title:     "测试绘画",
		Prompt:    "一只可爱的小猫",
		Status:    "completed",
		ImageUrls: "http://example.com/image1.jpg,http://example.com/image2.jpg",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// 验证DTO字段
	if dto.ID != "test-painting-123" {
		t.Errorf("期望ID为 'test-painting-123', 实际为 '%s'", dto.ID)
	}

	if dto.Title != "测试绘画" {
		t.Errorf("期望Title为 '测试绘画', 实际为 '%s'", dto.Title)
	}

	if dto.Prompt != "一只可爱的小猫" {
		t.<PERSON><PERSON>rf("期望Prompt为 '一只可爱的小猫', 实际为 '%s'", dto.Prompt)
	}

	if dto.Status != "completed" {
		t.<PERSON><PERSON>("期望Status为 'completed', 实际为 '%s'", dto.Status)
	}

	if dto.ImageUrls == "" {
		t.Error("ImageUrls字段不能为空")
	}
}

// TestPaintingRecordEntity 测试绘画记录实体的基本功能
func TestPaintingRecordEntity(t *testing.T) {
	// 创建测试实体
	entity := &entity.PaintingRecordEntity{
		ID:        "test-painting-123",
		Title:     "测试绘画",
		Prompt:    "一只可爱的小猫",
		Status:    "completed",
		ImageUrls: "http://example.com/image1.jpg,http://example.com/image2.jpg",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// 验证实体方法
	if entity.GetId() != "test-painting-123" {
		t.Errorf("期望GetId()返回 'test-painting-123', 实际为 '%s'", entity.GetId())
	}

	if entity.GetTitle() != "测试绘画" {
		t.Errorf("期望GetTitle()返回 '测试绘画', 实际为 '%s'", entity.GetTitle())
	}

	if entity.GetPrompt() != "一只可爱的小猫" {
		t.Errorf("期望GetPrompt()返回 '一只可爱的小猫', 实际为 '%s'", entity.GetPrompt())
	}

	if entity.GetStatus() != "completed" {
		t.Errorf("期望GetStatus()返回 'completed', 实际为 '%s'", entity.GetStatus())
	}

	if entity.GetImageUrls() == "" {
		t.Error("GetImageUrls()返回空字符串")
	}
}

// TestPaintingRecordPO 测试绘画记录PO的基本功能
func TestPaintingRecordPO(t *testing.T) {
	// 创建测试PO
	po := &po.PaintingPO{
		ID:        "test-painting-123",
		Title:     "测试绘画",
		Prompt:    "一只可爱的小猫",
		Status:    "completed",
		ImageUrls: "http://example.com/image1.jpg,http://example.com/image2.jpg",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// 验证PO字段
	if po.ID != "test-painting-123" {
		t.Errorf("期望ID为 'test-painting-123', 实际为 '%s'", po.ID)
	}

	if po.Title != "测试绘画" {
		t.Errorf("期望Title为 '测试绘画', 实际为 '%s'", po.Title)
	}

	if po.Prompt != "一只可爱的小猫" {
		t.Errorf("期望Prompt为 '一只可爱的小猫', 实际为 '%s'", po.Prompt)
	}

	if po.Status != "completed" {
		t.Errorf("期望Status为 'completed', 实际为 '%s'", po.Status)
	}

	if po.ImageUrls == "" {
		t.Error("ImageUrls字段不能为空")
	}

	// 验证表名
	if po.TableName() != "bot_painting" {
		t.Errorf("期望表名为 'bot_painting', 实际为 '%s'", po.TableName())
	}
}

// TestPaintingRecordQueryDTO 测试绘画记录查询DTO的基本功能
func TestPaintingRecordQueryDTO(t *testing.T) {
	// 创建测试查询DTO
	queryDTO := &dto.PaintingRecordQueryDTO{
		ID:     "test-painting-123",
		Title:  "测试",
		Status: "completed",
	}

	// 验证查询DTO字段
	if queryDTO.ID != "test-painting-123" {
		t.Errorf("期望ID为 'test-painting-123', 实际为 '%s'", queryDTO.ID)
	}

	if queryDTO.Title != "测试" {
		t.Errorf("期望Title为 '测试', 实际为 '%s'", queryDTO.Title)
	}

	if queryDTO.Status != "completed" {
		t.Errorf("期望Status为 'completed', 实际为 '%s'", queryDTO.Status)
	}
} 