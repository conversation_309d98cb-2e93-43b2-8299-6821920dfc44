package tests

import (
	"bot/application/dto"
	"bot/domain/entity"
	"bot/infrastructure/po"
	"testing"
	"time"
)

// TestPaintingNegativePromptDTO 测试负面提示词DTO的基本功能
func TestPaintingNegativePromptDTO(t *testing.T) {
	// 创建测试数据
	dto := &dto.PaintingNegativePromptDTO{
		ID:        "test-id-123",
		Prompt:    "模糊、低质量、变形、不清晰",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// 验证DTO字段
	if dto.ID != "test-id-123" {
		t.Errorf("期望ID为 'test-id-123', 实际为 '%s'", dto.ID)
	}

	if dto.Prompt != "模糊、低质量、变形、不清晰" {
		t.Errorf("期望Prompt为 '模糊、低质量、变形、不清晰', 实际为 '%s'", dto.Prompt)
	}
}

// TestPaintingNegativePromptEntity 测试负面提示词实体的基本功能
func TestPaintingNegativePromptEntity(t *testing.T) {
	// 创建测试数据
	entity := &entity.PaintingNegativePromptEntity{
		ID:        "test-id-123",
		Prompt:    "模糊、低质量、变形、不清晰",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// 验证实体字段
	if entity.ID != "test-id-123" {
		t.Errorf("期望ID为 'test-id-123', 实际为 '%s'", entity.ID)
	}

	if entity.Prompt != "模糊、低质量、变形、不清晰" {
		t.Errorf("期望Prompt为 '模糊、低质量、变形、不清晰', 实际为 '%s'", entity.Prompt)
	}
}

// TestPaintingNegativePromptPO 测试负面提示词PO的基本功能
func TestPaintingNegativePromptPO(t *testing.T) {
	// 创建测试数据
	po := &po.PaintingNegativePromptPO{
		ID:        "test-id-123",
		Prompt:    "模糊、低质量、变形、不清晰",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// 验证PO字段
	if po.ID != "test-id-123" {
		t.Errorf("期望ID为 'test-id-123', 实际为 '%s'", po.ID)
	}

	if po.Prompt != "模糊、低质量、变形、不清晰" {
		t.Errorf("期望Prompt为 '模糊、低质量、变形、不清晰', 实际为 '%s'", po.Prompt)
	}

	// 验证表名
	if po.TableName() != "bot_painting_negative_prompt" {
		t.Errorf("期望表名为 'bot_painting_negative_prompt', 实际为 '%s'", po.TableName())
	}
} 