package tests

import (
	"bot/application/dto"
	"bot/infrastructure/po"
	"encoding/json"
	"fmt"
	"net/http/httptest"
	"testing"
	"time"
)

// TestPaintingRecordAPI 测试绘画记录 API 接口
func TestPaintingRecordAPI(t *testing.T) {
	// 模拟绘画记录数据
	mockPaintingRecord := &po.PaintingPO{
		ID:        "test-painting-123",
		Title:     "测试绘画",
		Prompt:    "一只可爱的小猫",
		Status:    "completed",
		ImageUrls: "http://example.com/image1.jpg,http://example.com/image2.jpg",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// 测试数据结构
	t.Run("测试绘画记录数据结构", func(t *testing.T) {
		// 验证基本字段
		if mockPaintingRecord.ID != "test-painting-123" {
			t.Errorf("期望ID为 'test-painting-123', 实际为 '%s'", mockPaintingRecord.ID)
		}

		if mockPaintingRecord.Title != "测试绘画" {
			t.<PERSON>rrorf("期望Title为 '测试绘画', 实际为 '%s'", mockPaintingRecord.Title)
		}

		if mockPaintingRecord.Prompt != "一只可爱的小猫" {
			t.Errorf("期望Prompt为 '一只可爱的小猫', 实际为 '%s'", mockPaintingRecord.Prompt)
		}

		if mockPaintingRecord.Status != "completed" {
			t.Errorf("期望Status为 'completed', 实际为 '%s'", mockPaintingRecord.Status)
		}

		if mockPaintingRecord.ImageUrls == "" {
			t.Error("ImageUrls字段不能为空")
		}

		// 验证表名
		if mockPaintingRecord.TableName() != "bot_painting" {
			t.Errorf("期望表名为 'bot_painting', 实际为 '%s'", mockPaintingRecord.TableName())
		}
	})

	// 测试 DTO 转换
	t.Run("测试DTO转换", func(t *testing.T) {
		// 创建 DTO
		dto := &dto.PaintingRecordDTO{
			ID:        mockPaintingRecord.ID,
			Title:     mockPaintingRecord.Title,
			Prompt:    mockPaintingRecord.Prompt,
			Status:    mockPaintingRecord.Status,
			ImageUrls: mockPaintingRecord.ImageUrls,
			CreatedAt: mockPaintingRecord.CreatedAt,
			UpdatedAt: mockPaintingRecord.UpdatedAt,
		}

		// 验证 DTO 字段
		if dto.ID != mockPaintingRecord.ID {
			t.Errorf("DTO ID 不匹配")
		}

		if dto.Title != mockPaintingRecord.Title {
			t.Errorf("DTO Title 不匹配")
		}

		if dto.Prompt != mockPaintingRecord.Prompt {
			t.Errorf("DTO Prompt 不匹配")
		}

		if dto.Status != mockPaintingRecord.Status {
			t.Errorf("DTO Status 不匹配")
		}

		if dto.ImageUrls != mockPaintingRecord.ImageUrls {
			t.Errorf("DTO ImageUrls 不匹配")
		}
	})

	// 测试查询 DTO
	t.Run("测试查询DTO", func(t *testing.T) {
		queryDTO := &dto.PaintingRecordQueryDTO{
			ID:     "test-painting-123",
			Title:  "测试",
			Status: "completed",
		}

		// 验证查询 DTO 字段
		if queryDTO.ID != "test-painting-123" {
			t.Errorf("查询DTO ID 不匹配")
		}

		if queryDTO.Title != "测试" {
			t.Errorf("查询DTO Title 不匹配")
		}

		if queryDTO.Status != "completed" {
			t.Errorf("查询DTO Status 不匹配")
		}
	})

	// 测试 JSON 序列化
	t.Run("测试JSON序列化", func(t *testing.T) {
		jsonData, err := json.Marshal(mockPaintingRecord)
		if err != nil {
			t.Errorf("JSON序列化失败: %v", err)
		}

		// 验证 JSON 包含必要字段
		jsonStr := string(jsonData)
		expectedFields := []string{"id", "title", "prompt", "status", "imageUrls", "createdAt", "updatedAt"}
		for _, field := range expectedFields {
			if !contains(jsonStr, field) {
				t.Errorf("JSON缺少字段: %s", field)
			}
		}
	})

	// 测试图片URL解析
	t.Run("测试图片URL解析", func(t *testing.T) {
		imageUrls := mockPaintingRecord.ImageUrls
		urls := parseImageUrls(imageUrls)

		if len(urls) != 2 {
			t.Errorf("期望2个图片URL, 实际为 %d", len(urls))
		}

		expectedURLs := []string{
			"http://example.com/image1.jpg",
			"http://example.com/image2.jpg",
		}

		for i, expectedURL := range expectedURLs {
			if i >= len(urls) || urls[i] != expectedURL {
				t.Errorf("图片URL %d 不匹配, 期望: %s, 实际: %s", i, expectedURL, urls[i])
			}
		}
	})

	// 测试状态验证
	t.Run("测试状态验证", func(t *testing.T) {
		validStatuses := []string{"pending", "processing", "completed", "failed"}
		for _, status := range validStatuses {
			if !isValidStatus(status) {
				t.Errorf("状态 '%s' 应该是有效的", status)
			}
		}

		invalidStatuses := []string{"invalid", "unknown", ""}
		for _, status := range invalidStatuses {
			if isValidStatus(status) {
				t.Errorf("状态 '%s' 应该是无效的", status)
			}
		}
	})
}

// 辅助函数
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || (len(s) > len(substr) && (s[:len(substr)] == substr || s[len(s)-len(substr):] == substr || containsSubstring(s, substr))))
}

func containsSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

func parseImageUrls(imageUrls string) []string {
	if imageUrls == "" {
		return []string{}
	}

	var urls []string
	// 简单的逗号分隔解析
	for _, url := range splitAndTrim(imageUrls, ",") {
		if url != "" {
			urls = append(urls, url)
		}
	}
	return urls
}

func splitAndTrim(s, sep string) []string {
	var result []string
	for _, item := range split(s, sep) {
		trimmed := trim(item)
		if trimmed != "" {
			result = append(result, trimmed)
		}
	}
	return result
}

func split(s, sep string) []string {
	var result []string
	start := 0
	for i := 0; i <= len(s)-len(sep); i++ {
		if s[i:i+len(sep)] == sep {
			result = append(result, s[start:i])
			start = i + len(sep)
		}
	}
	result = append(result, s[start:])
	return result
}

func trim(s string) string {
	start := 0
	end := len(s)

	// 去除前导空格
	for start < end && isSpace(s[start]) {
		start++
	}

	// 去除尾随空格
	for end > start && isSpace(s[end-1]) {
		end--
	}

	return s[start:end]
}

func isSpace(c byte) bool {
	return c == ' ' || c == '\t' || c == '\n' || c == '\r'
}

func isValidStatus(status string) bool {
	validStatuses := map[string]bool{
		"pending":    true,
		"processing": true,
		"completed":  true,
		"failed":     true,
	}
	return validStatuses[status]
}

// TestPaintingRecordEndpoints 测试绘画记录 API 端点
func TestPaintingRecordEndpoints(t *testing.T) {
	// 这里可以添加实际的 HTTP 测试
	// 由于需要完整的应用上下文，这里只是示例结构

	t.Run("测试查询绘画记录端点", func(t *testing.T) {
		// 模拟 HTTP 请求
		req := httptest.NewRequest("GET", "/api/bot/painting-records", nil)
		_ = httptest.NewRecorder() // 忽略未使用的变量

		// 这里应该调用实际的处理器
		// 由于没有完整的应用上下文，这里只是验证请求创建
		if req.Method != "GET" {
			t.Errorf("期望GET方法, 实际为 %s", req.Method)
		}

		if req.URL.Path != "/api/bot/painting-records" {
			t.Errorf("期望路径 /api/bot/painting-records, 实际为 %s", req.URL.Path)
		}
	})

	t.Run("测试创建绘画记录端点", func(t *testing.T) {
		// 模拟创建请求数据
		createData := map[string]interface{}{
			"title":     "测试绘画",
			"prompt":    "一只可爱的小猫",
			"status":    "completed",
			"imageUrls": "http://example.com/image1.jpg",
		}

		jsonData, err := json.Marshal(createData)
		if err != nil {
			t.Errorf("JSON序列化失败: %v", err)
		}

		req := httptest.NewRequest("POST", "/api/bot/painting-records/create", nil)
		_ = httptest.NewRecorder() // 忽略未使用的变量

		// 验证请求数据
		if len(jsonData) == 0 {
			t.Error("请求数据不能为空")
		}

		if req.Method != "POST" {
			t.Errorf("期望POST方法, 实际为 %s", req.Method)
		}
	})
}

// BenchmarkPaintingRecordOperations 性能测试
func BenchmarkPaintingRecordOperations(b *testing.B) {
	mockRecord := &po.PaintingPO{
		ID:        "benchmark-test",
		Title:     "性能测试绘画",
		Prompt:    "性能测试提示词",
		Status:    "completed",
		ImageUrls: "http://example.com/benchmark.jpg",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	b.Run("JSON序列化性能", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			_, err := json.Marshal(mockRecord)
			if err != nil {
				b.Errorf("JSON序列化失败: %v", err)
			}
		}
	})

	b.Run("图片URL解析性能", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			parseImageUrls(mockRecord.ImageUrls)
		}
	})

	b.Run("状态验证性能", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			isValidStatus(mockRecord.Status)
		}
	})
}

// 示例用法
func ExamplePaintingRecordUsage() {
	// 创建绘画记录
	record := &po.PaintingPO{
		ID:        "example-123",
		Title:     "示例绘画",
		Prompt:    "一个美丽的风景",
		Status:    "completed",
		ImageUrls: "http://example.com/image1.jpg,http://example.com/image2.jpg",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// 转换为 DTO
	dto := &dto.PaintingRecordDTO{
		ID:        record.ID,
		Title:     record.Title,
		Prompt:    record.Prompt,
		Status:    record.Status,
		ImageUrls: record.ImageUrls,
		CreatedAt: record.CreatedAt,
		UpdatedAt: record.UpdatedAt,
	}

	// 创建查询 DTO
	queryDTO := &dto.PaintingRecordQueryDTO{
		Title:  "示例",
		Status: "completed",
	}

	fmt.Printf("绘画记录: %s\n", record.Title)
	fmt.Printf("DTO标题: %s\n", dto.Title)
	fmt.Printf("查询条件: %s\n", queryDTO.Title)

	// Output:
	// 绘画记录: 示例绘画
	// DTO标题: 示例绘画
	// 查询条件: 示例
}
