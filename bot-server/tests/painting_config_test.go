package tests

import (
	"bot/application/dto"
	"bot/domain/entity"
	"bot/infrastructure/po"
	"encoding/json"
	"testing"
	"time"
)

// TestPaintingConfigDTO 测试绘画配置DTO的基本功能
func TestPaintingConfigDTO(t *testing.T) {
	// 创建测试配置数据
	testConfig := map[string]interface{}{
		"name":              "测试配置",
		"promptTemplate":    "一只可爱的小猫",
		"size":              "1:1 (1024*1024)",
		"guidanceScale":     7.5,
		"negativePrompt":    "模糊、低质量",
		"width":             1024,
		"height":            1024,
		"useCustomSize":     false,
		"imageCount":        1,
		"numInferenceSteps": 20,
	}

	configJSON, err := json.Marshal(testConfig)
	if err != nil {
		t.Fatalf("序列化配置失败: %v", err)
	}

	// 创建DTO
	dto := &dto.PaintingConfigDTO{
		ID:        "test-id-123",
		Config:    string(configJSON),
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// 验证DTO字段
	if dto.ID != "test-id-123" {
		t.<PERSON><PERSON>("期望ID为 'test-id-123', 实际为 '%s'", dto.ID)
	}

	if dto.Config == "" {
		t.Error("配置字段不能为空")
	}

	// 验证JSON配置可以正确解析
	var parsedConfig map[string]interface{}
	if err := json.Unmarshal([]byte(dto.Config), &parsedConfig); err != nil {
		t.Errorf("解析配置JSON失败: %v", err)
	}

	if parsedConfig["name"] != "测试配置" {
		t.Errorf("期望名称为 '测试配置', 实际为 '%v'", parsedConfig["name"])
	}
}

// TestPaintingConfigEntity 测试绘画配置实体的基本功能
func TestPaintingConfigEntity(t *testing.T) {
	// 创建测试配置数据
	testConfig := map[string]interface{}{
		"name":              "实体测试配置",
		"promptTemplate":    "美丽的风景画",
		"size":              "16:9 (1024*576)",
		"guidanceScale":     8.0,
		"negativePrompt":    "模糊、变形",
		"width":             1024,
		"height":            576,
		"useCustomSize":     false,
		"imageCount":        2,
		"numInferenceSteps": 25,
	}

	configJSON, err := json.Marshal(testConfig)
	if err != nil {
		t.Fatalf("序列化配置失败: %v", err)
	}

	// 创建实体
	entity := &entity.PaintingConfigEntity{
		ID:        "entity-test-id-456",
		Config:    string(configJSON),
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// 验证实体字段
	if entity.ID != "entity-test-id-456" {
		t.Errorf("期望ID为 'entity-test-id-456', 实际为 '%s'", entity.ID)
	}

	if entity.Config == "" {
		t.Error("配置字段不能为空")
	}

	// 验证JSON配置可以正确解析
	var parsedConfig map[string]interface{}
	if err := json.Unmarshal([]byte(entity.Config), &parsedConfig); err != nil {
		t.Errorf("解析配置JSON失败: %v", err)
	}

	if parsedConfig["name"] != "实体测试配置" {
		t.Errorf("期望名称为 '实体测试配置', 实际为 '%v'", parsedConfig["name"])
	}
}

// TestPaintingConfigPO 测试绘画配置PO的基本功能
func TestPaintingConfigPO(t *testing.T) {
	// 创建测试配置数据
	testConfig := map[string]interface{}{
		"name":              "PO测试配置",
		"promptTemplate":    "抽象艺术风格",
		"size":              "9:16 (576*1024)",
		"guidanceScale":     6.5,
		"negativePrompt":    "写实、照片",
		"width":             576,
		"height":            1024,
		"useCustomSize":     true,
		"imageCount":        4,
		"numInferenceSteps": 30,
	}

	configJSON, err := json.Marshal(testConfig)
	if err != nil {
		t.Fatalf("序列化配置失败: %v", err)
	}

	// 创建PO
	po := &po.PaintingConfigPO{
		ID:        "po-test-id-789",
		Config:    string(configJSON),
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// 验证PO字段
	if po.ID != "po-test-id-789" {
		t.Errorf("期望ID为 'po-test-id-789', 实际为 '%s'", po.ID)
	}

	if po.Config == "" {
		t.Error("配置字段不能为空")
	}

	// 验证表名
	if po.TableName() != "bot_painting_prompt_config" {
		t.Errorf("期望表名为 'bot_painting_prompt_config', 实际为 '%s'", po.TableName())
	}

	// 验证JSON配置可以正确解析
	var parsedConfig map[string]interface{}
	if err := json.Unmarshal([]byte(po.Config), &parsedConfig); err != nil {
		t.Errorf("解析配置JSON失败: %v", err)
	}

	if parsedConfig["name"] != "PO测试配置" {
		t.Errorf("期望名称为 'PO测试配置', 实际为 '%v'", parsedConfig["name"])
	}
}

// TestPaintingConfigQueryDTO 测试绘画配置查询DTO的基本功能
func TestPaintingConfigQueryDTO(t *testing.T) {
	// 创建查询DTO
	queryDTO := &dto.PaintingConfigQueryDTO{
		ID: "query-test-id-123",
	}

	// 验证查询DTO字段
	if queryDTO.ID != "query-test-id-123" {
		t.Errorf("期望ID为 'query-test-id-123', 实际为 '%s'", queryDTO.ID)
	}

	// 测试空查询DTO
	emptyQueryDTO := &dto.PaintingConfigQueryDTO{}
	if emptyQueryDTO.ID != "" {
		t.Errorf("期望空查询DTO的ID为空字符串, 实际为 '%s'", emptyQueryDTO.ID)
	}
}
