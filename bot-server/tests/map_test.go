package tests

import (
	"bot/infrastructure/repository/ai-bot-common/llm/gdmap"
	"context"
	"encoding/json"
	"os"
	"path/filepath"
	"testing"

	"github.com/xuri/excelize/v2"
)

func TestIP(t *testing.T) {
	gdMap := gdmap.NewGDMap("05f0304a9f5ad3e34d522457fbd8ae7b")
	gps, err := gdMap.IpLocation(context.Background())
	if err != nil {
		t.Error(err.Error())
		return
	}
	t.Log(gps)
}

func TestWeather(t *testing.T) {
	gdMap := gdmap.NewGDMap("05f0304a9f5ad3e34d522457fbd8ae7b")
	gps, err := gdMap.CityWeather(context.Background(), "龙岗区")
	if err != nil {
		t.Error(err.Error())
		return
	}
	t.Log(gps)
}

func TestGeo(t *testing.T) {
	gdMap := gdmap.NewGDMap("05f0304a9f5ad3e34d522457fbd8ae7b")
	gps, err := gdMap.IpLocation(context.Background())
	if err != nil {
		t.Error(err.Error())
		return
	}
	data, err := gdMap.Geo("", gps)
	if err != nil {
		t.Error(err.Error())
		return
	}
	t.Log(data)
}

func TestToJsonFile(t *testing.T) {
	// 打开 Excel 文件
	xlsxFile, err := excelize.OpenFile("AMap_adcode_citycode.xlsx")
	if err != nil {
		t.Fatalf("无法打开 Excel 文件: %v", err)
	}
	defer func() {
		if err := xlsxFile.Close(); err != nil {
			t.Errorf("关闭 Excel 文件时发生错误: %v", err)
		}
	}()

	// 获取第一个 sheet 的所有行
	rows, err := xlsxFile.GetRows(xlsxFile.GetSheetList()[0])
	if err != nil {
		t.Fatalf("无法读取 sheet 内容: %v", err)
	}

	if len(rows) == 0 {
		t.Fatalf("Excel 文件为空")
	}

	// 假设第一行是标题
	headers := rows[0]

	// 找到必要字段的索引
	nameIndex := getIndex(headers, "中文名")
	adcodeIndex := getIndex(headers, "adcode")
	citycodeIndex := getIndex(headers, "citycode")

	if nameIndex == -1 || adcodeIndex == -1 || citycodeIndex == -1 {
		t.Fatalf("必要字段缺失")
	}

	// 创建两个 map 来存储结果
	resultByName := make(map[string]map[string]string)
	resultByAdcode := make(map[string]map[string]string)

	// 遍历每一行（跳过标题行）
	for _, row := range rows[1:] {
		if len(row) <= nameIndex || len(row) <= adcodeIndex || len(row) <= citycodeIndex {
			continue // 跳过不完整的行
		}

		name := row[nameIndex]
		adcode := row[adcodeIndex]
		citycode := row[citycodeIndex]

		rowData := map[string]string{
			"adcode":   adcode,
			"citycode": citycode,
		}

		resultByName[name] = rowData

		rowDataByAdcode := map[string]string{
			"name":     name,
			"citycode": citycode,
		}

		resultByAdcode[adcode] = rowDataByAdcode
	}

	// 将数据转换为 JSON 并写入文件
	writeJSON(t, resultByName, "gdmap_adcode_citycode_by_name.json")
	writeJSON(t, resultByAdcode, "gdmap_adcode_citycode_by_adcode.json")

	t.Log("JSON 文件已成功创建")
}

func getIndex(headers []string, target string) int {
	for i, header := range headers {
		if header == target {
			return i
		}
	}
	return -1
}

func writeJSON(t *testing.T, data map[string]map[string]string, filename string) {
	jsonData, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		t.Fatalf("无法将数据转换为 JSON: %v", err)
	}

	outputPath := filepath.Join("./", filename)
	err = os.WriteFile(outputPath, jsonData, 0644)
	if err != nil {
		t.Fatalf("无法写入 JSON 文件: %v", err)
	}
}
