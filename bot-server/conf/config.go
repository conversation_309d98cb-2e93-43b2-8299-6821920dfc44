package conf

import (
	_ "embed"
	"github.com/spf13/pflag"
	"github.com/spf13/viper"
	"os"
	"path/filepath"
	"strings"
)

var Evn = &Configuration{}

func NewEnv() *Configuration {
	return Environment()
}

// Configuration 配置文件映射
type Configuration struct {
	Number     int64
	ServerName string
	Host       string
	Port       string
	Node       int64
	Database   string
	Logger     struct {
		Level      string
		Path       string
		FileName   string
		MaxSize    int
		MaxBackups int
		MaxAge     int
	}
	Redis struct {
		Host     string
		Port     int
		Password string
		DB       int
	}
	Auth struct {
		AccessSecret string
		AccessExpire int64
	}
	Key    string
	Ollama struct {
		Port string
	}
	Minio struct {
		Endpoint        string
		AccessKeyID     string
		SecretAccessKey string
		Secure          bool
	}
	Tencent struct {
		BucketURL  string
		ServiceURL string
		SecretID   string
		SecretKey  string
	}
	RabbitMQ struct {
		Host     string
		Port     int
		User     string
		Password string
	}
	Pay struct {
		AliPay struct {
			AppId           string
			AppPublicCert   string
			AliPayPublicKey string
			AliPayRootKey   string
		}
	}
	Email struct {
		Host     string
		Port     int
		User     string
		Password string
	}
}

// Environment
// description: 加载配置
func Environment() *Configuration {
	getwd, err := os.Getwd()
	if err != nil {
		panic(err)
	}
	config := pflag.String("config", "", "Activation configuration")
	active := pflag.String("active", "dev", "Activation configuration")
	pflag.Parse()
	name := "config"
	if active != nil {
		name = strings.Join([]string{name, *active}, "-")
	}
	if config != nil {
		getwd = filepath.Join(getwd, *config)
	}
	viper.SetConfigName(name)
	viper.SetConfigType("yaml")
	viper.AddConfigPath(getwd)
	viper.AutomaticEnv()
	if err = viper.ReadInConfig(); err != nil {
		panic(err)
	}
	if err = viper.Unmarshal(&Evn); err != nil {
		panic(err)
	}
	return Evn
}
