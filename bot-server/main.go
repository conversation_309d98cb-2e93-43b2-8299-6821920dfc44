package main

import (
	"bot/docs"
	"bot/infrastructure/common/clean"
	"log"
	"os"
	"os/signal"
	"syscall"

	"go.uber.org/zap"
)

func init() {
	docs.SwaggerInfo.Title = "ai bot"
	docs.SwaggerInfo.Description = "ai bot"
	docs.SwaggerInfo.Version = "1.0"
	docs.SwaggerInfo.Host = "localhost:2080"
	docs.SwaggerInfo.BasePath = "/"
	docs.SwaggerInfo.Schemes = []string{"http", "https"}

	// https://github.com/swaggo/swag
	// 初始化文档使用 swag init -g main.go -pd
}

func main() {
	app, err := InitApp()
	if err != nil {
		log.Fatal("app init failed", zap.Error(err))
		return
	}

	// 设置优雅退出
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGINT)
	go func() {
		<-c
		app.AppLog.Info("正在关闭应用...")
		close(clean.Ch)
		for handler := range clean.Ch {
			handler()
		}
		os.Exit(0)
	}()

	// 启动 HTTP 服务器
	if err := app.run(); err != nil {
		app.AppLog.Error("服务器运行失败", zap.Error(err))
		os.Exit(1)
	}
}
