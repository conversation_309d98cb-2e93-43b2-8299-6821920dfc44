package main

import (
	"bot/application/dto"
	"bot/conf"
	"bot/domain/vo"
	"bot/infrastructure/common/db"
	"bot/infrastructure/common/logs"
	"bot/infrastructure/common/util/uuidutils/uuid"
	"bot/infrastructure/po"
	"bot/interfaces"
	"context"
	"fmt"
	"net/http"
	"os"
	"path/filepath"
)

type App struct {
	http.Handler
	*db.ChatDB
	*conf.Configuration
	*logs.AppLog
	*interfaces.Api
	*uuid.Node
	ctx context.Context
}

func NewApp(
	handler http.Handler,
	db *db.ChatDB,
	c *conf.Configuration,
	log *logs.AppLog,
	api *interfaces.Api,
	node *uuid.Node,
) *App {
	return &App{
		Handler:       handler,
		ChatDB:        db,
		Configuration: c,
		AppLog:        log,
		Api:           api,
		Node:          node,
		ctx:           context.Background(),
	}
}

func (app *App) init() {
	app.Api.Init()
	app.initFileDB()
}

func (app *App) run() error {
	app.init()
	sprintf := fmt.Sprintf("http://%s:%s/swagger/index.html", "localhost", app.Configuration.Port)
	address := fmt.Sprintf("%s:%s", app.Configuration.Host, app.Configuration.Port)
	server := &http.Server{
		Addr:    address,
		Handler: app.Handler,
	}
	app.Info(fmt.Sprintf("Starting server on %s ...\n", sprintf))
	if err := server.ListenAndServe(); err != nil {
		return err
	}
	return nil
}

func (app *App) initFileDB() {
	var err error
	migrator := app.ChatDB.Migrator()

	if err = migrator.AutoMigrate(
		&po.ConversationPO{},
		&po.MessagePO{},
		&po.McpPO{},
		&po.FilePO{},
		&po.PaintingConfigPO{},
		&po.PaintingNegativePromptPO{},
		&po.PaintingPO{},
		&po.PlatformPO{},
		&po.PluginPO{},
	); err != nil {
		panic(err)
	}
	defaultPlatform := &po.PlatformPO{}
	if !migrator.HasTable(&po.PlatformPO{}) {
		if err = migrator.CreateTable(&po.PlatformPO{}); err != nil {
			panic(err)
		}
		// 初始化 默认平台 Ollama
		defaultPlatform.ID = app.Node.String()
		defaultPlatform.Type = vo.OllamaPlatform
		defaultPlatform.Name = vo.OllamaPlatform

		// todo 优化初始化模型 从本机请求到正确的模型数据进行筛选，最后没有选择初始化 空字符串
		ollamaConfigDTO := dto.OllamaConfigDTO{
			// 初始化 Ollama 为本地 Ollama 服务
			Api: "http://localhost:11434",
			// 初始化为默认模型，客户端不存在当前模型需要在客户端手动切换为已下载模型
			CurrentModel: "qwen2.5:7b",
		}
		defaultPlatform.Config = ollamaConfigDTO.Json()
		defaultPlatform.QuickConfigView = vo.OllamaQuickConfig
		defaultPlatform.ConfigView = vo.OllamaPlatformConfig
		defaultPlatform.Icon = "platform-icon\\ollama.svg"
		defaultPlatform.Current = true
		if err = app.ChatDB.Create(defaultPlatform).Error; err != nil {
			panic(err)
		}
	}
	if !migrator.HasTable(&po.PluginPO{}) {
		if err = migrator.CreateTable(&po.PluginPO{}); err != nil {
			panic(err)
		}
		// 初始化默认插件 及平台实现
		var plugins = []*po.PluginPO{
			{
				ID:         app.Node.String(),
				Name:       "Bot",
				PlatformID: defaultPlatform.ID,
				Code:       vo.BotPluginCode,
				Icon:       "ChatGPT",
				ConfigView: vo.BotPluginConfig,
			},
			{
				ID:         app.Node.String(),
				Name:       "写作",
				PlatformID: defaultPlatform.ID,
				Code:       vo.BotWriterCode,
				Icon:       "code",
				ConfigView: vo.BotWriterConfig,
			},
			{
				ID:         app.Node.String(),
				Name:       "知识库",
				PlatformID: defaultPlatform.ID,
				Code:       vo.KnowledgePluginCode,
				Icon:       "zhishi",
				ConfigView: vo.KnowledgePluginConfig,
			},
		}
		if err = app.ChatDB.Create(&plugins).Error; err != nil {
			panic(err)
		}
	}
	if !migrator.HasTable(&po.SettingPO{}) {
		if err = migrator.CreateTable(&po.SettingPO{}); err != nil {
			panic(err)
		}
		// 初始化 默认设置
		defaultSetting := &po.SettingPO{}
		defaultSetting.ID = app.Node.String()
		// 读取 setting_init.json 文件
		settingFilePath := filepath.Join("setting_init.json")
		settingData, err := os.ReadFile(settingFilePath)
		if err != nil {
			app.AppLog.Error("Failed to read setting_init.json file: " + err.Error())
			panic(err)
		}
		defaultSetting.Value = string(settingData)
		if err = app.ChatDB.Create(defaultSetting).Error; err != nil {
			panic(err)
		}
	}
}
