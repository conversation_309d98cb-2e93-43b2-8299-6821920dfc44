package ai_bot

import (
	"bot/application/dto"
	"bot/domain/entity"
	"bot/domain/repository/mcp_repo"
	"errors"
	"github.com/google/uuid"
	"github.com/mark3labs/mcp-go/mcp"
)

type BotMcpService struct {
	*AiBot
	mcpRepo mcp_repo.IMcp
}

func NewBotMcpService(bot *AiBot, mcpRepo mcp_repo.IMcp) *BotMcpService {
	return &BotMcpService{
		AiBot:   bot,
		mcpRepo: mcpRepo,
	}
}

// AddMcpService 添加MCP服务
func (receiver *BotMcpService) AddMcpService(mcpDTO *dto.McpDTO) error {
	// 生成UUID
	mcpDTO.ID = uuid.New().String()
	// 转换为领域实体
	mcpEntity := dto.NewMcpEntity(mcpDTO)
	mcpEntity.ID = mcpDTO.ID
	// 存储到数据库
	return receiver.mcpRepo.Add(mcpEntity)
}

// GetMcpService 获取MCP列表
func (receiver *BotMcpService) GetMcpService() ([]*entity.McpEntity, error) {
	// 从仓储层获取数据
	mcpEntities, err := receiver.mcpRepo.GetAll()
	if err != nil {
		return nil, err
	}
	return mcpEntities, nil
}

// GetMcpInfoService 获取MCP详情
func (receiver *BotMcpService) GetMcpInfoService(id string) (*dto.McpDTO, error) {
	if id == "" {
		return nil, errors.New("ID不能为空")
	}

	// 从仓储层获取数据
	mcpEntity, err := receiver.mcpRepo.GetById(id)
	if err != nil {
		return nil, err
	}

	// 转换为DTO
	result := &dto.McpDTO{
		ID:       mcpEntity.ID,
		Name:     mcpEntity.Name,
		Command:  mcpEntity.Command,
		Args:     mcpEntity.Args,
		Env:      mcpEntity.Env,
		Disabled: mcpEntity.Disabled,
		Timeout:  mcpEntity.Timeout,
	}

	return result, nil
}

func (receiver *BotMcpService) GetMcpTools(id string) ([]mcp.Tool, error) {
	if id == "" {
		return nil, errors.New("ID不能为空")
	}
	// 从仓储层获取数据
	mcpEntity, err := receiver.mcpRepo.GetById(id)
	if err != nil {
		return nil, err
	}
	tools, err := receiver.mcpRepo.GetTools(mcpEntity)
	if err != nil {
		return nil, err
	}
	return tools, nil
}

func (receiver *BotMcpService) ReloadMcpService(id string) error {
	if id == "" {
		return errors.New("ID不能为空")
	}

	return receiver.mcpRepo.Reload(id)
}

// UpdateMcpService 更新MCP服务
func (receiver *BotMcpService) UpdateMcpService(mcpDTO *dto.McpDTO) error {
	if mcpDTO.ID == "" {
		return errors.New("ID不能为空")
	}

	// 转换为领域实体
	mcpEntity := dto.NewMcpEntity(mcpDTO)
	mcpEntity.ID = mcpDTO.ID

	// 更新到数据库
	return receiver.mcpRepo.Update(mcpEntity)
}

// DeleteMcpService 删除MCP服务
func (receiver *BotMcpService) DeleteMcpService(id string) error {
	if id == "" {
		return errors.New("ID不能为空")
	}

	// 从仓储层删除数据
	return receiver.mcpRepo.Delete(id)
}

// StartMcpService 启动MCP服务
func (receiver *BotMcpService) StartMcpService(id string) error {
	if id == "" {
		return errors.New("ID不能为空")
	}

	// 获取MCP信息
	mcpEntity, err := receiver.mcpRepo.GetById(id)
	if err != nil {
		return err
	}

	// 设置为启用状态
	mcpEntity.Disabled = 0

	// 更新到数据库
	return receiver.mcpRepo.Update(mcpEntity)
}

// StopMcpService 停止MCP服务
func (receiver *BotMcpService) StopMcpService(id string) error {
	if id == "" {
		return errors.New("ID不能为空")
	}

	// 获取MCP信息
	mcpEntity, err := receiver.mcpRepo.GetById(id)
	if err != nil {
		return err
	}

	// 设置为禁用状态
	mcpEntity.Disabled = 1

	// 更新到数据库
	return receiver.mcpRepo.Update(mcpEntity)
}
