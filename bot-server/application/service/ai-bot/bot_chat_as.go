package ai_bot

import (
	"bot/application/dto"
	"bot/domain/service/bot"
	botcontent "bot/domain/service/bot-context"
	"bot/domain/service/bot/sdk/handler"
	"context"
	"net/http"

	"go.uber.org/zap"
)

type ChatService struct {
	*AiBot
}

func NewChatService(bot *AiBot) *ChatService {
	return &ChatService{AiBot: bot}
}

func (chat *ChatService) ChatStream(ctx context.Context, param *dto.ChatDTO, r *http.Request, w http.ResponseWriter) {
	agentImp, err := chat.LoadAgentInstance(ctx, w, r, param.BaseChat)
	if err != nil {
		botContext := botcontent.NewBotContext(
			botcontent.WithHTTPWriter(w),
			botcontent.WithHTTPRequest(r))
		sseHandler := handler.NewLLMHandler(botContext)
		if err = sseHandler.SSEErrorResponse(err.Error()); err != nil {
			chat.AppLog.Error(err.<PERSON>rror(), zap.Error(err))
		}
		return
	}
	// 创建 BotDomain
	botDomain := chat.NewBotDomain(bot.WithBase(agentImp))
	// 调用 BotDomain
	chatEntity := dto.NewChatEntity(param)
	botDomain.AgentStream(ctx, chatEntity)
}
