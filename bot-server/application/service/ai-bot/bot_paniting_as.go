package ai_bot

import (
	"bot/application/dto"
	"bot/domain/service/bot"
	botcontent "bot/domain/service/bot-context"
	"bot/domain/service/bot/sdk/handler"
	"bot/infrastructure/po"
	"context"
	"net/http"

	"go.uber.org/zap"
)

type PaintingService struct {
	*AiBot
}

func NewPaintingService(bot *AiBot) *PaintingService {
	return &PaintingService{AiBot: bot}
}

func (service *PaintingService) Send(ctx context.Context, param *dto.PaintingDTO, r *http.Request, w http.ResponseWriter) (*po.PaintingPO, error) {
	agentImp, err := service.LoadAgentInstance(ctx, w, r, param.BaseChat)
	if err != nil {
		return nil, err
	}
	return agentImp.SendPainting(ctx, dto.NewPaintingEntity(param))
}

func (service *PaintingService) Save(ctx context.Context, dto *dto.PaintingDTO, r *http.Request, w http.ResponseWriter) (*po.MessagePO, error) {
	_, err := service.LoadAgentInstance(ctx, w, r, dto.BaseChat)
	if err != nil {
		return nil, err
	}

	return nil, nil
}

func (service *PaintingService) PaintingStream(ctx context.Context, param *dto.PaintingDTO, r *http.Request, w http.ResponseWriter) {
	agentImp, err := service.LoadAgentInstance(ctx, w, r, param.BaseChat)
	if err != nil {
		botContext := botcontent.NewBotContext(
			botcontent.WithHTTPWriter(w),
			botcontent.WithHTTPRequest(r))
		sseHandler := handler.NewLLMHandler(botContext)
		if err = sseHandler.SSEErrorResponse(err.Error()); err != nil {
			service.AppLog.Error(err.Error(), zap.Error(err))
		}
		return
	}
	// 创建 BotDomain
	botDomain := service.NewBotDomain(bot.WithBase(agentImp))
	// 调用 BotDomain
	paintingEntity := dto.NewPaintingEntity(param)
	botDomain.PaintingStream(ctx, paintingEntity)
}
