package ai_bot

import (
	"bot/application/dto"
	"bot/domain/service/bot"
	botcontent "bot/domain/service/bot-context"
	"bot/domain/service/bot/sdk/handler"
	"context"
	"net/http"

	"go.uber.org/zap"
)

type AgentService struct {
	*AiBot
}

func NewAgentService(bot *AiBot) *AgentService {
	return &AgentService{
		AiBot: bot,
	}
}

func (agent *AgentService) AgentStream(ctx context.Context, param *dto.ChatDTO, r *http.Request, w http.ResponseWriter) {
	agentImp, err := agent.LoadAgentInstance(ctx, w, r, param.BaseChat)
	if err != nil {
		botContext := botcontent.NewBotContext(
			botcontent.WithHTTPWriter(w),
			botcontent.WithHTTPRequest(r))
		sseHandler := handler.NewLLMHandler(botContext)
		if err = sseHandler.SSEErrorResponse(err.<PERSON>rror()); err != nil {
			agent.AppLog.Error(err.<PERSON><PERSON>r(), zap.Error(err))
		}
		return
	}
	// 创建 BotDomain
	botDomain := agent.NewBotDomain(bot.WithBase(agentImp))
	// 调用 BotDomain
	chatEntity := dto.NewChatEntity(param)
	botDomain.AgentStream(ctx, chatEntity)
}
