package ai_bot

import (
	"bot/domain/entity"
	botcontent "bot/domain/service/bot-context"
	"bot/domain/service/bot/sdk"
	"bot/infrastructure/po"
	"bot/infrastructure/repository/ai-bot-nlp-impl/system"
	"context"
	"encoding/json"
	"errors"
	"net/http"

	"gorm.io/gorm"
)

// BotContext
// 当前默认实现的 Context 提供
func (aibot *AiBot) BotContext(ctx context.Context, w http.ResponseWriter, r *http.Request, param *sdk.BaseChat) (*botcontent.Context, error) {
	var platform *po.PlatformPO
	var plugin *po.PluginPO
	var config *entity.PlatformConfig
	var err error
	chatDB := aibot.BaseRepository.ChatDB
	// todo 优化前端默认传递基础插件
	if err = chatDB.Where("ID=?", param.PluginID).First(&plugin).Error; err != nil {
		// 兼容一些只需要使用平台id的功能
		if errors.Is(err, gorm.ErrRecordNotFound) {
			plugin.PlatformID = param.PlatformID
		}
		if plugin.PlatformID == "" {
			return nil, err
		}
	}
	if err = chatDB.Where("ID =?", plugin.PlatformID).First(&platform).Error; err != nil {
		return nil, err
	}
	if config, err = aibot.PlatformConfig(platform); err != nil {
		return nil, err
	}

	setting := aibot.Setting()
	baseNluIntent := system.NewBaseNluIntent()
	mcpDomain := aibot.NewMcpDomain()
	botContext := botcontent.NewBotContext(
		botcontent.WithQuery(param.Message),
		botcontent.WithPlugin(plugin),
		botcontent.WithPlatform(platform),
		botcontent.WithSetting(setting),
		botcontent.WithPlatformConfig(config),
		botcontent.WithHTTPWriter(w),
		botcontent.WithHTTPRequest(r),
		botcontent.WithIntent(baseNluIntent),
		botcontent.WithMcp(mcpDomain),
	)
	return botContext, err
}

// Setting
// 获取系统配置信息
func (aibot *AiBot) Setting() *entity.SettingEntity {
	var data *po.SettingPO
	var setting *entity.SettingEntity
	if err := aibot.ChatDB.First(&data, "id!=''").Error; err != nil {
		// Handle error
		return &entity.SettingEntity{}
	}
	if err := json.Unmarshal([]byte(data.Value), &setting); err != nil {
		// Handle error
		return &entity.SettingEntity{}
	}
	return setting
}

func (aibot *AiBot) PlatformConfig(platform *po.PlatformPO) (*entity.PlatformConfig, error) {
	var err error
	var config *entity.PlatformConfig
	if platform == nil {
		return nil, nil
	}
	if err = json.Unmarshal([]byte(platform.Config), &config); err != nil {
		return nil, err
	}
	return config, nil
}
