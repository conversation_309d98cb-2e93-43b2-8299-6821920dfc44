package ai_bot

import (
	"bot/application/dto"
	"bot/infrastructure/po"
	"context"

	"go.opentelemetry.io/otel"
	"gorm.io/gorm"
)

type BotManageService struct {
	*AiBot
}

func NewBotManageService(bot *AiBot) *BotManageService {
	return &BotManageService{
		AiBot: bot,
	}
}

// Conversations
// 查询所有回话列表
func (manage *BotManageService) Conversations(ctx context.Context) ([]po.ConversationPO, error) {
	// 开启 OpenTelemetry Span
	tracer := otel.Tracer("BotManageService.Conversations")
	start, span := tracer.Start(ctx, "BotManageService.Conversations")
	defer span.End()

	manageDomain := manage.NewManageDomain()
	conversations, err := manageDomain.QueryConversations(start)
	if err != nil {
		return nil, err
	}
	return conversations, nil
}

// CreateConversation
// 创建一个会话
func (manage *BotManageService) CreateConversation(ctx context.Context, data *dto.ConversationDTO) (string, error) {
	newConversationEntity := dto.NewConversationEntity(data)
	manageDomain := manage.NewManageDomain()
	return manageDomain.CreateConversation(ctx, newConversationEntity)
}

// DeleteConversations
// 删除会话
func (manage *BotManageService) DeleteConversations(ctx context.Context, data *dto.ConversationDTO) error {
	newConversationEntity := dto.NewConversationEntity(data)
	manageDomain := manage.NewManageDomain()
	return manageDomain.DeleteConversation(ctx, newConversationEntity)
}

// UpdateConversation
// 更新会话
func (manage *BotManageService) UpdateConversation(ctx context.Context, data *dto.ConversationDTO) (*po.ConversationPO, error) {
	conversationEntity := dto.NewConversationEntity(data)
	manageDomain := manage.NewManageDomain()
	return manageDomain.UpdateConversation(ctx, conversationEntity)
}

// Messages
// 查询会话消息
func (manage *BotManageService) Messages(ctx context.Context, data *dto.ConversationDTO) ([]*po.MessagePO, error) {
	manageDomain := manage.NewManageDomain()
	conversationEntity := dto.NewConversationEntity(data)
	return manageDomain.QueryMessages(ctx, conversationEntity)
}

func (manage *BotManageService) Message(ctx context.Context, data *dto.MessageDTO) (*po.MessagePO, error) {
	manageDomain := manage.NewManageDomain()
	messageEntity := dto.NewMessageEntity(data)
	return manageDomain.QueryMessage(ctx, messageEntity)
}

// DeleteMessage
// 删除会话消息
func (manage *BotManageService) DeleteMessages(ctx context.Context, data *dto.ConversationDTO) error {
	manageDomain := manage.NewManageDomain()
	conversationEntity := dto.NewConversationEntity(data)
	return manageDomain.DeleteMessages(ctx, conversationEntity)
}

// DeleteMessage
// 删除会话消息
func (manage *BotManageService) DeleteMessage(ctx context.Context, data *dto.MessageDTO) error {
	manageDomain := manage.NewManageDomain()
	newMessageEntity := dto.NewMessageEntity(data)
	return manageDomain.DeleteMessage(ctx, newMessageEntity)
}

// Plugins
// 查询所有插件
func (manage *BotManageService) Plugins(ctx context.Context) ([]*po.PluginPO, error) {
	manageDomain := manage.NewManageDomain()
	return manageDomain.QueryPlugins(ctx)
}

func (manage *BotManageService) UpdatePlugin(ctx context.Context, data *dto.PluginDTO) error {
	pluginEntity := dto.NewPluginEntity(data)
	manageDomain := manage.NewManageDomain()
	return manageDomain.UpdatePlugin(ctx, pluginEntity)
}
func (manage *BotManageService) UpdateAllPluginPlatform(ctx context.Context, data *dto.PlatformDTO) error {
	platformEntity := dto.NewPlatformEntity(data)
	manageDomain := manage.NewManageDomain()
	return manageDomain.UpdateAllPluginPlatform(ctx, platformEntity)
}

func (manage *BotManageService) Platforms(ctx context.Context) ([]*po.PlatformPO, error) {
	manageDomain := manage.NewManageDomain()
	return manageDomain.QueryPlatforms(ctx)
}

func (manage *BotManageService) CreatePlatform(ctx context.Context, data *dto.PlatformDTO) error {
	newPlatformEntity := dto.NewPlatformEntity(data)
	manageDomain := manage.NewManageDomain()
	return manageDomain.CreatePlatform(ctx, newPlatformEntity)
}

func (manage *BotManageService) UpdatePlatform(ctx context.Context, data *dto.PlatformDTO) error {
	platformEntity := dto.NewPlatformEntity(data)
	manageDomain := manage.NewManageDomain()
	return manageDomain.UpdatePlatform(ctx, platformEntity)
}

func (manage *BotManageService) DeletePlatforms(ctx context.Context, data *dto.PlatformDTO) error {
	platform := dto.NewPlatformEntity(data)
	manageDomain := manage.NewManageDomain()
	return manageDomain.DeletePlatform(ctx, platform)
}

func (manage *BotManageService) UUID() string {
	return manage.Node.String()
}

func (manage *BotManageService) OllamaModels(ctx context.Context, data *dto.PlatformDTO) (any, error) {
	ollamaManageDomain := manage.NewOllamaManageDomain(data)
	return ollamaManageDomain.Models(ctx)
}

func (manage *BotManageService) DeepSeekModels(ctx context.Context, data *dto.PlatformDTO) (any, error) {
	seekManageDomain := manage.NewDeepSeekManageDomain(data)
	return seekManageDomain.Models(ctx)
}

func (manage *BotManageService) VolcengineModels(ctx context.Context, data *dto.PlatformDTO) (any, error) {
	volcengineManageDomain := manage.NewVolcengineManageDomain(data)
	return volcengineManageDomain.Models(ctx)
}

func (manage *BotManageService) GiteeAIModels(ctx context.Context, data *dto.PlatformDTO, model string) (any, error) {
	volcengineManageDomain := manage.NewGiteeAIManageDomain(data)
	return volcengineManageDomain.Models(ctx, model)
}

func (manage *BotManageService) SystemSetting(ctx context.Context) (any, error) {
	data := new(po.SettingPO)
	if err := manage.ChatDB.Where("ID!=''").First(&data).Error; err != nil {
		return nil, err
	}
	return data, nil
}

func (manage *BotManageService) UpdateSystemSetting(ctx context.Context, data *dto.SettingDTO) error {
	settingPO := dto.NewSettingPO(data)
	if err := manage.ChatDB.Save(settingPO).Error; err != nil {
		return err
	}
	return nil
}

func (manage *BotManageService) Clear(ctx context.Context) error {
	// 清空所有会话消息记录
	chatDB := manage.ChatDB
	var list []*po.MessagePO
	chatDB.Find(&list)
	if len(list) == 0 {
		return nil
	}
	if err := chatDB.Delete(&list).Error; err != nil {
		return err
	}
	return nil
}

// CreateFile 创建文件或目录
func (manage *BotManageService) CreateFile(ctx context.Context, data *dto.FileCreateDTO) (*po.FilePO, error) {
	fileEntity := dto.NewFileEntityFromCreate(data)
	fileDomain := manage.NewFileDomain()
	return fileDomain.CreateFile(ctx, fileEntity)
}

// DeleteFile 删除文件或目录
func (manage *BotManageService) DeleteFile(ctx context.Context, data *dto.FileDeleteDTO) error {
	// 使用事务确保删除操作的原子性
	return manage.ChatDB.Transaction(func(tx *gorm.DB) error {
		// 创建带事务的文件域服务
		fileEntity := dto.NewFileEntityFromDelete(data)
		fileDomain := manage.NewFileDomainWithTx(tx)

		// 在事务中执行删除操作
		return fileDomain.DeleteFile(ctx, fileEntity)
	})
}

// UpdateFile 更新文件信息
func (manage *BotManageService) UpdateFile(ctx context.Context, data *dto.FileUpdateDTO) (*po.FilePO, error) {
	fileEntity := dto.NewFileEntityFromUpdate(data)
	fileDomain := manage.NewFileDomain()
	return fileDomain.UpdateFile(ctx, fileEntity)
}

// QueryFile 查询单个文件信息
func (manage *BotManageService) QueryFile(ctx context.Context, data *dto.FileQueryDTO) (*po.FilePO, error) {
	fileEntity := dto.NewFileEntityFromQuery(data)
	fileDomain := manage.NewFileDomain()
	return fileDomain.QueryFile(ctx, fileEntity)
}

// QueryFiles 查询文件列表
func (manage *BotManageService) QueryFiles(ctx context.Context, data *dto.FileQueryDTO) ([]*po.FilePO, error) {
	fileEntity := dto.NewFileEntityFromQuery(data)
	fileDomain := manage.NewFileDomain()
	return fileDomain.QueryFiles(ctx, fileEntity)
}

// QueryFilesByPID 根据父级ID查询文件列表
func (manage *BotManageService) QueryFilesByPID(ctx context.Context, pid string) ([]*po.FilePO, error) {
	fileDomain := manage.NewFileDomain()
	return fileDomain.QueryFilesByPID(ctx, pid)
}

// GetFileTree 获取文件树结构
func (manage *BotManageService) GetFileTree(ctx context.Context, rootPID string) ([]*po.FilePO, error) {
	fileDomain := manage.NewFileDomain()
	return fileDomain.GetFileTree(ctx, rootPID)
}

// MoveFile 移动文件或目录
func (manage *BotManageService) MoveFile(ctx context.Context, fileID, newPID string) (*po.FilePO, error) {
	fileDomain := manage.NewFileDomain()
	return fileDomain.MoveFile(ctx, fileID, newPID)
}

// PaintingConfig 相关方法

// QueryPaintingConfigs 查询所有绘画配置
func (manage *BotManageService) QueryPaintingConfigs(ctx context.Context) ([]*po.PaintingConfigPO, error) {
	paintingDomain := manage.NewPaintingManageDomain()
	return paintingDomain.QueryPaintingConfigs(ctx)
}

// QueryPaintingConfigsByCondition 根据条件查询绘画配置
func (manage *BotManageService) QueryPaintingConfigsByCondition(ctx context.Context, data *dto.PaintingConfigQueryDTO) ([]*po.PaintingConfigPO, error) {
	paintingConfigEntity := dto.NewPaintingConfigEntityFromQuery(data)
	paintingDomain := manage.NewPaintingManageDomain()
	return paintingDomain.QueryPaintingConfigsByCondition(ctx, paintingConfigEntity)
}

// CreatePaintingConfig 创建绘画配置
func (manage *BotManageService) CreatePaintingConfig(ctx context.Context, data *dto.PaintingConfigDTO) (*po.PaintingConfigPO, error) {
	paintingConfigEntity := dto.NewPaintingConfigEntity(data)
	paintingDomain := manage.NewPaintingManageDomain()
	return paintingDomain.CreatePaintingConfig(ctx, paintingConfigEntity)
}

// UpdatePaintingConfig 更新绘画配置
func (manage *BotManageService) UpdatePaintingConfig(ctx context.Context, data *dto.PaintingConfigDTO) (*po.PaintingConfigPO, error) {
	paintingConfigEntity := dto.NewPaintingConfigEntity(data)
	paintingDomain := manage.NewPaintingManageDomain()
	return paintingDomain.UpdatePaintingConfig(ctx, paintingConfigEntity)
}

// DeletePaintingConfig 删除绘画配置
func (manage *BotManageService) DeletePaintingConfig(ctx context.Context, data *dto.PaintingConfigDTO) error {
	paintingConfigEntity := dto.NewPaintingConfigEntity(data)
	paintingDomain := manage.NewPaintingManageDomain()
	return paintingDomain.DeletePaintingConfig(ctx, paintingConfigEntity)
}

// GetPaintingConfig 获取单个绘画配置
func (manage *BotManageService) GetPaintingConfig(ctx context.Context, data *dto.PaintingConfigDTO) (*po.PaintingConfigPO, error) {
	paintingConfigEntity := dto.NewPaintingConfigEntity(data)
	paintingDomain := manage.NewPaintingManageDomain()
	return paintingDomain.GetPaintingConfig(ctx, paintingConfigEntity)
}

// PaintingNegativePrompt 相关方法

// QueryPaintingNegativePrompts 查询所有负面提示词
func (manage *BotManageService) QueryPaintingNegativePrompts(ctx context.Context) ([]*po.PaintingNegativePromptPO, error) {
	paintingDomain := manage.NewPaintingManageDomain()
	return paintingDomain.QueryPaintingNegativePrompts(ctx)
}

// QueryPaintingNegativePromptsByCondition 根据条件查询负面提示词
func (manage *BotManageService) QueryPaintingNegativePromptsByCondition(ctx context.Context, data *dto.PaintingNegativePromptDTO) ([]*po.PaintingNegativePromptPO, error) {
	paintingNegativePromptEntity := dto.NewPaintingNegativePromptEntity(data)
	paintingDomain := manage.NewPaintingManageDomain()
	return paintingDomain.QueryPaintingNegativePromptsByCondition(ctx, paintingNegativePromptEntity)
}

// CreatePaintingNegativePrompt 创建负面提示词
func (manage *BotManageService) CreatePaintingNegativePrompt(ctx context.Context, data *dto.PaintingNegativePromptDTO) (*po.PaintingNegativePromptPO, error) {
	paintingNegativePromptEntity := dto.NewPaintingNegativePromptEntity(data)
	paintingDomain := manage.NewPaintingManageDomain()
	return paintingDomain.CreatePaintingNegativePrompt(ctx, paintingNegativePromptEntity)
}

// UpdatePaintingNegativePrompt 更新负面提示词
func (manage *BotManageService) UpdatePaintingNegativePrompt(ctx context.Context, data *dto.PaintingNegativePromptDTO) (*po.PaintingNegativePromptPO, error) {
	paintingNegativePromptEntity := dto.NewPaintingNegativePromptEntity(data)
	paintingDomain := manage.NewPaintingManageDomain()
	return paintingDomain.UpdatePaintingNegativePrompt(ctx, paintingNegativePromptEntity)
}

// DeletePaintingNegativePrompt 删除负面提示词
func (manage *BotManageService) DeletePaintingNegativePrompt(ctx context.Context, data *dto.PaintingNegativePromptDTO) error {
	paintingNegativePromptEntity := dto.NewPaintingNegativePromptEntity(data)
	paintingDomain := manage.NewPaintingManageDomain()
	return paintingDomain.DeletePaintingNegativePrompt(ctx, paintingNegativePromptEntity)
}

// GetPaintingNegativePrompt 获取单个负面提示词
func (manage *BotManageService) GetPaintingNegativePrompt(ctx context.Context, data *dto.PaintingNegativePromptDTO) (*po.PaintingNegativePromptPO, error) {
	paintingNegativePromptEntity := dto.NewPaintingNegativePromptEntity(data)
	paintingDomain := manage.NewPaintingManageDomain()
	return paintingDomain.GetPaintingNegativePrompt(ctx, paintingNegativePromptEntity)
}

// PaintingRecord 相关方法

// QueryPaintingRecords 查询所有绘画记录
func (manage *BotManageService) QueryPaintingRecords(ctx context.Context) ([]*po.PaintingPO, error) {
	paintingDomain := manage.NewPaintingManageDomain()
	return paintingDomain.QueryPaintingRecords(ctx)
}

// QueryPaintingRecordsByCondition 根据条件查询绘画记录
func (manage *BotManageService) QueryPaintingRecordsByCondition(ctx context.Context, data *dto.PaintingRecordQueryDTO) ([]*po.PaintingPO, error) {
	paintingRecordEntity := dto.NewPaintingRecordEntityFromQuery(data)
	paintingDomain := manage.NewPaintingManageDomain()
	return paintingDomain.QueryPaintingRecordsByCondition(ctx, paintingRecordEntity)
}

// CreatePaintingRecord 创建绘画记录
func (manage *BotManageService) CreatePaintingRecord(ctx context.Context, data *dto.PaintingRecordDTO) (*po.PaintingPO, error) {
	paintingRecordEntity := dto.NewPaintingRecordEntity(data)
	paintingDomain := manage.NewPaintingManageDomain()
	return paintingDomain.CreatePaintingRecord(ctx, paintingRecordEntity)
}

// UpdatePaintingRecord 更新绘画记录
func (manage *BotManageService) UpdatePaintingRecord(ctx context.Context, data *dto.PaintingRecordDTO) (*po.PaintingPO, error) {
	paintingRecordEntity := dto.NewPaintingRecordEntity(data)
	paintingDomain := manage.NewPaintingManageDomain()
	return paintingDomain.UpdatePaintingRecord(ctx, paintingRecordEntity)
}

// DeletePaintingRecord 删除绘画记录
func (manage *BotManageService) DeletePaintingRecord(ctx context.Context, data *dto.PaintingRecordDTO) error {
	paintingRecordEntity := dto.NewPaintingRecordEntity(data)
	paintingDomain := manage.NewPaintingManageDomain()
	return paintingDomain.DeletePaintingRecord(ctx, paintingRecordEntity)
}

// GetPaintingRecord 获取单个绘画记录
func (manage *BotManageService) GetPaintingRecord(ctx context.Context, data *dto.PaintingRecordDTO) (*po.PaintingPO, error) {
	paintingRecordEntity := dto.NewPaintingRecordEntity(data)
	paintingDomain := manage.NewPaintingManageDomain()
	return paintingDomain.GetPaintingRecord(ctx, paintingRecordEntity)
}
