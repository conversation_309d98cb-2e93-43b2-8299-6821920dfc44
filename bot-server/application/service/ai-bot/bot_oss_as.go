package ai_bot

import "context"

type BotOssService struct {
	*AiBot
}

func NewBotOssService(bot *AiBot) *BotOssService {
	return &BotOssService{
		AiBot: bot,
	}
}

func (oss *BotOssService) UploadFile(ctx context.Context, filename string, data []byte) error {
	ossDomain := oss.OssDomain()
	return ossDomain.Put(ctx, filename, data)
}

func (oss *BotOssService) Get(ctx context.Context, filename string) ([]byte, error) {
	ossDomain := oss.OssDomain()
	return ossDomain.Get(ctx, filename)
}
