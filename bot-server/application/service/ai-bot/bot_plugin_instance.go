package ai_bot

import (
	drespo "bot/domain/repository"
	botcontext "bot/domain/service/bot-context"
	"bot/domain/service/bot/sdk"
	"bot/domain/vo"
	ai_bot_platform "bot/infrastructure/repository/ai-bot-platform"
	"context"
	"net/http"
)

// LoadPlugin
// 更具不同的 平台创建对应的 实现
func (aibot *AiBot) LoadAgentInstance(ctx context.Context, w http.ResponseWriter, r *http.Request, param *sdk.BaseChat) (drespo.IBase, error) {
	var err error
	var botContext *botcontext.Context
	var instance drespo.IBase
	instance = ai_bot_platform.NewDefaultBot(aibot.BaseRepository)
	// 获取上下文
	if botContext, err = aibot.BotContext(ctx, w, r, param); err != nil {
		return instance, err
	}
	switch botContext.Platform.Type {
	case vo.OllamaPlatform:
		instance, err = ai_bot_platform.NewLlama(botContext, aibot.BaseRepository)
	case vo.DeepSeekPlatform:
		instance, err = ai_bot_platform.NewDeepSeek(botContext, aibot.BaseRepository)
	case vo.GiteeAIPlatform:
		instance, err = ai_bot_platform.NewGiteeAI(botContext, aibot.BaseRepository)
	case vo.VolcenginePlatform:
		instance, err = ai_bot_platform.NewVolcengine(botContext, aibot.BaseRepository)
	}
	return instance, err
}
