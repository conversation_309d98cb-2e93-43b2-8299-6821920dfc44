package ai_bot

import (
	"bot/application/dto"
	"bot/domain/service/bot"
	botfile "bot/domain/service/bot-file"
	botmanage "bot/domain/service/bot-manage"
	botmcp "bot/domain/service/bot-mcp"
	botoss "bot/domain/service/bot-oss"
	botpainting "bot/domain/service/bot-painting"
	"bot/infrastructure/common/db"
	"bot/infrastructure/po"
	aibotmcp "bot/infrastructure/repository/ai-bot-mcp"
	ai_bot_platform "bot/infrastructure/repository/ai-bot-platform"
	botfileimpl "bot/infrastructure/repository/bot-file-impl"
	irespo "bot/infrastructure/repository/bot-manmge-impl"
	botossimpl "bot/infrastructure/repository/bot-oss-impl"
	botpaintingimpl "bot/infrastructure/repository/bot-painting-impl"
	"context"
	"net/http"

	"gorm.io/gorm"
)

type AiBot struct {
	*ai_bot_platform.BaseRepository
}

func NewAiBot(baseRepository *ai_bot_platform.BaseRepository) *AiBot {
	return &AiBot{
		BaseRepository: baseRepository,
	}
}

// Send
// 默认消息发送
func (aibot *AiBot) Send(ctx context.Context, param *dto.ChatDTO, r *http.Request, w http.ResponseWriter) (*po.MessagePO, error) {
	baseImpl, err := aibot.LoadAgentInstance(ctx, w, r, param.BaseChat)
	if err != nil {
		return nil, err
	}
	botDomain := aibot.NewBotDomain(bot.WithBase(baseImpl))
	chatEntity := dto.NewChatEntity(param)
	return botDomain.IBase.Send(ctx, chatEntity)
}

// SaveAgent
// 保存代理消息
func (aibot *AiBot) SaveAgent(ctx context.Context, param *dto.ChatDTO, r *http.Request, w http.ResponseWriter) (*po.MessagePO, error) {
	baseImpl, err := aibot.LoadAgentInstance(ctx, w, r, param.BaseChat)
	if err != nil {
		return nil, err
	}
	botDomain := aibot.NewBotDomain(bot.WithBase(baseImpl))
	chatEntity := dto.NewChatEntity(param)
	return botDomain.SaveAgent(ctx, chatEntity)
}

func (aibot *AiBot) NewBotDomain(option ...bot.Option) *bot.Bot {
	b := bot.New(option...)
	return b
}

func (aibot *AiBot) NewManageDomain() *botmanage.ManageDomain {
	return botmanage.NewManageDomain(irespo.NewManageImpl(aibot.BaseRepository))
}

func (aibot *AiBot) NewPaintingManageDomain() *botpainting.PaintingManageDomain {
	return botpainting.NewPaintingManageDomain(
		botpaintingimpl.NewPaintingConfigImpl(aibot.BaseRepository),
		botpaintingimpl.NewPaintingNegativePromptImpl(aibot.BaseRepository),
		botpaintingimpl.NewPaintingRecordImpl(aibot.BaseRepository),
	)
}

func (aibot *AiBot) NewOllamaManageDomain(data *dto.PlatformDTO) *botmanage.OllamaManageDomain {
	platform := &po.PlatformPO{ID: data.ID}
	aibot.ChatDB.First(platform)
	config, err := aibot.PlatformConfig(platform)
	if err != nil {
		return nil
	}
	return botmanage.NewOllamaManageDomain(config)
}

func (aibot *AiBot) NewDeepSeekManageDomain(data *dto.PlatformDTO) *botmanage.DeepSeekManageDomain {
	platform := &po.PlatformPO{ID: data.ID}
	aibot.ChatDB.First(platform)
	config, err := aibot.PlatformConfig(platform)
	if err != nil {
		return nil
	}
	return botmanage.NewDeepSeekManageDomain(config)
}

func (aibot *AiBot) NewMcpDomain() *botmcp.McpDomain {
	return botmcp.NewMcpDomain(aibotmcp.NewMCPClientManage(aibot.BaseRepository))
}

func (aibot *AiBot) NewVolcengineManageDomain(data *dto.PlatformDTO) *botmanage.VolcengineManageDomain {
	platform := &po.PlatformPO{ID: data.ID}
	aibot.ChatDB.First(platform)
	config, err := aibot.PlatformConfig(platform)
	if err != nil {
		return nil
	}
	return botmanage.NewVolcengineManageDomain(config)
}

func (aibot *AiBot) NewGiteeAIManageDomain(data *dto.PlatformDTO) *botmanage.GiteeAIManageDomain {
	platform := &po.PlatformPO{ID: data.ID}
	aibot.ChatDB.First(platform)
	config, err := aibot.PlatformConfig(platform)
	if err != nil {
		return nil
	}
	return botmanage.NewGiteeAIManageDomain(config)
}

func (aibot *AiBot) NewFileDomain() *botfile.FileDomain {
	return botfile.NewFileDomain(botfileimpl.NewFileImpl(aibot.BaseRepository))
}

// NewFileDomainWithTx 创建带事务的文件域服务
func (aibot *AiBot) NewFileDomainWithTx(tx *gorm.DB) *botfile.FileDomain {
	// 创建一个临时的 BaseRepository，使用事务连接
	baseRepoWithTx := &ai_bot_platform.BaseRepository{
		ChatDB: &db.ChatDB{DB: tx},
		AppLog: aibot.BaseRepository.AppLog,
		Node:   aibot.BaseRepository.Node,
	}
	return botfile.NewFileDomain(botfileimpl.NewFileImpl(baseRepoWithTx))
}

func (aibot *AiBot) OssDomain() *botoss.BotOssDomain {
	return botoss.NewBotOssDomain(botossimpl.NewLocalOssImpl("static"))
}
