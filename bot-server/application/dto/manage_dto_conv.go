package dto

import (
	"bot/domain/entity"
	"bot/infrastructure/po"
)

// NewPaintingConfigEntity 从PaintingConfigDTO创建PaintingConfigEntity
func NewPaintingConfigEntity(dto *PaintingConfigDTO) *entity.PaintingConfigEntity {
	if dto == nil {
		return nil
	}
	return &entity.PaintingConfigEntity{
		ID:        dto.ID,
		Config:    dto.Config,
		CreatedAt: dto.CreatedAt,
		UpdatedAt: dto.UpdatedAt,
		DeletedAt: dto.DeletedAt,
	}
}

// NewPaintingConfigPO 从PaintingConfigEntity创建PaintingConfigPO
func NewPaintingConfigPO(entity *entity.PaintingConfigEntity) *po.PaintingConfigPO {
	if entity == nil {
		return nil
	}
	return &po.PaintingConfigPO{
		ID:        entity.ID,
		Config:    entity.Config,
		CreatedAt: entity.CreatedAt,
		UpdatedAt: entity.UpdatedAt,
		DeletedAt: entity.DeletedAt,
	}
}

// PaintingConfigEntityFromPO 从PaintingConfigPO创建PaintingConfigEntity
func PaintingConfigEntityFromPO(po *po.PaintingConfigPO) *entity.PaintingConfigEntity {
	if po == nil {
		return nil
	}
	return &entity.PaintingConfigEntity{
		ID:        po.ID,
		Config:    po.Config,
		CreatedAt: po.CreatedAt,
		UpdatedAt: po.UpdatedAt,
		DeletedAt: po.DeletedAt,
	}
}

// NewPaintingConfigDTO 从PaintingConfigEntity创建PaintingConfigDTO
func NewPaintingConfigDTO(entity *entity.PaintingConfigEntity) *PaintingConfigDTO {
	if entity == nil {
		return nil
	}
	return &PaintingConfigDTO{
		ID:        entity.ID,
		Config:    entity.Config,
		CreatedAt: entity.CreatedAt,
		UpdatedAt: entity.UpdatedAt,
		DeletedAt: entity.DeletedAt,
	}
}

// NewPaintingConfigEntityFromQuery 从PaintingConfigQueryDTO创建PaintingConfigEntity
func NewPaintingConfigEntityFromQuery(dto *PaintingConfigQueryDTO) *entity.PaintingConfigEntity {
	if dto == nil {
		return nil
	}
	return &entity.PaintingConfigEntity{
		ID: dto.ID,
	}
}

func NewSettingPO(dto *SettingDTO) *po.SettingPO {
	return &po.SettingPO{
		ID:    dto.ID,
		Value: dto.Value,
	}
}

func NewPaintingNegativePromptEntity(dto *PaintingNegativePromptDTO) *entity.PaintingNegativePromptEntity {
	return &entity.PaintingNegativePromptEntity{
		ID:        dto.ID,
		Prompt:    dto.Prompt,
		CreatedAt: dto.CreatedAt,
		UpdatedAt: dto.UpdatedAt,
		DeletedAt: dto.DeletedAt,
	}
}

// NewPaintingNegativePromptPO 从PaintingNegativePromptEntity创建PaintingNegativePromptPO
func NewPaintingNegativePromptPO(entity *entity.PaintingNegativePromptEntity) *po.PaintingNegativePromptPO {
	if entity == nil {
		return nil
	}
	return &po.PaintingNegativePromptPO{
		ID:        entity.ID,
		Prompt:    entity.Prompt,
		CreatedAt: entity.CreatedAt,
		UpdatedAt: entity.UpdatedAt,
		DeletedAt: entity.DeletedAt,
	}
}

// PaintingNegativePromptEntityFromPO 从PaintingNegativePromptPO创建PaintingNegativePromptEntity
func PaintingNegativePromptEntityFromPO(po *po.PaintingNegativePromptPO) *entity.PaintingNegativePromptEntity {
	if po == nil {
		return nil
	}
	return &entity.PaintingNegativePromptEntity{
		ID:        po.ID,
		Prompt:    po.Prompt,
		CreatedAt: po.CreatedAt,
		UpdatedAt: po.UpdatedAt,
		DeletedAt: po.DeletedAt,
	}
}

// NewPaintingNegativePromptDTO 从PaintingNegativePromptEntity创建PaintingNegativePromptDTO
func NewPaintingNegativePromptDTO(entity *entity.PaintingNegativePromptEntity) *PaintingNegativePromptDTO {
	if entity == nil {
		return nil
	}
	return &PaintingNegativePromptDTO{
		ID:        entity.ID,
		Prompt:    entity.Prompt,
		CreatedAt: entity.CreatedAt,
		UpdatedAt: entity.UpdatedAt,
		DeletedAt: entity.DeletedAt,
	}
}

// NewPaintingRecordEntity 从PaintingRecordDTO创建PaintingRecordEntity
func NewPaintingRecordEntity(dto *PaintingRecordDTO) *entity.PaintingRecordEntity {
	if dto == nil {
		return nil
	}
	return &entity.PaintingRecordEntity{
		ID:        dto.ID,
		Title:     dto.Title,
		Prompt:    dto.Prompt,
		Status:    dto.Status,
		ImageUrls: dto.ImageUrls,
		CreatedAt: dto.CreatedAt,
		UpdatedAt: dto.UpdatedAt,
		DeletedAt: dto.DeletedAt,
	}
}

// NewPaintingRecordPO 从PaintingRecordEntity创建PaintingRecordPO
func NewPaintingRecordPO(entity *entity.PaintingRecordEntity) *po.PaintingPO {
	if entity == nil {
		return nil
	}
	return &po.PaintingPO{
		ID:        entity.ID,
		Title:     entity.Title,
		Prompt:    entity.Prompt,
		Status:    entity.Status,
		ImageUrls: entity.ImageUrls,
		CreatedAt: entity.CreatedAt,
		UpdatedAt: entity.UpdatedAt,
		DeletedAt: entity.DeletedAt,
	}
}

// PaintingRecordEntityFromPO 从PaintingRecordPO创建PaintingRecordEntity
func PaintingRecordEntityFromPO(po *po.PaintingPO) *entity.PaintingRecordEntity {
	if po == nil {
		return nil
	}
	return &entity.PaintingRecordEntity{
		ID:        po.ID,
		Title:     po.Title,
		Prompt:    po.Prompt,
		Status:    po.Status,
		ImageUrls: po.ImageUrls,
		CreatedAt: po.CreatedAt,
		UpdatedAt: po.UpdatedAt,
		DeletedAt: po.DeletedAt,
	}
}

// NewPaintingRecordDTO 从PaintingRecordEntity创建PaintingRecordDTO
func NewPaintingRecordDTO(entity *entity.PaintingRecordEntity) *PaintingRecordDTO {
	if entity == nil {
		return nil
	}
	return &PaintingRecordDTO{
		ID:        entity.ID,
		Title:     entity.Title,
		Prompt:    entity.Prompt,
		Status:    entity.Status,
		ImageUrls: entity.ImageUrls,
		CreatedAt: entity.CreatedAt,
		UpdatedAt: entity.UpdatedAt,
		DeletedAt: entity.DeletedAt,
	}
}

// NewPaintingRecordEntityFromQuery 从PaintingRecordQueryDTO创建PaintingRecordEntity
func NewPaintingRecordEntityFromQuery(dto *PaintingRecordQueryDTO) *entity.PaintingRecordEntity {
	if dto == nil {
		return nil
	}
	return &entity.PaintingRecordEntity{
		ID:     dto.ID,
		Title:  dto.Title,
		Status: dto.Status,
	}
}
