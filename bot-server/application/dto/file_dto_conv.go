package dto

import (
	"bot/domain/entity"
	"bot/infrastructure/po"
)

// NewFileEntity 从FileDTO创建FileEntity
func NewFileEntity(dto *FileDTO) *entity.FileEntity {
	if dto == nil {
		return nil
	}
	return &entity.FileEntity{
		ID:        dto.ID,
		PID:       dto.PID,
		Name:      dto.Name,
		Size:      dto.Size,
		IsDir:     dto.IsDir,
		CreatedAt: dto.CreatedAt,
		UpdatedAt: dto.UpdatedAt,
		DeletedAt: dto.DeletedAt,
	}
}

// NewFileEntityFromQuery 从FileQueryDTO创建FileEntity
func NewFileEntityFromQuery(dto *FileQueryDTO) *entity.FileEntity {
	if dto == nil {
		return nil
	}
	entity := &entity.FileEntity{
		ID:   dto.ID,
		PID:  dto.PID,
		Name: dto.Name,
	}
	if dto.IsDir != nil {
		entity.IsDir = *dto.IsDir
	}
	return entity
}

// NewFileEntityFromCreate 从FileCreateDTO创建FileEntity
func NewFileEntityFromCreate(dto *FileCreateDTO) *entity.FileEntity {
	if dto == nil {
		return nil
	}
	return &entity.FileEntity{
		PID:   dto.PID,
		Name:  dto.Name,
		Size:  dto.Size,
		IsDir: dto.IsDir,
	}
}

// NewFileEntityFromUpdate 从FileUpdateDTO创建FileEntity
func NewFileEntityFromUpdate(dto *FileUpdateDTO) *entity.FileEntity {
	if dto == nil {
		return nil
	}
	return &entity.FileEntity{
		ID:   dto.ID,
		Name: dto.Name,
		PID:  dto.PID,
	}
}

// NewFileEntityFromDelete 从FileDeleteDTO创建FileEntity
func NewFileEntityFromDelete(dto *FileDeleteDTO) *entity.FileEntity {
	if dto == nil {
		return nil
	}
	return &entity.FileEntity{
		ID: dto.ID,
	}
}

// NewFilePO 从FileEntity创建FilePO
func NewFilePO(entity *entity.FileEntity) *po.FilePO {
	if entity == nil {
		return nil
	}
	return &po.FilePO{
		ID:        entity.ID,
		PID:       entity.PID,
		Name:      entity.Name,
		Size:      entity.Size,
		IsDir:     entity.IsDir,
		CreatedAt: entity.CreatedAt,
		UpdatedAt: entity.UpdatedAt,
		DeletedAt: entity.DeletedAt,
	}
}

// FileEntityFromPO 从FilePO创建FileEntity
func FileEntityFromPO(po *po.FilePO) *entity.FileEntity {
	if po == nil {
		return nil
	}
	return &entity.FileEntity{
		ID:        po.ID,
		PID:       po.PID,
		Name:      po.Name,
		Size:      po.Size,
		IsDir:     po.IsDir,
		CreatedAt: po.CreatedAt,
		UpdatedAt: po.UpdatedAt,
		DeletedAt: po.DeletedAt,
	}
}
