package dto

import (
	"database/sql"
	"time"
)

type SettingDTO struct {
	ID    string `json:"ID,omitempty"`
	Value string `json:"value,omitempty"`
}

type PaintingNegativePromptDTO struct {
	ID        string       `json:"id"`        // ID 绘画负面提示词ID
	Prompt    string       `json:"prompt"`    // Prompt 绘画负面提示词
	CreatedAt time.Time    `json:"createdAt"` // CreatedAt 创建时间
	UpdatedAt time.Time    `json:"updatedAt"` // UpdatedAt 更新时间
	DeletedAt sql.NullTime `json:"deletedAt"` // DeletedAt 删除时间
}

// PaintingConfigDTO 绘画配置DTO
type PaintingConfigDTO struct {
	ID        string       `json:"id"`        // ID 绘画配置ID
	Config    string       `json:"config"`    // Config 绘画配置 json 格式
	CreatedAt time.Time    `json:"createdAt"` // CreatedAt 创建时间
	UpdatedAt time.Time    `json:"updatedAt"` // UpdatedAt 更新时间
	DeletedAt sql.NullTime `json:"deletedAt"` // DeletedAt 删除时间
}

// PaintingConfigQueryDTO 绘画配置查询DTO
type PaintingConfigQueryDTO struct {
	ID string `json:"id,omitempty"` // 绘画配置ID - 用于精确查询
}

// PaintingRecordDTO 绘画记录DTO
type PaintingRecordDTO struct {
	ID        string       `json:"id"`        // ID 绘画ID
	Title     string       `json:"title"`     // Title 绘画名称
	Prompt    string       `json:"prompt"`    // Prompt 绘画提示词
	Status    string       `json:"status"`    // Status 绘画状态
	ImageUrls string       `json:"imageUrls"` // ImageUrls 绘画图片URL 多个图片URL用逗号分隔
	CreatedAt time.Time    `json:"createdAt"` // CreatedAt 创建时间
	UpdatedAt time.Time    `json:"updatedAt"` // UpdatedAt 更新时间
	DeletedAt sql.NullTime `json:"deletedAt"` // DeletedAt 删除时间
}

// PaintingRecordQueryDTO 绘画记录查询DTO
type PaintingRecordQueryDTO struct {
	ID     string `json:"id,omitempty"`     // 绘画ID - 用于精确查询
	Title  string `json:"title,omitempty"`  // 绘画名称 - 用于模糊查询
	Status string `json:"status,omitempty"` // 绘画状态 - 用于状态筛选
}
