package dto

import (
	"database/sql"
	"time"
)

// FileDTO 文件数据传输对象
type FileDTO struct {
	ID        string       `json:"id,omitempty"`        // 文件ID
	PID       string       `json:"pid,omitempty"`       // 父级ID
	Name      string       `json:"name,omitempty"`      // 文件名称
	Size      int64        `json:"size,omitempty"`      // 文件大小
	IsDir     bool         `json:"isDir,omitempty"`     // 是否是目录
	CreatedAt time.Time    `json:"createdAt,omitempty"` // 创建时间
	UpdatedAt time.Time    `json:"updatedAt,omitempty"` // 更新时间
	DeletedAt sql.NullTime `json:"deletedAt,omitempty"` // 删除时间
}

// FileQueryDTO 文件查询DTO
type FileQueryDTO struct {
	ID    string `json:"id,omitempty"`    // 文件ID
	PID   string `json:"pid,omitempty"`   // 父级ID - 用于查询指定目录下的文件
	Name  string `json:"name,omitempty"`  // 文件名称 - 用于模糊查询
	IsDir *bool  `json:"isDir,omitempty"` // 是否是目录 - 用于过滤文件类型
}

// FileCreateDTO 文件创建DTO
type FileCreateDTO struct {
	PID   string `json:"pid"`                     // 父级ID (根目录时为空字符串)
	Name  string `json:"name" binding:"required"` // 文件名称
	IsDir bool   `json:"isDir"`                   // 是否是目录
	Size  int64  `json:"size,omitempty"`          // 文件大小
}

// FileUpdateDTO 文件更新DTO
type FileUpdateDTO struct {
	ID   string `json:"id" binding:"required"` // 文件ID
	Name string `json:"name,omitempty"`        // 新文件名称
	PID  string `json:"pid,omitempty"`         // 新父级ID (用于移动文件)
}

// FileDeleteDTO 文件删除DTO
type FileDeleteDTO struct {
	ID string `json:"id" binding:"required"` // 文件ID
}
