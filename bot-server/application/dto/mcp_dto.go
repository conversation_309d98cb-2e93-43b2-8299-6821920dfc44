package dto

type McpDTO struct {
	ID       string            ` json:"id"`
	Name     string            ` json:"name"`     // mcp 名称
	Command  string            ` json:"command"`  // 执行命令
	Args     []string          ` json:"args"`     // 命令参数
	Env      map[string]string ` json:"env"`      // 环境变量
	Disabled int               ` json:"disabled"` // 是否启用
	Timeout  int               ` json:"timeout"`  // 接口超时配置
}
