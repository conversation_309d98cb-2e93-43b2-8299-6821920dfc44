// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"bot/application/service/ai-bot"
	"bot/conf"
	"bot/infrastructure/common/db"
	"bot/infrastructure/common/logs"
	"bot/infrastructure/common/util/uuidutils/uuid"
	"bot/infrastructure/common/web"
	"bot/infrastructure/repository/ai-bot-mcp"
	"bot/infrastructure/repository/ai-bot-platform"
	"bot/interfaces"
	"bot/interfaces/controller/bot"
	"bot/interfaces/controller/manage"
	"bot/interfaces/controller/mcp"
	"bot/interfaces/controller/oss"
)

// Injectors from wire.go:

func InitApp() (*App, error) {
	engine := web.NewGin()
	configuration := conf.NewEnv()
	appLog := logs.NewAppLog(configuration)
	handler := web.NewServer(engine, appLog)
	chatDB := db.NewChatDB(configuration, appLog)
	node := uuid.NewUID(configuration)
	baseRepository := ai_bot_platform.NewBaseRepository(chatDB, appLog, node)
	aiBot := ai_bot.NewAiBot(baseRepository)
	chatService := ai_bot.NewChatService(aiBot)
	paintingService := ai_bot.NewPaintingService(aiBot)
	botManageService := ai_bot.NewBotManageService(aiBot)
	control := bot.NewControl(appLog, configuration, chatService, paintingService, botManageService)
	botOssService := ai_bot.NewBotOssService(aiBot)
	ossControl := oss.NewControl(botOssService)
	manageControl := manage.NewControl(appLog, configuration, botManageService)
	iMcp := ai_bot_mcp.NewMCPClientManage(baseRepository)
	botMcpService := ai_bot.NewBotMcpService(aiBot, iMcp)
	mcpControl := mcp.NewControl(appLog, configuration, botMcpService)
	api := interfaces.NewApi(configuration, appLog, engine, chatDB, control, ossControl, manageControl, mcpControl)
	app := NewApp(handler, chatDB, configuration, appLog, api, node)
	return app, nil
}
