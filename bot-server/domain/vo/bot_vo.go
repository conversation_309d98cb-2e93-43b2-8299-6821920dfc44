package vo

const (

	// ==========================================================

	// UserRoleType 用户角色
	UserRoleType = "user"

	// AssistantRoleType 助手角色
	AssistantRoleType = "assistant"

	// SystemRoleType 系统角色
	SystemRoleType = "system"

	// 工具调用角色
	ToolRoleType = "tool"

	// ==========================================================

	// Platform 平台 常量定义 =============

	// OllamaPlatform Ollama 本地平台 属于类型分类
	OllamaPlatform = "Ollama"
	// OllamaPlatformConfig Ollama 配置面板
	OllamaPlatformConfig = "OllamaConfig"
	// OllamaQuickConfig Ollama 快捷配置面板
	OllamaQuickConfig = "OllamaQuickConfig"

	DeepSeekPlatform       = "DeepSeek"
	DeepSeekPlatformConfig = "DeepSeekConfig"
	DeepSeekQuickConfig    = "DeepSeekQuickConfig"

	GiteeAIPlatform       = "GiteeAI"
	GiteeAIPlatformConfig = "GiteeAIConfig"
	GiteeAIQuickConfig    = "GiteeAIQuickConfig"

	VolcenginePlatform       = "Volcengine"
	VolcenginePlatformConfig = "VolcEngineConfig"
	VolcengineQuickConfig    = "VolcengineQuickConfig"
)
