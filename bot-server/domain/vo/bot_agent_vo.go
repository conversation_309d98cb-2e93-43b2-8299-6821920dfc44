package vo

const (

	// AgentItemStatus 代理状态
	AgentItemStatusDefault   = "default"   // 默认状态 一般不具备任何意义
	AgentItemStatusInit      = "init"      // 初始化状态
	AgentItemStatusStart     = "start"     // 启动状态
	AgentItemStatusRunning   = "running"   // 运行状态
	AgentItemStatusWait      = "wait"      // 等待状态
	AgentItemStatusPause     = "pause"     // 暂停状态
	AgentItemStatusStop      = "stop"      // 停止状态
	AgentItemStatusLoading   = "loading"   // 加载状态
	AgentItemStatusStreaming = "streaming" // 流状态
	AgentItemStatusFinish    = "finish"    // 完成状态
	AgentItemStatusCancel    = "cancel"    // 取消状态
	AgentItemStatusTimeout   = "timeout"   // 超时状态
	AgentItemStatusUnknown   = "unknown"   // 未知状态
	AgentItemStatusFailed    = "failed"    // 失败状态
	AgentItemStatusPending   = "pending"   // 等待状态
	AgentItemStatusSuccess   = "success"   // 成功状态
	AgentItemStatusError     = "error"     // 错误状态

	// AgentPaintingItemStatus AI 绘画状态比较多，需要专项制定
	AgentPaintingItemStatus        = "painting_default"
	AgentPaintingItemStatusStart   = "painting_start"
	AgentPaintingItemStatusRunning = "painting_running"
	AgentPaintingItemStatusFinish  = "painting_finish"

	AgentMcpCall        = "mcp"             // Mcp 调用
	AgentActionCall     = "action"          // Action 动作
	AgentMarkdown       = "markdown"        // Markdown 数据
	AgentThinking       = "thinking"        // Thinking 思考数据
	AgentPainting       = "painting"        // Painting 绘画
	AgentPaintingBegin  = "painting_begin"  // Painting 绘画开始
	AgentPaintingFinish = "painting_finish" // Painting 绘画完成
	AgentPaintingCancel = "painting_cancel" // Painting 绘画取消
)
