package repository

import (
	"bot/domain/entity"
	"bot/infrastructure/po"
	"context"
)

type IBase interface {
	// Send 函数实现 用户聊天消息的发送入库存储
	Send(context.Context, *entity.ChatEntity) (*po.MessagePO, error)
	// SaveAgent 函数实现 代理消息的保存入库存储
	SaveAgent(context.Context, *entity.ChatEntity) (*po.MessagePO, error)
	// AgentStream 函数实现 代理消息的推送
	AgentStream(context.Context, *entity.ChatEntity)

	// SendPainting 函数实现 绘画消息的发送入库存储
	SendPainting(context.Context, *entity.PaintingEntity) (*po.PaintingPO, error)
	// SavePainting 函数实现 绘画消息的保存入库存储
	SavePainting(context.Context, *entity.PaintingEntity) (*po.PaintingPO, error)
	// PaintingStream 函数实现 绘画消息的推送
	PaintingStream(context.Context, *entity.PaintingEntity)
}
