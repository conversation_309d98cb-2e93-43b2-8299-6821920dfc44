package repository

import (
	"bot/domain/entity"
	"bot/infrastructure/po"
	"context"
)

type IManage interface {
	IConversation
	IPlugin
	IPlatform
	IFile
}

type IConversation interface {
	// CreateConversation 创建会话
	CreateConversation(context.Context, *entity.ConversationEntity) (string, error)

	// DeleteConversation 删除会话
	DeleteConversation(context.Context, *entity.ConversationEntity) error

	// QueryConversations 查询会话列表
	QueryConversations(context.Context) ([]po.ConversationPO, error)

	// UpdateConversation 更新会话
	UpdateConversation(context.Context, *entity.ConversationEntity) (*po.ConversationPO, error)

	// QueryMessages 查询会话消息
	QueryMessages(context.Context, *entity.ConversationEntity) ([]*po.MessagePO, error)

	QueryMessage(context.Context, *entity.MessageEntity) (*po.MessagePO, error)

	// DeleteMessages 删除会话消息
	DeleteMessages(context.Context, *entity.ConversationEntity) error

	// DeleteMessage 删除会话消息
	DeleteMessage(context.Context, *entity.MessageEntity) error
}

type IMessage interface {
}

type IPlugin interface {
	// QueryPlugins 查询所有插件
	QueryPlugins(context.Context) ([]*po.PluginPO, error)

	// UpdatePlugin 更新插件信息
	UpdatePlugin(context.Context, *entity.PluginEntity) error

	// UpdateAllPluginPlatform 更新所有的 插件 平台基座
	UpdateAllPluginPlatform(context.Context, *entity.PlatformEntity) error
}

type IPlatform interface {
	// CreatePlatform 创建平台
	CreatePlatform(context.Context, *entity.PlatformEntity) error

	// DeletePlatform 删除平台
	DeletePlatform(context.Context, *entity.PlatformEntity) error

	// UpdatePlatform 更新平台信息
	UpdatePlatform(context.Context, *entity.PlatformEntity) error

	// QueryPlatforms 查询平台列表
	QueryPlatforms(context.Context) ([]*po.PlatformPO, error)
}

type IFile interface {
	// CreateFile 创建文件或目录
	CreateFile(context.Context, *entity.FileEntity) (*po.FilePO, error)

	// DeleteFile 删除文件或目录
	DeleteFile(context.Context, *entity.FileEntity) error

	// UpdateFile 更新文件信息(重命名、移动等)
	UpdateFile(context.Context, *entity.FileEntity) (*po.FilePO, error)

	// QueryFile 查询单个文件信息
	QueryFile(context.Context, *entity.FileEntity) (*po.FilePO, error)

	// QueryFiles 查询文件列表
	QueryFiles(context.Context, *entity.FileEntity) ([]*po.FilePO, error)

	// QueryFilesByPID 根据父级ID查询文件列表
	QueryFilesByPID(context.Context, string) ([]*po.FilePO, error)
}

type IPaintingConfig interface {
	// QueryPaintingConfigs 查询所有绘画配置
	QueryPaintingConfigs(context.Context) ([]*po.PaintingConfigPO, error)

	// QueryPaintingConfigsByCondition 根据条件查询绘画配置
	QueryPaintingConfigsByCondition(context.Context, *entity.PaintingConfigEntity) ([]*po.PaintingConfigPO, error)

	// CreatePaintingConfig 创建绘画配置
	CreatePaintingConfig(context.Context, *entity.PaintingConfigEntity) (*po.PaintingConfigPO, error)

	// UpdatePaintingConfig 更新绘画配置
	UpdatePaintingConfig(context.Context, *entity.PaintingConfigEntity) (*po.PaintingConfigPO, error)

	// DeletePaintingConfig 删除绘画配置
	DeletePaintingConfig(context.Context, *entity.PaintingConfigEntity) error

	// GetPaintingConfig 获取单个绘画配置
	GetPaintingConfig(context.Context, *entity.PaintingConfigEntity) (*po.PaintingConfigPO, error)
}

type IPaintingNegativePrompt interface {
	// QueryPaintingNegativePrompts 查询所有负面提示词
	QueryPaintingNegativePrompts(context.Context) ([]*po.PaintingNegativePromptPO, error)

	// QueryPaintingNegativePromptsByCondition 根据条件查询负面提示词
	QueryPaintingNegativePromptsByCondition(context.Context, *entity.PaintingNegativePromptEntity) ([]*po.PaintingNegativePromptPO, error)

	// CreatePaintingNegativePrompt 创建负面提示词
	CreatePaintingNegativePrompt(context.Context, *entity.PaintingNegativePromptEntity) (*po.PaintingNegativePromptPO, error)

	// UpdatePaintingNegativePrompt 更新负面提示词
	UpdatePaintingNegativePrompt(context.Context, *entity.PaintingNegativePromptEntity) (*po.PaintingNegativePromptPO, error)

	// DeletePaintingNegativePrompt 删除负面提示词
	DeletePaintingNegativePrompt(context.Context, *entity.PaintingNegativePromptEntity) error

	// GetPaintingNegativePrompt 获取单个负面提示词
	GetPaintingNegativePrompt(context.Context, *entity.PaintingNegativePromptEntity) (*po.PaintingNegativePromptPO, error)
}

type IPaintingRecord interface {
	// QueryPaintingRecords 查询所有绘画记录
	QueryPaintingRecords(context.Context) ([]*po.PaintingPO, error)

	// QueryPaintingRecordsByCondition 根据条件查询绘画记录
	QueryPaintingRecordsByCondition(context.Context, *entity.PaintingRecordEntity) ([]*po.PaintingPO, error)

	// CreatePaintingRecord 创建绘画记录
	CreatePaintingRecord(context.Context, *entity.PaintingRecordEntity) (*po.PaintingPO, error)

	// UpdatePaintingRecord 更新绘画记录
	UpdatePaintingRecord(context.Context, *entity.PaintingRecordEntity) (*po.PaintingPO, error)

	// DeletePaintingRecord 删除绘画记录
	DeletePaintingRecord(context.Context, *entity.PaintingRecordEntity) error

	// GetPaintingRecord 获取单个绘画记录
	GetPaintingRecord(context.Context, *entity.PaintingRecordEntity) (*po.PaintingPO, error)
}
