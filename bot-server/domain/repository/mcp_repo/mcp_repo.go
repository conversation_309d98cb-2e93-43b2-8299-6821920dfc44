package mcp_repo

import (
	"bot/domain/entity"
	"bot/infrastructure/repository/ai-bot-common/mcpclien"

	"github.com/mark3labs/mcp-go/mcp"
)

type IMcp interface {
	// Add 添加MCP
	Add(mcp *entity.McpEntity) error

	FindMCPsById(ids []string) []*mcpclien.MCPClient
	// GetAllClients 获取所有MCP客户端
	GetAllClients() []*mcpclien.MCPClient
	// GetById 根据ID获取MCP
	GetById(id string) (*entity.McpEntity, error)

	// GetAll 获取所有MCP
	GetAll() ([]*entity.McpEntity, error)

	// Update 更新MCP
	Update(mcp *entity.McpEntity) error

	// Delete 删除MCP
	Delete(id string) error

	// GetTools 获取mcp 调用工具列表
	GetTools(data *entity.McpEntity) ([]mcp.Tool, error)

	// CallTool 调用指定mcp 的对应工具
	CallTool(data *entity.McpEntity, tool mcpclien.McpCall) *mcp.CallToolResult

	// ClientInfo 获取mcp服务端信息
	ClientInfo(id string) (*mcpclien.MCPClient, error)

	// Reload 重启mcp 客户端
	Reload(id string) error
}
