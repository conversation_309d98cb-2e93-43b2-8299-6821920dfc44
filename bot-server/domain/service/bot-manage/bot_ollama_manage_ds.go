package bot_manage

import (
	"bot/domain/entity"
	"bot/domain/vo"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"

	"github.com/ollama/ollama/api"
)

type OllamaModel struct {
	Models []struct {
		Name string `json:"name"`
		Size int    `json:"size"`
	} `json:"models"`
}

// OllamaManageDomain
// Ollama 专属配置管理
type OllamaManageDomain struct {
	config *entity.PlatformConfig
}

func NewOllamaManageDomain(configEntity *entity.PlatformConfig) *OllamaManageDomain {
	return &OllamaManageDomain{
		config: configEntity,
	}
}

// Models
// 获取 ollama 模型列表
func (manage *OllamaManageDomain) Models(ctx context.Context) (any, error) {
	get, err := http.Get(fmt.Sprintf("%s/api/tags", manage.config.Api))
	if err != nil {
		return nil, err
	}
	defer get.Body.Close()
	if get.StatusCode != http.StatusOK {
		return nil, errors.New(get.Status)
	}
	var data api.ListResponse
	if err = json.NewDecoder(get.Body).Decode(&data); err != nil {
		return nil, err
	}
	models := make([]*entity.ModelEntity, 0)
	for _, model := range data.Models {
		models = append(models, &entity.ModelEntity{
			Name: model.Model,
			Type: vo.ChatModel,
		})
	}
	return models, nil
}
