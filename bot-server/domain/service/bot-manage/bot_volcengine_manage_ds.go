package bot_manage

import (
	"bot/domain/entity"
	"bot/domain/service/bot/sdk/arg"
	"context"
	"encoding/json"
	"errors"
	"net/http"
)

// VolcengineManageDomain
// Volcengine 专属配置管理
type VolcengineManageDomain struct {
	config *entity.PlatformConfig
}

func NewVolcengineManageDomain(configEntity *entity.PlatformConfig) *VolcengineManageDomain {
	return &VolcengineManageDomain{
		config: configEntity,
	}
}

// Models
// 获取 Volcengine 模型列表
func (manage *VolcengineManageDomain) Models(ctx context.Context) (any, error) {
	// 构建请求参数
	param := &arg.ChatRequestParam{
		RequestParam: arg.RequestParam{
			Method: http.MethodGet,
			Url:    "/models",
		},
		PlatformConfig: manage.config,
	}
	req, err := param.Build()
	if err != nil {
		return nil, err
	}
	client := http.Client{}
	response, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer response.Body.Close()
	if response.StatusCode != http.StatusOK {
		return nil, errors.New(response.Status)
	}
	var data any
	if err = json.NewDecoder(response.Body).Decode(&data); err != nil {
		return nil, err
	}
	return data, nil
}
