package bot_manage

import (
	"bot/domain/entity"
	"bot/domain/vo"
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"strings"
)

var modelMap = map[string]struct{}{
	vo.ChatModel:       {},
	vo.Text2ImageModel: {},
	vo.Image2TextModel: {},
	vo.Image2video:     {},
}

type GiteeAIResponse struct {
	Data []struct {
		ID string `json:"id"`
	} `json:"data"`
}

// GiteeAIManageDomain
// GiteeAI 专属配置管理
type GiteeAIManageDomain struct {
	config *entity.PlatformConfig
}

func NewGiteeAIManageDomain(configEntity *entity.PlatformConfig) *GiteeAIManageDomain {
	return &GiteeAIManageDomain{
		config: configEntity,
	}
}

// Models
// 获取 GiteeAI 模型列表
func (manage *GiteeAIManageDomain) Models(ctx context.Context, modelType string) (any, error) {
	if modelType == "" {
		modelType = "all"
	}
	req, err := http.NewRequest(http.MethodGet, "https://ai.gitee.com/v1/models?type="+modelType, nil)
	if err != nil {
		return nil, err
	}
	client := http.Client{}
	response, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer response.Body.Close()
	if response.StatusCode != http.StatusOK {
		return nil, errors.New(response.Status)
	}
	var data GiteeAIResponse
	if err = json.NewDecoder(response.Body).Decode(&data); err != nil {
		return nil, err
	}

	// 解析模型列表
	models := make([]*entity.ModelEntity, 0)
	for _, model := range data.Data {
		modelName := strings.ToLower(model.ID)
		// 封装自定义类型 
		if _, _ok := modelMap[modelType]; _ok {
			models = append(models, &entity.ModelEntity{
				Name: model.ID,
				Type: modelType,
			})
			continue
		}
		// 特殊处理
		if strings.HasPrefix(modelName, "deepseek") || strings.HasPrefix(modelName, "qwq") {
			models = append(models, &entity.ModelEntity{
				Name: model.ID,
				Type: vo.ChatModel,
			})
		}
		if strings.HasPrefix(modelName, "flux") ||
			strings.HasPrefix(modelName, "kolors") ||
			strings.HasPrefix(modelName, "hidream") ||
			strings.HasPrefix(modelName, "stable-diffusion") {
			models = append(models, &entity.ModelEntity{
				Name: model.ID,
				Type: vo.Text2ImageModel,
			})
		}
	}
	return models, nil
}
