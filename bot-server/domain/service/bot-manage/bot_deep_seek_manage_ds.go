package bot_manage

import (
	"bot/domain/entity"
	"bot/domain/service/bot/sdk/arg"
	"bot/domain/vo"
	"context"
	"encoding/json"
	"errors"
	"net/http"
)

type DeepSeekModel struct {
	Data []struct {
		ID string `json:"id"`
	} `json:"data"`
}

// DeepSeekManageDomain
// DeepSeek 专属配置管理
type DeepSeekManageDomain struct {
	config *entity.PlatformConfig
}

func NewDeepSeekManageDomain(configEntity *entity.PlatformConfig) *DeepSeekManageDomain {
	return &DeepSeekManageDomain{
		config: configEntity,
	}
}

// Models
// 获取 DeepSeek 模型列表
func (manage *DeepSeekManageDomain) Models(ctx context.Context) (any, error) {
	// 构建请求参数
	param := &arg.ChatRequest{
		RequestParam: arg.RequestParam{
			Method: http.MethodGet,
			Url:    "/models",
		},
		PlatformConfig: manage.config,
	}
	req, err := param.Build()
	if err != nil {
		return nil, err
	}
	client := http.Client{}
	response, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer response.Body.Close()
	if response.StatusCode != http.StatusOK {
		return nil, errors.New(response.Status)
	}
	var data DeepSeekModel
	if err = json.NewDecoder(response.Body).Decode(&data); err != nil {
		return nil, err
	}
	models := make([]*entity.ModelEntity, 0)
	for _, model := range data.Data {
		models = append(models, &entity.ModelEntity{
			Name: model.ID,
			Type: vo.ChatModel,
		})
	}
	return models, nil
}
