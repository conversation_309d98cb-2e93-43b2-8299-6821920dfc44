package common

type BaseError struct {
	Code int    `json:"code,omitempty"`
	Msg  string `json:"msg,omitempty"`
	Err  error  `json:"err,omitempty"`
}

func (e *BaseError) Error() string {
	return e.Msg
}

func (e *BaseError) GetCode() int {
	return e.Code
}

func NewBaseErrorWithErr(code int, msg string, err error) *BaseError {
	return &BaseError{
		Code: code,
		Msg:  msg,
		Err:  err,
	}
}

func NewBaseError(code int, msg string) *BaseError {
	return &BaseError{
		Code: code,
		Msg:  msg,
	}
}
