# 流解析器优化说明

## 背景

`AgentStreamInstruction`和`StreamParser`用于解析AI助手生成的流式输出，特别是解析其中的工具调用部分（`<tool_call>`标签）。原来的实现在处理单字符流输入时效率较低，因为每次接收到一个字符都会尝试在整个缓冲区中搜索完整的标记，造成不必要的计算开销。

## 优化内容

1. **字符级匹配优化**：
   - 增加了专门处理单字符输入的`FeedChar`方法
   - 实现了增量匹配开始标记的逻辑，避免每次都遍历整个缓冲区
   - 当开始标记完全匹配后才标记进入标记内状态

2. **状态追踪优化**：
   - 新增了`startTagMatchPos`和`startTagMatchCount`字段追踪标记匹配状态
   - 在匹配失败时能够快速重置状态并尝试从当前字符重新开始匹配

3. **处理逻辑优化**：
   - 在`StreamTool.CheckToolCall`中根据输入长度选择合适的处理方法
   - 单字符输入使用优化的`FeedChar`方法
   - 多字符输入保持使用原有的`Feed`方法，保持向后兼容性

4. **返回值结构优化**：
   - 修改了返回值结构，确保能获取到原始输入内容
   - 修改后的方法返回三个值：原始输入、处理后的内容、是否找到工具调用

5. **标签闭合问题修复**：
   - 添加了对结束标签的逐字符匹配逻辑
   - 新增`endTagMatchPos`和`endTagMatchCount`字段追踪结束标签匹配状态
   - 确保只有在完整匹配到结束标签时才标记解析完成
   - 优化了内容提取逻辑，确保能正确处理完整标签

6. **完整标签返回**：
   - 新增了`GetTagWithWrapper`方法，用于获取完整的标签（包括开始和结束标记）
   - 修改了`CheckToolCall`方法，使其在工具调用被识别时返回完整的标签
   - 确保返回的标签中包含完整的结束标记 `</tool_call>`，解决了之前标签闭合不完整的问题

## 测试用例

添加了多种测试场景验证优化效果：
1. 单个标记的解析
2. 混合文本和标记的解析 
3. 嵌套标记的解析
4. 标签闭合测试（确保正确识别完整标签）
5. 标签完整性测试（确保返回的标签包含完整的开始和结束标记）

## 使用方法

### 基本用法

```go
// 创建实例
agentStreamInstruction := NewAgentStreamInstruction()

// 处理流式输入的每个字符
for _, r := range inputStream {
    value := string(r)
    origin, content, isToolCall := agentStreamInstruction.ProcessStreamToolCall(value)
    if isToolCall {
        // 处理工具调用内容
        handleToolCall(content)
        // origin 包含完整的标签，可以直接用于输出或存储
        fmt.Println("完整标签:", origin)
    } else {
        // 处理普通文本
        handleNormalText(content)
    }
}
```

### 示例

完整示例代码可以在 `examples/tool_call_example.go` 中找到：

```go
// 模拟LLM输出的流式内容
llmOutput := "我理解您需要获取当前天气信息。让我为您查询。<tool_call>{\"name\":\"get_weather\",\"arguments\":{\"location\":\"深圳\",\"unit\":\"celsius\"}}</tool_call>根据天气查询结果，今天深圳的天气是晴天，温度28度。"

// 逐字符处理LLM输出
for _, r := range llmOutput {
    char := string(r)
    origin, content, isToolCall := agentStreamInstruction.ProcessStreamToolCall(char)
    
    if isToolCall {
        // 发现了完整的工具调用
        fmt.Println("原始标签:", origin)  // 包含完整的 <tool_call>...</tool_call>
        fmt.Println("内容:", content)    // 只有标签内部的内容
        
        // 将完整标签添加到结果中
        resultText.WriteString(origin)
    } else {
        // 普通文本，直接添加到结果中
        resultText.WriteString(content)
    }
}
```

## 返回值解释

- **origin**: 原始输入的字符串，如果是工具调用则返回完整的标签（包括开始和结束标记）
- **content**: 处理后的内容。如果是工具调用则返回标签中的内容；否则返回原始输入
- **ok/isToolCall**: 布尔值，表示是否解析到完整的工具调用

## 性能提升

通过字符级别的增量匹配，减少了不必要的字符串搜索操作，特别是在长文本流中效率提升明显。优化后的算法在处理单字符流时具有更好的时间复杂度。 