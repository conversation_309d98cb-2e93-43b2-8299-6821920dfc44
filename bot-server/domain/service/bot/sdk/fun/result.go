package fun

import "context"

type Func<PERSON>allingH<PERSON>le func(ctx context.Context, value string) CallingResult

type CallingResult interface {
	HasToolCalls() bool
	GetToolCalls() []ToolCall
}

type ToolCall struct {
	ID       string       `json:"id"`
	Type     string       `json:"type"`
	Function FunctionCall `json:"function"`
}

// FunctionCall 表示函数调用
type FunctionCall struct {
	Name      string `json:"name"`
	Arguments string `json:"arguments"`
}
