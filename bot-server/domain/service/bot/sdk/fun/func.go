package fun

import (
	"context"
	"encoding/json"
	"reflect"
)

type FuncCalling map[string]reflect.Value
type FuncDefinition map[string]FunctionSpec

// FunctionProperty 定义函数参数的属性
type FunctionProperty struct {
	Type        string `json:"type"`
	Description string `json:"description"`
}

// FunctionParameters 定义函数的参数结构
type FunctionParameters struct {
	Type       string                      `json:"type"`
	Properties map[string]FunctionProperty `json:"properties"`
	Required   []string                    `json:"required"`
}

func (receiver *FunctionParameters) GetRequired(ctx context.Context, value string) []reflect.Value {
	var list []reflect.Value
	var data map[string]any
	list = append(list, reflect.ValueOf(ctx))
	err := json.Unmarshal([]byte(value), &data)
	if err != nil {
		return list
	}
	for _, name := range receiver.Required {
		if v, ok := data[name]; ok {
			list = append(list, reflect.ValueOf(v))
		}
	}
	return list
}

// FunctionDefinition 定义函数的基本信息
type FunctionDefinition struct {
	Name        string             `json:"name"`
	Description string             `json:"description"`
	Parameters  FunctionParameters `json:"parameters"`
}

// FunctionSpec 定义完整的函数规范
type FunctionSpec struct {
	Type     string             `json:"type"`
	Function FunctionDefinition `json:"function"`
}

// GetCurrentWeather 根据给定的城市和经纬度获取当前天气情况

// WeatherFunctionSpec 返回天气查询功能的函数定义
func WeatherFunctionSpec() FunctionSpec {
	return FunctionSpec{
		Type: "function",
		Function: FunctionDefinition{
			Name:        "get_current_weather",
			Description: "根据经纬度城市名，获取指定地点的天气情况",
			Parameters: FunctionParameters{
				Type: "object",
				Properties: map[string]FunctionProperty{
					"city": {
						Type:        "string",
						Description: "根据用户提到的地点推测城市",
					},
					"x": {
						Type:        "float",
						Description: "城市的经度，根据你的知识推测",
					},
					"y": {
						Type:        "float",
						Description: "城市的纬度，根据你的知识推测",
					},
				},
				Required: []string{"city", "x", "y"},
			},
		},
	}
}

// MCPToolsResponse 定义从MCP获取的工具列表响应结构
type MCPToolsResponse struct {
	Jsonrpc string `json:"jsonrpc"`
	ID      int    `json:"id"`
	Result  struct {
		Tools []struct {
			Description string          `json:"description"`
			InputSchema json.RawMessage `json:"inputSchema"`
			Name        string          `json:"name"`
		} `json:"tools"`
	} `json:"result"`
}

// ConvertMCPToolsResponseToFuncDefinition 将MCP返回的工具列表响应转换为函数定义
func ConvertMCPToolsResponseToFuncDefinition(jsonData string) (FuncDefinition, error) {
	var response MCPToolsResponse
	err := json.Unmarshal([]byte(jsonData), &response)
	if err != nil {
		return nil, err
	}

	funcDef := make(FuncDefinition)

	for _, tool := range response.Result.Tools {
		// 解析inputSchema为参数结构
		var inputSchema struct {
			Type       string                      `json:"type"`
			Properties map[string]FunctionProperty `json:"properties"`
			Required   []string                    `json:"required"`
		}

		err = json.Unmarshal(tool.InputSchema, &inputSchema)
		if err != nil {
			// 如果解析失败，使用默认空对象
			inputSchema.Type = "object"
			inputSchema.Properties = make(map[string]FunctionProperty)
			inputSchema.Required = []string{}
		}

		// 创建函数定义
		funcDef[tool.Name] = FunctionSpec{
			Type: "function",
			Function: FunctionDefinition{
				Name:        tool.Name,
				Description: tool.Description,
				Parameters: FunctionParameters{
					Type:       inputSchema.Type,
					Properties: inputSchema.Properties,
					Required:   inputSchema.Required, // 使用解析得到的必需参数列表
				},
			},
		}
	}

	return funcDef, nil
}

// ConvertMCPToolsResponseToFunctionDefinitionList 将MCP返回的工具列表响应转换为函数定义列表
func ConvertMCPToolsResponseToFunctionDefinitionList(jsonData string) ([]FunctionDefinition, error) {
	var response MCPToolsResponse
	err := json.Unmarshal([]byte(jsonData), &response)
	if err != nil {
		return nil, err
	}

	funcDefList := make([]FunctionDefinition, 0, len(response.Result.Tools))

	for _, tool := range response.Result.Tools {
		// 解析inputSchema为参数结构
		var inputSchema struct {
			Type       string                      `json:"type"`
			Properties map[string]FunctionProperty `json:"properties"`
			Required   []string                    `json:"required"`
		}

		err = json.Unmarshal(tool.InputSchema, &inputSchema)
		if err != nil {
			// 如果解析失败，使用默认空对象
			inputSchema.Type = "object"
			inputSchema.Properties = make(map[string]FunctionProperty)
			inputSchema.Required = []string{}
		}

		// 创建函数定义并添加到列表
		funcDef := FunctionDefinition{
			Name:        tool.Name,
			Description: tool.Description,
			Parameters: FunctionParameters{
				Type:       inputSchema.Type,
				Properties: inputSchema.Properties,
				Required:   inputSchema.Required,
			},
		}

		funcDefList = append(funcDefList, funcDef)
	}

	return funcDefList, nil
}

// ConvertMCPToolsResponseBoth 将MCP返回的工具列表响应同时转换为函数定义映射和列表
func ConvertMCPToolsResponseBoth(jsonData string) (FuncDefinition, []FunctionDefinition, error) {
	var response MCPToolsResponse
	err := json.Unmarshal([]byte(jsonData), &response)
	if err != nil {
		return nil, nil, err
	}

	// 创建返回结果
	funcDef := make(FuncDefinition)
	funcDefList := make([]FunctionDefinition, 0, len(response.Result.Tools))

	for _, tool := range response.Result.Tools {
		// 解析inputSchema为参数结构
		var inputSchema struct {
			Type       string                      `json:"type"`
			Properties map[string]FunctionProperty `json:"properties"`
			Required   []string                    `json:"required"`
		}

		err = json.Unmarshal(tool.InputSchema, &inputSchema)
		if err != nil {
			// 如果解析失败，使用默认空对象
			inputSchema.Type = "object"
			inputSchema.Properties = make(map[string]FunctionProperty)
			inputSchema.Required = []string{}
		}

		// 创建FunctionDefinition对象
		funcDefinition := FunctionDefinition{
			Name:        tool.Name,
			Description: tool.Description,
			Parameters: FunctionParameters{
				Type:       inputSchema.Type,
				Properties: inputSchema.Properties,
				Required:   inputSchema.Required,
			},
		}

		// 添加到列表
		funcDefList = append(funcDefList, funcDefinition)

		// 添加到映射
		funcDef[tool.Name] = FunctionSpec{
			Type:     "function",
			Function: funcDefinition,
		}
	}

	return funcDef, funcDefList, nil
}
