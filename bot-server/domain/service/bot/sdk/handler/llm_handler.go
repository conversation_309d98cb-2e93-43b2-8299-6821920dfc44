package handler

import (
	"bot/domain/entity"
	botcontext "bot/domain/service/bot-context"
	"bot/domain/service/bot/sdk"
	"bot/domain/service/bot/sdk/arg"
	"bot/domain/service/bot/sdk/fun"
	"bot/domain/service/bot/sdk/sdkutil"
	"bot/domain/service/bot/sdk/sse"
	"bot/infrastructure/po"
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"reflect"
	"strings"
	"sync"
	"time"

	jsoniter "github.com/json-iterator/go"
)

var pool *sync.Pool

func init() {
	pool = &sync.Pool{}
	pool.New = func() interface{} {
		return &http.Client{
			Timeout: time.Hour,
		}
	}
}

type LLMHandler struct {
	writer  chan string
	w       http.ResponseWriter
	flusher http.Flusher
	*botcontext.Context
}

func NewLLMHandler(ctx *botcontext.Context) *LLMHandler {
	return &LLMHandler{
		writer:  make(chan string, 10000),
		w:       ctx.ResponseWriter,
		Context: ctx,
		flusher: ctx.ResponseWriter.(http.Flusher),
	}
}

func (receiver *LLMHandler) DoRequest(request *http.Request) (resp *http.Response, err error) {
	client := pool.Get().(*http.Client)
	defer pool.Put(client)
	response, err := client.Do(request)
	if err != nil {
		return nil, err
	}
	return response, nil
}

func (receiver *LLMHandler) Close() {
	close(receiver.writer)
}

func (receiver *LLMHandler) Reply(ctx context.Context, message string, chat *entity.ChatEntity) *po.MessagePO {
	messagePO := po.NewChatToMessagePO(chat)
	messagePO.Content = message
	buffer := bytes.Buffer{}
	for _, s := range []rune(message) {
		llmData := sdk.NewLLMContent(sse.Data, string(s), nil)
		buffer.Write(llmData.Byte())
		if buffer.Len() >= 1024 { // Flush every 1KB
			if err := receiver.Writer(buffer.String()); err != nil {
				messagePO.SetError(fmt.Errorf("failed to write message: %w", err))
				return messagePO
			}
			buffer.Reset()
		}
	}

	// Flush any remaining content
	if buffer.Len() > 0 {
		if err := receiver.Writer(buffer.String()); err != nil {
			messagePO.SetError(fmt.Errorf("failed to write message: %w", err))
			return messagePO
		}
	}

	llmData := sdk.NewLLMContent(sse.Finish, "", nil)
	receiver.Writer(llmData.Byte())
	return messagePO
}

// FuncCalling
// 递归调用 函数，最终获取到所有调用结果
func (receiver *LLMHandler) FuncCalling(ctx context.Context, param arg.IParam, handler fun.FuncCallingHandle) ([]string, string) {
	var list []string
	var value, save string
	var ok bool
	param.SetStream(false)
	if value, save, ok = receiver.funCalling(ctx, param, handler); ok {
		if value != "" {
			list = append(list, value)
		}
		param.Prompt(value)
		if funcCalling, savemsg := receiver.FuncCalling(ctx, param, handler); len(funcCalling) > 0 {
			list = append(list, funcCalling...)
			save += savemsg
		}
	}
	return list, save
}

// funCalling
// 执行函数调用操作，返回一个调用结果，和一个 是否继续调用的 bool
// 如果 返回 继续调用 ，则继续封装 上一次调用结果，作为上下文关系 继续请求函数调用
func (receiver *LLMHandler) funCalling(ctx context.Context, param arg.IParam, handler fun.FuncCallingHandle) (string, string, bool) {
	isCall := false
	request, err := param.Build()
	if err != nil {
		return "", "", false
	}
	response, err := receiver.DoRequest(request)
	if err != nil {
		return "", "", false
	}

	defer response.Body.Close()
	body, err := io.ReadAll(response.Body)
	if err != nil {
		return "", "", false
	}

	var data fun.CallingResult
	if data = handler(ctx, string(body)); data == nil {
		return "", "", false
	}
	builder := strings.Builder{}
	/*	if isCall = data.HasToolCalls(); isCall {
		// 执行函数调用逻辑
		calls := data.GetToolCalls()
		valueError := reflect.TypeOf(new(error)).Elem()
		for _, call := range calls {
			if callback, ok := receiver.Calling[call.Function.Name]; ok {
				functionSpec := receiver.CallingDefinition[call.Function.Name]
				required := functionSpec.Function.Parameters.GetRequired(ctx, call.Function.Arguments)
				values := callback.Call(required)
				for _, value := range values {
					switch value.Kind() {
					case reflect.Interface:
						if value.Type().Implements(valueError) {
							// 执行报错提示
							if !value.IsNil() {
								funErr := fmt.Errorf("func %s callback: %s", call.Function.Name, value.Interface().(error).Error())
								err = errors.Join(funErr)
								continue
							}
						}
						v := value.Interface()
						switch callValue := v.(type) {
						case string:
							builder.WriteString(strings.TrimSpace(callValue) + "\r\n")
						default:
						}
					default:
					}
				}
			}
		}
	}*/
	save := strings.Builder{}
	if isCall = data.HasToolCalls(); isCall {
		calls := data.GetToolCalls()
		for _, call := range calls {
			funcContentStart := sdk.NewLLMContent(sse.Data, "\n\r"+":::function"+"\n\r", nil)
			save.WriteString("\n\r" + ":::function" + "\n\r")
			receiver.Writer(funcContentStart)

			var callErr error
			callValue := ""
			out := map[string]any{}
			out["name"] = call.Function.Name
			if callValue, callErr = receiver.LocationFuncCalling(ctx, call); callErr != nil {
				callValue = callErr.Error()
			}
			builder.WriteString(callValue)

			out["value"] = callValue

			funcValue, _ := json.Marshal(out)
			funcContent := sdk.NewLLMContent(sse.Data, string(funcValue), nil)
			save.WriteString(string(funcValue))
			receiver.Writer(funcContent)

			funcContentEnd := sdk.NewLLMContent(sse.Data, "\n\r"+":::"+"\n\r", nil)
			save.WriteString("\n\r" + ":::" + "\n\r")
			receiver.Writer(funcContentEnd)
		}
	}

	return builder.String(), save.String(), isCall
}

func (receiver *LLMHandler) FuncCall(ctx context.Context, calls []fun.ToolCall) (string, error) {
	var errs error
	builder := strings.Builder{}

	for _, call := range calls {
		var result string
		var err error
		if result, err = receiver.LocationFuncCalling(ctx, call); err != nil {
			errs = errors.Join(err)
		}
		builder.WriteString(result + "\r\n")
	}
	return builder.String(), errs
}

func (receiver *LLMHandler) LocationFuncCalling(ctx context.Context, call fun.ToolCall) (string, error) {
	var err error
	valueError := reflect.TypeOf(new(error)).Elem()
	if callback, ok := receiver.Calling[call.Function.Name]; ok {
		functionSpec := receiver.CallingDefinition[call.Function.Name]
		required := functionSpec.Function.Parameters.GetRequired(ctx, call.Function.Arguments)
		values := callback.Call(required)
		for _, value := range values {
			switch value.Kind() {
			case reflect.Interface:
				if value.Type().Implements(valueError) {
					// 执行报错提示
					if !value.IsNil() {
						funErr := fmt.Errorf("func %s callback: %s", call.Function.Name, value.Interface().(error).Error())
						err = errors.Join(funErr)
						continue
					}
				}
				v := value.Interface()
				switch callValue := v.(type) {
				case string:
					return callValue, err
				default:
				}
			default:
			}
		}
	}
	return "", err
}

// Complete
// 最原始的聊天完成
func (receiver *LLMHandler) Complete(ctx context.Context, response *http.Response) sse.Reader {
	return func(write chan<- string) {
		defer close(write)
		var err error
		var buf []byte
		if response == nil {
			return
		}
		defer response.Body.Close()
		// 使用 bufio.NewReader 创建一个读取器，方便按行读取
		scanner := bufio.NewScanner(response.Body)
		for scanner.Scan() {
			buf = scanner.Bytes()
			err = scanner.Err()
			if err == io.EOF {
				return
			} else if errors.Is(err, io.ErrUnexpectedEOF) || errors.Is(err, io.ErrClosedPipe) {
				return
			} else if err != nil {
				return
			}
			jsonData := sdkutil.ExtractJSON(buf)
			write <- string(jsonData)
		}
	}
}

// SyncMCPComplete
// @Return recover LLM 返回的消息
func (receiver *LLMHandler) SyncMCPComplete(ctx context.Context, param arg.IParam) (recover string, err error) {
	request, err := param.Build()
	if err != nil {
		return "", err
	}
	response, err := receiver.DoRequest(request)
	if err != nil {
		return "", err
	}
	defer response.Body.Close()
	body, err := io.ReadAll(response.Body)
	if err != nil {
		return "", err
	}
	jsonData := sdkutil.ExtractJSON(body)
	recover = string(jsonData)
	return
}

// ErrorHandle
// 处理流数据中的 error
// 数据中有错误数据下发 到客户端 成功处理后返回 true 表示有错误处理了，就不需要进行后面的处理逻辑
func (receiver *LLMHandler) ErrorHandle(data *sdk.LLMData) bool {
	if data.Err != nil {
		buf := data.Byte()
		sprintf := fmt.Sprintf("%s%s", buf, sse.Segmentation)
		_ = receiver.Writer(sprintf)
		return true
	}
	return false
}

func (receiver *LLMHandler) Reader(reader sse.Reader) <-chan string {
	go reader(receiver.writer)
	return receiver.writer
}

func (receiver *LLMHandler) Writer(data any) error {
	var buf []byte
	var err error
	defer receiver.flusher.Flush()
	switch v := data.(type) {
	case string:
		if _, err = receiver.w.Write([]byte(v + sse.Segmentation)); err != nil {
			return err
		}
	default:
		if buf, err = jsoniter.Marshal(v); err != nil {
			return err
		}
		buffer := bytes.NewBuffer(buf)
		buffer.WriteString(sse.Segmentation)
		if _, err = buffer.WriteTo(receiver.w); err != nil {
			return err
		}
	}
	return nil
}

// SSEErrorResponse
// 下发流失错误消息，发送一个完整的错响应
func (receiver *LLMHandler) SSEErrorResponse(value any) error {
	var err error
	data := sdk.NewErrorLLMData(value)
	err = receiver.Writer(data)
	if err != nil {
		return err
	}
	finishLLMData := sdk.NewFinishLLMData()
	err = receiver.Writer(finishLLMData)
	if err != nil {
		return err
	}
	return nil
}

// StreamIter
// 获取消息读取迭代器
func (receiver *LLMHandler) StreamIter(reader sse.Reader) chan string {
	go reader(receiver.writer)
	return receiver.writer
}
