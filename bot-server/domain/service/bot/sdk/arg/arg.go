package arg

import (
	"bot/domain/entity"
	"bot/infrastructure/common/errs"
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"
)

// 签名相关的辅助函数
func hmacSHA256(key []byte, content string) []byte {
	mac := hmac.New(sha256.New, key)
	mac.Write([]byte(content))
	return mac.Sum(nil)
}

func getSignedKey(secretKey, date, region, service string) []byte {
	kDate := hmacSHA256([]byte(secretKey), date)
	kRegion := hmacSHA256(kDate, region)
	kService := hmacSHA256(kRegion, service)
	kSigning := hmacSHA256(kService, "request")
	return kSigning
}

func hashSHA256(data []byte) []byte {
	hash := sha256.New()
	hash.Write(data)
	return hash.Sum(nil)
}

type ChatRequestParam struct {
	RequestParam
	*entity.PlatformConfig
}

func (param *ChatRequestParam) Clone() IParam {
	return nil
}

func (param *ChatRequestParam) Build() (*http.Request, error) {
	if param.PlatformConfig == nil {
		return nil, errs.ParamBuildError
	}
	if param.AccessKeyID == "" || param.SecretAccessKey == "" {
		return nil, errs.ParamBuildError
	}
	if param.Api == "" {
		return nil, errs.ParamBuildError
	}

	// 构建查询参数
	urlValues := &url.Values{}
	for key, value := range param.Params {
		urlValues.Add(key, value)
	}

	u, err := url.Parse(param.Api)
	if err != nil {
		return nil, errs.ParamBuildError
	}

	path := param.Url
	u.Path = path

	// 创建请求
	req, err := http.NewRequest(param.Method, u.String(), bytes.NewBuffer(param.Body()))
	if err != nil {
		return nil, errs.ParamBuildError
	}

	// 添加签名
	now := time.Now()
	date := now.UTC().Format("20060102T150405Z")
	authDate := date[:8]
	req.Header.Set("X-Date", date)

	// 计算 payload
	payload := hex.EncodeToString(hashSHA256(param.Body()))
	req.Header.Set("X-Content-Sha256", payload)
	req.Header.Set("Content-Type", "application/json")

	// 构建签名字符串
	queryString := strings.Replace(urlValues.Encode(), "+", "%20", -1)
	signedHeaders := []string{"host", "x-date", "x-content-sha256", "content-type"}
	var headerList []string
	for _, header := range signedHeaders {
		if header == "host" {
			headerList = append(headerList, header+":"+req.Host)
		} else {
			v := req.Header.Get(header)
			headerList = append(headerList, header+":"+strings.TrimSpace(v))
		}
	}
	headerString := strings.Join(headerList, "\n")

	canonicalString := strings.Join([]string{
		param.Method,
		path,
		queryString,
		headerString + "\n",
		strings.Join(signedHeaders, ";"),
		payload,
	}, "\n")

	hashedCanonicalString := hex.EncodeToString(hashSHA256([]byte(canonicalString)))

	region := param.Region
	if region == "" {
		region = "cn-beijing"
	}

	credentialScope := fmt.Sprintf("%s/%s/%s/request", authDate, region, "iam")
	signString := strings.Join([]string{
		"HMAC-SHA256",
		date,
		credentialScope,
		hashedCanonicalString,
	}, "\n")

	// 计算签名
	signedKey := getSignedKey(param.SecretAccessKey, authDate, region, "iam")
	signature := hex.EncodeToString(hmacSHA256(signedKey, signString))

	// 构建认证头
	authorization := fmt.Sprintf("HMAC-SHA256 Credential=%s/%s, SignedHeaders=%s, Signature=%s",
		param.AccessKeyID,
		credentialScope,
		strings.Join(signedHeaders, ";"),
		signature)

	req.Header.Set("Authorization", authorization)

	return req, nil
}

func (param *ChatRequestParam) String() string {
	if param == nil {
		return "{}"
	}
	baseStr := param.RequestParam.String()
	// 移除最后的 }
	baseStr = baseStr[:len(baseStr)-1]

	configInfo := "nil"
	if param.PlatformConfig != nil {
		configInfo = "{\"api\":\"" + param.Api + "\",\"key\":\"***\"}"
	}
	return baseStr + ",\"config\":" + configInfo + "}"
}
