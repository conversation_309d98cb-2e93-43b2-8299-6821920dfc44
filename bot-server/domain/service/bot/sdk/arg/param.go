package arg

import (
	"bot/domain/entity"
	"bot/infrastructure/common/errs"
	"bytes"
	"fmt"
	"net/http"
	"net/url"
)

type ChatRequest struct {
	RequestParam
	*entity.PlatformConfig
}

// NewChatRequest
// 初始化 请求
func NewChatRequest(config *entity.PlatformConfig) *ChatRequest {
	return &ChatRequest{
		RequestParam: RequestParam{
			Method: http.MethodPost,
			LLMReq: LLMReq{
				LLMPublicArgs: LLMPublicArgs{
					Model:  config.CurrentModel,
					Stream: true, // 默认启用流式响应
				},
			},
		},
		PlatformConfig: config,
	}
}

func (receiver *ChatRequest) Build() (*http.Request, error) {
	urlValues := &url.Values{}
	for key, value := range receiver.Params {
		urlValues.Add(key, value)
	}

	u, err := url.Parse(receiver.PlatformConfig.Api)
	if err != nil {
		return nil, errs.ParamBuildError
	}

	u.Path = receiver.Url
	u.RawQuery = urlValues.Encode()

	body := receiver.Body()
	fmt.Println(string(body))
	req, err := http.NewRequest(receiver.Method, u.String(), bytes.NewBuffer(body))
	if err != nil {
		return nil, errs.ParamBuildError
	}

	req.Header.Set("Authorization", "Bearer "+receiver.PlatformConfig.Key)
	if receiver.Method == http.MethodPost {
		req.Header.Set("Content-Type", "application/json")
		req.Header.Add("Accept", "application/json")
	}
	return req, nil
}

func (receiver *ChatRequest) Clone() IParam {
	// 创建新的实例，浅拷贝基本字段
	clone := &ChatRequest{
		RequestParam:   receiver.RequestParam,
		PlatformConfig: receiver.PlatformConfig,
	}

	// 只对消息进行深度克隆
	if receiver.Messages != nil {
		if messages, ok := receiver.Messages.([]*entity.HistoryEntity); ok {
			cloneMessages := make([]*entity.HistoryEntity, len(messages))
			for i, msg := range messages {
				if msg != nil {
					cloneMsg := &entity.HistoryEntity{
						Role:    msg.Role,
						Content: msg.Content,
					}
					cloneMessages[i] = cloneMsg
				}
			}
			clone.Messages = cloneMessages
			clone.History = cloneMessages
		}
	}

	return clone
}

func (receiver *ChatRequest) String() string {
	if receiver == nil {
		return "{}"
	}
	baseStr := receiver.RequestParam.String()
	// 移除最后的 }
	baseStr = baseStr[:len(baseStr)-1]

	configInfo := "nil"
	if receiver.PlatformConfig != nil {
		configInfo = "{\"api\":\"" + receiver.PlatformConfig.Api + "\",\"key\":\"***\"}"
	}
	return baseStr + ",\"config\":" + configInfo + "}"
}
