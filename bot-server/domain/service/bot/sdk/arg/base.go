package arg

import (
	"bot/domain/entity"
	"bot/domain/vo"
	"encoding/json"
	"net/http"
	"time"
)

const (
	DefaultTimeout = 30 * time.Second // 默认超时时间
)

type LLMData string

func (receiver LLMData) name() {

}

type LLMMessage = []*entity.HistoryEntity

type LLMPublicArgs struct {
	// 消息 历史数据
	Messages any `json:"messages"`
	// 调用模型
	Model string `json:"model"`
	// 流开关
	Stream bool `json:"stream"`
	// 工具调用 列表
	Tools     any   `json:"tools,omitempty"`
	TopP      any   `json:"top_p,omitempty"`
	MaxTokens int64 `json:"max_tokens"`
}

type LLMReq struct {
	LLMPublicArgs
	// 业务 历史消息
	History LLMMessage `json:"-"`
}

// Prompt
// 添加一个系统提示词
// 在最后一个用户消息之前插入数据
func (receiver *LLMReq) Prompt(value string) {
	if value == "" {
		return
	}
	var list LLMMessage
	if len(receiver.History) == 0 {
		receiver.History = append(receiver.History, &entity.HistoryEntity{Role: vo.SystemRoleType,
			Content: value,
		})
	}

	// 复制 最后一条消息之前的数据
	list = append(list, receiver.History[0:len(receiver.History)-1]...)
	// 添加 插入的消息
	list = append(list, &entity.HistoryEntity{Role: vo.SystemRoleType, Content: value})
	// 整合 所有消息
	receiver.History = append(list, receiver.History[len(receiver.History)-1])
	receiver.Messages = receiver.History
}

func (receiver *LLMReq) AddUserMessage(value string) {
	receiver.History = append(receiver.History, &entity.HistoryEntity{Role: vo.UserRoleType, Content: value})
	receiver.Messages = receiver.History
}

func (receiver *LLMReq) SetTools(tools any) {
	receiver.Tools = tools
	receiver.Model = "Qwen2.5-72B-Instruct"
}

// SetStream 设置是否使用流式响应
func (receiver *LLMReq) SetStream(stream bool) {
	receiver.Stream = stream
}

// SetMessage 设置消息
func (receiver *LLMReq) SetMessage(History LLMMessage) {
	receiver.Messages = History
	receiver.History = History
}

// GetMessage
// 获取消息
func (receiver *LLMReq) GetMessage() LLMMessage {
	return receiver.History
}

// IParam
// 模型请求参数接口
// 获取会逐渐统一到此接口
type IParam interface {
	// Build 构建最终请求
	Build() (*http.Request, error)
	// Body 获取请求体
	Body() []byte
	// SetStream 设置请求方式
	SetStream(bool)
	// SetMessage 设置请求 历史
	SetMessage(LLMMessage)

	// SetTools 设置函数调用工具
	SetTools(tools any)

	// Prompt 向消息中添加一个系统提示词
	Prompt(value string)
	AddUserMessage(value string)
	// GetMessage 获取请求历史
	GetMessage() LLMMessage
	Clone() IParam
}

// RequestParam 基础请求参数
type RequestParam struct {
	// 请求类型
	Method string
	// 请求 接口路径
	Url    string
	Params map[string]string
	LLMReq
}

func (param *RequestParam) Body() []byte {
	marshal, err := json.Marshal(param.LLMReq)
	if err != nil {
		return nil
	}
	return marshal
}

func (param *RequestParam) String() string {
	if param == nil {
		return "{}"
	}
	bodyStr := "null"
	body := param.Body()
	if len(body) > 0 {
		bodyStr = string(body)
	}
	paramsStr := "null"
	if len(param.Params) > 0 {
		paramsStr = "{"
		first := true
		for k, v := range param.Params {
			if !first {
				paramsStr += ","
			}
			paramsStr += "\"" + k + "\":\"" + v + "\""
			first = false
		}
		paramsStr += "}"
	}
	return "{\"url\":\"" + param.Url + "\",\"method\":\"" + param.Method + "\",\"params\":" + paramsStr + ",\"body\":" + bodyStr + "}"
}
