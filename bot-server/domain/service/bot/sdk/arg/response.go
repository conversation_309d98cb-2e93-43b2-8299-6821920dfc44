package arg

import "bot/domain/service/bot/sdk/fun"

type Message struct {
	Content          string         `json:"content"`
	ReasoningContent string         `json:"reasoning_content"` // 适配 giteeai deepseek 的思考模式
	Role             string         `json:"role"`
	ToolCalls        []fun.ToolCall `json:"tool_calls,omitempty"`
}

type ChatCompletion struct {
	ID      string                      `json:"id"`
	Object  string                      `json:"object"`
	Created int64                       `json:"created"`
	Choices []ChatCompletionChunkChoice `json:"choices"`
}

type FuncCompletion struct {
	ID      string                      `json:"id"`
	Object  string                      `json:"object"`
	Created int64                       `json:"created"`
	Choices []FuncCompletionChunkChoice `json:"choices"`
}

// ToolCall 表示工具调用
type ToolCall struct {
	ID       string       `json:"id"`
	Type     string       `json:"type"`
	Function FunctionCall `json:"function"`
}

// FunctionCall 表示函数调用
type FunctionCall struct {
	Name      string `json:"name"`
	Arguments string `json:"arguments"`
}

// ChatCompletionChunkChoice 表示聊天完成块的选择
type ChatCompletionChunkChoice struct {
	Index        int     `json:"index"`
	Message      Message `json:"delta"`
	MessageV     Message `json:"message"`
	LogProbs     *string `json:"logprobs"`
	FinishReason *string `json:"finish_reason"`
}

// GetContent 获取响应消息的具体内容
func (c *ChatCompletion) GetContent() string {
	if len(c.Choices) == 0 {
		return ""
	}
	if c.Choices[0].Message.Content != "" {
		return c.Choices[0].Message.Content
	}
	if c.Choices[0].Message.ReasoningContent != "" {
		return c.Choices[0].Message.ReasoningContent
	}
	if c.Choices[0].MessageV.Content != "" {
		return c.Choices[0].MessageV.Content
	}
	return ""
}

func (c *ChatCompletion) GetMcpContent() string {
	if len(c.Choices) == 0 {
		return ""
	}
	// 优先使用 messageV 的 content
	if c.Choices[0].MessageV.Content != "" {
		return c.Choices[0].MessageV.Content
	}
	// 其次使用 message 的 content
	if c.Choices[0].Message.Content != "" {
		return c.Choices[0].Message.Content
	}
	return ""
}
func (c *ChatCompletion) GetMcpThinking() string {
	if len(c.Choices) == 0 {
		return ""
	}
	return c.Choices[0].MessageV.ReasoningContent
}

// IsFinished 判断消息是否已经结束
func (c *ChatCompletion) IsFinished() bool {
	if len(c.Choices) == 0 {
		return false
	}
	return c.Choices[0].FinishReason != nil && *c.Choices[0].FinishReason == "stop"
}

func (c *ChatCompletion) IsThinking() bool {
	if len(c.Choices) == 0 {
		return false
	}
	if (*c).Choices[0].Message.Content == (*c).Choices[0].Message.ReasoningContent {
		return true
	}
	return (*c).Choices[0].Message.ReasoningContent != ""
}

func (c *ChatCompletion) IsEmpty() bool {
	if len(c.Choices) == 0 {
		return true
	}
	return c.Choices[0].Message.Content == "" && c.Choices[0].Message.ReasoningContent == ""
}

type FuncCompletionChunkChoice struct {
	Index        int     `json:"index"`
	Message      Message `json:"message"`
	LogProbs     *string `json:"logprobs"`
	FinishReason *string `json:"finish_reason"`
}

// IsToolCallsFinished 判断是否为函数调用结束
func (c *FuncCompletion) IsToolCallsFinished() bool {
	if len(c.Choices) == 0 {
		return false
	}
	// 检查 FinishReason 是否为 tool_calls，并且 ToolCalls 不为空
	return c.Choices[0].FinishReason != nil &&
		*c.Choices[0].FinishReason == "tool_calls" &&
		len(c.Choices[0].Message.ToolCalls) > 0
}

// HasToolCalls 判断是否有需要调用的函数
func (c *FuncCompletion) HasToolCalls() bool {
	if len(c.Choices) == 0 {
		return false
	}
	return len(c.Choices[0].Message.ToolCalls) > 0
}

// GetToolCalls 获取工具调用信息
func (c *FuncCompletion) GetToolCalls() []fun.ToolCall {
	if len(c.Choices) == 0 {
		return nil
	}
	return c.Choices[0].Message.ToolCalls
}
