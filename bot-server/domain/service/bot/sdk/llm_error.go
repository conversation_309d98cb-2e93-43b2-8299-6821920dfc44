package sdk

import "bot/domain/service/bot/common"

var (
	LLMRequestError = newSdkError(10001, "LLM 请求错误")
	// LLMTransmissionError LLM 传输错误
	LLMTransmissionError = newSdkError(10002, "LLM 传输错误")
	// LLMDataError LLM 原始数据错误
	LLMDataError = newSdkError(10003, "LLM 原始数据错误")

	// GiteeAIRequestError GiteeAI 请求错误 2开头 gitee ai 请求错误
	GiteeAIRequestError    = newSdkError(20001, "GiteeAI 请求错误")
	DeepSeekRequestError   = newSdkError(30001, "DeepSeek 请求错误")
	VolcengineRequestError = newSdkError(40001, "Volcengine 请求错误")

	// SSEDisconnectError SSE 断开连接
	SSEDisconnectError = newSdkError(40001, "停止回复")
)

type llmError struct {
	base *common.BaseError
}

func (e *llmError) Error() string {
	return e.base.Msg
}

func (e *llmError) SetError(err error) *llmError {
	e.base.Err = err
	return e
}

func (e *llmError) Instance() *LLMError {
	return e.clone()
}

func (e *llmError) clone() *LLMError {
	return &LLMError{
		BaseError: common.NewBaseErrorWithErr(e.base.Code, e.base.Msg, e.base.Err),
	}
}

type LLMError struct {
	*common.BaseError
}

func (e *LLMError) SetError(err error) *LLMError {
	if err == nil {
		return e
	}
	e.BaseError.Err = err
	e.BaseError.Msg = err.Error()
	return e
}

func newSdkError(code int, msg string) *llmError {
	return &llmError{
		base: common.NewBaseError(code, msg),
	}
}

func NewSdkByError(code int, msg string, err error) *LLMError {
	return &LLMError{
		BaseError: common.NewBaseErrorWithErr(code, msg, err),
	}
}
