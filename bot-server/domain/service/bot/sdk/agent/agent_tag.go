package agent

import (
	"strings"
)

type AgentTag struct {
	TagType  string
	StartTag string
	EndTag   string
}

func NewAgentTag(tagType string) *AgentTag {
	if strings.HasPrefix(tagType, "<") && strings.HasSuffix(tagType, ">") {
		tagType = strings.Trim(tagType, "<>")
	}
	return &AgentTag{
		TagType:  tagType,
		StartTag: "<" + tagType + ">",
		EndTag:   "</" + tagType + ">",
	}
}

type AgentTagHandler struct {
	buffer     strings.Builder
	currentTag *AgentTag
	tag        []*AgentTag
}

func NewAgentTagHandler(tagType ...string) *AgentTagHandler {
	tags := make([]*AgentTag, 0)
	for _, t := range tagType {
		tags = append(tags, NewAgentTag(t))
	}
	return &AgentTagHandler{
		buffer: strings.Builder{},
		tag:    tags,
	}
}

func (h *AgentTagHandler) AddTag(tag *AgentTag) {
	h.tag = append(h.tag, tag)
}

// Feed 输入数据 返回内容 和 是否完成
// 如果输入的内容 不满足 一个完整的标签 会返回 累计的字符串 和 false
// 如果输入的内容 正处于一个标签的中间 会返回 空字符串 和 false
// 如果输入的内容 满足 一个完整的标签 会返回 内容 和 true
func (h *AgentTagHandler) Feed(data string) (string, string, bool) {
	h.buffer.WriteString(data)
	content := strings.TrimSpace(h.buffer.String())
	l := len(content)
	// 匹配进入标签
	isMatch := false
	if h.currentTag == nil {
		for _, tag := range h.tag {
			if l <= len(tag.StartTag) {
				if strings.HasPrefix(tag.StartTag, content) {
					isMatch = true
					// 进阶检查 删除所有的回车换行 查看是否是一个正常的结构
					if tag.StartTag == content {
						h.currentTag = tag
					}
				}
			}
		}
	}
	// 命中 待选标签，但没有找到完整的标签
	if h.currentTag == nil && isMatch {
		return "", "", false

		// 没有命中任何数据
	} else if h.currentTag == nil && !isMatch {
		result := h.buffer.String()
		h.buffer.Reset()
		return result, "", false
	}

	if h.currentTag != nil {
		if strings.HasPrefix(content, h.currentTag.StartTag) && strings.HasSuffix(content, h.currentTag.EndTag) {
			result := h.buffer.String()
			h.buffer.Reset()
			defer func() {
				h.currentTag = nil
			}()
			return result, h.currentTag.TagType, true
		}
	}
	// 如果执行到这里，说明正在处理标签的中间状态
	return "", "", false
}

func (h *AgentTagHandler) GetContent() string {
	return h.buffer.String()
}
