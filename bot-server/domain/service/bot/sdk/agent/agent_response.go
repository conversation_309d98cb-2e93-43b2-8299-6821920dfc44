package agent

import (
	"time"

	"github.com/ollama/ollama/api"
)

// Completion 表示聊天完成响应
type Completion struct {
	ID      string                  `json:"id"`
	Object  string                  `json:"object"`
	Created int64                   `json:"created"`
	Choices []CompletionChunkChoice `json:"choices"`

	// Ollama 响应解析
	Message            api.Message   `json:"message"`
	Model              string        `json:"model"`
	DoneReason         string        `json:"done_reason,omitempty"`
	Done               bool          `json:"done"`
	TotalDuration      time.Duration `json:"total_duration,omitempty"`
	LoadDuration       time.Duration `json:"load_duration,omitempty"`
	PromptEvalCount    int           `json:"prompt_eval_count,omitempty"`
	PromptEvalDuration time.Duration `json:"prompt_eval_duration,omitempty"`
	EvalCount          int           `json:"eval_count,omitempty"`
	EvalDuration       time.Duration `json:"eval_duration,omitempty"`
}

// CompletionChunkChoice 表示聊天完成块的选择
type CompletionChunkChoice struct {
	Index        int     `json:"index"`
	Message      Message `json:"message"`
	Delta        Message `json:"delta"`
	LogProbs     *string `json:"logprobs"`
	FinishReason *string `json:"finish_reason"`
}

type Message struct {
	Role             string `json:"role"`
	Content          string `json:"content"`
	ReasoningContent string `json:"reasoning_content"` // 适配 giteeai deepseek 的思考模式
}

// GetContent 获取响应消息的具体内容
func (agent *Completion) GetContent() string {
	if len(agent.Choices) == 0 {
		return agent.Message.Content
	}
	if agent.Choices[0].Message.Content != "" {
		return agent.Choices[0].Message.Content
	}
	if agent.Choices[0].Message.ReasoningContent != "" {
		return agent.Choices[0].Message.ReasoningContent
	}
	if agent.Choices[0].Delta.Content != "" {
		return agent.Choices[0].Delta.Content
	}
	if agent.Choices[0].Delta.ReasoningContent != "" {
		return agent.Choices[0].Delta.ReasoningContent
	}
	return ""
}

// IsFinished 判断消息是否已经结束
func (agent *Completion) IsFinished() bool {
	if len(agent.Choices) == 0 {
		// 兼容 ollama
		return agent.Done
	}
	return agent.Choices[0].FinishReason != nil && *agent.Choices[0].FinishReason == "stop"
}

func (agent *Completion) IsThinking() bool {
	if len(agent.Choices) == 0 {
		return false
	}
	if (*agent).Choices[0].Delta.Content == (*agent).Choices[0].Delta.ReasoningContent && (*agent).Choices[0].Delta.Content != "" {
		return true
	}
	return (*agent).Choices[0].Delta.ReasoningContent != ""
}

func (agent *Completion) IsEmpty() bool {
	isEmpty := true
	if len(agent.Choices) == 0 {
		return isEmpty
	}
	if agent.Choices[0].Delta.Content != "" || agent.Choices[0].Delta.ReasoningContent != "" {
		isEmpty = false
	}
	if agent.Choices[0].Message.Content != "" || agent.Choices[0].Message.ReasoningContent != "" {
		isEmpty = false
	}
	return isEmpty
}
