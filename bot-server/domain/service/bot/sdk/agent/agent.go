package agent

import (
	"bot/domain/vo"
	"encoding/json"
)

type Callback func(value *Completion) (*Item, bool, error)

type Item struct {
	Type      string `json:"type"`      // 消息类型
	Status    string `json:"status"`    // 消息状态
	McpToolID string `json:"mcpToolId"` // mcp调用期间的随机数 同一个mcp之间的随机数保持一致
	ID        string `json:"id"`        // 代理项 id 用于标识同一个数据的多状态唯一标识 后续替代 McpToolID 属性
	Data      string `json:"data"`      // 消息数据
}

func (receiver *Item) Json() string {
	json, err := json.Marshal(receiver)
	if err != nil {
		return ""
	}
	return string(json)
}

// NewMarkdownAgentItem 创建一个 markdown 代理项
// 代理项的类型是 markdown
// 代理项的状态是默认状态
// 代理项的数据是 data
func NewMarkdownAgentItem(data string) *Item {
	return &Item{
		Type:   vo.AgentMarkdown,
		Status: vo.AgentItemStatusDefault,
		Data:   data,
	}
}

// NewAgentItem 创建一个代理项
// 代理项的类型是 agentType
// 代理项的状态是 status
// 代理项的数据是 data
func NewAgentItem(agentType string, status string, data string) *Item {
	return &Item{
		Type:   agentType,
		Status: status,
		Data:   data,
	}
}

func NewMcpCallAgentItemByStatus(id, status, data string) *Item {
	return &Item{
		Type:      vo.AgentMcpCall,
		Status:    status,
		McpToolID: id,
		Data:      data,
	}
}

// NewAIPaintingAgentItemByStatus
// 创建一个 AI painting 代理项
// 每个不同的图片或者操作需要一个
func NewAIPaintingAgentItemByStatus(id, status, data string) *Item {
	return &Item{
		Type:   vo.AgentPainting,
		Status: status,
		ID:     id,
		Data:   data,
	}
}
