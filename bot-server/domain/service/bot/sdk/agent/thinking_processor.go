package agent

import (
	"bot/domain/vo"
	"strings"
	"sync"
)

// ThinkingProcessResult 思考处理结果
type ThinkingProcessResult struct {
	Content       string // 处理后的内容
	Type          string // 消息类型 (AgentThinking/AgentMarkdown)
	Status        string // 消息状态
	IsThinkingEnd bool   // 思考模式是否结束
}

// ThinkingProcessor 思考处理器接口
// 用于处理 AI 模型的思考模式逻辑
type ThinkingProcessor interface {
	// ProcessThinkingContent 处理思考内容
	// 参数：
	//   - chatResponse: 聊天响应
	//   - isThinkingEnabled: 是否启用思考模式
	// 返回：
	//   - result: 处理结果，包含拼接好的内容、类型、状态和思考结束标志
	ProcessThinkingContent(chatResponse *Completion, isThinkingEnabled bool) *ThinkingProcessResult

	// Reset 重置处理器状态
	Reset()
}

// DefaultThinkingProcessor 默认思考处理器实现
type DefaultThinkingProcessor struct {
	// 状态控制
	startOnce *sync.Once // 控制思考开始标记的插入
	endOnce   *sync.Once // 控制思考结束标记的插入

	// 当前状态
	currentType   string // 当前消息类型 (AgentThinking/AgentMarkdown)
	currentStatus string // 当前消息状态
	isThinking    bool   // 当前是否在思考状态
}

// NewDefaultThinkingProcessor 创建默认思考处理器
func NewDefaultThinkingProcessor() *DefaultThinkingProcessor {
	return &DefaultThinkingProcessor{
		startOnce:     &sync.Once{},
		endOnce:       &sync.Once{},
		currentType:   vo.AgentMarkdown,
		currentStatus: vo.AgentItemStatusDefault,
		isThinking:    false,
	}
}

// ProcessThinkingContent 处理思考内容
func (p *DefaultThinkingProcessor) ProcessThinkingContent(chatResponse *Completion, isThinkingEnabled bool) *ThinkingProcessResult {
	content := chatResponse.GetContent()
	isThinkingEnd := false

	// 如果未启用思考模式，直接返回 Markdown 格式
	if !isThinkingEnabled {
		return &ThinkingProcessResult{
			Content:       content,
			Type:          vo.AgentMarkdown,
			Status:        vo.AgentItemStatusDefault,
			IsThinkingEnd: false,
		}
	}

	// 处理思考模式
	if chatResponse.IsThinking() {
		// 正在思考状态
		content = p.handleThinkingStart(content)
	} else {
		// 非思考状态 - 可能是思考结束
		if p.isThinking {
			content = p.handleThinkingEnd(content)
			isThinkingEnd = true
		}
	}

	return &ThinkingProcessResult{
		Content:       content,
		Type:          p.currentType,
		Status:        p.currentStatus,
		IsThinkingEnd: isThinkingEnd,
	}
}

// handleThinkingStart 处理思考开始逻辑
func (p *DefaultThinkingProcessor) handleThinkingStart(content string) string {
	if strings.TrimSpace(content) != "" {
		p.startOnce.Do(func() {
			// 添加思考渲染块开始标记
			content = ":::thinking\n\r" + content
			p.endOnce = &sync.Once{} // 重置结束标记
			p.currentType = vo.AgentThinking
			p.currentStatus = vo.AgentItemStatusDefault
		})
		p.isThinking = true
	}
	return content
}

// handleThinkingEnd 处理思考结束逻辑
func (p *DefaultThinkingProcessor) handleThinkingEnd(content string) string {
	if p.isThinking {
		p.endOnce.Do(func() {
			// 添加思考渲染块结束标记
			content = "\n\n:::\n\r" + content
			// 重置思考模式输出
			p.startOnce = &sync.Once{}
			p.isThinking = false
			p.currentStatus = vo.AgentItemStatusStop
		})
	}

	return content
}

// Reset 重置处理器状态
func (p *DefaultThinkingProcessor) Reset() {
	p.startOnce = &sync.Once{}
	p.endOnce = &sync.Once{}
	p.currentType = vo.AgentMarkdown
	p.currentStatus = vo.AgentItemStatusDefault
	p.isThinking = false
}
