package sse

import (
	"encoding/json"
	"github.com/pkg/errors"
	"net/http"
)

var (
	JSONError = errors.New("JSON Error")
)

// HttpStreamClient
// 单向流数据推送客户端
type HttpStreamClient struct {
	w       http.ResponseWriter
	flusher http.Flusher
}

func NewHttpStreamClient(w http.ResponseWriter) *HttpStreamClient {
	return &HttpStreamClient{
		w:       w,
		flusher: w.(http.Flusher),
	}
}

func (c *HttpStreamClient) Push(data any) error {
	return c.write(data)
}

func (c *HttpStreamClient) write(data any) error {
	if data == nil {
		return nil
	}
	marshal, _ := json.Marshal(data)
	_, err := c.w.Write(marshal)
	if err != nil {
		return err
	}
	c.flusher.Flush()
	return nil
}
