package sse

import (
	"bytes"
	jsoniter "github.com/json-iterator/go"
	"net/http"
)

type Options func(sse *HttpSSE)

// HttpSSE
// 基于http协议实现的流消息传递
type HttpSSE struct {
	writer chan string
	http.ResponseWriter
	http.Flusher
}

func New(w http.ResponseWriter, opts ...Options) Stream {
	w.Header().Set("Content-Type", "text/event-stream")
	w.<PERSON>er().Set("Cache-Control", "no-cache")
	w.Header().Set("Connection", "keep-alive")
	sse := &HttpSSE{
		writer:         make(chan string),
		ResponseWriter: w,
		Flusher:        w.(http.Flusher),
	}

	for _, opt := range opts {
		opt(sse)
	}
	return sse
}

func WithDataChannelSize(size int) Options {
	return func(sse *HttpSSE) {
		sse.writer = make(chan string, size)
	}
}

func (s *HttpSSE) Writer(data any) error {
	var buf []byte
	var err error
	defer s.Flusher.Flush()
	switch v := any(data).(type) {
	case string:
		if _, err = s.ResponseWriter.Write([]byte(v + Segmentation)); err != nil {
			return err
		}
	default:
		if buf, err = jsoniter.Marshal(v); err != nil {
			return err
		}
		buffer := bytes.NewBuffer(buf)
		buffer.WriteString(Segmentation)
		if _, err = buffer.WriteTo(s.ResponseWriter); err != nil {
			return err
		}
	}
	return nil
}

func (s *HttpSSE) Reader(reader Reader) <-chan string {
	go reader(s.writer)
	return s.writer
}
