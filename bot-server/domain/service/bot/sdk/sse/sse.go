package sse

type Status string

const (
	// Segmentation  流消息分段符号
	Segmentation = "\n"
)

var (
	// Error 流数据错误
	Error = Status("Error")

	// Begin 数据开始标识
	Begin = Status("Begin")

	// Data 流数据 数据传输状态
	Data = Status("Data")

	// Finish 流数据结束标识
	Finish = Status("Finish")
)

// Reader 自定义第三方 SSE 实现读取
type Reader func(write chan<- string)

func init() {
}

// Stream
// 当前项目内标准的 sse 接口定义
type Stream interface {
	// Writer 向sse中写入数据
	Writer(data any) error
	// Reader reader 中读取sse数据 到 <-chan T
	Reader(reader Reader) <-chan string
}
