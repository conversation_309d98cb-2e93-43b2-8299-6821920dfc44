package sdk

import "bot/domain/entity"

type BaseChat struct {
	// Id 消息id
	ID string `json:"id" form:"id"`

	// 消息内容
	Message string `json:"message" `

	// 图片数据 存储的是服务器 url 资源
	Images []string `json:"images"`

	// 消息角色
	Role string `json:"role" form:"role" `

	// 消息类型
	MessageType string `json:"messageType" form:"messageType" `

	// ReplyMsgId 回复的消息id
	ReplyMsgID string `json:"replyMsgID" form:"replyMsgID" `

	// conversationID 会话id
	ConversationID string `json:"conversationID" form:"conversationID" `

	// History 消息列表
	History []*entity.HistoryEntity `form:"history" json:"history" `

	// Stream  是否流式响应
	Stream bool `form:"stream" json:"stream"`

	// PlatformID 平台id
	PluginID string `form:"pluginID" json:"pluginID"`

	// PlatformID 平台id
	PlatformID string `form:"platformID" json:"platformID"`

	// NetWork 网络搜索
	WebSearch bool `form:"webSearch" json:"webSearch"`

	// Thinking 思考模式
	Thinking bool `form:"thinking" json:"thinking"`

	Mcp []string `form:"mcp" json:"mcp"`
}
