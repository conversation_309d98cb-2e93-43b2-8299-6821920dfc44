package ollamautils

import (
	"bot/domain/entity"
	"github.com/ollama/ollama/api"
)

func NewMessages(list []*entity.HistoryEntity) []api.Message {
	var messages []api.Message
	for _, msg := range list {
		images := make([]api.ImageData, len(msg.Images))
		for i, image := range msg.Images {
			images[i] = image
		}
		messages = append(messages, api.Message{
			Role:    msg.Role,
			Content: msg.Content.(string),
			Images:  images,
		})
	}
	return messages
}
