package sdk

import (
	"bot/domain/service/bot/sdk/sse"
	"bot/domain/vo"
	"encoding/json"
	"errors"
	"io"
)

// LLMDataHandle
// llm 通用数据处理函数
// any 通用数据
// bool 通用数据是否结束
// error 通用数据错误
type LLMDataHandle func(value string) (ILLMData, error)

type ILLMData interface {
	IsFinished() bool
}

// Callback
// @Return string 消息历史
// @Return error 错误信息
type Callback func(value string) (string, error)

// LLMData
// llm 通用数据传输
type LLMData struct {
	// Data 流数据具体内容
	Data any `json:"data,omitempty"`
	// 文本数据
	Content string `json:"content"`

	// 消息类型 消息过程中的 数据类型 一般是 场景类型 和正常数据类型 Type 为场景类型时候一般是下发渲染场景
	Type string `json:"type"`

	// 消息场景 一般消息场景在一个完整的流中是保持一致的
	Scene string `json:"scene"`

	// Status 流数据状态阶段
	Status      sse.Status `json:"status"`
	MessageType int        `json:"messageType,omitempty"`
	// Err 流数据错误
	Err error `json:"-"`
}

func (receiver *LLMData) Byte() []byte {
	marshal, _ := json.Marshal(receiver)
	return marshal
}

func NewErrorLLMData(value any) *LLMData {
	return &LLMData{
		Status: sse.Error,
		Data:   value,
	}
}

// NewRender
// 构建一个 Render流数据下发
// 默认是 Begin 数据
func NewRender(scene string) *LLMData {
	return &LLMData{
		Scene:  scene,
		Type:   vo.RenderType,
		Status: sse.Begin,
	}
}

// NewFinishLLMData
// 创建一个完成状态的 LLM 数据
func NewFinishLLMData() *LLMData {
	return &LLMData{
		Status: sse.Finish,
	}
}

func NewBeginLLMData() *LLMData {
	return &LLMData{
		Status: sse.Begin,
	}
}

func NewLLMData(status sse.Status, data any, err error) *LLMData {
	return &LLMData{
		Status: status,
		Data:   data,
		Err:    err,
	}
}

func NewLLMContent(status sse.Status, content string, err error) *LLMData {
	return &LLMData{
		Status:  status,
		Content: content,
		Err:     err,
	}
}

// LLMHandler
// LLM 通用处理器
// V 类型是 LLM模型返回的流数据对应的数据
// T 类型对应的是 实现的流处理器之间的数据传递
type LLMHandler struct {
	sse.Stream
}

func LLMStreamErrorHandle(err error) (*LLMError, bool) {
	if err == io.EOF {
		return nil, true
	} else if errors.Is(err, io.ErrUnexpectedEOF) || errors.Is(err, io.ErrClosedPipe) {
		return LLMTransmissionError.Instance().SetError(err), true
	} else if err != nil {
		return LLMTransmissionError.Instance().SetError(err), true
	}
	return nil, false
}
