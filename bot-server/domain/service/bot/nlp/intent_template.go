package nlp

// IntentTemplate 意图模板结构
type IntentTemplate struct {
	Name  string             `json:"name"`  // 意图名称
	Score float64            `json:"score"` // 命中词槽的数量所占总体词槽数量的比值分值，分数比值超越 Score 阈值 将认定 输入的语句匹配上了此模板
	Slots map[string][]*Slot `json:"slots"` // 意图词槽，key为词槽名称，value为相同名称的词槽列表（按定义顺序排序）
}

// GetSlot 获取指定词槽名称的第一个词槽对象
// slotName: 词槽名称
// 返回值: 词槽对象，如果词槽不存在则返回 nil
func (t *IntentTemplate) GetSlot(slotName string) *Slot {
	if slots, exists := t.Slots[slotName]; exists && len(slots) > 0 {
		return slots[0]
	}
	return nil
}

// GetSlotByIndex 通过 index 和槽名获取词槽对象
// slotName: 词槽名称
// index: 词槽在模板中定义的位置索引
// 返回值: 词槽对象，如果不存在指定 index 的词槽则返回 nil
func (t *IntentTemplate) GetSlotByIndex(slotName string, index int) *Slot {
	if slots, exists := t.Slots[slotName]; exists {
		for _, slot := range slots {
			if slot.Index == index {
				return slot
			}
		}
	}
	return nil
}

// GetSlotValueByIndex 通过 index 和槽名获取词槽值
// slotName: 词槽名称
// index: 词槽在模板中定义的位置索引
// 返回值: 词槽值，如果不存在指定 index 的词槽或未匹配则返回空字符串
func (t *IntentTemplate) GetSlotValueByIndex(slotName string, index int) string {
	if slot := t.GetSlotByIndex(slotName, index); slot != nil && slot.Hit {
		return slot.Value
	}
	return ""
}

// GetSlotValue 获取指定词槽名称的第一个匹配值
// slotName: 词槽名称
// 返回值: 词槽值，如果词槽不存在或未匹配则返回空字符串
func (t *IntentTemplate) GetSlotValue(slotName string) string {
	if slot := t.GetSlot(slotName); slot != nil && slot.Hit {
		return slot.Value
	}
	return ""
}

type IntentMatchTemplate struct {
	Name      string
	Templates []string
}
