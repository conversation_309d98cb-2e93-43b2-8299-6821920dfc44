package nlp

import (
	"regexp"
	"strings"
)

type IntentMatch struct {
	Intent  string
	Pattern []string
	Score   float64
}

// Match 检查给定的query和词槽是否匹配当前意图的模式
// slots 格式为 map[string]string，key为词槽名称，value为词槽值
// 返回匹配成功的意图名，如果没有匹配成功返回空字符串
func (im *IntentMatch) Match(query string, slots map[string]string) string {
	// 遍历所有模式
	for _, pattern := range im.Pattern {
		// 替换模式中的词槽占位符
		finalPattern := pattern
		// 使用正则表达式找出所有的槽位占位符，匹配{{xxx}}格式
		slotRegex := regexp.MustCompile(`\{\{(.*?)\}\}`)
		matches := slotRegex.FindAllStringSubmatch(pattern, -1)

		for _, match := range matches {
			if len(match) > 1 {
				slotName := match[1]
				placeholder := "{{" + slotName + "}}"
				// 获取槽值，如果不存在则使用空字符串
				slotValue := slots[slotName]
				// 对正则特殊字符进行转义
				escapedValue := regexp.QuoteMeta(slotValue)
				finalPattern = strings.ReplaceAll(finalPattern, placeholder, escapedValue)
			}
		}

		// 编译修��后的正则表达式
		reg, err := regexp.Compile(finalPattern)
		if err != nil {
			continue // 如果正则表达式仍然无效，跳过当前模式
		}

		// 尝试匹配
		if reg.MatchString(query) {
			return im.Intent // 返回匹配成功的意图名
		}
	}

	return "" // 没有匹配成功返回空字符串
}
