package nlp

import (
	"fmt"

	"github.com/BurntSushi/toml"
)

// Slot 词槽结构体，用于定义和存储词槽的属性和匹配结果
type Slot struct {
	Name   string           `json:"name"`   // 词槽名称，对应 slots.toml 中的词槽类型
	Index  int              `json:"index"`  // 词槽在模板中的位置索引
	Must   bool             `json:"must"`   // 是否为必需词槽
	Value  string           `json:"value"`  // 词槽匹配到的标准值
	Hit    bool             `json:"hit"`    // 是否命中匹配
	Score  float64          `json:"score"`  // 词槽匹配的置信度分数
	Result *SlotMatchResult `json:"result"` // 词槽的详细匹配结果
}

// SlotConfig 词槽配置结构
// 用于定义不同类型词槽的标准值和别名映射关系
type SlotConfig struct {
	Slots map[string]map[string][]string `toml:"slots"` // 通用词槽配置，key为词槽类型
}

// SlotMatcher 词槽匹配器
// 用于实现词槽的标准化匹配功能
type SlotMatcher struct {
	// 标准值到别名的映射
	standardToAliases map[string][]string
	// 别名到标准值的映射
	aliasToStandard map[string]string
	// 词槽类型
	slotType string
}

// NewSlotMatcher 创建新的词槽匹配器
func NewSlotMatcher(slotType string) *SlotMatcher {
	return &SlotMatcher{
		standardToAliases: make(map[string][]string),
		aliasToStandard:   make(map[string]string),
		slotType:          slotType,
	}
}

// LoadSlotConfig 加载词槽配置
// path: TOML配置文件路径
func LoadSlotConfig(path string) (*SlotConfig, error) {
	var config SlotConfig
	config.Slots = make(map[string]map[string][]string)
	if _, err := toml.DecodeFile(path, &config); err != nil {
		return nil, fmt.Errorf("解析词槽配置文件失败: %w", err)
	}
	return &config, nil
}

// BuildMatcher 构建词槽匹配器
// config: 词槽配置映射
// slotType: 词槽类型标识
func BuildMatcher(config map[string][]string, slotType string) *SlotMatcher {
	matcher := NewSlotMatcher(slotType)

	// 遍历配置构建映射关系
	for standard, aliases := range config {
		matcher.standardToAliases[standard] = aliases
		// 标准值也作为一个别名
		matcher.aliasToStandard[standard] = standard
		// 添加所有别名到标准值的映射
		for _, alias := range aliases {
			matcher.aliasToStandard[alias] = standard
		}
	}

	return matcher
}

// Match 匹配输入文本，返回标准值
// text: 待匹配的文本
// 返回值: (标准值, 是否匹配成功)
func (m *SlotMatcher) Match(text string) (string, bool) {
	if standard, ok := m.aliasToStandard[text]; ok {
		return standard, true
	}
	return "", false
}

// GetAliases 获取标准值的所有别名
// standard: 标准值
// 返回值: 别名列表，如果标准值不存在则返回nil
func (m *SlotMatcher) GetAliases(standard string) []string {
	if aliases, ok := m.standardToAliases[standard]; ok {
		return aliases
	}
	return nil
}

// GetSlotType 获取词槽类型
func (m *SlotMatcher) GetSlotType() string {
	return m.slotType
}

// GetAllStandards 获取所有标准值
// 返回值: 所有标准值的列表
func (m *SlotMatcher) GetAllStandards() []string {
	standards := make([]string, 0, len(m.standardToAliases))
	for standard := range m.standardToAliases {
		standards = append(standards, standard)
	}
	return standards
}

// SlotMatchResult 词槽匹配结果
type SlotMatchResult struct {
	SlotType     string `json:"slotType"`     // 词槽类型
	StandardText string `json:"standardText"` // 标准值
	MatchedText  string `json:"matchedText"`  // 匹配到的原文
	StartPos     int    `json:"startPos"`     // 开始位置
	EndPos       int    `json:"endPos"`       // 结束位置
	Index        int    `json:"index"`        // 词槽在模板中的位置索引
}
