package nlp

import (
	"bufio"
	"fmt"
	"os"
	"sort"
	"strconv"
	"strings"

	"github.com/BurntSushi/toml"
	"github.com/go-ego/gse"
)

// IntentMatchItem 定义意图匹配项
type IntentMatchItem struct {
	Patterns []string `toml:"patterns"` // 正则匹配模式
	Score    float64  `toml:"score"`    // 匹配分数
}

// IntentMatchConfig 定义 TOML 配置结构
type IntentMatchConfig struct {
	Intents map[string]IntentMatchItem `toml:"intents"`
}

// IntentTemplateConfig 定义意图模板的 TOML 配置结构
type IntentTemplateConfig struct {
	Templates map[string][]TemplateItem `toml:"templates"`
}

// TemplateItem 定义模板项
type TemplateItem struct {
	Score    float64 `toml:"score"`    // 分值
	Template string  `toml:"template"` // 模板字符串，包含词槽定义
}

type BaseConfig struct {
	Slots          *SlotConfig
	IntentTemplate []*IntentTemplate
	IntentMatch    map[string]*IntentMatch
}

// LoadPrompt 加载提示词分词器
func LoadPrompt(path string) (gse.Segmenter, error) {
	var seg gse.Segmenter
	var err error

	// 初始化分词器
	seg.Init()

	// 打开并读取文件
	file, err := os.Open(path)
	if err != nil {
		return seg, fmt.Errorf("打开提示词文件失败: %w", err)
	}
	defer file.Close()

	// 创建词典map用于加载
	dict := make([]map[string]string, 0)

	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := scanner.Text()
		if line == "" {
			continue
		}

		// 解析行内容
		parts := strings.Fields(line)
		if len(parts) != 3 {
			continue
		}

		word := parts[0]
		score := parts[1]
		pos := parts[2]

		// 构建分词器词典项
		dictItem := map[string]string{
			"text": word,
			"freq": score,
			"pos":  pos,
		}
		dict = append(dict, dictItem)
	}

	if err = scanner.Err(); err != nil {
		return seg, fmt.Errorf("读取文件失败: %w", err)
	}

	// 加载词典到分词器
	err = seg.LoadDictMap(dict)
	if err != nil {
		return seg, fmt.Errorf("加载词典失败: %w", err)
	}

	return seg, nil
}

// LoadIntentTemplate 加载意图模板
func LoadIntentTemplate(path string) ([]*IntentTemplate, error) {
	var config IntentTemplateConfig
	if _, err := toml.DecodeFile(path, &config); err != nil {
		return nil, fmt.Errorf("解析意图模板TOML文件失败: %w", err)
	}

	templates := make([]*IntentTemplate, 0)
	for intentName, items := range config.Templates {
		// 处理每个模板项
		for _, item := range items {
			// 解析词槽字符串
			slotMap := make(map[string][]*Slot)
			slotParts := strings.Split(item.Template, ";")

			for slotNum, slot := range slotParts {
				if strings.TrimSpace(slot) == "" {
					continue
				}

				// 分割词槽名和属性
				slotNameAndAttr := strings.Split(slot, "[")
				if len(slotNameAndAttr) != 2 {
					return nil, fmt.Errorf("意图 %s 的第%d个词槽格式错误，缺少属性定义: %s", intentName, slotNum+1, slot)
				}

				// 处理词槽名，去掉冒号
				slotName := strings.TrimSpace(slotNameAndAttr[0])
				if strings.Contains(slotName, ":") {
					slotName = strings.Split(slotName, ":")[0]
					slotName = strings.TrimSpace(slotName)
				}

				// 去除结尾的 "]"
				attrStr := strings.TrimSuffix(slotNameAndAttr[1], "]")
				if attrStr == slotNameAndAttr[1] {
					return nil, fmt.Errorf("意图 %s 的第%d个词槽格式错误，缺少结束符 ']': %s", intentName, slotNum+1, slot)
				}

				// 解析属性
				attrs := strings.Split(attrStr, ",")
				if len(attrs) != 2 {
					return nil, fmt.Errorf("意图 %s 的第%d个词槽属性格式错误，需要两个属性值: %s", intentName, slotNum+1, attrStr)
				}

				// 解析 index
				index, err := strconv.Atoi(strings.TrimSpace(attrs[0]))
				if err != nil {
					return nil, fmt.Errorf("意图 %s 的第%d个词槽index解析错误: %s", intentName, slotNum+1, attrs[0])
				}

				// 解析是否必须
				required, err := strconv.ParseBool(strings.TrimSpace(attrs[1]))
				if err != nil {
					return nil, fmt.Errorf("意图 %s 的第%d个词槽must属性解析错误: %s", intentName, slotNum+1, attrs[1])
				}

				// 创建新的词槽
				newSlot := &Slot{
					Name:  slotName,
					Index: index,
					Must:  required,
				}

				// 将词槽添加到对应的切片中
				slotMap[slotName] = append(slotMap[slotName], newSlot)
			}

			// 对每个词槽类型的切片按照规则排序
			for _, slots := range slotMap {
				sort.Slice(slots, func(i, j int) bool {
					// 如果两个词槽都带顺序，按 index 升序排序
					if slots[i].Index > 0 && slots[j].Index > 0 {
						return slots[i].Index < slots[j].Index
					}
					// 如果只有一个带顺序，带顺序的排在前面
					if slots[i].Index > 0 {
						return true
					}
					if slots[j].Index > 0 {
						return false
					}
					// 都不带顺序，保持原有顺序
					return i < j
				})
			}

			template := &IntentTemplate{
				Name:  intentName,
				Score: item.Score,
				Slots: slotMap,
			}

			// 检查有序词槽的顺序唯一性
			orderedSlots := make(map[int][]string) // key: 顺序索引, value: 词槽名称列表
			for slotName, slots := range slotMap {
				for _, slot := range slots {
					if slot.Index > 0 { // 只检查有序词槽
						if slot.Index < 1 {
							return nil, fmt.Errorf("意图 %s 的词槽 %s 的顺序索引必须从1开始", intentName, slotName)
						}
						orderedSlots[slot.Index] = append(orderedSlots[slot.Index], slotName)
						if len(orderedSlots[slot.Index]) > 1 {
							return nil, fmt.Errorf("意图 %s 存在多个顺序索引为 %d 的词槽: %v",
								intentName,
								slot.Index,
								orderedSlots[slot.Index])
						}
					}
				}
			}

			// 检查顺序是否连续
			if len(orderedSlots) > 0 {
				maxIndex := 0
				minIndex := int(^uint(0) >> 1) // int的最大值
				existIndexes := make([]int, 0)

				// 收集所有存在的索引
				for index := range orderedSlots {
					existIndexes = append(existIndexes, index)
					if index > maxIndex {
						maxIndex = index
					}
					if index < minIndex {
						minIndex = index
					}
				}

				// 排序以便找出缺失的区间
				sort.Ints(existIndexes)

				// 检查是否从1开始
				if minIndex != 1 {
					return nil, fmt.Errorf("意图 %s 的有序词槽顺序必须从1开始，当前最小索引为 %d", intentName, minIndex)
				}

				// 查找缺失的区间
				var missingRanges []string
				prev := 1
				for _, curr := range existIndexes {
					for j := prev + 1; j < curr; j++ {
						if len(missingRanges) == 0 {
							missingRanges = append(missingRanges, fmt.Sprintf("%d", j))
						} else {
							last := missingRanges[len(missingRanges)-1]
							if strings.Contains(last, "~") {
								// 已经是一个范围，更新结束值
								parts := strings.Split(last, "~")
								missingRanges[len(missingRanges)-1] = parts[0] + "~" + fmt.Sprintf("%d", j)
							} else {
								// 检查是否可以形成新的范围
								lastNum, _ := strconv.Atoi(last)
								if lastNum == j-1 {
									missingRanges[len(missingRanges)-1] = fmt.Sprintf("%d~%d", lastNum, j)
								} else {
									missingRanges = append(missingRanges, fmt.Sprintf("%d", j))
								}
							}
						}
					}
					prev = curr
				}

				if len(missingRanges) > 0 {
					return nil, fmt.Errorf("意图 %s 的有序词槽顺序不连续，缺少索引: [%s]，现有索引: %v",
						intentName,
						strings.Join(missingRanges, ", "),
						existIndexes)
				}
			}

			templates = append(templates, template)
		}
	}

	return templates, nil
}

// LoadIntentMatch 加载意图泛化匹配
func LoadIntentMatch(path string) (map[string]*IntentMatch, error) {
	var config IntentMatchConfig
	if _, err := toml.DecodeFile(path, &config); err != nil {
		return nil, fmt.Errorf("解析意图匹配TOML文件失败: %w", err)
	}

	matches := make(map[string]*IntentMatch)
	for intentName, item := range config.Intents {
		// 只设置默认分数
		if item.Score <= 0 {
			item.Score = 1.0 // 默认分数
		}

		matches[intentName] = &IntentMatch{
			Intent:  intentName,
			Pattern: item.Patterns,
			Score:   item.Score,
		}
	}

	return matches, nil
}
