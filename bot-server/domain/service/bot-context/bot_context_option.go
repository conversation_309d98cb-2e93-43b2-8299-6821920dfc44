package bot_context

import (
	"bot/domain/entity"
	"bot/domain/repository"
	"bot/domain/repository/mcp_repo"
	"bot/domain/service/bot/sdk/fun"
	"bot/infrastructure/po"
	"net/http"
)

type Option func(ctx *Context)

func WithQuery(value string) Option {
	return func(ctx *Context) {
		ctx.Query = value
	}
}

func WithPlatform(platform *po.PlatformPO) Option {
	return func(ctx *Context) {
		ctx.Platform = platform
	}
}

func WithPlugin(plugin *po.PluginPO) Option {
	return func(ctx *Context) {
		ctx.Plugin = plugin
	}
}

func WithSetting(setting *entity.SettingEntity) Option {
	return func(ctx *Context) {
		ctx.Setting = setting
	}
}

func WithPlatformConfig(value *entity.PlatformConfig) Option {
	return func(ctx *Context) {
		ctx.PlatformConfig = value
	}
}

func WithHTTPWriter(value http.ResponseWriter) Option {
	return func(ctx *Context) {
		ctx.ResponseWriter = value
	}
}
func WithHTTPRequest(req *http.Request) Option {
	return func(ctx *Context) {
		ctx.Request = req
	}
}

func WithIntent(intent repository.INlpIntent) Option {
	return func(ctx *Context) {
		ctx.Handler = intent
	}
}

func WithIntentCalling(calling fun.FuncCalling) Option {
	return func(ctx *Context) {
		ctx.Calling = calling
	}
}

func WithCallingDefinition(definition fun.FuncDefinition) Option {
	return func(ctx *Context) {
		ctx.CallingDefinition = definition
	}
}

func WithMcp(mcp mcp_repo.IMcp) Option {
	return func(ctx *Context) {
		ctx.IMcp = mcp
	}
}
