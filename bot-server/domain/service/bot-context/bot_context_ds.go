package bot_context

import (
	"bot/domain/entity"
	"bot/domain/repository"
	"bot/domain/repository/mcp_repo"
	nlp "bot/domain/service/bot/nlp"
	"bot/domain/service/bot/sdk/fun"
	"bot/infrastructure/po"
	"context"
	"net/http"
)

// Context
// 整个 LLM 功能的上下文
type Context struct {
	// 当前调用平台
	Platform *po.PlatformPO
	*entity.PlatformConfig

	// 当前调用的插件
	Plugin *po.PluginPO
	// 用户输入消息文本
	Query string

	// 系统 配置
	Setting *entity.SettingEntity

	// 流响应
	http.ResponseWriter
	*http.Request

	// 意图处理
	Handler repository.INlpIntent

	// mcp 工具
	mcp_repo.IMcp

	// 具体调用实例
	Calling fun.FuncCalling

	// 函数调用定义
	CallingDefinition fun.FuncDefinition

	// 需要调用的工具列表
	FuncTools []fun.FunctionSpec
}


func NewBotContext(options ...Option) *Context {
	ctx := &Context{}
	for _, option := range options {
		option(ctx)
	}
	return ctx
}

// Intent
// 获取意图解析
func (c *Context) Intent() *nlp.Intent {
	return c.Handler.Intent(context.Background(), c.Query)
}
