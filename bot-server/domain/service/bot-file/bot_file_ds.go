package bot_file

import (
	"bot/domain/entity"
	"bot/domain/repository"
	"bot/infrastructure/po"
	"context"
	"errors"
	"fmt"
	"path/filepath"
	"strings"
	"time"

	"github.com/google/uuid"
)

// FileDomain 文件领域服务
type FileDomain struct {
	repository.IFile
}

// NewFileDomain 创建文件领域服务
func NewFileDomain(fileRepo repository.IFile) *FileDomain {
	return &FileDomain{
		IFile: fileRepo,
	}
}

// CreateFile 创建文件或目录
func (f *FileDomain) CreateFile(ctx context.Context, fileEntity *entity.FileEntity) (*po.FilePO, error) {
	if fileEntity == nil {
		return nil, errors.New("文件实体不能为空")
	}

	// 验证文件名
	if err := f.validateFileName(fileEntity.Name); err != nil {
		return nil, err
	}

	// 生成ID和时间戳
	fileEntity.ID = uuid.New().String()
	now := time.Now()
	fileEntity.CreatedAt = now
	fileEntity.UpdatedAt = now

	// 检查同名文件是否存在
	checkEntity := &entity.FileEntity{
		PID:  fileEntity.PID,
		Name: fileEntity.Name,
	}
	existing, _ := f.QueryFiles(ctx, checkEntity)
	if len(existing) > 0 {
		return nil, fmt.Errorf("文件或目录 '%s' 已存在", fileEntity.Name)
	}

	return f.IFile.CreateFile(ctx, fileEntity)
}

// DeleteFile 删除文件或目录
func (f *FileDomain) DeleteFile(ctx context.Context, fileEntity *entity.FileEntity) error {
	if fileEntity == nil || fileEntity.ID == "" {
		return errors.New("文件ID不能为空")
	}

	// 获取文件信息
	file, err := f.QueryFile(ctx, &entity.FileEntity{ID: fileEntity.ID})
	if err != nil {
		return fmt.Errorf("获取文件信息失败: %w", err)
	}

	// 如果是目录，递归删除所有子项
	if file.IsDir {
		if err := f.deleteDirectoryRecursively(ctx, fileEntity.ID); err != nil {
			return fmt.Errorf("递归删除目录失败: %w", err)
		}
	}

	// 删除当前文件或目录
	return f.IFile.DeleteFile(ctx, fileEntity)
}

// deleteDirectoryRecursively 递归删除目录及其所有子项
func (f *FileDomain) deleteDirectoryRecursively(ctx context.Context, dirID string) error {
	// 获取目录下的所有子项
	children, err := f.QueryFilesByPID(ctx, dirID)
	if err != nil {
		return fmt.Errorf("获取子项列表失败: %w", err)
	}

	// 递归删除所有子项
	for _, child := range children {
		childEntity := &entity.FileEntity{
			ID:    child.ID,
			IsDir: child.IsDir,
		}

		if child.IsDir {
			// 如果是目录，递归删除
			if err := f.deleteDirectoryRecursively(ctx, child.ID); err != nil {
				return fmt.Errorf("递归删除子目录 %s 失败: %w", child.Name, err)
			}
		}

		// 删除当前子项
		if err := f.IFile.DeleteFile(ctx, childEntity); err != nil {
			return fmt.Errorf("删除子项 %s 失败: %w", child.Name, err)
		}
	}

	return nil
}

// UpdateFile 更新文件信息
func (f *FileDomain) UpdateFile(ctx context.Context, fileEntity *entity.FileEntity) (*po.FilePO, error) {
	if fileEntity == nil || fileEntity.ID == "" {
		return nil, errors.New("文件ID不能为空")
	}

	// 验证新文件名（如果提供了的话）
	if fileEntity.Name != "" {
		if err := f.validateFileName(fileEntity.Name); err != nil {
			return nil, err
		}

		// 检查同名文件是否存在（除了当前文件）
		checkEntity := &entity.FileEntity{
			PID:  fileEntity.PID,
			Name: fileEntity.Name,
		}
		existing, _ := f.QueryFiles(ctx, checkEntity)
		for _, file := range existing {
			if file.ID != fileEntity.ID {
				return nil, fmt.Errorf("文件或目录 '%s' 已存在", fileEntity.Name)
			}
		}
	}

	fileEntity.UpdatedAt = time.Now()
	return f.IFile.UpdateFile(ctx, fileEntity)
}

// QueryFile 查询单个文件信息
func (f *FileDomain) QueryFile(ctx context.Context, fileEntity *entity.FileEntity) (*po.FilePO, error) {
	if fileEntity == nil {
		return nil, errors.New("查询参数不能为空")
	}

	return f.IFile.QueryFile(ctx, fileEntity)
}

// QueryFiles 查询文件列表
func (f *FileDomain) QueryFiles(ctx context.Context, fileEntity *entity.FileEntity) ([]*po.FilePO, error) {
	return f.IFile.QueryFiles(ctx, fileEntity)
}

// QueryFilesByPID 根据父级ID查询文件列表
func (f *FileDomain) QueryFilesByPID(ctx context.Context, pid string) ([]*po.FilePO, error) {
	return f.IFile.QueryFilesByPID(ctx, pid)
}

// GetFileTree 获取文件树结构
func (f *FileDomain) GetFileTree(ctx context.Context, rootPID string) ([]*po.FilePO, error) {
	files, err := f.QueryFilesByPID(ctx, rootPID)
	if err != nil {
		return nil, err
	}

	// 可以在这里添加递归逻辑来构建完整的树结构
	return files, nil
}

// MoveFile 移动文件或目录
func (f *FileDomain) MoveFile(ctx context.Context, fileID, newPID string) (*po.FilePO, error) {
	if fileID == "" {
		return nil, errors.New("文件ID不能为空")
	}

	// 检查目标目录是否存在
	if newPID != "" {
		target, err := f.QueryFile(ctx, &entity.FileEntity{ID: newPID})
		if err != nil {
			return nil, fmt.Errorf("目标目录不存在: %w", err)
		}
		if !target.IsDir {
			return nil, errors.New("目标必须是目录")
		}
	}

	return f.UpdateFile(ctx, &entity.FileEntity{
		ID:  fileID,
		PID: newPID,
	})
}

// validateFileName 验证文件名
func (f *FileDomain) validateFileName(name string) error {
	if name == "" {
		return errors.New("文件名不能为空")
	}

	// 检查文件名长度
	if len(name) > 255 {
		return errors.New("文件名长度不能超过255个字符")
	}

	// 检查非法字符
	invalidChars := []string{"<", ">", ":", "\"", "|", "?", "*"}
	for _, char := range invalidChars {
		if strings.Contains(name, char) {
			return fmt.Errorf("文件名不能包含字符: %s", char)
		}
	}

	// 检查保留名称
	reserved := []string{"CON", "PRN", "AUX", "NUL", "COM1", "COM2", "COM3", "COM4", "COM5", "COM6", "COM7", "COM8", "COM9", "LPT1", "LPT2", "LPT3", "LPT4", "LPT5", "LPT6", "LPT7", "LPT8", "LPT9"}
	baseName := strings.ToUpper(strings.TrimSuffix(name, filepath.Ext(name)))
	for _, reservedName := range reserved {
		if baseName == reservedName {
			return fmt.Errorf("文件名不能使用保留名称: %s", reservedName)
		}
	}

	return nil
}
