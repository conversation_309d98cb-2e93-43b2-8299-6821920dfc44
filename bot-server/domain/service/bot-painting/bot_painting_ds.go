package bot_painting

import (
	"bot/domain/entity"
	"bot/domain/repository"
	"bot/infrastructure/po"
	"context"
)

type PaintingManageDomain struct {
	repository.IPaintingConfig
	repository.IPaintingNegativePrompt
	repository.IPaintingRecord
}

func NewPaintingManageDomain(paintingConfig repository.IPaintingConfig, paintingNegativePrompt repository.IPaintingNegativePrompt, paintingRecord repository.IPaintingRecord) *PaintingManageDomain {
	return &PaintingManageDomain{
		IPaintingConfig:         paintingConfig,
		IPaintingNegativePrompt: paintingNegativePrompt,
		IPaintingRecord:         paintingRecord,
	}
}

// QueryPaintingConfigs 查询所有绘画配置
func (painting *PaintingManageDomain) QueryPaintingConfigs(ctx context.Context) ([]*po.PaintingConfigPO, error) {
	return painting.IPaintingConfig.QueryPaintingConfigs(ctx)
}

// QueryPaintingConfigsByCondition 根据条件查询绘画配置
func (painting *PaintingManageDomain) QueryPaintingConfigsByCondition(ctx context.Context, paintingConfigEntity *entity.PaintingConfigEntity) ([]*po.PaintingConfigPO, error) {
	return painting.IPaintingConfig.QueryPaintingConfigsByCondition(ctx, paintingConfigEntity)
}

// CreatePaintingConfig 创建绘画配置
func (painting *PaintingManageDomain) CreatePaintingConfig(ctx context.Context, paintingConfigEntity *entity.PaintingConfigEntity) (*po.PaintingConfigPO, error) {
	return painting.IPaintingConfig.CreatePaintingConfig(ctx, paintingConfigEntity)
}

// UpdatePaintingConfig 更新绘画配置
func (painting *PaintingManageDomain) UpdatePaintingConfig(ctx context.Context, paintingConfigEntity *entity.PaintingConfigEntity) (*po.PaintingConfigPO, error) {
	return painting.IPaintingConfig.UpdatePaintingConfig(ctx, paintingConfigEntity)
}

// DeletePaintingConfig 删除绘画配置
func (painting *PaintingManageDomain) DeletePaintingConfig(ctx context.Context, paintingConfigEntity *entity.PaintingConfigEntity) error {
	return painting.IPaintingConfig.DeletePaintingConfig(ctx, paintingConfigEntity)
}

// GetPaintingConfig 获取单个绘画配置
func (painting *PaintingManageDomain) GetPaintingConfig(ctx context.Context, paintingConfigEntity *entity.PaintingConfigEntity) (*po.PaintingConfigPO, error) {
	return painting.IPaintingConfig.GetPaintingConfig(ctx, paintingConfigEntity)
}

// QueryPaintingNegativePrompts 查询所有负面提示词
func (painting *PaintingManageDomain) QueryPaintingNegativePrompts(ctx context.Context) ([]*po.PaintingNegativePromptPO, error) {
	return painting.IPaintingNegativePrompt.QueryPaintingNegativePrompts(ctx)
}

// QueryPaintingNegativePromptsByCondition 根据条件查询负面提示词
func (painting *PaintingManageDomain) QueryPaintingNegativePromptsByCondition(ctx context.Context, paintingNegativePromptEntity *entity.PaintingNegativePromptEntity) ([]*po.PaintingNegativePromptPO, error) {
	return painting.IPaintingNegativePrompt.QueryPaintingNegativePromptsByCondition(ctx, paintingNegativePromptEntity)
}

// CreatePaintingNegativePrompt 创建负面提示词
func (painting *PaintingManageDomain) CreatePaintingNegativePrompt(ctx context.Context, paintingNegativePromptEntity *entity.PaintingNegativePromptEntity) (*po.PaintingNegativePromptPO, error) {
	return painting.IPaintingNegativePrompt.CreatePaintingNegativePrompt(ctx, paintingNegativePromptEntity)
}

// UpdatePaintingNegativePrompt 更新负面提示词
func (painting *PaintingManageDomain) UpdatePaintingNegativePrompt(ctx context.Context, paintingNegativePromptEntity *entity.PaintingNegativePromptEntity) (*po.PaintingNegativePromptPO, error) {
	return painting.IPaintingNegativePrompt.UpdatePaintingNegativePrompt(ctx, paintingNegativePromptEntity)
}

// DeletePaintingNegativePrompt 删除负面提示词
func (painting *PaintingManageDomain) DeletePaintingNegativePrompt(ctx context.Context, paintingNegativePromptEntity *entity.PaintingNegativePromptEntity) error {
	return painting.IPaintingNegativePrompt.DeletePaintingNegativePrompt(ctx, paintingNegativePromptEntity)
}

// GetPaintingNegativePrompt 获取单个负面提示词
func (painting *PaintingManageDomain) GetPaintingNegativePrompt(ctx context.Context, paintingNegativePromptEntity *entity.PaintingNegativePromptEntity) (*po.PaintingNegativePromptPO, error) {
	return painting.IPaintingNegativePrompt.GetPaintingNegativePrompt(ctx, paintingNegativePromptEntity)
}

// QueryPaintingRecords 查询所有绘画记录
func (painting *PaintingManageDomain) QueryPaintingRecords(ctx context.Context) ([]*po.PaintingPO, error) {
	return painting.IPaintingRecord.QueryPaintingRecords(ctx)
}

// QueryPaintingRecordsByCondition 根据条件查询绘画记录
func (painting *PaintingManageDomain) QueryPaintingRecordsByCondition(ctx context.Context, paintingRecordEntity *entity.PaintingRecordEntity) ([]*po.PaintingPO, error) {
	return painting.IPaintingRecord.QueryPaintingRecordsByCondition(ctx, paintingRecordEntity)
}

// CreatePaintingRecord 创建绘画记录
func (painting *PaintingManageDomain) CreatePaintingRecord(ctx context.Context, paintingRecordEntity *entity.PaintingRecordEntity) (*po.PaintingPO, error) {
	return painting.IPaintingRecord.CreatePaintingRecord(ctx, paintingRecordEntity)
}

// UpdatePaintingRecord 更新绘画记录
func (painting *PaintingManageDomain) UpdatePaintingRecord(ctx context.Context, paintingRecordEntity *entity.PaintingRecordEntity) (*po.PaintingPO, error) {
	return painting.IPaintingRecord.UpdatePaintingRecord(ctx, paintingRecordEntity)
}

// DeletePaintingRecord 删除绘画记录
func (painting *PaintingManageDomain) DeletePaintingRecord(ctx context.Context, paintingRecordEntity *entity.PaintingRecordEntity) error {
	return painting.IPaintingRecord.DeletePaintingRecord(ctx, paintingRecordEntity)
}

// GetPaintingRecord 获取单个绘画记录
func (painting *PaintingManageDomain) GetPaintingRecord(ctx context.Context, paintingRecordEntity *entity.PaintingRecordEntity) (*po.PaintingPO, error) {
	return painting.IPaintingRecord.GetPaintingRecord(ctx, paintingRecordEntity)
}
