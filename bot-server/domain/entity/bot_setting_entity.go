package entity

// SettingEntity
// 系统设置 对应 setting_init.json
type SettingEntity struct {

	// Map 定义地图开放平台配置
	Map MapSetting `json:"map"`
	// tts 语音配置
	TTS TtsSetting `json:"tts"`

	// 标识第一次安装客户端 true 标识 不是第一次进入客户端
	Install bool `json:"install"`
}

type MapSetting struct {
	CurrentMap string             `json:"currentMap"` // CurrentMap 当前用户首选地图 对应的地图 Type
	List       []MapSettingEntity `json:"list"`       // List 系统支持的地图平台列表
}

type TtsSetting struct {
	CurrentTTS string             `json:"currentTTS"` // CurrentTTS 当前用户首选地图 对应的地图 Type
	List       []TTSSettingEntity `json:"list"`       // List 系统支持的地图平台列表
}

// GetCurrentMapConfig
// 获取当前地图平台配置
func (setting *SettingEntity) GetCurrentMapConfig() MapSettingEntity {
	for _, entity := range setting.Map.List {
		if setting.Map.CurrentMap == entity.Type {
			return entity
		}
	}
	return MapSettingEntity{} // 若未找到匹配的地图，返回空
}

// MapSettingEntity
// 地图平台配置细节，可根据情况扩充配置项
type MapSettingEntity struct {
	Name string `json:"name"` // Name 地图平台名称
	Type string `json:"type"` // Type 地图平台类型
	Key  string `json:"key"`  // Key  地图平台 api key
}

type TTSSettingEntity struct {
	Name string `json:"type"` // Name 地图平台类型
	API  string `json:"api"`  // Name 地图平台名称
	Key  string `json:"key"`  // Key  地图平台 api key
}
