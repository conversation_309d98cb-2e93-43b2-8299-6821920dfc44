package entity

import (
	"database/sql"
	"time"
)

// PaintingRecordEntity 绘画记录实体
type PaintingRecordEntity struct {
	ID        string       `json:"id"`        // 绘画ID
	Title     string       `json:"title"`     // 绘画名称
	Prompt    string       `json:"prompt"`    // 绘画提示词
	Status    string       `json:"status"`    // 绘画状态
	ImageUrls string       `json:"imageUrls"` // 绘画图片URL 多个图片URL用逗号分隔
	CreatedAt time.Time    `json:"createdAt"` // 创建时间
	UpdatedAt time.Time    `json:"updatedAt"` // 更新时间
	DeletedAt sql.NullTime `json:"deletedAt"` // 删除时间
}

// GetId 获取绘画ID
func (p *PaintingRecordEntity) GetId() string {
	return p.ID
}

// GetTitle 获取绘画名称
func (p *PaintingRecordEntity) GetTitle() string {
	return p.Title
}

// GetPrompt 获取绘画提示词
func (p *PaintingRecordEntity) GetPrompt() string {
	return p.Prompt
}

// GetStatus 获取绘画状态
func (p *PaintingRecordEntity) GetStatus() string {
	return p.Status
}

// GetImageUrls 获取绘画图片URL
func (p *PaintingRecordEntity) GetImageUrls() string {
	return p.ImageUrls
} 