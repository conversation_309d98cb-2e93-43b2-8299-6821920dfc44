package entity

import (
	"bot/domain/service/bot/sdk/fun"
	"bot/domain/service/bot/sdk/sse"
	"database/sql"
	"encoding/json"
	"time"
)

// HistoryEntity
// 标准历史数据结构
type HistoryEntity struct {
	Role      string         `json:"role"`                // Role one of ["system", "user", "assistant","tool"]
	Content   any            `json:"content"`             // Content 消息正文内容
	Images    [][]byte       `json:"images,omitempty"`    // Images Ollama[传递图片二进制]
	ToolCall  []fun.ToolCall `json:"tool_call,omitempty"` // 函数调用定义
	Tool      any
	ToolCalls any // mcp 调用结果集合 用于提示词模版生成调用历史数据 any 可用于定制不同的格式
}

type ChatEntity struct {
	ID             string           `json:"id" form:"id" binding:"required"`                         // ID 消息id
	Message        string           `json:"message" binding:"required"`                              // Message 消息正文
	Images         []string         `json:"images"`                                                  // Images 图片数据，存储的数据时用户本地路径
	Role           string           `json:"role" form:"role" binding:"required"`                     // Role ["system", "user", "assistant"]
	MessageType    string           `json:"messageType" form:"messageType" binding:"required"`       // MessageType 消息类型一般是MD格式
	ReplyMsgID     string           `json:"replyMsgID" form:"replyMsgID" binding:"required"`         // ReplyMsgID 回复的消息id
	ConversationID string           `json:"conversationID" form:"conversationID" binding:"required"` // ConversationID 会话id
	History        []*HistoryEntity `json:"history,omitempty"`                                       // History 消息列表
	Stream         bool             `json:"stream,omitempty"`                                        // Stream  是否流式响应
	PluginID       string           `form:"pluginID" json:"pluginID"`
	PlatformID     string           `form:"platformID" json:"platformID"`
	WebSearch      bool             `form:"webSearch" json:"webSearch"`
	Thinking       bool             `form:"thinking" json:"thinking"`
	Mcp            []string         `form:"mcp" json:"mcp"`
	Agent          bool             `form:"agent" json:"agent"`
}

func (c *ChatEntity) Clone() *ChatEntity {
	list := make([]*HistoryEntity, len(c.History))
	for i, v := range c.History {
		list[i] = &HistoryEntity{
			Role:     v.Role,
			Content:  v.Content,
			Images:   v.Images,
			ToolCall: v.ToolCall,
		}
	}
	return &ChatEntity{
		ID:             c.ID,
		Message:        c.Message,
		Images:         c.Images,
		Role:           c.Role,
		MessageType:    c.MessageType,
		ReplyMsgID:     c.ReplyMsgID,
		ConversationID: c.ConversationID,
		History:        list,
		Mcp:            c.Mcp,
	}
}

type WriterEntity struct {
	*ChatEntity
}

// PaintingEntity
// 绘画请求实体
type PaintingEntity struct {
	// 基础信息
	*ChatEntity
	PromptTemplate    string `json:"promptTemplate"`     // 提示词模版 一般用于特定场景的固定提示词
	NumInferenceSteps int    `json:"numInferenceSteps"` // 推理步数
	GuidanceScale     int    `json:"guidanceScale"`      // 引导强度
	NegativePrompt    string `json:"negativePrompt"`     // 负面提示词
	Prompt            string `json:"prompt"`              // 提示词
	Size              string `json:"size"`                // 尺寸
	Height            int    `json:"height"`              // 高度
	Width             int    `json:"width"`               // 宽度
	ImageCount        int    `json:"imageCount"`         // 每次绘画的图片数量
}

type PaintingNegativePromptEntity struct {
	ID        string       `json:"id"`        // ID 绘画负面提示词ID
	Prompt    string       `json:"prompt"`    // Prompt 绘画负面提示词
	CreatedAt time.Time    `json:"createdAt"` // 创建时间
	UpdatedAt time.Time    `json:"updatedAt"` // 更新时间
	DeletedAt sql.NullTime `json:"deletedAt"` // 删除时间
}

// PaintingConfigEntity 绘画配置实体
type PaintingConfigEntity struct {
	ID        string       `json:"id"`        // ID 绘画配置ID
	Config    string       `json:"config"`    // Config 绘画配置 json 格式
	CreatedAt time.Time    `json:"createdAt"` // 创建时间
	UpdatedAt time.Time    `json:"updatedAt"` // 更新时间
	DeletedAt sql.NullTime `json:"deletedAt"` // 删除时间
}

type ConversationEntity struct {
	ID       string `json:"id,omitempty"`       // ID 会话id
	Picture  string `json:"picture,omitempty"`  // Picture 头像
	Title    string `json:"title,omitempty"`    // Title 标题
	LastMsg  string `json:"last_msg,omitempty"` // LastMsg 最后消息
	UpdateAt string `json:"updateAt,omitempty"` // LastTime 最后消息时间
}

type MessageEntity struct {
	ID             string `json:"id"`             // ID 消息id
	ConversationID string `json:"conversationID"` // ConversationID 会话id
	Picture        string `json:"picture"`        // Picture 头像
	ReplyMsgID     string `json:"ReplyMsgID"`     // ReplyMsgID 回复的消息id
	Role           string `json:"role"`           // Role 消息的角色
	MessageType    string `json:"messageType"`    // MessageType 消息类型
	Ex             string `json:"ex"`             // Ex 扩展
	Content        string `json:"content"`        // Content 消息内容
}

// PluginEntity LLM模型插件
type PluginEntity struct {
	ID         string `json:"id"`
	Name       string `json:"name"`       // ID 模型名称
	Code       string `json:"code"`       // Code 模型代码
	Icon       string `json:"icon"`       // Icon 图标
	PlatformID string `json:"platformID"` // PlatformID 所属平台
	ConfigView string `json:"configView"` // ConfigView 窗口
	Props      string `json:"props"`      // Props 属性
	Status     bool   `json:"status"`     // Status 状态
}

// PlatformEntity
// LLM 能力平台
type PlatformEntity struct {
	ID              string `json:"id"`              // ID 平台id
	Name            string `json:"name"`            // Name 平台名称
	Icon            string `json:"icon"`            // 图标
	Type            string `json:"type"`            // Type 平台类型
	Config          string `json:"config"`          // Config 配置项
	QuickConfigView string `json:"quickConfigView"` // QuickConfigView 快捷配置面板
	ConfigView      string `json:"configView"`      // ConfigView 配置面板
	Current         bool   `json:"current"`
}

type MessageExtendEntity struct {
	DebugINFO DebugINFO `json:"debugINFO"`
}

func (m *MessageExtendEntity) Ex() string {
	json, _ := json.Marshal(m)
	return string(json)
}

type DebugINFO struct {
	Tokens string     `json:"token"`
	Status sse.Status `json:"status"`
	ErrMsg string     `json:"errMsg"`
}

type ModelEntity struct {
	// 模型名称
	Name string `json:"name,omitempty"`
	// 模型描述
	Description string `json:"description,omitempty"`
	// 模型类型
	Type string `json:"type,omitempty"`
}
