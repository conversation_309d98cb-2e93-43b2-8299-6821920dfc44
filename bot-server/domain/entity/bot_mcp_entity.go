package entity

type McpTool struct {
	Name        string ` json:"name"`
	Description string ` json:"description"`
}

type McpEntity struct {
	ID       string            ` json:"id"`
	Type     string            ` json:"type"`     // mcp 协议类型
	Name     string            ` json:"name"`     // mcp 名称
	Command  string            ` json:"command"`  // 执行命令
	Args     []string          ` json:"args"`     // 命令参数
	Env      map[string]string ` json:"env"`      // 环境变量
	Disabled int               ` json:"disabled"` // 是否启用
	Timeout  int               ` json:"timeout"`  // 接口超时配置
	Tools    []McpTool         ` json:"tools"`    // mcp 可调用工具列表
}
