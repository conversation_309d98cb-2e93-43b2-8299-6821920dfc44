package entity

type PlatformConfig struct {
	// api 接口
	Api string `json:"api"`
	// api token
	Key string `json:"key"`
	// 调用模型
	CurrentModel string `json:"currentModel"`

	// 聊天模型
	ChatModel string `json:"chatModel"`

	// 视觉模型
	ViewImageModel string `json:"viewImageModel"`

	// OCR 模型
	OcrImageModel string `json:"ocrImageModel"`

	// 视频模型
	VideoModel string `json:"videoModel"`

	AccessKeyID     string `json:"accessKeyID"`
	SecretAccessKey string `json:"secretAccessKey"`
	Region          string `json:"region"`
}

type BaseConfig struct {
	Api          string `json:"api"`
	Key          string `json:"key"`
	CurrentModel string `json:"currentModel"`
}

// OllamaConfigEntity
// Ollama 平台运行配置
type OllamaConfigEntity struct {
	BaseConfig
}

// DeepSeekConfigEntity
// DeepSeek 运行配置
type DeepSeekConfigEntity struct {
	BaseConfig
	CurrentModel string `json:"currentModel"`
}

type GiteeAIConfigEntity struct {
	BaseConfig
}

type VolcengineConfigEntity struct {
	BaseConfig
	AccessKeyID     string `json:"accessKeyID"`
	SecretAccessKey string `json:"secretAccessKey"`
	Region          string `json:"region"`
}
