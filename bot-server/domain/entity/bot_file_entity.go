package entity

import (
	"database/sql"
	"time"
)

// FileEntity 文件实体
type FileEntity struct {
	ID        string       `json:"id"`        // 文件ID
	PID       string       `json:"pid"`       // 父级ID
	Name      string       `json:"name"`      // 文件名称
	Size      int64        `json:"size"`      // 文件大小
	IsDir     bool         `json:"isDir"`     // 是否是目录
	CreatedAt time.Time    `json:"createdAt"` // 创建时间
	UpdatedAt time.Time    `json:"updatedAt"` // 更新时间
	DeletedAt sql.NullTime `json:"deletedAt"` // 删除时间
}

// GetId 获取文件ID
func (f *FileEntity) GetId() string {
	return f.ID
}

// GetPid 获取父级ID
func (f *FileEntity) GetPid() string {
	return f.PID
}

// GetName 获取文件名称
func (f *FileEntity) GetName() string {
	return f.Name
}

// IsDirectory 判断是否是目录
func (f *FileEntity) IsDirectory() bool {
	return f.IsDir
}

// GetSize 获取文件大小
func (f *FileEntity) GetSize() int64 {
	return f.Size
}
