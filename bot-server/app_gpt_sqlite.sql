drop table if exists app_setting;
create table app_setting
(
    id          varchar(30) primary key,
    name        varchar(100) not null,
    value       varchar(500) not null,
    setting     json                  default '{}',
    create_time timestamp(0) not null default current_timestamp
);

drop table if exists app_chat_conversation;
create table app_chat_conversation
(
    id          varchar(30) primary key,
    picture     varchar(500)          default '',
    title       varchar(100) not null,
    last_model  varchar(30)           default '',
    last_msg    text                  default '',
    last_time   timestamp(0) not null default current_timestamp,
    is_delete   int          not null default 0,
    create_time timestamp(0) not null default current_timestamp
);

drop table if exists app_chat_message;
create table app_chat_message
(
    id              varchar(30) primary key,
    conversation_id varchar(30)  not null,
    reply_msg_id    varchar(30)  not null,
    model_id        varchar(30)  not null,
    picture         varchar(100)          default '',
    role            varchar(30)  not null,
    ex text,
    message_type    int,
    content         text,
    is_delete       int          not null default 0,
    create_time     timestamp(0) not null default current_timestamp
);

drop table if exists app_chat_model;
create table app_chat_model
(
    id            varchar(30) primary key,
    pid           varchar(30)  not null,
    name          varchar(100) not null,
    model         varchar(100) not null,
    picture       varchar(100)          default '',
    size          varchar(50)  not null,
    digest        varchar(100)          default '',
    model_details json                  default '{}',
    is_download   boolean               default false,
    create_time   timestamp(0) not null default current_timestamp
);
create index model_key on app_chat_model (model);


drop table if exists app_chat_knowledge_file;
create table app_chat_knowledge_file
(
    id          varchar(30) primary key,
    pid         varchar(30)  not null,
    file_name   varchar(100) not null,
    file_path   varchar(100) not null,
    file_type   int          not null,
    create_time timestamp(0) not null default ''
);


drop table if exists app_chat_knowledge_instance;
create table app_chat_knowledge_instance
(
    id                    varchar(30) primary key,
    knowledge_name        varchar(500) not null default '',
    knowledge_files       json         not null default '[]',
    knowledge_description varchar(500) not null default '',
    knowledge_type        int          not null default 0,
    create_time           timestamp(0) not null default current_timestamp
);

drop table if exists app_chat_plugin;
create table app_chat_plugin
(
    id          varchar(30) primary key,-- 插件id
    name        varchar(200) not null,-- 插件名
    code        varchar(100) not null,-- 插件代码
    icon        varchar(100)          default '',--插件图标
    model       varchar(100)          default '',--插件模型
    float_view  varchar(100)          default '',--插件悬浮菜单面板组件
    props       json                  default '{}',--插件悬浮菜单面板组件属性
    status      boolean               default true,--状态 0:未启用 1:启用
    create_time timestamp(0) not null default current_timestamp
);

/*
insert into app_chat_plugin(id, name, code, icon, model)
VALUES (1, 'AI 助手', 'default', 'jimu-ChatGPT', 'qwen2:7b');

insert into app_chat_plugin(id, name, code, icon, model, float_view)
VALUES (2, '编程助手', 'programming', 'jimu-code', 'llama3:latest', 'ProgrammingAssistantPanelView');

insert into app_chat_plugin(id, name, code, icon, model, float_view)
VALUES (3, '知识库', 'knowledge', 'jimu-zhishi', 'qwen2:7b', 'KnowledgePanelView');
*/

-- 文件系统表（支持软删除）
drop table if exists bot_file;
create table bot_file
(
    id          varchar(50) primary key,              -- 文件ID
    pid         varchar(50)         default '',       -- 父级ID，根目录为空
    name        varchar(255) not null,                -- 文件名称
    size        bigint              default 0,        -- 文件大小（字节）
    is_dir      boolean             default false,    -- 是否是目录
    created_at  timestamp(0) not null default current_timestamp, -- 创建时间
    updated_at  timestamp(0) not null default current_timestamp, -- 更新时间
    deleted_at  timestamp(0)        default null      -- 删除时间（软删除，NULL表示未删除）
);

-- 创建索引以优化查询性能
create index idx_bot_file_pid on bot_file (pid);                    -- 父级ID索引
create index idx_bot_file_deleted_at on bot_file (deleted_at);      -- 软删除索引
create index idx_bot_file_name on bot_file (name);                  -- 文件名索引
create index idx_bot_file_is_dir on bot_file (is_dir);             -- 目录类型索引

-- 复合索引用于常见查询场景
create index idx_bot_file_pid_deleted on bot_file (pid, deleted_at); -- 按父级查询未删除文件
create index idx_bot_file_pid_dir_deleted on bot_file (pid, is_dir, deleted_at); -- 按父级和类型查询未删除文件
