{{/* 移除顶层 Messages 存在性校验，直接渲染所有内容 */}}
{{- if or .System .Tools }}<|start_header_id|>system<|end_header_id|>

{{/* ################ 强化指令约束层 ################ */}}
<format_constraints>
1. 禁止直接暴露<tool_call>原始数据结构
2. 严格过滤非对话标签字符（如<|start_header_id|>）
</format_constraints>


{{/* #################### 元标签学习层 #################### */}}
<mcp_instruction>
  <tag_def>
    <name>start_header_id</name>
    <meaning>对话角色区块开始标记，后接 system/user/assistant/tool 等角色名</meaning>
    <usage>必须与对应的 end_header_id 成对出现</usage>
  </tag_def>
  <tag_def>
    <name>eot_id</name>
    <meaning>End of Turn，表示当前角色的发言结束</meaning>
    <usage>每条消息末尾必须且只能出现一次</usage>
  </tag_def>

  <tag_def>
    <name>mcp_info</name>
    <meaning>内部存放是的是分组工具对应 分组的描述信息有助于对话中对比查看</meaning>
    <usage>在综合总结内容时候，可能会需要运用到对应的信息输出内容</usage>
  </tag_def>

  <tag_def>
    <name>mcp_tools</name>
    <meaning>内部存放的是 工具调用集合，没个工具调用集合通过对应分组都可以在 mcp_info 找到对应的信息说明</meaning>
    <usage>在综合总结内容时候，可能会需要运用到对应的信息输出内容</usage>
  </tag_def>
</mcp_instruction>
{{/* #################################################### */}}


{{ .System }}
{{- if.Tools}} gives you a grouping function signature in the <mcp_tools></mcp_tools> XML tag. Return an empty tag when tools are not needed: <tool call></tool call>. When continuous tool calls are needed, add the field "isContinue": true. Analyze the group's background before you call. Returns calls grouped by original category in the <tool_call></tool_call> XML tag, disallowing comments entered in <tool_call></tool_call> as follows:<tool_call>
{
  "isContinue":<Whether to continue calling: true if yes, false if no.>,
  "<group-id>": [
    {"name": <function-name>, "arguments": <args-dict>},
    ...
  ],
  ...
}
</tool_call>


{{- if.McpTool}} {{/* 新增 mcp_list 标签 */}}
<mcp_info>
{{.McpTool |json}}
</mcp_info>
{{- end}}

Here are the available tools:
<mcp_tools>
{{.Tools |json}}
</mcp_tools>
{{- end }}<|eot_id|>
{{- end }}

{{- /* 消息列表处理 */}}
{{- range .Messages }}
{{- if ne .Role "system" }}<|start_header_id|>{{ .Role }}<|end_header_id|>
{{ if eq .Role "user" }}
{{ .Content }}
{{- else if eq .Role "assistant" }}
{{- if .Content }}{{ .Content }}
{{- else if .ToolCalls }}
<tool_call>
{{.ToolCalls |json}}
</tool_call>
{{- end }}
{{- else if eq .Role "tool" }}
<tool_response>
{{ .Content }}
</tool_response>
{{- end }}<|eot_id|>
{{- end }}
{{- end }}

{{- /* 独立提示词处理 */}}
{{- if .Prompt }}<|start_header_id|>user<|end_header_id|>
{{ .Prompt }}<|eot_id|>
{{- end }}