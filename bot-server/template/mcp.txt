{{/* 移除顶层 Messages 存在性校验，直接渲染所有内容 */}}
{{- if or .System .Tools }}<|start_header_id|>system<|end_header_id|>

{{/* #################### 元标签学习层 #################### */}}
<mcp_instruction>
  <tag_def>
    <name>start_header_id</name>
    <meaning>对话角色区块开始标记，后接 system/user/assistant/tool 等角色名</meaning>
    <usage>必须与对应的 end_header_id 成对出现</usage>
  </tag_def>
  <tag_def>
    <name>eot_id</name>
    <meaning>End of Turn，表示当前角色的发言结束</meaning>
    <usage>每条消息末尾必须且只能出现一次</usage>
  </tag_def>

  <tag_def>
    <name>mcp_info</name>
    <meaning>内部存放是的是分组工具对应 分组的描述信息有助于对话中对比查看</meaning>
    <usage>在综合总结内容时候，可能会需要运用到对应的信息输出内容</usage>
  </tag_def>

  <tag_def>
    <name>mcp_tools</name>
    <meaning>内部存放的是 工具调用集合，没个工具调用集合通过对应分组都可以在 mcp_info 找到对应的信息说明</meaning>
    <usage>在综合总结内容时候，可能会需要运用到对应的信息输出内容</usage>
  </tag_def>
</mcp_instruction>
{{/* #################################################### */}}


{{ .System }}
{{- if.Tools}} gives you a grouping function signature in the <mcp_tools></mcp_tools> XML tag. Analyze the group's background before you call. Returns calls grouped by original category in the <tool_call></tool_call> XML tag, disallowing comments entered in <tool_call></tool_call> as follows:<tool_call>
{
  "<group-name>": [
    {"name": <function-name>, "arguments": <args-dict>},
    ...
  ],
  ...
}
</tool_call>

{{- if.McpTool}} {{/* 新增 mcp_list 标签 */}}
<mcp_info>
{{.McpTool |json}}
</mcp_info>
{{- end}}

Here are the available tools:
<mcp_tools>
{{.Tools |json}}
</mcp_tools>
{{- end }}<|eot_id|>
{{- end }}

{{- /* 消息列表处理 */}}
{{- range .Messages }}
{{- if ne .Role "system" }}<|start_header_id|>{{ .Role }}<|end_header_id|>
{{ if eq .Role "user" }}{{ .Content }}
{{- else if eq .Role "assistant" }}
{{- if .Content }}{{ .Content }}
{{- else if .ToolCalls }}
<tool_call>
{{.ToolCalls |json}}
</tool_call>
{{- end }}
{{- else if eq .Role "tool" }}<tool_response>
{{ .Content }}
</tool_response>
{{- end }}<|eot_id|>
{{- end }}
{{- end }}

{{- /* 独立提示词处理 */}}
{{- if .Prompt }}<|start_header_id|>user<|end_header_id|>
{{ .Prompt }}<|eot_id|>
{{- end }}

{{- /* 新增总结归纳指令 */}}
{{- if .NeedSummary }}<|start_header_id|>assistant<|end_header_id|>
# 第一阶段 - 结构化解析
<mcp_analysis_flow>
    1 逆向追溯最近所有对话
    2 识别工具调用触发词
    3 映射到对应工具分组
    4 对每个工具调用的进行总结调用原因
</mcp_analysis_flow>

# 第二阶段 - 规范输出

<mcp_validation_rules>
1. 格式校验层：
   ✓ 严格校验JSON括号配对
   ✓ 强制字段顺序：mcp→tool→value→arguments
   ✓ 字符串值必须双引号包裹

2. 语义校验层：
   ✓ mcp字段必须匹配最近5轮内的<tool_call>分组名
   ✓ tool字段必须存在于对应分组的工具列表
   ✓ arguments键值必须100%复制原始调用参数

3. 数据溯源层：
   ✓ value字段必须取自最近匹配<tool_response>的数值路径
   ✓ 时间范围参数必须与响应数据时间戳对齐
   ✓ 数值类型需保持原始精度（如92.5%转为0.925浮点数）
</mcp_validation_rules>

:::mcp
{
  "mcp":<对应分组名>,
  "tool": <具体方法名称>,
  "value":<对应函数调用value>
  "arguments": {
    <arg1>: <value1>,
    <arg2>: <value2>,
    ...
  },
}
:::

# 第三阶段 - 知识整合
✦ 采用「总-分-总」结构
✦ 关键数据用**强调标记**
✦ 保留原始参数数值
✦ 时间线用 → 符号连接
✦ 禁用任何代码块
✦ 当前的要求内容不应该出现在思考和回答内容中

最终总结应呈现为：<|eot_id|>
{{- end }}