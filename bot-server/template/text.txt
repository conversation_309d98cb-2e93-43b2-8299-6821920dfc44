"<|start_header_id|>system<|end_header_id|>\r\n\r\n\r\n<mcp_instruction>\r\n  <tag_def>\r\n    <name>start_header_id</name>\r\n    <meaning>对话角色区块开始标记，后接 system/user/assistant/tool 等角色名</meaning>\r\n    <usage>必须与对应的 end_header_id 成对出现</usage>\r\n  </tag_def>\r\n  <tag_def>\r\n    <name>eot_id</name>\r\n    <meaning>End of Turn，表示当前角色的发言结束</meaning>\r\n    <usage>每条消息末尾必须且只能出现一次</usage>\r\n  </tag_def>\r\n\r\n  <tag_def>\r\n    <name>mcp_info</name>\r\n    <meaning>内部存放是的是分组工具对应 分组的描述信息有助于对话中对比查看</meaning>\r\n    <usage>在综合总结内容时候，可能会需要运用到对应的信息输出内容</usage>\r\n  </tag_def>\r\n\r\n  <tag_def>\r\n    <name>mcp_tools</name>\r\n    <meaning>内部存放的是 工具调用集合，没个工具调用集合通过对应分组都可以在 mcp_info 找到对应的信息说明</meaning>\r\n    <usage>在综合总结内容时候，可能会需要运用到对应的信息输出内容</usage>\r\n  </tag_def>\r\n</mcp_instruction>\r\n\r\n\r\n\r\n gives you a grouping function signature in the <mcp_tools></mcp_tools> XML tag. Analyze the group's background before you call. Returns calls grouped by original category in the <tool_call></tool_call> XML tag, disallowing comments entered in <tool_call></tool_call> as follows:<tool_call>\r\n{\r\n  \"<group-name>\": [\r\n    {\"name\": <function-name>, \"arguments\": <args-dict>},\r\n    ...\r\n  ],\r\n  ...\r\n}\r\n</tool_call> \r\n<mcp_info>\r\n{\n  \"dd6843fa-1e07-44e0-a328-bb7f1823e509\": \"mcp-server/amap-maps\"\n}\r\n</mcp_info>\r\n\r\nHere are the available tools:\r\n<mcp_tools>\r\n{\n  \"dd6843fa-1e07-44e0-a328-bb7f1823e509\": [\n    {\n      \"description\": \"将一个高德经纬度坐标转换为行政区划地址信息\",\n      \"inputSchema\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"location\": {\n            \"description\": \"经纬度\",\n            \"type\": \"string\"\n          }\n        },\n        \"required\": [\n          \"location\"\n        ]\n      },\n      \"name\": \"maps_regeocode\"\n    },\n    {\n      \"description\": \"将详细的结构化地址转换为经纬度坐标。支持对地标性名胜景区、建筑物名称解析为经纬度坐标\",\n      \"inputSchema\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"address\": {\n            \"description\": \"待解析的结构化地址信息\",\n            \"type\": \"string\"\n          },\n          \"city\": {\n            \"description\": \"指定查询的城市\",\n            \"type\": \"string\"\n          }\n        },\n        \"required\": [\n          \"address\"\n        ]\n      },\n      \"name\": \"maps_geo\"\n    },\n    {\n      \"description\": \"IP 定位根据用户输入的 IP 地址，定位 IP 的所在位置\",\n      \"inputSchema\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"ip\": {\n            \"description\": \"IP地址\",\n            \"type\": \"string\"\n          }\n        },\n        \"required\": [\n          \"ip\"\n        ]\n      },\n      \"name\": \"maps_ip_location\"\n    },\n    {\n      \"description\": \"根据城市名称或者标准adcode查询指定城市的天气\",\n      \"inputSchema\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"city\": {\n            \"description\": \"城市名称或者adcode\",\n            \"type\": \"string\"\n          }\n        },\n        \"required\": [\n          \"city\"\n        ]\n      },\n      \"name\": \"maps_weather\"\n    },\n    {\n      \"description\": \"查询关键词搜或者周边搜获取到的POI ID的详细信息\",\n      \"inputSchema\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"id\": {\n            \"description\": \"关键词搜或者周边搜获取到的POI ID\",\n            \"type\": \"string\"\n          }\n        },\n        \"required\": [\n          \"id\"\n        ]\n      },\n      \"name\": \"maps_search_detail\"\n    },\n    {\n      \"description\": \"骑行路径规划用于规划骑行通勤方案，规划时会考虑天桥、单行线、封路等情况。最大支持 500km 的骑行路线规划\",\n      \"inputSchema\": {\n        \"type\": \"object\",\n        \"properties\": {\n  ...+5333 more"