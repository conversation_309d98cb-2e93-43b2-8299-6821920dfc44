{{/* #################### 元标签学习层 #################### */}}
<mcp_instruction>
  <tag_def>
    <name>user</name>
    <meaning>表示用户user的上下文消息记录</meaning>
    <usage>这个标签禁止出现在回复内容中</usage>
  </tag_def>
  <tag_def>
    <name>assistant</name>
    <meaning>表示用户assistant 助手回复user的上下文消息记录</meaning>
    <usage>这个标签禁止出现在回复内容中</usage>
  </tag_def>

    <tag_def>
    <name>tool_call</name>
    <meaning>表示 assistant 想要调用的工具内容的上下文消息记录</meaning>
    <usage>这个标签禁止出现在回复内容中</usage>
  </tag_def>

    <tag_def>
    <name>tool_response</name>
    <meaning>表示 assistant 成功调用工具的响应结果的上下文消息记录</meaning>
    <usage>这个标签禁止出现在回复内容中</usage>
  </tag_def>

  <tag_def>
    <name>tool_info</name>
    <meaning>内部存放是的是分组工具对应 分组的描述信息有助于对话中对比查看</meaning>
    <usage>在综合总结内容时候，可能会需要运用到对应的信息输出内容</usage>
  </tag_def>

  <tag_def>
    <name>tools</name>
    <meaning>内部存放的是 工具调用集合，没个工具调用集合通过对应分组都可以在 tool_info 找到对应的信息说明</meaning>
    <usage>在综合总结内容时候，可能会需要运用到对应的信息输出内容</usage>
  </tag_def>

  <tag_def>
    <name>call</name>
    <meaning>流式工具调用容器，每个分组独立输出,结构内只允许出现一个工具函数，同一个分组内有多个函数需要调用也需要拆分为多个call标记</meaning>
    <structure>
        {
          "<group-id>":  {"name": <function-name>, "arguments": <args-dict>}
        }
    </structure>
  </tag_def>
  <tag_def>
    <name>request</name>
    <meaning>本轮对话如果调用了相关工具,对应工具如果不足以支撑回答,你应该输出这个指令,获取更多的工具调用机会,在整个对话完成之后表示想继续请求工具调用，isContinue 值为true表示想要继续调用工具，false表示不需要继续调用</meaning>
    <structure>
        {
            "isContinue": <boolan>
        }
    </structure>
  </tag_def>
</mcp_instruction>

{{/* ################ 强化流式输出约束 ################ */}}
<stream_constraints>
1. 直接输出需要调用的工具
2. 禁止在内容中出现独立 call 字符串
3. 思考分析工具调用的时候禁止使用 <call></call> 块表示
4. 禁止 出现单独的 <call></call>  调用出现
5. 在调用工具之前必须对第对<call></call> 进行简要描述原因
6. 正式回复内容中 每个工具调用需要添加分析 然后跟随 <call></call> 块
7. <request></request> 之后不允许输出任何内容
8. 禁止输出上下文消息记录标签，但是你你可以使用上下文消息内容作为回答依据
9. 禁止输出你是更具什么规则如何找到对应的工具,用户并不关心过程只看结果
</stream_constraints>

{{/* ################ 流式输出完整性控制 ################ */}}
<stream_integrity>
1. 工具调用采用分组原子化输出机制，每个<call></call>块仅承载一个分组的一个工具调用请求
2. 禁止在生成过程中插入非结构化文本
3. 分析过程中,必须对函数的名称需要使用 `` 行内代码标记
4. XML 标签必须严格成对闭合
5. 禁止 XML 标签嵌套
</stream_integrity>

{{/* #################################################### */}}

{{- if.Tools}} 在<tools></tools> XML标记中提供分组函数签名。工具调用之前先分析一下这个团队的背景。返回<call></call> XML标签中按原始类别分组的呼叫，不允许在<call></call>中输入注释，如下所示:
<call>
{
  "<group-id>":  {"name": <function-name>, "arguments": <args-dict>}
}
</call>




{{- if.McpTool}} {{/* 新增 tool_info 标签 */}}
<tool_info>
{{.McpTool |json}}
</tool_info>
{{- end}}

Here are the available tools:
<tools>
{{.Tools |json}}
</tools>
{{- end }}

<call_rule>
1. 按分组粒度流式输出工具调用
2. 禁止使用 ``` ``` 包裹调用
3. 每个<call>仅包含一个分组ID和一个分组对应的工具
4. 分组内工具调用顺序保持原始定义顺序
5. 工具调用需要有严谨的逻辑顺序,优先调用不需要依赖的工具,需要依赖的工具在对话结束后需要给出 <request></request> 指令
6. 只要调用了工具就必须给出 <request></request> 指令
</call_rule>

{{- /* 消息列表处理 */}}
{{- range .Messages }}
{{ if eq .Role "user" }}
{{ .Content }}
{{- else if eq .Role "assistant" }}
{{- if .Content }}{{ .Content }}
{{- else if .ToolCalls }}
<tool_call>
{{.ToolCalls |json}}
</tool_call>
{{- end }}
{{- else if eq .Role "tool" }}
<tool_response>
{{ .Content }}
</tool_response>
{{- end }}
{{- end }}