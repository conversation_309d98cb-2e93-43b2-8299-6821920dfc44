package main

import (
	"context"
	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcp-go/server"
	"log"
	"time"
)

type MCPServer struct {
	server *server.MCPServer
}

func (s *MCPServer) ServeStdio() error {
	return server.ServeStdio(s.server)
}

func main() {
	s := NewMCPServer()
	if err := s.ServeStdio(); err != nil {
		log.Fatalf("Server error: %v", err)
	}
}

func NewMCPServer() *MCPServer {
	mcpServer := server.NewMCPServer(
		"time-mcp",
		"0.0.1",
		server.WithResourceCapabilities(true, true),
		server.WithPromptCapabilities(true),
		server.WithToolCapabilities(true),
	)
	mcpServer.AddTool(mcp.NewTool("get_now_time",
		mcp.WithDescription("获取当前的时间"),
	), GetNowTime)

	return &MCPServer{
		server: mcpServer,
	}
}

func GetNowTime(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	return mcp.NewToolResultText(time.Now().Format("2006-01-02 15:04:05")), nil
}
