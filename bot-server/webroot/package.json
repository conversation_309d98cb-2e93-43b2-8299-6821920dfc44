{"name": "a<PERSON><PERSON>", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite --mode development", "build": "vite build", "test": "vite --mode test", "produc": "vite --mode production"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@ckeditor/ckeditor5-alignment": "^43.1.1", "@ckeditor/ckeditor5-autoformat": "^43.1.1", "@ckeditor/ckeditor5-basic-styles": "^43.1.1", "@ckeditor/ckeditor5-block-quote": "^43.1.1", "@ckeditor/ckeditor5-clipboard": "^43.1.1", "@ckeditor/ckeditor5-code-block": "^43.1.1", "@ckeditor/ckeditor5-core": "^43.1.1", "@ckeditor/ckeditor5-dev-utils": "^39.9.1", "@ckeditor/ckeditor5-editor-balloon": "^43.1.1", "@ckeditor/ckeditor5-editor-decoupled": "^43.1.1", "@ckeditor/ckeditor5-engine": "^43.3.1", "@ckeditor/ckeditor5-enter": "^43.1.1", "@ckeditor/ckeditor5-essentials": "^43.1.1", "@ckeditor/ckeditor5-font": "^43.1.1", "@ckeditor/ckeditor5-heading": "^43.1.1", "@ckeditor/ckeditor5-highlight": "^43.1.1", "@ckeditor/ckeditor5-html-support": "^43.1.1", "@ckeditor/ckeditor5-image": "^43.1.1", "@ckeditor/ckeditor5-indent": "^43.1.1", "@ckeditor/ckeditor5-inspector": "^4.1.0", "@ckeditor/ckeditor5-link": "^43.1.1", "@ckeditor/ckeditor5-list": "^43.1.1", "@ckeditor/ckeditor5-markdown-gfm": "^43.1.1", "@ckeditor/ckeditor5-media-embed": "^43.1.1", "@ckeditor/ckeditor5-mention": "^43.1.1", "@ckeditor/ckeditor5-page-break": "^43.1.1", "@ckeditor/ckeditor5-paragraph": "^43.1.1", "@ckeditor/ckeditor5-style": "^43.1.1", "@ckeditor/ckeditor5-table": "^43.1.1", "@ckeditor/ckeditor5-theme-lark": "^43.1.1", "@ckeditor/ckeditor5-typing": "^43.1.1", "@ckeditor/ckeditor5-ui": "^43.1.1", "@ckeditor/ckeditor5-upload": "^43.1.1", "@ckeditor/ckeditor5-utils": "^43.1.1", "@ckeditor/ckeditor5-vue": "^5.1.0", "@ckeditor/ckeditor5-widget": "^43.1.1", "@ckeditor/ckeditor5-word-count": "^43.1.1", "@ckeditor/vite-plugin-ckeditor5": "^0.1.3", "@codemirror/commands": "^6.8.1", "@codemirror/lang-json": "^6.0.1", "@codemirror/language": "^6.11.0", "@codemirror/state": "^6.5.2", "@codemirror/theme-one-dark": "^6.1.2", "@codemirror/view": "^6.36.8", "@types/file-saver": "^2.0.7", "@types/uuid": "^9.0.8", "abort-controller": "^3.0.0", "animate.css": "^4.1.1", "ant-design-vue": "^4.2.6", "axios": "^1.7.2", "babel-plugin-prismjs": "^2.1.0", "codemirror": "^6.0.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "dedent": "^1.5.1", "dom-to-image": "^2.6.0", "electron-geolocation": "^1.0.2", "fast-diff": "^1.3.0", "file-saver": "^2.0.5", "form-data": "^4.0.0", "formdata-node": "^6.0.3", "formdata-polyfill": "^4.0.10", "gridstack": "^11.4.0", "hover.css": "^2.3.2", "html2canvas": "^1.4.1", "html2pdf": "^0.0.11", "iconfont-tools": "^1.7.13", "idb": "^8.0.2", "jspdf": "^2.5.2", "linkify-it": "^5.0.0", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "markdown-it": "^14.1.0", "markdown-it-abbr": "^2.0.0", "markdown-it-anchor": "^9.2.0", "markdown-it-cjk-breaks": "^2.0.0", "markdown-it-container": "^3.0.0", "markdown-it-deflist": "^3.0.0", "markdown-it-emoji": "^3.0.0", "markdown-it-footnote": "^4.0.0", "markdown-it-for-inline": "^2.0.1", "markdown-it-ins": "^4.0.0", "markdown-it-katex": "^2.0.3", "markdown-it-mark": "^4.0.0", "markdown-it-quote": "^1.0.4", "markdown-it-sub": "^2.0.0", "markdown-it-sup": "^2.0.0", "markdown-it-testgen": "^0.1.6", "marked": "^15.0.12", "mitt": "^3.0.0", "node-fetch": "^3.3.2", "ollama": "^0.5.14", "particles.js": "^2.0.0", "pinia": "^3.0.2", "pinia-plugin-persistedstate": "^4.3.0", "prismjs": "^1.29.0", "sass-loader": "^14.1.1", "tailwindcss": "^3.4.15", "turndown": "^7.2.0", "typed.js": "^2.1.0", "unplugin-auto-import": "^0.17.5", "unplugin-vue-components": "^0.27.4", "uuid": "^9.0.1", "v-contextmenu": "^3.0.0", "vite-plugin-html": "^3.2.2", "vue": "^3.5.12", "vue-i18n": "^9.2.2", "vue-monoplasty-slide-verify": "^1.3.1", "vue-router": "^4.4.5", "vue-virtual-scroller": "^2.0.0-beta.8", "vue3-draggable-resizable": "^1.6.5", "vue3-slide-verify": "^1.1.5", "vuedraggable": "^4.1.0"}, "devDependencies": {"@babel/types": "^7.18.10", "@quasar/vite-plugin": "^1.5.0", "@univerjs/vite-plugin": "^0.3.5", "@vitejs/plugin-vue": "^4.1.0", "sass": "^1.69.5", "typescript": "~5.6.0", "vite": "^5.4.10", "vite-plugin-electron": "^0.29.0", "vite-plugin-electron-renderer": "^0.14.6", "vite-plugin-env-compatible": "^2.0.1", "vue-tsc": "^2.1.6"}}