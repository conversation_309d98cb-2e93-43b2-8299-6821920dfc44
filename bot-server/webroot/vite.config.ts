// @ts-ignore
import {defineConfig, loadEnv} from 'vite'
// @ts-ignore
import vue from '@vitejs/plugin-vue'
// CKEditor
import {createRequire} from 'node:module';
import ckeditor5 from '@ckeditor/vite-plugin-ckeditor5';


import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import {AntDesignVueResolver} from 'unplugin-vue-components/resolvers'
import path from "path";
import tailwindcss from 'tailwindcss'

const require = createRequire(import.meta.url);
// https://vitejs.dev/config/
export default defineConfig(({command, mode}) => {
    return {
        resolve: {
            alias: {
                "@": path.join(__dirname, "src"),
            },
        },
        css: {
            postcss: {
                plugins: [
                    tailwindcss({
                        content: ['./src/**/*.vue']
                    })
                ]
            }
        },
        plugins: [
            AutoImport({
                resolvers: [
                    AntDesignVueResolver(),
                ]
            }),
            Components({
                resolvers: [
                    AntDesignVueResolver(
                        {
                            importStyle: false
                        }
                    ),
                ],
            }),
            ckeditor5({theme: require.resolve('@ckeditor/ckeditor5-theme-lark')}),
            vue(),
        ],
        server: {
            host: true,
            // 跨域设置允许
            cors: true,
            // 如果端口已占用直接退出
            strictPort: true,
        },
        build: {
            minify: 'terser',
            terserOptions: {
                compress: {
                    //生产环境时移除console、debugger
                    drop_console: true,
                    drop_debugger: true,
                },
            },
        }
    }
})
