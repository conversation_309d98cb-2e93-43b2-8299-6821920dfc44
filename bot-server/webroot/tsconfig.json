{"compilerOptions": {"types": ["vite/client"], "target": "ESNext", "useDefineForClassFields": true, "module": "ESNext", "moduleResolution": "Node", "strict": false, "resolveJsonModule": true, "isolatedModules": false, "esModuleInterop": true, "skipDefaultLibCheck": true, "noImplicitAny": false, "allowJs": true, "strictPropertyInitialization": false, "allowSyntheticDefaultImports": true, "lib": ["ESNext", "DOM"], "skipLibCheck": true, "noEmit": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}}, "include": ["src/**/*", "src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"], "references": [{"path": "./tsconfig.node.json"}]}