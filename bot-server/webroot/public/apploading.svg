<?xml version="1.0" encoding="UTF-8"?>
<svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#6366f1"/>
      <stop offset="100%" style="stop-color:#8b5cf6"/>
    </linearGradient>
  </defs>
  <style>
    .background {
      fill: url(#gradient);
    }
    .spinner {
      transform-origin: center;
      animation: spin 1s linear infinite;
    }
    .circle {
      fill: none;
      stroke: #ffffff;
      stroke-width: 2.5;
      stroke-linecap: round;
      stroke-dasharray: 45, 100;
      animation: dash 1.5s ease-in-out infinite;
    }
    @keyframes spin {
      100% {
        transform: rotate(360deg);
      }
    }
    @keyframes dash {
      0% {
        stroke-dasharray: 1, 100;
      }
      50% {
        stroke-dasharray: 45, 100;
      }
      100% {
        stroke-dasharray: 1, 100;
      }
    }
  </style>
  <circle class="background" cx="12" cy="12" r="12"/>
  <g class="spinner">
    <circle class="circle" cx="12" cy="12" r="8"/>
  </g>
</svg>
