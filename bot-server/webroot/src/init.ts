import {App} from "vue";
import KnowledgePanelView from "@/components/ai-bot-plugins/plugin-config/config-ui/KnowledgePluginConfig.vue";
import ProgrammingAssistantPanelView
    from "@/components/ai-bot-plugins/plugin-config/config-ui/ProgrammingAssistantPluginConfig.vue";
import OllamaQuickConfig from "@/components/ai-bot-platform/ollama-config/OllamaQuickConfig.vue";
import OllamaConfig from "@/components/ai-bot-platform/ollama-config/OllamaConfig.vue";
import BotPluginConfig from "@/components/ai-bot-plugins/plugin-config/config-ui/BotPluginConfig.vue";
import MapSetting from "@/components/ai-bot-setting/open-platform-setting/map-setting/MapSetting.vue";
import SystemSetting from "@/components/ai-bot-setting/system-setting/SystemSetting.vue";
import DeepSeekConfig from "@/components/ai-bot-platform/deep-seek-config/DeepSeekConfig.vue";
import DeepSeekQuickConfig from "@/components/ai-bot-platform/deep-seek-config/DeepSeekQuickConfig.vue";
import GiteeAIConfig from "@/components/ai-bot-platform/gitee-ai-config/GiteeAIConfig.vue";
import GiteeAIQuickConfig from "@/components/ai-bot-platform/gitee-ai-config/GiteeAIQuickConfig.vue";
import BotWriterConfig from "@/components/ai-bot-plugins/plugin-config/config-ui/BotWriterConfig.vue";
import UserScene from "@/components/ai-bot-message/scene/UserScene.vue";
import ChatScene from "@/components/ai-bot-message/scene/ChatScene.vue";
import AiWriterMessage from "@/components/ai-bot-message/scene/AiWriterMessage.vue";
import VolcengineConfig from "@/components/ai-bot-platform/volcengine-ai-config/VolcengineConfig.vue";
import VolcengineQuickConfig from "@/components/ai-bot-platform/volcengine-ai-config/VolcengineQuickConfig.vue";
import CustomConfig from "@/components/ai-bot-platform/custom-llm/CustomConfig.vue";
import AiBotUI from "@/components/ai-bot/AiBotUI.vue";
import PlatformUI from "@/components/ai-bot-platform/PlatformUI.vue";
import AiWriterUI from "@/components/ai-bot-writer/AiWriterUI.vue";
import AiMusicUI from "@/components/ai-bot-music/AiMusicUI.vue";
import AiPaintingUI from "@/components/ai-bot-painting/AiPaintingUI.vue";
import McpUI from "@/components/ai-bot-mcp/McpUI.vue";
import RenderScene from "@/components/ai-bot-message/scene/RenderScene.vue";
import AgentUserScene from "@/components/ai-bot-message/scene/agent/AgentUserScene.vue";
import AgentChatScene from "@/components/ai-bot-message/scene/agent/AgentChatScene.vue";
import AgentRenderScene from "@/components/ai-bot-message/scene/agent/AgentRenderScene.vue";
import AiBotDebugUI from "@/components/ai-bot-debug/AiBotDebugUI.vue";
import McpPromptDebugComponent from "@/components/ai-bot-debug/components/McpPromptDebugComponent.vue";
import GeneralSettings from "@/components/ai-bot-setting/system-setting/GeneralSettings.vue";
import About from "@/components/ai-bot-setting/system-setting/About.vue";
import PersonalInformationSetting from "@/components/ai-bot-setting/system-setting/PersonalInformationSetting.vue";
import DataSetting from "@/components/ai-bot-setting/system-setting/DataSetting.vue";
import TweetUI from "@/components/ai-bot-tweet/TweetUI.vue";
// 处理 electron 中文件上传
if (typeof window.FormData === 'undefined') {
    // window.FormData = require('form-data');
}

const components = [
    AiBotUI,
    AiWriterUI,
    AiMusicUI,
    AiPaintingUI,
    PlatformUI,
    McpUI,
    TweetUI,
    SystemSetting,
    MapSetting,
    GeneralSettings,
    PersonalInformationSetting,
    DataSetting,
    About,
    AiBotDebugUI,
    McpPromptDebugComponent,

    BotPluginConfig,
    BotWriterConfig,
    KnowledgePanelView,
    ProgrammingAssistantPanelView,

    OllamaConfig,
    OllamaQuickConfig,
    DeepSeekConfig,
    DeepSeekQuickConfig,
    GiteeAIConfig,
    GiteeAIQuickConfig,
    VolcengineConfig,
    VolcengineQuickConfig,
    CustomConfig,

    RenderScene,
    UserScene,
    ChatScene,
    AiWriterMessage,

    
    AgentUserScene,
    AgentChatScene,
    AgentRenderScene,
]

export function init(app: App) {
    components.forEach(component => {
        if (component.__name !== undefined) {
            app.component(component.__name, component)
        }
        if (component.name !== undefined) {
            app.component(component.name, component)
        }
    })
}