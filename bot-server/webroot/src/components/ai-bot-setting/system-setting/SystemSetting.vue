<template>
  <PageLayout>
    <div class="size-full flex flex-row justify-between">
      <div class="menu-list py-1">
      <!-- 左侧菜单列表 -->
      <MenuList>
        <MenuListItem 
          v-for="item in settingItems" 
          :key="item.key"
          :text="item.name"
          :isActive="activeSettingKey === item.key"
          @click="handleMenuClick(item)"
        />
      </MenuList>
      </div>
      <!-- 右侧配置面板 -->
      <div class="setting-panel flex-1 py-1 px-1">
        <component :is="currentSettingItem.component" />
      </div>
    </div>
  </PageLayout>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref, watch } from "vue";
import { useAiBot } from "@/components/store/bot";
import { message, theme as antTheme } from "ant-design-vue";
import { clear } from "../req";
import { useGlobalSetting } from "@/components/store/bot-global-setting";
import PageLayout from "@/components/ai-bot-layout/PageLayout.vue";
import { debounce } from 'lodash-es';


const { useToken } = antTheme;
const { token } = useToken();

interface AppSetting {
  bg: string;
  cardColor: string;
  blurSize: number;
}

interface SettingItem {
  key: string;
  name: string;
  description: string;
  component?: string; // 组件名称，用于动态加载
}

const settingItems: SettingItem[] = [
{
    key: 'GeneralSettings',
    name: '通用设置',
    description: '通用设置',
    component: 'GeneralSettings',
  },
  {
    key: 'PersonalInformationSetting',
    name: '个人资料',
    description: '个人资料',
    component: 'PersonalInformationSetting',
  },
  {
    key: 'PlatformUI',
    name: '模型服务',
    description: '选择模型服务',
    component: 'PlatformUI',
  },
  {
    key: 'McpUI',
    name: 'MCP 模型服务',
    description: 'MCP 模型服务',
    component: 'McpUI',
  },
  {
    key: 'DataSetting',
    name: '数据设置',
    description: '数据设置',
    component: 'DataSetting',
  },
  {
    key: 'About',
    name: '关于',
    description: '关于',
    component: 'About',
  },
];

// 当前激活的设置项
const activeSettingKey = ref(settingItems[0].key);

// 根据当前选中的key获取对应的设置项
const currentSettingItem = computed(() => {
  return settingItems.find(item => item.key === activeSettingKey.value) || settingItems[0];
});

const bot = useAiBot();
const clearingCache = ref(false);
const clearingMessageCache = ref(false);

// 主题设置
const theme = ref(bot.theme);

// 监听主题变化
watch(theme, (newTheme) => {
  bot.setTheme(newTheme);
});








  // 清空所有缓存
const clearCache = async () => {
  try {
    clearingCache.value = true;
    // 清除localStorage
    localStorage.clear();
    // 清除sessionStorage
    sessionStorage.clear();
    // 清除其他缓存
    await clear();
    message.success('缓存已清空，页面即将刷新...');
    // 延迟1秒后刷新页面，让用户能看到提示消息
    setTimeout(() => {
      window.location.reload();
    }, 1000);
  } catch (error) {
    message.error('清空缓存失败');
    console.error('清空缓存失败:', error);
  } finally {
    clearingCache.value = false;
  }
};

// 清空消息缓存
const clearMessageCache = async () => {
  try {
    clearingMessageCache.value = true;
    // 清除其他缓存
    await clear();
    message.success('消息记录已清空，页面即将刷新...');
    setTimeout(() => {
      window.location.reload();
    }, 1000);
  } catch (error) {
    message.error('清空消息记录失败');
    console.error('清空消息记录失败:', error);
  } finally {
    clearingMessageCache.value = false;
  }
};



function handleMenuClick(item: SettingItem) {
  activeSettingKey.value = item.key;
  bot.setting = item.component;
}

onMounted(() => {
  activeSettingKey.value = bot.setting
})


</script>

<style scoped>
.size-full {
  width: 100%;
  height: 100%;
}

.menu-list {
  width: 160px;
  height: 100%;
  border-right: 1px solid v-bind('token.colorBorder');
}

.setting-card {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.setting-container {
  width: 100%;
  height: 100%;
  padding: 16px 24px;
  display: flex;
  flex-direction: column;
}

.setting-container h2 {
  margin-bottom: 24px;
  font-weight: 500;
  font-size: 20px;
}

/* 设置布局 */
.setting-layout {
  display: flex;
  flex: 1;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* 右侧配置面板样式 */
.setting-panel {
  overflow-y: auto;
  height: 100%;
}

.panel-item {
  width: 100%;
  max-width: 800px;
}
</style>