<template>
    <div class="personal-info-container">
        <div class="content-area">
            <a-form class="form-container" :model="formState" layout="vertical" :rules="rules" ref="formRef">
                <a-row :gutter="24">
                    <a-col :span="12">
                        <a-form-item name="name">
                            <a-input-group compact>
                                <a-button>
                                    姓名
                                </a-button>
                                <a-input v-model:value="formState.name" style="width: calc(100% - 70px)" />
                            </a-input-group>
                        </a-form-item>
                        <a-form-item name="wechat">
                            <a-input-group compact>
                                <a-button>
                                    微信
                                </a-button>
                                <a-input v-model:value="formState.wechat" style="width: calc(100% - 70px)" />
                            </a-input-group>
                        </a-form-item>
                        <a-form-item name="email">
                            <a-input-group compact>
                                <a-button>
                                    邮箱
                                </a-button>
                                <a-input v-model:value="formState.email" style="width: calc(100% - 70px)" />
                            </a-input-group>
                        </a-form-item>
                        <a-form-item name="linkedin">
                            <a-input-group compact>
                                <a-button>
                                    LinkedIn
                                </a-button>
                                <a-input v-model:value="formState.linkedin" style="width: calc(100% - 95px)" />
                            </a-input-group>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item label="添加头像">
                            <a-upload v-model:file-list="fileList" name="avatar" list-type="picture-card"
                                class="avatar-uploader" :show-upload-list="false" :before-upload="beforeUpload"
                                @change="handleChange" :disabled="uploading">
                                <div v-if="uploading" class="upload-loading">
                                    <a-spin />
                                    <div style="margin-top: 8px;">上传中...</div>
                                </div>
                                <div v-else-if="formState.avatar">
                                    <img :src="formState.avatar" alt="avatar" class="uploaded-avatar" />
                                </div>
                                <div v-else>
                                    <plus-outlined />
                                    <div>添加照片</div>
                                </div>
                            </a-upload>
                        </a-form-item>
                    </a-col>
                </a-row>

                <a-row :gutter="24">
                    <a-col :span="12">
                        <a-form-item name="phone">
                            <a-input-group compact>
                                <a-button>
                                    电话
                                </a-button>
                                <a-input v-model:value="formState.phone" style="width: calc(100% - 70px)"
                                    placeholder="请输入" />
                            </a-input-group>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item>
                            <a-input-group compact>
                                <a-button>
                                    性别
                                </a-button>
                                <a-select v-model:value="formState.gender" style="width: calc(100% - 70px)">
                                    <a-select-option class="my-1" value="男">男</a-select-option>
                                    <a-select-option class="my-1" value="女">女</a-select-option>
                                    <a-select-option class="my-1" value="其他">其他</a-select-option>
                                </a-select>
                            </a-input-group>
                        </a-form-item>
                    </a-col>
                </a-row>

                <a-row :gutter="24">
                    <a-col :span="24">
                        <a-form-item>
                            <a-input-group compact>
                                <a-button>
                                    现居城市
                                </a-button>
                                <a-input v-model:value="formState.address" style="width: calc(100% - 95px)"
                                    placeholder="如：北京" />
                            </a-input-group>
                        </a-form-item>
                    </a-col>
                </a-row>

                <!-- 其他信息的显示区域 -->
                <div v-if="hasAnyOtherInfo" class="other-info-container">
                    <!-- 身高 -->
                    <div v-if="formState.height" class="info-item">
                        <a-form-item>
                            <a-input-group compact>
                                <a-button>
                                    身高
                                </a-button>
                                <a-input-number v-model:value="formState.height" :min="100" :max="250"
                                    style="width: calc(100% - 123px)" />
                                <a-button>cm</a-button>
                                <a-button @click="removeOtherInfoField('height')">
                                    <template #icon><close-outlined /></template>
                                </a-button>
                            </a-input-group>
                        </a-form-item>
                    </div>

                    <!-- 体重 -->
                    <div v-if="formState.weight" class="info-item">
                        <a-form-item>
                            <a-input-group compact>
                                <a-button>
                                    体重
                                </a-button>
                                <a-input-number v-model:value="formState.weight" :min="30" :max="200"
                                    style="width: calc(100% - 123px)" />
                                <a-button>kg</a-button>
                                <a-button @click="removeOtherInfoField('weight')">
                                    <template #icon><close-outlined /></template>
                                </a-button>
                            </a-input-group>
                        </a-form-item>
                    </div>

                    <!-- 民族 -->
                    <div v-if="formState.ethnicity" class="info-item">
                        <a-form-item>
                            <a-input-group compact>
                                <a-button>
                                    民族
                                </a-button>
                                <a-input v-model:value="formState.ethnicity" style="width: calc(100% - 90px)"
                                    placeholder="请输入民族" />
                                <a-button @click="removeOtherInfoField('ethnicity')">
                                    <template #icon><close-outlined /></template>
                                </a-button>
                            </a-input-group>
                        </a-form-item>
                    </div>

                    <!-- 政治面貌 -->
                    <div v-if="typeof formState.politicalStatus !== 'undefined'" class="info-item">
                        <a-form-item>
                            <a-input-group compact>
                                <a-button>
                                    政治面貌
                                </a-button>
                                <a-input v-model:value="formState.politicalStatus" 
                                    style="width: calc(100% - 115px)" 
                                    placeholder="请输入政治面貌" />
                                <a-button @click="removeOtherInfoField('politicalStatus')">
                                    <template #icon><close-outlined /></template>
                                </a-button>
                            </a-input-group>
                        </a-form-item>
                    </div>

                    <!-- 婚姻状况 -->
                    <div v-if="typeof formState.maritalStatus !== 'undefined'" class="info-item">
                        <a-form-item>
                            <a-input-group compact>
                                <a-button>
                                    婚姻状况
                                </a-button>
                                <a-input v-model:value="formState.maritalStatus" 
                                    style="width: calc(100% - 115px)"
                                    placeholder="请输入婚姻状况" />
                                <a-button @click="removeOtherInfoField('maritalStatus')">
                                    <template #icon><close-outlined /></template>
                                </a-button>
                            </a-input-group>
                        </a-form-item>
                    </div>

                    <!-- 出生日期 -->
                    <div v-if="formState.birthday" class="info-item">
                        <a-form-item>
                            <a-input-group compact>
                                <a-button>
                                    出生日期
                                </a-button>
                                <a-date-picker v-model:value="birthdayDate" style="width: calc(100% - 115px)"
                                    @change="updateBirthday" />
                                <a-button @click="removeOtherInfoField('birthday')">
                                    <template #icon><close-outlined /></template>
                                </a-button>
                            </a-input-group>
                        </a-form-item>
                    </div>

                    <!-- 个人网站 -->
                    <div v-if="formState.personalWebsite !== undefined" class="info-item">
                        <a-form-item name="personalWebsite">
                            <a-input-group compact>
                                <a-button>
                                    个人网站
                                </a-button>
                                <a-input v-model:value="formState.personalWebsite" style="width: calc(100% - 115px)" 
                                    placeholder="请输入网址" />
                                <a-button @click="removeOtherInfoField('personalWebsite')">
                                    <template #icon><close-outlined /></template>
                                </a-button>
                            </a-input-group>
                        </a-form-item>
                    </div>
                </div>

                <a-row gutter="24" class="tag-container flex justify-between pl-3">
                    <div class="size-full flex flex-wrap gap-1">
                        <a-button v-if="!formState.height" @click="showOtherInfoField('height')">
                            <template #icon><column-height-outlined /></template>
                            身高
                        </a-button>
                        <a-button v-if="!formState.weight" @click="showOtherInfoField('weight')">
                            <template #icon><dashboard-outlined /></template>
                            体重
                        </a-button>
                        <a-button v-if="!formState.ethnicity" @click="showOtherInfoField('ethnicity')">
                            <template #icon><team-outlined /></template>
                            民族
                        </a-button>
                        <a-button v-if="typeof formState.politicalStatus === 'undefined'" @click="showOtherInfoField('politicalStatus')">
                            <template #icon><flag-outlined /></template>
                            政治面貌
                        </a-button>
                        <a-button v-if="typeof formState.maritalStatus === 'undefined'" @click="showOtherInfoField('maritalStatus')">
                            <template #icon><heart-outlined /></template>
                            婚姻状况
                        </a-button>
                        <a-button v-if="!formState.birthday" @click="showOtherInfoField('birthday')">
                            <template #icon><calendar-outlined /></template>
                            年龄或生日
                        </a-button>
                        <a-button v-if="formState.personalWebsite === undefined" @click="showOtherInfoField('personalWebsite')">
                            <template #icon><global-outlined /></template>
                            个人网站
                        </a-button>
                    </div>
                </a-row>
                <a-divider > 求职意向</a-divider>
                <a-row :gutter="24">
                    <a-col :span="12">
                        <a-form-item name="currentStatus">
                            <a-input-group compact>
                                <a-button>
                                    当前状态
                                </a-button>
                                <a-select v-model:value="formState.currentStatus" style="width: calc(100% - 95px)"
                                    placeholder="请选择当前状态">
                                    <a-select-option value="在职，看看机会">在职，看看机会</a-select-option>
                                    <a-select-option value="在职，积极找工作">在职，积极找工作</a-select-option>
                                    <a-select-option value="离职，正在找工作">离职，正在找工作</a-select-option>
                                    <a-select-option value="应届毕业生">应届毕业生</a-select-option>
                                    <a-select-option value="待业">待业</a-select-option>
                                </a-select>
                            </a-input-group>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item>
                            <a-input-group compact>
                                <a-button>
                                    意向城市
                                </a-button>
                                <a-input v-model:value="formState.intentCity" style="width: calc(100% - 95px)"
                                    placeholder="请填写意向城市" />
                            </a-input-group>
                        </a-form-item>
                    </a-col>
                </a-row>

                <a-row :gutter="24">
                    <a-col :span="12">
                        <a-form-item>
                            <a-input-group compact>
                                <a-button>
                                    期望职位
                                </a-button>
                                <a-input v-model:value="formState.desiredPosition" style="width: calc(100% - 95px)"
                                    placeholder="请填写期望职位" />
                            </a-input-group>
                        </a-form-item>
                    </a-col>
                </a-row>
            </a-form>

            <!-- 工作经历部分 -->
            <div class="section-container">
                <div class="section-header">
                    <div class="section-title-row">
                        <span class="icon-circle"><history-outlined /></span>
                        <h2>工作经历</h2>
                    </div>
                </div>
                <div class="section-content">
                    <a-collapse :bordered="false" expand-icon-position="end">
                        <template v-for="(work, index) in formState.workExperiences" :key="`work-${index}`">
                            <a-collapse-panel>
                                <template #header>
                                    <div class="work-exp-header">
                                        <span class="work-company">{{ work.company || '公司未填写' }}</span>
                                        <span class="work-position">{{ work.position || '职位未填写' }}</span>
                                        <span class="work-date">
                                            {{ formatDate(work.startDate) || '开始日期未填写' }} -
                                            {{ work.endDate ? formatDate(work.endDate) : '至今' }}
                                        </span>
                                    </div>
                                </template>
                                <a-form layout="vertical">
                                    <a-row :gutter="16">
                                        <a-col :span="12">
                                            <a-form-item>
                                                <a-input-group compact>
                                                    <a-button>
                                                        公司名称
                                                    </a-button>
                                                    <a-input v-model:value="formState.workExperiences[index].company"
                                                        style="width: calc(100% - 95px)" placeholder="请输入公司名称" />
                                                </a-input-group>
                                            </a-form-item>
                                        </a-col>
                                        <a-col :span="12">
                                            <a-form-item>
                                                <a-input-group compact>
                                                    <a-button>
                                                        职位名称
                                                    </a-button>
                                                    <a-input v-model:value="formState.workExperiences[index].position"
                                                        style="width: calc(100% - 95px)" placeholder="请输入职位名称" />
                                                </a-input-group>
                                            </a-form-item>
                                        </a-col>
                                    </a-row>
                                    <a-row :gutter="16">
                                        <a-col :span="12">
                                            <a-form-item>
                                                <a-input-group compact>
                                                    <a-button>
                                                        所在部门
                                                    </a-button>
                                                    <a-input v-model:value="formState.workExperiences[index].department"
                                                        style="width: calc(100% - 95px)" placeholder="请输入部门名称" />
                                                </a-input-group>
                                            </a-form-item>
                                        </a-col>
                                        <a-col :span="12">
                                            <a-form-item>
                                                <a-input-group compact>
                                                    <a-button>
                                                        所在城市
                                                    </a-button>
                                                    <a-input v-model:value="formState.workExperiences[index].city"
                                                        style="width: calc(100% - 95px)" placeholder="请输入城市" />
                                                </a-input-group>
                                            </a-form-item>
                                        </a-col>
                                    </a-row>
                                    <a-row :gutter="16">
                                        <a-col :span="12">
                                            <a-form-item>
                                                <a-input-group compact>
                                                    <a-button>
                                                        开始时间
                                                    </a-button>
                                                    <a-date-picker v-model:value="workExperienceDates[index].startDate"
                                                        style="width: calc(100% - 95px)" placeholder="请选择开始日期"
                                                        @change="updateWorkExperienceDate(index, 'startDate')" />
                                                </a-input-group>
                                            </a-form-item>
                                        </a-col>
                                        <a-col :span="12">
                                            <a-form-item>
                                                <a-input-group compact>
                                                    <a-button>
                                                        结束时间
                                                    </a-button>
                                                    <a-date-picker v-model:value="workExperienceDates[index].endDate"
                                                        style="width: calc(100% - 170px)" placeholder="请选择结束日期"
                                                        :disabled="workExperienceDates[index].isCurrent"
                                                        @change="updateWorkExperienceDate(index, 'endDate')" />
                                                    <a-button>
                                                        <a-checkbox v-model:checked="workExperienceDates[index].isCurrent"
                                                            @change="updateWorkExperienceIsCurrent(index)">至今</a-checkbox>
                                                    </a-button>
                                                </a-input-group>
                                            </a-form-item>
                                        </a-col>
                                    </a-row>
                                    <a-form-item label="工作内容">
                                        <div class="rich-editor-container">
                                            <DescriptionEditing
                                                v-model:description="formState.workExperiences[index].description"
                                                />
                                        </div>
                                    </a-form-item>
                                    <a-form-item>
                                        <a-button type="primary" @click="saveWorkExperience(index)">保存修改</a-button>
                                        <a-button type="danger" style="margin-left: 10px"
                                            @click="removeWorkExperience(index)">删除</a-button>
                                    </a-form-item>
                                </a-form>
                            </a-collapse-panel>
                        </template>

                        <a-collapse-panel v-if="showNewWorkForm" key="new-work">
                            <template #header>
                                <div class="work-exp-header">
                                    <span class="work-company">{{ newWorkForm.company || '新工作经历' }}</span>
                                    <span class="work-position">{{ newWorkForm.position || '请填写职位' }}</span>
                                    <span class="work-date">新添加</span>
                                </div>
                            </template>
                            <a-form layout="vertical">
                                <a-row :gutter="16">
                                    <a-col :span="12">
                                        <a-form-item>
                                            <a-input-group compact>
                                                <a-button>
                                                    公司名称
                                                </a-button>
                                                <a-input v-model:value="newWorkForm.company"
                                                    style="width: calc(100% - 95px)" placeholder="请输入公司名称" />
                                            </a-input-group>
                                        </a-form-item>
                                    </a-col>
                                    <a-col :span="12">
                                        <a-form-item>
                                            <a-input-group compact>
                                                <a-button>
                                                    职位名称
                                                </a-button>
                                                <a-input v-model:value="newWorkForm.position"
                                                    style="width: calc(100% - 95px)" placeholder="请输入职位名称" />
                                            </a-input-group>
                                        </a-form-item>
                                    </a-col>
                                </a-row>
                                <a-row :gutter="16">
                                    <a-col :span="12">
                                        <a-form-item>
                                            <a-input-group compact>
                                                <a-button>
                                                    所在部门
                                                </a-button>
                                                <a-input v-model:value="newWorkForm.department"
                                                    style="width: calc(100% - 95px)" placeholder="请输入部门名称" />
                                            </a-input-group>
                                        </a-form-item>
                                    </a-col>
                                    <a-col :span="12">
                                        <a-form-item>
                                            <a-input-group compact>
                                                <a-button>
                                                    所在城市
                                                </a-button>
                                                <a-input v-model:value="newWorkForm.city"
                                                    style="width: calc(100% - 95px)" placeholder="请输入城市" />
                                            </a-input-group>
                                        </a-form-item>
                                    </a-col>
                                </a-row>
                                <a-row :gutter="16">
                                    <a-col :span="12">
                                        <a-form-item>
                                            <a-input-group compact>
                                                <a-button>
                                                    开始时间
                                                </a-button>
                                                <a-date-picker v-model:value="newWorkForm.startDate"
                                                    style="width: calc(100% - 95px)" placeholder="请选择开始日期" />
                                            </a-input-group>
                                        </a-form-item>
                                    </a-col>
                                    <a-col :span="12">
                                        <a-form-item>
                                            <a-input-group compact>
                                                <a-button>
                                                    结束时间
                                                </a-button>
                                                <a-date-picker v-model:value="newWorkForm.endDate"
                                                    style="width: calc(100% - 170px)" placeholder="请选择结束日期"
                                                    :disabled="newWorkForm.isCurrent" />
                                                <a-button>
                                                    <a-checkbox v-model:checked="newWorkForm.isCurrent">至今</a-checkbox>
                                                </a-button>
                                            </a-input-group>
                                        </a-form-item>
                                    </a-col>
                                </a-row>
                                <a-form-item label="工作内容">
                                    <div class="rich-editor-container">
                                        <DescriptionEditing v-model:description="newWorkForm.description" />
                                    </div>
                                </a-form-item>
                                <a-form-item>
                                    <a-button type="primary" @click="addNewWorkExperience">保存</a-button>
                                    <a-button style="margin-left: 10px" @click="cancelNewWorkForm">取消</a-button>
                                </a-form-item>
                            </a-form>
                        </a-collapse-panel>
                    </a-collapse>

                    <a-button class="add-btn" style="margin-top: 16px;" @click="startAddWorkExperience"
                        :disabled="showNewWorkForm">
                        <plus-outlined /> 添加一段工作经历
                    </a-button>
                </div>
            </div>

            <!-- 项目部分 -->
            <div class="section-container">
                <div class="section-header">
                    <div class="section-title-row">
                        <span class="icon-circle"><code-outlined /></span>
                        <h2>开源项目及作品</h2>
                    </div>
                </div>
                <div class="section-content">
                    <a-collapse :bordered="false" expand-icon-position="end">
                        <template v-for="(project, index) in formState.projects" :key="`project-${index}`">
                            <a-collapse-panel>
                                <template #header>
                                    <div class="work-exp-header">
                                        <span class="work-company">{{ project.name || '项目未命名' }}</span>
                                        <span class="work-date">
                                            {{ formatDate(project.startDate) || '开始日期未填写' }} -
                                            {{ formatDate(project.endDate) || '结束日期未填写' }}
                                        </span>
                                    </div>
                                </template>
                                <a-form layout="vertical">
                                    <a-form-item>
                                        <a-input-group compact>
                                            <a-button>
                                                项目名称
                                            </a-button>
                                            <a-input v-model:value="formState.projects[index].name" 
                                                style="width: calc(100% - 95px)" placeholder="请输入项目名称" />
                                        </a-input-group>
                                    </a-form-item>
                                    <a-form-item>
                                        <a-input-group compact>
                                            <a-button>
                                                项目链接
                                            </a-button>
                                            <a-input v-model:value="formState.projects[index].link" 
                                                style="width: calc(100% - 95px)" placeholder="请输入项目链接" />
                                        </a-input-group>
                                    </a-form-item>
                                    <a-row :gutter="16">
                                        <a-col :span="12">
                                            <a-form-item>
                                                <a-input-group compact>
                                                    <a-button>
                                                        开始时间
                                                    </a-button>
                                                    <a-date-picker v-model:value="projectDates[index].startDate"
                                                        style="width: calc(100% - 95px)" @change="updateProjectDate(index, 'startDate')" />
                                                </a-input-group>
                                            </a-form-item>
                                        </a-col>
                                        <a-col :span="12">
                                            <a-form-item>
                                                <a-input-group compact>
                                                    <a-button>
                                                        结束时间
                                                    </a-button>
                                                    <a-date-picker v-model:value="projectDates[index].endDate"
                                                        style="width: calc(100% - 95px)" @change="updateProjectDate(index, 'endDate')" />
                                                </a-input-group>
                                            </a-form-item>
                                        </a-col>
                                    </a-row>
                                    <a-form-item label="项目描述">
                                        <DescriptionEditing v-model:description="formState.projects[index].description" />
                                    </a-form-item>
                                    <a-form-item>
                                        <a-button type="primary" @click="saveProject(index)">保存修改</a-button>
                                        <a-button type="danger" style="margin-left: 10px"
                                            @click="removeProject(index)">删除</a-button>
                                    </a-form-item>
                                </a-form>
                            </a-collapse-panel>
                        </template>

                        <a-collapse-panel v-if="showNewProjectForm" key="new-project">
                            <template #header>
                                <div class="work-exp-header">
                                    <span class="work-company">{{ newProjectForm.name || '新项目' }}</span>
                                    <span class="work-date">新添加</span>
                                </div>
                            </template>
                            <a-form layout="vertical">
                                <a-form-item>
                                    <a-input-group compact>
                                        <a-button>
                                            项目名称
                                        </a-button>
                                        <a-input v-model:value="newProjectForm.name" 
                                            style="width: calc(100% - 95px)" placeholder="请输入项目名称" />
                                    </a-input-group>
                                </a-form-item>
                                <a-form-item>
                                    <a-input-group compact>
                                        <a-button>
                                            项目链接
                                        </a-button>
                                        <a-input v-model:value="newProjectForm.link" 
                                            style="width: calc(100% - 95px)" placeholder="请输入项目链接" />
                                    </a-input-group>
                                </a-form-item>
                                <a-row :gutter="16">
                                    <a-col :span="12">
                                        <a-form-item>
                                            <a-input-group compact>
                                                <a-button>
                                                    开始时间
                                                </a-button>
                                                <a-date-picker v-model:value="newProjectForm.startDate" 
                                                    style="width: calc(100% - 95px)" />
                                            </a-input-group>
                                        </a-form-item>
                                    </a-col>
                                    <a-col :span="12">
                                        <a-form-item>
                                            <a-input-group compact>
                                                <a-button>
                                                    结束时间
                                                </a-button>
                                                <a-date-picker v-model:value="newProjectForm.endDate" 
                                                    style="width: calc(100% - 95px)" />
                                            </a-input-group>
                                        </a-form-item>
                                    </a-col>
                                </a-row>
                                <a-form-item label="项目描述">
                                    <DescriptionEditing v-model:description="newProjectForm.description" />
                                </a-form-item>
                                <a-form-item>
                                    <a-button type="primary" @click="addNewProject">保存</a-button>
                                    <a-button style="margin-left: 10px" @click="cancelNewProjectForm">取消</a-button>
                                </a-form-item>
                            </a-form>
                        </a-collapse-panel>
                    </a-collapse>

                    <a-button class="add-btn" style="margin-top: 16px;" @click="startAddProject"
                        :disabled="showNewProjectForm">
                        <plus-outlined /> 添加一段开源项目及作品
                    </a-button>
                </div>
            </div>

            <!-- 教育经历部分 -->
            <div class="section-container">
                <div class="section-header">
                    <div class="section-title-row">
                        <span class="icon-circle"><read-outlined /></span>
                        <h2>教育经历</h2>
                    </div>
                </div>
                <div class="section-content">
                    <a-collapse :bordered="false" expand-icon-position="end">
                        <template v-for="(edu, index) in formState.education" :key="`edu-${index}`">
                            <a-collapse-panel>
                                <template #header>
                                    <div class="work-exp-header">
                                        <span class="work-company">{{ edu.school || '学校未填写' }}</span>
                                        <span class="work-position">{{ edu.major || '专业未填写' }} · {{ edu.degree || '学历未填写'
                                            }}</span>
                                        <span class="work-date">{{ formatYear(edu.graduationDate) || '毕业年份未填写' }}</span>
                                    </div>
                                </template>
                                <a-form layout="vertical">
                                    <a-row :gutter="16">
                                        <a-col :span="12">
                                            <a-form-item>
                                                <a-input-group compact>
                                                    <a-button>
                                                        学校名称
                                                    </a-button>
                                                    <a-input v-model:value="formState.education[index].school"
                                                        style="width: calc(100% - 95px)" placeholder="请输入学校名称" />
                                                </a-input-group>
                                            </a-form-item>
                                        </a-col>
                                        <a-col :span="12">
                                            <a-form-item>
                                                <a-input-group compact>
                                                    <a-button>
                                                        专业名称
                                                    </a-button>
                                                    <a-input v-model:value="formState.education[index].major"
                                                        style="width: calc(100% - 95px)" placeholder="请输入专业名称" />
                                                </a-input-group>
                                            </a-form-item>
                                        </a-col>
                                    </a-row>
                                    <a-row :gutter="16">
                                        <a-col :span="12">
                                            <a-form-item>
                                                <a-input-group compact>
                                                    <a-button>
                                                        学历
                                                    </a-button>
                                                    <a-select v-model:value="formState.education[index].degree"
                                                        style="width: calc(100% - 70px)" placeholder="请选择学历">
                                                        <a-select-option value="高中">高中</a-select-option>
                                                        <a-select-option value="大专">大专</a-select-option>
                                                        <a-select-option value="本科">本科</a-select-option>
                                                        <a-select-option value="硕士">硕士</a-select-option>
                                                        <a-select-option value="博士">博士</a-select-option>
                                                    </a-select>
                                                </a-input-group>
                                            </a-form-item>
                                        </a-col>
                                        <a-col :span="12">
                                            <a-form-item>
                                                <a-input-group compact>
                                                    <a-button>
                                                        毕业年份
                                                    </a-button>
                                                    <a-date-picker v-model:value="educationDates[index].graduationDate"
                                                        picker="year" style="width: calc(100% - 95px)"
                                                        @change="updateEducationDate(index)" />
                                                </a-input-group>
                                            </a-form-item>
                                        </a-col>
                                    </a-row>
                                    <a-form-item label="在校经历">
                                        <DescriptionEditing v-model:description="formState.education[index].experience" />
                                    </a-form-item>
                                    <a-form-item>
                                        <a-button type="primary" @click="saveEducation(index)">保存修改</a-button>
                                        <a-button type="danger" style="margin-left: 10px"
                                            @click="removeEducation(index)">删除</a-button>
                                    </a-form-item>
                                </a-form>
                            </a-collapse-panel>
                        </template>

                        <a-collapse-panel v-if="showNewEducationForm" key="new-education">
                            <template #header>
                                <div class="work-exp-header">
                                    <span class="work-company">{{ newEducationForm.school || '新教育经历' }}</span>
                                    <span class="work-position">{{ newEducationForm.major || '专业未填写' }} · {{
                                        newEducationForm.degree || '学历未填写' }}</span>
                                    <span class="work-date">新添加</span>
                                </div>
                            </template>
                            <a-form layout="vertical">
                                <a-row :gutter="16">
                                    <a-col :span="12">
                                        <a-form-item>
                                            <a-input-group compact>
                                                <a-button>
                                                    学校名称
                                                </a-button>
                                                <a-input v-model:value="newEducationForm.school" 
                                                    style="width: calc(100% - 95px)" placeholder="请输入学校名称" />
                                            </a-input-group>
                                        </a-form-item>
                                    </a-col>
                                    <a-col :span="12">
                                        <a-form-item>
                                            <a-input-group compact>
                                                <a-button>
                                                    专业名称
                                                </a-button>
                                                <a-input v-model:value="newEducationForm.major" 
                                                    style="width: calc(100% - 95px)" placeholder="请输入专业名称" />
                                            </a-input-group>
                                        </a-form-item>
                                    </a-col>
                                </a-row>
                                <a-row :gutter="16">
                                    <a-col :span="12">
                                        <a-form-item>
                                            <a-input-group compact>
                                                <a-button>
                                                    学历
                                                </a-button>
                                                <a-select v-model:value="newEducationForm.degree" 
                                                    style="width: calc(100% - 70px)" placeholder="请选择学历">
                                                    <a-select-option value="高中">高中</a-select-option>
                                                    <a-select-option value="大专">大专</a-select-option>
                                                    <a-select-option value="本科">本科</a-select-option>
                                                    <a-select-option value="硕士">硕士</a-select-option>
                                                    <a-select-option value="博士">博士</a-select-option>
                                                </a-select>
                                            </a-input-group>
                                        </a-form-item>
                                    </a-col>
                                    <a-col :span="12">
                                        <a-form-item>
                                            <a-input-group compact>
                                                <a-button>
                                                    毕业年份
                                                </a-button>
                                                <a-date-picker v-model:value="newEducationForm.graduationDate" 
                                                    picker="year" style="width: calc(100% - 95px)" />
                                            </a-input-group>
                                        </a-form-item>
                                    </a-col>
                                </a-row>
                                <a-form-item label="在校经历">
                                    <DescriptionEditing v-model:description="newEducationForm.experience" />
                                </a-form-item>
                                <a-form-item>
                                    <a-button type="primary" @click="addNewEducation">保存</a-button>
                                    <a-button style="margin-left: 10px" @click="cancelNewEducationForm">取消</a-button>
                                </a-form-item>
                            </a-form>
                        </a-collapse-panel>
                    </a-collapse>

                    <a-button class="add-btn" style="margin-top: 16px;" @click="startAddEducation"
                        :disabled="showNewEducationForm">
                        <plus-outlined /> 添加一段教育经历
                    </a-button>
                </div>
            </div>
        </div>

        <!-- 固定在底部的保存按钮 -->
        <div class="footer-actions">
            <a-button type="primary" @click="handleSaveBasicInfo">保存</a-button>
            <a-button style="margin-left: 10px" @click="resetForm">取消</a-button>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, computed } from 'vue';
import {
    PlusOutlined,
    GlobalOutlined,
    WechatOutlined,
    LinkedinOutlined,
    ManOutlined,
    ColumnHeightOutlined,
    DashboardOutlined,
    TeamOutlined,
    DollarOutlined,
    FlagOutlined,
    HeartOutlined,
    CalendarOutlined,
    HistoryOutlined,
    CodeOutlined,
    ReadOutlined,
    EditOutlined,
    DeleteOutlined,
    UndoOutlined,
    RedoOutlined,
    BoldOutlined,
    ItalicOutlined,
    UnorderedListOutlined,
    OrderedListOutlined,
    MenuOutlined,
    LinkOutlined,
    FormatPainterOutlined,
    CloseOutlined
} from '@ant-design/icons-vue';
import type { UploadChangeParam } from 'ant-design-vue';
import { message, theme } from 'ant-design-vue';
import dayjs from 'dayjs';
import DescriptionEditing from '@/components/ai-bot-widget/DescriptionEditing.vue';
import PersonalInformationNavComponent from '@/components/ai-bot-widget/nav/PersonalInformationNavComponent.vue';
import { useInformation } from '@/components/store/information';
const { useToken } = theme;
const { token } = useToken();


interface PersonalInformation {
    name: string;
    email: string;
    phone: string;
    address: string;
    avatar: string;
    currentStatus: string;
    intentCity: string;
    desiredPosition: string;
    salaryMin: number;
    salaryMax: number;
    personalWebsite: string;
    wechat: string;
    linkedin: string;
    gender: string;
    birthday: string;
    height?: number;
    weight?: number;
    ethnicity?: string;
    currentSalary?: number;
    politicalStatus?: string;
    maritalStatus?: string;
    workExperiences: WorkExperience[];
    projects: Project[];
    education: Education[];
}


interface Education {
    school: string;
    major: string;
    degree: string;
    graduationDate: string;
    experience: string;
}

interface Project {
    name: string;
    link: string;
    startDate: string;
    endDate: string;
    description: string;
}

interface WorkExperience {
    company: string;
    position: string;
    department?: string;
    city?: string;
    startDate: string;
    endDate: string;
    description: string;
}

const fileList = ref([]);

// 添加上传状态管理
const uploading = ref(false);

// 获取information store
const informationStore = useInformation();

// 表单数据
const formState = reactive<PersonalInformation>({
    name: '',
    email: '',
    phone: '',
    address: '',
    avatar: '',
    currentStatus: '',
    intentCity: '',
    desiredPosition: '',
    salaryMin: undefined,
    salaryMax: undefined,
    personalWebsite: '',
    wechat: '',
    linkedin: '',
    gender: '',
    birthday: '',
    workExperiences: [],
    projects: [],
    education: []
});

// localStorage存储键名
const STORAGE_KEY = 'personal_info_data';

// 从localStorage加载数据
const loadDataFromStorage = () => {
    try {
        // 首先尝试从information store获取当前模板
        if (informationStore.currentTemplate) {
            const templateData = informationStore.currentTemplate.personalInfo;
            if (templateData) {
                // 合并数据到formState
                Object.keys(templateData).forEach(key => {
                    if (key in formState) {
                        formState[key] = templateData[key];
                    }
                });
                console.log('数据已从当前模板加载');
                return;
            }
        }

        // 兼容旧版：如果store中没有数据，尝试从localStorage获取
        const savedData = localStorage.getItem(STORAGE_KEY);
        if (savedData) {
            const parsedData = JSON.parse(savedData);

            // 合并数据到formState
            Object.keys(parsedData).forEach(key => {
                if (key in formState) {
                    formState[key] = parsedData[key];
                }
            });

            // 将旧数据迁移到新store中
            if (informationStore.currentTemplate) {
                informationStore.updateResumeTemplate(
                    informationStore.currentTemplateId,
                    { personalInfo: parsedData }
                );
            }

            console.log('数据已从本地存储加载并迁移到模板中');
        }
    } catch (error) {
        console.error('加载数据失败:', error);
    }
};

// 保存数据到store
const saveDataToStorage = () => {
    try {
        // 保存到当前模板
        if (informationStore.currentTemplateId) {
            informationStore.updateResumeTemplate(
                informationStore.currentTemplateId,
                { personalInfo: JSON.parse(JSON.stringify(formState)) }
            );
            console.log('数据已保存到当前模板');
        }
        
        // 兼容旧版：同时也保存到localStorage
        localStorage.setItem(STORAGE_KEY, JSON.stringify(formState));
    } catch (error) {
        console.error('保存数据失败:', error);
    }
};

// 监听数据变化，自动保存
watch(
    () => formState,
    () => {
        saveDataToStorage();
    },
    { deep: true }
);

// 监听当前模板变化
watch(
    () => informationStore.currentTemplateId,
    () => {
        loadDataFromStorage();
    }
);

// 组件挂载时加载数据
onMounted(() => {
    // 确保有默认模板
    informationStore.initDefaultTemplate();
    
    loadDataFromStorage();
    initWorkExperienceDates();

    // 确保项目和教育经历的日期都被正确初始化
    if (!projectDates.value.length && formState.projects.length) {
        initProjectDates();
    }

    if (!educationDates.value.length && formState.education.length) {
        initEducationDates();
    }

    // 初始化空数组以避免索引错误
    if (formState.workExperiences.length === 0) {
        formState.workExperiences = [];
    }

    if (formState.projects.length === 0) {
        formState.projects = [];
    }

    if (formState.education.length === 0) {
        formState.education = [];
    }

    // 初始化生日日期控件
    if (formState.birthday) {
        birthdayDate.value = dayjs(formState.birthday);
    }

    // 确保性别字段有默认值
    if (!formState.gender) {
        formState.gender = '男';
    }
    
    // 设置当前状态默认值
    if (!formState.currentStatus) {
        formState.currentStatus = '待业';
    }
});

// 上传头像前的验证
const beforeUpload = (file: File) => {
    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/gif' || file.type === 'image/webp';
    if (!isJpgOrPng) {
        message.error('只能上传JPG、PNG、GIF或WebP格式的图片!');
        return false;
    }
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
        message.error('图片必须小于2MB!');
        return false;
    }

    // 设置上传状态
    uploading.value = true;

    // 模拟上传：直接转换为base64并保存
    getBase64(file, (url: string) => {
        formState.avatar = url;
        saveDataToStorage();
        uploading.value = false;
        message.success('头像上传成功!');
    });

    return false; // 阻止真正的上传请求，实现模拟上传
};

// 处理头像上传变化
const handleChange = (info: UploadChangeParam) => {
    // 由于我们在beforeUpload中已经处理了文件转换和保存，
    // 这里主要用于处理UI状态更新
    if (info.file.status === 'uploading') {
        return;
    }

    // 即使没有真正上传，也可能需要处理一些UI状态
    if (info.file.originFileObj) {
        // 确保头像能够正确显示（作为备用方案）
        if (!formState.avatar) {
            getBase64(info.file.originFileObj, (url: string) => {
                formState.avatar = url;
                saveDataToStorage();
            });
        }
    }
};

// 转换文件为base64
const getBase64 = (img: File, callback: (base64Url: string) => void) => {
    const reader = new FileReader();
    reader.addEventListener('load', () => callback(reader.result as string));
    reader.readAsDataURL(img);
};

// 控制表单显示状态
const showProjectForm = ref(false);
const showEducationForm = ref(false);

// 项目与教育经历相关变量
const currentEditingProjectIndex = ref<number | null>(null);
const currentEditingEducationIndex = ref<number | null>(null);

// 项目表单数据
const projectForm = reactive({
    name: '',
    link: '',
    startDate: null,
    endDate: null,
    description: ''
});

// 教育经历表单数据
const educationForm = reactive({
    school: '',
    major: '',
    degree: '',
    graduationDate: null,
    experience: ''
});

// 工作经历日期处理
const workExperienceDates = ref([]);

// 新的工作经历表单
const newWorkForm = reactive({
    company: '',
    position: '',
    department: '',
    city: '',
    startDate: null,
    endDate: null,
    isCurrent: false,
    description: ''
});

// 控制新工作经历表单的显示
const showNewWorkForm = ref(false);

// 初始化日期对象
const initWorkExperienceDates = () => {
    if (!formState.workExperiences) {
        formState.workExperiences = [];
        workExperienceDates.value = [];
        return;
    }

    workExperienceDates.value = formState.workExperiences.map(exp => {
        return {
            startDate: exp.startDate ? dayjs(exp.startDate) : null,
            endDate: exp.endDate ? dayjs(exp.endDate) : null,
            isCurrent: !exp.endDate
        };
    });
};

// 处理现有工作经历日期更新
const updateWorkExperienceDate = (index, dateType) => {
    if (dateType === 'startDate') {
        formState.workExperiences[index].startDate = workExperienceDates.value[index].startDate
            ? workExperienceDates.value[index].startDate.format('YYYY-MM-DD')
            : '';
    } else {
        formState.workExperiences[index].endDate = workExperienceDates.value[index].endDate
            ? workExperienceDates.value[index].endDate.format('YYYY-MM-DD')
            : '';
    }
    saveDataToStorage();
};

// 处理工作经历"至今"复选框
const updateWorkExperienceIsCurrent = (index) => {
    if (workExperienceDates.value[index].isCurrent) {
        workExperienceDates.value[index].endDate = null;
        formState.workExperiences[index].endDate = '';
    }
    saveDataToStorage();
};

// 添加新工作经历
const startAddWorkExperience = () => {
    showNewWorkForm.value = true;
    Object.keys(newWorkForm).forEach(key => {
        if (key === 'isCurrent') {
            newWorkForm[key] = false;
        } else {
            newWorkForm[key] = '';
        }
    });
    newWorkForm.startDate = null;
    newWorkForm.endDate = null;
};

// 保存新工作经历
const addNewWorkExperience = () => {
    // 非空字段验证
    if (!newWorkForm.company) {
        message.warning('请填写公司名称');
    }
    if (!newWorkForm.position) {
        message.warning('请填写职位名称');
    }
    if (!newWorkForm.startDate) {
        message.warning('请选择开始日期');
    }
    if (!newWorkForm.endDate && !newWorkForm.isCurrent) {
        message.warning('请选择结束日期或勾选"至今"');
    }

    // 即使有警告也继续添加
    const experience = {
        company: newWorkForm.company,
        position: newWorkForm.position,
        department: newWorkForm.department,
        city: newWorkForm.city,
        startDate: newWorkForm.startDate ? newWorkForm.startDate.format('YYYY-MM-DD') : '',
        endDate: newWorkForm.isCurrent ? '' : (newWorkForm.endDate ? newWorkForm.endDate.format('YYYY-MM-DD') : ''),
        description: newWorkForm.description
    };

    formState.workExperiences.push(experience);
    workExperienceDates.value.push({
        startDate: newWorkForm.startDate,
        endDate: newWorkForm.isCurrent ? null : newWorkForm.endDate,
        isCurrent: newWorkForm.isCurrent
    });

    saveDataToStorage();
    showNewWorkForm.value = false;
    message.success('工作经历已添加');
};

// 取消添加新工作经历
const cancelNewWorkForm = () => {
    showNewWorkForm.value = false;
};

// 保存修改工作经历
const saveWorkExperience = (index) => {
    // 非空字段验证
    if (!formState.workExperiences[index].company) {
        message.warning('请填写公司名称');
    }
    if (!formState.workExperiences[index].position) {
        message.warning('请填写职位名称');
    }

    saveDataToStorage();
    message.success('修改已保存');
};

// 删除工作经历
const removeWorkExperience = (index) => {
    formState.workExperiences.splice(index, 1);
    workExperienceDates.value.splice(index, 1);
    saveDataToStorage();
    message.success('工作经历已删除');
};

// 项目相关方法已移除 - 使用新版函数实现
// 教育经历相关方法已移除 - 使用新版函数实现

// 项目相关处理
const handleProjectOk = () => {
    // 验证必填字段
    if (!projectForm.name) {
        return;
    }

    const project: Project = {
        name: projectForm.name,
        link: projectForm.link,
        startDate: projectForm.startDate ? projectForm.startDate.toISOString() : '',
        endDate: projectForm.endDate ? projectForm.endDate.toISOString() : '',
        description: projectForm.description
    };

    if (currentEditingProjectIndex.value !== null) {
        // 更新现有项目
        formState.projects[currentEditingProjectIndex.value] = project;
        currentEditingProjectIndex.value = null;
    } else {
        // 添加新的项目
        formState.projects.push(project);
    }

    // 重置表单，准备添加下一条
    resetProjectForm();

    // 保存到localStorage
    saveDataToStorage();
};

const resetProjectForm = () => {
    projectForm.name = '';
    projectForm.link = '';
    projectForm.startDate = null;
    projectForm.endDate = null;
    projectForm.description = '';
    currentEditingProjectIndex.value = null;
};

const cancelProjectForm = () => {
    resetProjectForm();
    showProjectForm.value = false;
};

// 教育经历相关方法已移除

// 教育经历相关处理
const handleEducationOk = () => {
    // 验证必填字段
    if (!educationForm.school || !educationForm.major || !educationForm.degree || !educationForm.graduationDate) {
        return;
    }

    const education: Education = {
        school: educationForm.school,
        major: educationForm.major,
        degree: educationForm.degree,
        graduationDate: educationForm.graduationDate ? educationForm.graduationDate.toISOString() : '',
        experience: educationForm.experience
    };

    if (currentEditingEducationIndex.value !== null) {
        // 更新现有教育经历
        formState.education[currentEditingEducationIndex.value] = education;
        currentEditingEducationIndex.value = null;
    } else {
        // 添加新的教育经历
        formState.education.push(education);
    }

    // 重置表单，准备添加下一条
    resetEducationForm();

    // 保存到localStorage
    saveDataToStorage();
};

const resetEducationForm = () => {
    educationForm.school = '';
    educationForm.major = '';
    educationForm.degree = '';
    educationForm.graduationDate = null;
    educationForm.experience = '';
    currentEditingEducationIndex.value = null;
};

const cancelEducationForm = () => {
    resetEducationForm();
    showEducationForm.value = false;
};

// 项目相关方法
const editProject = (index: number) => {
    const project = formState.projects[index];
    projectForm.name = project.name;
    projectForm.link = project.link || '';
    projectForm.description = project.description;

    // 处理日期
    if (project.startDate) {
        projectForm.startDate = new Date(project.startDate);
    }

    if (project.endDate) {
        projectForm.endDate = new Date(project.endDate);
    }

    currentEditingProjectIndex.value = index;
    showProjectForm.value = true;
};

const removeProject = (index: number) => {
    formState.projects.splice(index, 1);
    saveDataToStorage();
};

// 教育经历相关方法
const editEducation = (index: number) => {
    const education = formState.education[index];
    educationForm.school = education.school;
    educationForm.major = education.major;
    educationForm.degree = education.degree;
    educationForm.experience = education.experience;

    // 处理日期
    if (education.graduationDate) {
        educationForm.graduationDate = new Date(education.graduationDate);
    }

    currentEditingEducationIndex.value = index;
    showEducationForm.value = true;
};

const removeEducation = (index: number) => {
    formState.education.splice(index, 1);
    saveDataToStorage();
};

const formatDate = (dateStr: string | null) => {
    if (!dateStr) return '';
    try {
        return dayjs(dateStr).format('YYYY-MM-DD');
    } catch (e) {
        console.error('日期格式化错误:', e);
        return '';
    }
};

const formatYear = (dateStr: string | null) => {
    if (!dateStr) return '';
    try {
        return dayjs(dateStr).format('YYYY');
    } catch (e) {
        console.error('日期格式化错误:', e);
        return '';
    }
};

// 表单引用
const formRef = ref(null);

// 验证函数
const validateName = (rule, value) => {
    if (!value) {
        return Promise.resolve();
    }
    
    const chineseChars = value.match(/[\u4e00-\u9fa5]/g);
    if (!chineseChars || chineseChars.length < 2) {
        return Promise.reject('姓名至少需要2个汉字');
    }
    
    return Promise.resolve();
};

const validatePhone = (rule, value) => {
    if (!value) {
        return Promise.resolve();
    }
    
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(value)) {
        return Promise.reject('请输入正确的手机号码');
    }
    
    return Promise.resolve();
};

const validateUrl = (rule, value) => {
    if (!value) {
        return Promise.resolve();
    }
    
    try {
        new URL(value);
        return Promise.resolve();
    } catch (error) {
        return Promise.reject('请输入正确的网址格式，包含http://或https://');
    }
};

// 表单规则
const rules = {
    name: [
        { required: true, message: '请输入姓名' },
        { validator: validateName }
    ],
    email: [
        { type: 'email', message: '请输入正确的邮箱格式' }
    ],
    phone: [
        { validator: validatePhone }
    ],
    personalWebsite: [
        { validator: validateUrl }
    ]
};

// 重置表单
const resetForm = () => {
    formRef.value?.resetFields();
};

// 基本信息保存按钮 - 修改为包含验证逻辑
const handleSaveBasicInfo = () => {
    formRef.value.validateFields().then(() => {
        saveDataToStorage();
        // 显示保存成功提示
        message.success('个人信息已保存');
    }).catch(errors => {
        console.log('表单验证失败:', errors);
        message.error('请检查表单中的错误');
    });
};

// 项目日期处理
const projectDates = ref([]);

// 新的项目表单
const newProjectForm = reactive({
    name: '',
    link: '',
    startDate: null,
    endDate: null,
    description: ''
});

// 控制新项目表单显示
const showNewProjectForm = ref(false);

// 教育经历日期处理
const educationDates = ref([]);

// 新的教育经历表单
const newEducationForm = reactive({
    school: '',
    major: '',
    degree: '',
    graduationDate: null,
    experience: ''
});

// 控制新教育经历表单显示
const showNewEducationForm = ref(false);

// 初始化项目日期
const initProjectDates = () => {
    if (!formState.projects) {
        formState.projects = [];
        projectDates.value = [];
        return;
    }

    projectDates.value = formState.projects.map(proj => {
        return {
            startDate: proj.startDate ? dayjs(proj.startDate) : null,
            endDate: proj.endDate ? dayjs(proj.endDate) : null
        };
    });
};

// 初始化教育经历日期
const initEducationDates = () => {
    if (!formState.education) {
        formState.education = [];
        educationDates.value = [];
        return;
    }

    educationDates.value = formState.education.map(edu => {
        return {
            graduationDate: edu.graduationDate ? dayjs(edu.graduationDate) : null
        };
    });
};

// 更新项目日期
const updateProjectDate = (index, dateType) => {
    if (dateType === 'startDate') {
        formState.projects[index].startDate = projectDates.value[index].startDate
            ? projectDates.value[index].startDate.format('YYYY-MM-DD')
            : '';
    } else {
        formState.projects[index].endDate = projectDates.value[index].endDate
            ? projectDates.value[index].endDate.format('YYYY-MM-DD')
            : '';
    }
    saveDataToStorage();
};

// 更新教育经历日期
const updateEducationDate = (index) => {
    formState.education[index].graduationDate = educationDates.value[index].graduationDate
        ? educationDates.value[index].graduationDate.format('YYYY')
        : '';
    saveDataToStorage();
};

// 添加新项目相关方法
const startAddProject = () => {
    showNewProjectForm.value = true;
    Object.keys(newProjectForm).forEach(key => {
        newProjectForm[key] = '';
    });
    newProjectForm.startDate = null;
    newProjectForm.endDate = null;
};

// 保存新项目
const addNewProject = () => {
    // 非空字段验证
    if (!newProjectForm.name) {
        message.warning('请填写项目名称');
    }

    // 即使有警告也继续添加
    const project = {
        name: newProjectForm.name,
        link: newProjectForm.link,
        startDate: newProjectForm.startDate ? newProjectForm.startDate.format('YYYY-MM-DD') : '',
        endDate: newProjectForm.endDate ? newProjectForm.endDate.format('YYYY-MM-DD') : '',
        description: newProjectForm.description
    };

    formState.projects.push(project);
    projectDates.value.push({
        startDate: newProjectForm.startDate,
        endDate: newProjectForm.endDate
    });

    saveDataToStorage();
    showNewProjectForm.value = false;
    message.success('项目已添加');
};

// 取消添加新项目
const cancelNewProjectForm = () => {
    showNewProjectForm.value = false;
};

// 保存项目修改
const saveProject = (index) => {
    // 非空字段验证
    if (!formState.projects[index].name) {
        message.warning('请填写项目名称');
    }

    saveDataToStorage();
    message.success('修改已保存');
};

// 添加新教育经历相关方法
const startAddEducation = () => {
    showNewEducationForm.value = true;
    Object.keys(newEducationForm).forEach(key => {
        newEducationForm[key] = '';
    });
    newEducationForm.graduationDate = null;
};

// 保存新教育经历
const addNewEducation = () => {
    // 非空字段验证
    if (!newEducationForm.school) {
        message.warning('请填写学校名称');
    }
    if (!newEducationForm.major) {
        message.warning('请填写专业名称');
    }
    if (!newEducationForm.degree) {
        message.warning('请选择学历');
    }

    // 即使有警告也继续添加
    const education = {
        school: newEducationForm.school,
        major: newEducationForm.major,
        degree: newEducationForm.degree,
        graduationDate: newEducationForm.graduationDate ? newEducationForm.graduationDate.format('YYYY') : '',
        experience: newEducationForm.experience
    };

    formState.education.push(education);
    educationDates.value.push({
        graduationDate: newEducationForm.graduationDate
    });

    saveDataToStorage();
    showNewEducationForm.value = false;
    message.success('教育经历已添加');
};

// 取消添加新教育经历
const cancelNewEducationForm = () => {
    showNewEducationForm.value = false;
};

// 保存教育经历修改
const saveEducation = (index) => {
    // 非空字段验证
    if (!formState.education[index].school) {
        message.warning('请填写学校名称');
    }
    if (!formState.education[index].major) {
        message.warning('请填写专业名称');
    }
    if (!formState.education[index].degree) {
        message.warning('请选择学历');
    }

    saveDataToStorage();
    message.success('修改已保存');
};

// 添加其他信息相关状态
const birthdayDate = ref(null);

// 添加其他信息字段显示方法
const showOtherInfoField = (field: string) => {
    switch (field) {
        case 'gender':
            formState.gender = '';
            break;
        case 'height':
            formState.height = 170;
            break;
        case 'weight':
            formState.weight = 60;
            break;
        case 'ethnicity':
            formState.ethnicity = '汉族';
            break;
        case 'currentSalary':
            formState.currentSalary = 0;
            break;
        case 'politicalStatus':
            formState.politicalStatus = '';
            break;
        case 'maritalStatus':
            formState.maritalStatus = '';
            break;
        case 'birthday':
            birthdayDate.value = dayjs();
            formState.birthday = dayjs().format('YYYY-MM-DD');
            break;
        case 'personalWebsite':
            formState.personalWebsite = '';
            break;
    }
    saveDataToStorage();
};

// 添加移除其他信息字段方法
const removeOtherInfoField = (field: string) => {
    switch (field) {
        case 'gender':
            formState.gender = '';
            break;
        case 'height':
            delete formState.height;
            break;
        case 'weight':
            delete formState.weight;
            break;
        case 'ethnicity':
            delete formState.ethnicity;
            break;
        case 'currentSalary':
            delete formState.currentSalary;
            break;
        case 'politicalStatus':
            delete formState.politicalStatus;
            break;
        case 'maritalStatus':
            delete formState.maritalStatus;
            break;
        case 'birthday':
            formState.birthday = '';
            birthdayDate.value = null;
            break;
        case 'personalWebsite':
            formState.personalWebsite = undefined;
            break;
    }
    saveDataToStorage();
};

// 添加更新生日的方法
const updateBirthday = () => {
    formState.birthday = birthdayDate.value ? birthdayDate.value.format('YYYY-MM-DD') : '';
    saveDataToStorage();
};

// 添加计算属性来判断是否有任何其他信息
const hasAnyOtherInfo = computed(() => {
    return formState.height ||
        formState.weight ||
        formState.ethnicity ||
        formState.politicalStatus ||
        formState.maritalStatus ||
        formState.birthday ||
        formState.personalWebsite !== undefined;
});
</script>

<style scoped>
.personal-info-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    position: relative;
    padding-bottom: 30px; /* 为页脚预留空间 */
}

.content-area {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
}

.footer-actions {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 10;
    text-align: center;
    width: 100%;
}

.section-title {
    font-size: 18px;
    font-weight: bold;
    margin: 24px 0 16px 0;
    color: #333;
}

.info-section {
    margin-bottom: 20px;
}

.info-field-label {
    font-size: 16px;
    margin-bottom: 8px;
    color: #333;
}

.tag-container {
    margin-bottom: 16px;
}

.delete-link {
    color: #ff4d4f;
    margin-top: 4px;
    cursor: pointer;
    font-size: 14px;
}

.input-group {
    display: flex;
    align-items: center;
}

.number-input {
    flex: 1;
}

.input-suffix {
    padding: 0 10px;
    height: 32px;
    line-height: 32px;
    background-color: v-bind('token.colorPrimary');
    border: 1px solid v-bind('token.colorPrimary');
    border-left: none;
    border-radius: 0 2px 2px 0;
}

.full-width {
    width: 100%;
}

.uploaded-avatar {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 6px;
}

:deep(.ant-upload-select) {
    width: 128px;
    height: 128px;
}

:deep(.avatar-uploader .ant-upload) {
    width: 128px;
    height: 128px;
    border-radius: 6px;
    border: 1px dashed #d9d9d9;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: border-color 0.3s;
}

:deep(.avatar-uploader .ant-upload:hover) {
    border-color: v-bind('token.colorPrimary');
}

:deep(.avatar-uploader .ant-upload > div) {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
}

:deep(.avatar-uploader .ant-upload .uploaded-avatar) {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 6px;
}

:deep(.ant-form-item) {
    margin-bottom: 16px;
}

.section-container {
    margin-top: 16px;
    padding: 5px;
    overflow: hidden;
    border: 1px solid v-bind('token.colorBorder');
    border-radius: 4px;

}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.section-title-row {
    display: flex;
    align-items: center;
}

.icon-circle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    margin-right: 10px;
}

.section-title-row h2 {
    margin: 0;
    font-size: 16px;
    font-weight: bold;
}

.add-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    border-radius: 4px;
    transition: all 0.3s;
}

.form-container {
    border: 1px solid v-bind('token.colorBorder');
    border-radius: 4px;
    padding: 5px;
}

.work-exp-header {
    display: flex;
    align-items: center;
}

.work-company {
    font-weight: bold;
    margin-right: 10px;
}

.work-position {
    margin-right: 10px;
}

.work-exp-content {
    padding: 10px;
}

.work-exp-description {
    margin-bottom: 10px;
}

.work-exp-actions {
    display: flex;
    justify-content: flex-end;
}

.rich-editor-container {
    position: relative;
}

.add-panel-header {
    display: flex;
    align-items: center;
}

.other-info-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    margin-bottom: 20px;
}

.tag-container {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 20px;
}

.flex {
    display: flex;
}

.justify-between {
    justify-content: flex-start;
}

.item-wrapper {
    position: relative;
}

.upload-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    color: v-bind('token.colorPrimary');
}

:deep(.ant-collapse-borderless >.ant-collapse-item) {
    border: none !important;
}
:deep(.ant-form-item .ant-form-item-explain-error) {
   font-size: 10px;
   text-align: center;
}
</style>