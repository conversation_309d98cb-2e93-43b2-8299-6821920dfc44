<template>
  <div class="general-settings">
    <div class="settings-title">通用设置</div>
    <div class="settings-content">
      <div class="setting-item">
        <div class="setting-label">
          <global-outlined class="setting-icon" />
          <span>语言</span>
        </div>
        <div class="setting-control">
          <a-select v-model:value="language" style="width: 200px">
            <a-select-option class="my-1" value="简体中文">简体中文</a-select-option>
            <a-select-option class="my-1" value="English">English</a-select-option>
          </a-select>
        </div>
      </div>

      <div class="setting-item">
        <div class="setting-label">
          <search-outlined class="setting-icon" />
          <span>搜索引擎</span>
        </div>
        <div class="setting-control">
          <div class="search-engine-container">
            <a-select v-model:value="searchEngine" style="width: 200px">
              <a-select-option class="my-1" value="baidu">百度</a-select-option>
              <a-select-option class="my-1" value="google">Google</a-select-option>
              <a-select-option class="my-1" value="bing">Bing</a-select-option>
            </a-select>
          </div>
        </div>
      </div>


      <div class="setting-item">
        <div class="setting-label">
          <swap-outlined class="setting-icon" />
          <span>代理模式</span>
        </div>
        <div class="setting-control">
          <a-select v-model:value="proxyMode" style="width: 200px">
            <a-select-option value="系统代理">系统代理</a-select-option>
            <a-select-option value="自定义代理">自定义代理</a-select-option>
            <a-select-option value="无代理">无代理</a-select-option>
          </a-select>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { Modal } from 'ant-design-vue';
import {
  GlobalOutlined,
  SearchOutlined,
  FileTextOutlined,
  SwapOutlined,
  AppstoreOutlined,
  EyeOutlined,
  DesktopOutlined,
  ReloadOutlined,
  PlusOutlined
} from '@ant-design/icons-vue';

// 状态管理
const language = ref('简体中文');
const searchEngine = ref('baidu');
const searchModel = ref('qwen2.5:14b');
const proxyMode = ref('系统代理');
const artifactsEnabled = ref(true);
const searchPreviewEnabled = ref(true);
const screenProtectionEnabled = ref(false);

// 重置数据确认
const confirmReset = () => {
  Modal.confirm({
    title: '确认重置',
    content: '确定要重置所有数据吗？此操作不可恢复。',
    okText: '确认',
    cancelText: '取消',
    onOk() {
      // 在此处实现重置逻辑
      console.log('数据已重置');
    }
  });
};
</script>

<style scoped lang="css">
.general-settings {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}

.settings-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.settings-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
}

.setting-label {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #333;
}

.setting-icon {
  margin-right: 10px;
  font-size: 18px;
}

.search-engine-container {
  display: flex;
  align-items: center;
  gap: 10px;
}
</style>
