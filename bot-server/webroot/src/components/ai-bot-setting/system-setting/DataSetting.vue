<template>
    <div class="data-setting-container">
        <!-- 数据目录 -->
        <a-card title="数据目录" class="setting-card">
            <!-- 应用数据 -->
            <div class="setting-item path-item">
                <span class="item-label">应用数据</span>
                <div class="path-container">
                    <a-input v-model:value="appDataPath" />
                    <a-button type="text" class="folder-btn" @click="selectFolder('appData')">
                        <template #icon>
                            <FolderOpenOutlined />
                        </template>
                    </a-button>
                </div>
            </div>

            <!-- 应用日志 -->
            <div class="setting-item path-item">
                <span class="item-label">应用日志</span>
                <div class="path-container">
                    <a-input v-model:value="appLogPath" />
                    <a-button type="text" class="folder-btn" @click="selectFolder('appLog')">
                        <template #icon>
                            <FolderOpenOutlined />
                        </template>
                    </a-button>
                </div>
            </div>

            <!-- 知识库文件 -->
            <div class="setting-item">
                <span class="item-label">知识库文件</span>
                <div class="item-controls">
                    <a-button @click="deleteKnowledgeFiles">删除文件</a-button>
                </div>
            </div>

            <!-- 清除缓存 -->
            <div class="setting-item">
                <span class="item-label">清除缓存</span>
                <div class="item-controls">
                    <a-button @click="clearCache">清除缓存</a-button>
                </div>
            </div>
        </a-card>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import {
    CopyOutlined,
    DownloadOutlined,
    UploadOutlined,
    FolderOpenOutlined
} from '@ant-design/icons-vue';
import { useElectronStore } from '@/components/store/electron';
// import { ipcRenderer } from 'electron';
// import path from 'path';
// import fs from 'fs';

const electron = useElectronStore();

// 数据路径
const appDataPath = ref('');
const appLogPath = ref('');

// 页面初始化时获取路径
onMounted(() => {
    fetchPaths();
});

// 获取应用路径信息
const fetchPaths = async () => {
    try {
        // 获取用户数据路径
        // const userDataPath = electron.getUserDataPath();

        // 检查是否有用户自定义的路径
        const customUserDataPath = localStorage.getItem('customUserDataPath');
        const customLogPath = localStorage.getItem('customLogPath');

        // 应用自定义路径或默认路径
        // appDataPath.value = customUserDataPath || userDataPath;

        // 日志路径
        if (customLogPath) {
            appLogPath.value = customLogPath;
        } else {
            // 推断日志路径 (通常在用户数据目录下的logs文件夹中)
            // appLogPath.value = path.join(userDataPath, 'logs', 'main.log');
        }
    } catch (error) {
        message.error('获取应用路径失败');
        console.error('获取应用路径失败:', error);
    }
};

// 复制路径到剪贴板
const copyPath = (path: string) => {
    navigator.clipboard.writeText(path)
        .then(() => {
            message.success('路径已复制到剪贴板');
        })
        .catch(() => {
            message.error('复制失败');
        });
};





// 确认重置
const confirmReset = () => {
    Modal.confirm({
        title: '警告',
        content: '确定要重置所有数据吗？此操作不可逆!',
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
            resetData();
        }
    });
};

// 重置数据
const resetData = async () => {
 
};

// 删除知识库文件
const deleteKnowledgeFiles = async () => {

};

// 清除缓存
const clearCache = async () => {

};

// 选择文件夹
const selectFolder = async (type: 'appData' | 'appLog') => {
 
};

// 保存用户数据路径
const saveUserDataPath = async (newPath: string) => {
    try {
        // 这里可以调用后端API或本地存储保存用户自定义的路径
        // 示例：使用localStorage保存
        localStorage.setItem('customUserDataPath', newPath);
        message.success('应用数据路径已更新');
    } catch (error) {
        message.error('保存路径失败');
        console.error('保存路径失败:', error);
    }
};

// 保存日志路径
const saveLogPath = async (newPath: string) => {
    try {
        // 这里可以调用后端API或本地存储保存用户自定义的路径
        // 示例：使用localStorage保存
        localStorage.setItem('customLogPath', newPath);
        message.success('应用日志路径已更新');
    } catch (error) {
        message.error('保存路径失败');
        console.error('保存路径失败:', error);
    }
};
</script>

<style scoped lang="scss">
.data-setting-container {
    padding: 16px;
}

.setting-card {
    margin-bottom: 16px;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
}

.setting-item:last-child {
    border-bottom: none;
}

.item-label {
    font-size: 14px;
    color: #333;
}

.item-controls {
    display: flex;
    gap: 8px;
}

.path-container {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 4px;
    width: 70%;
}

.path-input {
    flex: 1;
    font-size: 13px;
    background-color: transparent;
    border: none;
}

.copy-btn,
.folder-btn {
    padding: 0 4px;
}

.path-item {
    align-items: flex-start;
}
</style>
