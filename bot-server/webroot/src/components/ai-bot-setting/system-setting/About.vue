<template>
  <div class="about-container size-full flex flex-col justify-center items-center">
    <div class="about-content size-full">
      <div class="logo-section">
        <!-- <img src="./app.png" alt="DeepChat Logo" class="logo" /> -->
        <h1>AI-Bot</h1>
        <div class="version">v{{ version }}</div>
      </div>
    
      <div class="divider"></div>
      
      <div class="info-section">
        <div class="info-item">
          <span class="info-label">版本：</span>
          <span class="info-value">{{ version }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">系统：</span>
          <span class="info-value">{{ systemInfo }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">构建时间：</span>
          <span class="info-value">{{ buildTime }}</span>
        </div>
      </div>
      
      <div class="divider"></div>
      
      <div class="links-section">
        <a-button type="link" @click="checkUpdate">
          <template #icon><sync-outlined /></template>
          检查更新
        </a-button>
      </div>
      
      <div class="divider"></div>
    
      <div class="footer">
        <p>© {{ currentYear }} DeepChat 团队. 保留所有权利</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { GlobalOutlined, SyncOutlined, FileTextOutlined } from '@ant-design/icons-vue';

// 获取版本信息
const version = ref('0.0.9');
const systemInfo = ref('');
const buildTime = ref('');
const currentYear = new Date().getFullYear();

// 团队成员信息
const teamMembers = [
  { name: '张三', role: '项目负责人' },
  { name: '李四', role: '前端开发' },
  { name: '王五', role: '后端开发' },
  { name: '赵六', role: 'UI设计师' }
];

// 获取系统信息
onMounted(async () => {
  try {
    // 这里可以从electron获取系统信息
    // 如果有electron的bridge，可以通过它来获取系统信息
    systemInfo.value = navigator.userAgent;
    buildTime.value = new Date().toLocaleDateString();
  } catch (error) {
    console.error('获取系统信息失败', error);
    systemInfo.value = '未知';
    buildTime.value = '未知';
  }
});

// 打开网站
const openWebsite = () => {
  if (window.electronAPI) {
    window.electronAPI.openExternal('https://deepchat.example.com');
  } else {
    window.open('https://deepchat.example.com', '_blank');
  }
};

// 检查更新
const checkUpdate = async () => {
  if (window.electronAPI) {
    await window.electronAPI.checkForUpdates();
  } else {
    console.log('检查更新功能在浏览器环境下不可用');
  }
};

// 查看许可协议
const viewLicense = () => {
  if (window.electronAPI) {
    window.electronAPI.openLicense();
  } else {
    window.open('https://deepchat.example.com/license', '_blank');
  }
};
</script>

<style scoped>
.about-container {
  display: flex;
  justify-content: center;
  align-items: center;

}

.about-content {
  max-width: 50vw;
  width: 100%;
  border-radius: 8px;
  padding: 30px;
  text-align: center;
}

.logo-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
}

.logo {
  width: 80px;
  height: 80px;
  margin-bottom: 10px;
}

h1 {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
  color: var(--primary-color, #1890ff);
}

.version {
  font-size: 14px;
  color: var(--secondary-text-color, #666666);
  margin-top: 5px;
}

.description {
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 20px;
  color: var(--text-color, #333333);
}

.divider {
  height: 1px;
  background-color: var(--border-color, #f0f0f0);
  margin: 5px 0;
}

.info-section {
  text-align: left;
}

.info-item {
  display: flex;
  font-size: 14px;
}

.info-label {
  width: 80px;
  color: var(--secondary-text-color, #666666);
}

.info-value {
  flex: 1;
  color: var(--text-color, #333333);
}

.links-section {
  display: flex;
  justify-content: center;
  gap: 15px;
  flex-wrap: wrap;
}

.team-section {
  text-align: left;
}

.team-section h3 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 15px;
  color: var(--primary-color, #1890ff);
}

.team-members {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 15px;
}

.team-member {
  background-color: var(--tag-bg-color, #f5f5f5);
  border-radius: 6px;
  padding: 10px;
  text-align: center;
}

.member-name {
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 5px;
}

.member-role {
  font-size: 12px;
  color: var(--secondary-text-color, #666666);
}

.footer {
  font-size: 12px;
  color: var(--secondary-text-color, #666666);
  margin-top: 20px;
}

/* 暗黑模式适配 */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-color: #1f1f1f;
    --card-bg-color: #2d2d2d;
    --text-color: #f0f0f0;
    --secondary-text-color: #a0a0a0;
    --border-color: #3d3d3d;
    --tag-bg-color: #363636;
  }
}
</style>
