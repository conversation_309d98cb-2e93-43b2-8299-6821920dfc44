import axiosForServer from "@/plugins/axiosForServer";
import {Result} from "@/components/common/model/system";
import { message } from 'ant-design-vue';

export function getSetting(): Promise<any> {
    return new Promise((resolve, reject) => {
        axiosForServer.get<Result<any>>('/api/setting/info')
            .then(({data}) => {
                if (data.code === 200) {
                    resolve(data.data)
                } else {
                    reject(new Error('Failed to get settings'))
                }
            })
            .catch(error => {
                reject(error)
            })
    })
}

export function updateSetting(id: string, data: any): Promise<any> {
    let stringify = JSON.stringify(data);  // 转换成 JSON 字符串
    return new Promise((resolve, reject) => {
        axiosForServer.post<Result<any>>('/api/setting/update', {
            id: id,
            value: stringify
        })
            .then(({data}) => {
                if (data.code === 200) {
                    resolve(data.data)
                } else {
                    reject(new Error('Failed to update settings'))
                }
            })
            .catch(error => {
                reject(error)
            })
    })
}

export function clear(): Promise<any> {
    return new Promise((resolve, reject) => {
        axiosForServer.post<Result<any>>('/api/setting/clear')
            .then(({data}) => {
                if (data.code === 200) {
                    resolve(data.data)
                } else {
                    reject(new Error(data.msg || 'Failed to clear settings'))
                }
            })
            .catch(error => {
                reject(error)
            })
    })
}
