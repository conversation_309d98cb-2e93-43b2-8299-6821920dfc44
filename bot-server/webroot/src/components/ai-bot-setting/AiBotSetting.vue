<template>
  <a-modal
      v-model:open="open"
      title="系统设置"
      style="width: 90vw;"
      class="setting-modal blur-modal"
      :footer="null"
      centered
  >
    <a-layout class="setting-layout">
      <a-layout-sider :width="siderWidth"
                      style="background: transparent; min-width: 180px;">
        <div class="custom-menu">
          <div class="submenu">
            <div class="submenu-title" @click="toggleSubmenu('system')">
              <span class="icon iconfont shezhi"></span>
              <span>系统设置</span>
              <span class="arrow" :class="{'expanded': expandedKeys.includes('system')}">
                <span class="icon iconfont xiajiantou"></span>
              </span>
            </div>

            <div class="submenu-content" v-show="expandedKeys.includes('system')">
              <div class="menu-item sub-item"
                   :class="{'selected': currentSetting === 'SystemSetting'}"
                   @click="handleSelect('SystemSetting')">
                <span class="icon iconfont shezhi"></span>
                <span>基础设置</span>
              </div>

              <div class="menu-item sub-item"
                   :class="{'selected': currentSetting === 'AppearanceSetting'}"
                   @click="handleSelect('AppearanceSetting')">
                <span class="icon iconfont zhuti"></span>
                <span>外观设置</span>
              </div>
            </div>
          </div>

          <div class="menu-item"
               :class="{'selected': currentSetting === 'TtsSetting'}"
               @click="handleSelect('TtsSetting')">
            <span class="icon iconfont yuyin"></span>
            <span>语音设置</span>
          </div>

          <div class="submenu">
            <div class="submenu-title" @click="toggleSubmenu('sub1')">
              <span class="icon iconfont kaifangpingtai"></span>
              <span>开放平台</span>
              <span class="arrow" :class="{'expanded': expandedKeys.includes('sub1')}">
                <span class="icon iconfont xiajiantou"></span>
              </span>
            </div>

            <div class="submenu-content" v-show="expandedKeys.includes('sub1')">
              <div class="menu-item sub-item"
                   :class="{'selected': currentSetting === 'MapSetting'}"
                   @click="handleSelect('MapSetting')">
                <span class="icon iconfont ditu"></span>
                <span>地图平台</span>
              </div>
            </div>
          </div>
        </div>
      </a-layout-sider>
      <a-layout-content class="content-wrapper">
        <component :is="currentSetting" class="content-inner"></component>
      </a-layout-content>
    </a-layout>
  </a-modal>
</template>

<script setup lang="ts">
import {computed, onMounted, ref} from "vue";
import {useGlobalSetting} from "@/components/store/bot-global-setting";
import {useAiBot} from "@/components/store/bot";
import TtsSetting from './tts-setting/TtsSetting.vue'
import AppearanceSetting from './system-setting/AppearanceSetting.vue'

const open = defineModel({default: false});
const currentSetting = ref('SystemSetting')
const expandedKeys = ref<string[]>(['sub1', 'system'])
const globalSettings = useGlobalSetting()
const bot = useAiBot()


// 修改 sider 宽度计算逻辑为对话框宽度的 1/4
const siderWidth = computed(() => {
  return Math.floor(window.innerWidth * 0.7 * 0.25); // 70vw * 1/4
});

function handleSelect(key: string) {
  currentSetting.value = key
}

function toggleSubmenu(key: string) {
  const index = expandedKeys.value.indexOf(key)
  if (index > -1) {
    expandedKeys.value.splice(index, 1)
  } else {
    expandedKeys.value.push(key)
  }
}

onMounted(() => {
  // 初始化配置
  globalSettings.getSetting()
})
</script>

<style>
.setting-modal .ant-modal-body {
  padding: 0 !important;
}

.setting-modal .ant-modal-header {
  border-style: none !important;
  margin-bottom: 8px !important;
  padding: 16px 24px !important;
}

.setting-modal .ant-modal-content {
  padding: 0 !important;
  border-radius: 8px;
  overflow: hidden;
}

.setting-modal .ant-modal-close {
  top: 12px !important;
  right: 16px !important;
}
</style>
<style scoped>

/* 统一布局大小 */
.setting-layout {
  height: calc(100% - 55px); /* 减去header的高度 */
  overflow: hidden;
  min-height: 400px;
  margin: 0;
  background: transparent;
  border-radius: 0 0 8px 8px;
}


.content-wrapper {
  height: 100%;
  overflow: hidden;
  padding: 8px 16px;
  box-sizing: border-box;
  background: transparent;
  border-radius: 0 0 8px 0;
}

.content-inner {
  height: 100%;
  overflow-y: auto;
  padding: 8px;
  background: transparent;
}

/* 图标样式 */
.icon.iconfont {
  font-size: 16px;
  margin-right: 8px;
  display: inline-flex;
  align-items: center;
}

/* 修改布局相关样式 */
.setting-layout {
  height: 100%;
  overflow: hidden;
}

/* 修改菜单容器样式 */
.custom-menu {
  height: 100%;
  overflow-y: auto;
  padding: 8px;
  color: rgba(0, 0, 0, 0.88);
  user-select: none;
  min-width: 100px;
  box-sizing: border-box;
}

/* 修改内容区域样式 */
.content-wrapper {
  height: 100%;
  overflow: hidden;
  padding: 16px;
}

.content-inner {
  height: 100%;
  overflow-y: auto;
}

/* 移除最大高度限制相关样式 */
.ant-modal {
  max-height: none;
}

.ant-modal-content {
  max-height: none;
}


/* 修改滚动条样式 */
.custom-menu::-webkit-scrollbar,
.content-inner::-webkit-scrollbar {
  width: 6px;
}

.custom-menu::-webkit-scrollbar-thumb,
.content-inner::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}


.custom-menu::-webkit-scrollbar-track,
.content-inner::-webkit-scrollbar-track {
  background-color: transparent;
}

/* 自定义菜单样式 */
.custom-menu {
  height: 100%;
  overflow-y: auto;
  padding: 4px;
}

/* 修改菜单项样式 */
.menu-item {
  padding: 0 12px;
  height: 40px;
  line-height: 40px;
  margin: 4px 0;
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  align-items: center;
  transition: all 0.3s;
  white-space: nowrap;
  font-size: 14px;
  user-select: none;
  width: 100%;
  box-sizing: border-box;
}

.menu-item span {
  display: inline-block;
}

.menu-item:hover {
  background-color: rgba(0, 0, 0, 0.06);
}


.menu-item.selected {
  background-color: #e6f4ff;
  color: #1890ff;
  font-weight: 500;
}


/* 修改子菜单标题样式 */
.submenu-title {
  padding: 0 12px;
  height: 40px;
  line-height: 40px;
  cursor: pointer;
  display: flex;
  align-items: center;
  margin: 4px 0;
  border-radius: 4px;
  transition: all 0.3s;
  white-space: nowrap;
  font-size: 14px;
  user-select: none;
  width: 100%;
  box-sizing: border-box;
}

.submenu-title span:not(.arrow) {
  display: inline-block;
}

.submenu-title:hover {
  background-color: rgba(0, 0, 0, 0.06);
}


.submenu-content {
  width: 100%;
  box-sizing: border-box;
}

.sub-item {
  padding-left: 36px !important;
  width: 100% !important;
  box-sizing: border-box !important;
}

.arrow {
  margin-left: auto;
  transition: transform 0.3s;
}

.arrow.expanded {
  transform: rotate(-180deg);
}

/* 滚动条样式 */
.custom-menu::-webkit-scrollbar {
  width: 6px;
}

.custom-menu::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.custom-menu::-webkit-scrollbar-track {
  background-color: transparent;
}

/* 图标样式调整 */
.icon.iconfont {
  font-size: 16px;
  margin-right: 8px;
  display: inline-flex;
  align-items: center;
  width: 16px;
  height: 16px;
  justify-content: center;
}
</style>