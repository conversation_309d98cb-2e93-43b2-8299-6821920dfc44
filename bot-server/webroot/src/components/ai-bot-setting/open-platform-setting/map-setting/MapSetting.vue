<template>
  <PageLayout>
    <a-card class="size-full">
      <div class="size-full">
        <a-divider orientation="left">开放平台配置</a-divider>
        <div class="input-wrapper">
          <div class="select-wrapper" ref="selectWrapper">
            <a-select
                class="w-full"
                v-model:value="currentMap"
                :field-names="{ label: 'name', value: 'type' }"
                :options="globalSetting.setting.map.list"
                @select="updateCurrentMap"
                :getPopupContainer="getPopupContainer"
                :status="mapError ? 'error' : ''"
            
            >
            </a-select>
            <div v-if="mapError" class="error-message">
              请选择地图类型
            </div>
          </div>
        </div>
        <a-form-item
            label="key"
            class="mt-3"
        >
          <a-input-password 
            @change="updateKey" 
            v-model:value="map.key"
            :status="keyError ? 'error' : ''"
          />
          <div v-if="keyError" class="error-message">
            请输入有效的 API Key
          </div>
        </a-form-item>
      </div>
    </a-card>
  </PageLayout>

</template>

<script setup lang="ts">
import {onMounted, ref} from "vue";
import {MapSettingEntity} from "@/components/ai-bot-setting/model";
import {useGlobalSetting} from "@/components/store/bot-global-setting";
import {message} from "ant-design-vue";
import PageLayout from "@/components/ai-bot-layout/PageLayout.vue";

const globalSetting = useGlobalSetting()
const currentMap = ref<string>(globalSetting.setting.map.currentMap)
const map = ref<MapSettingEntity>({
  name: '',
  type: '',
})

const mapError = ref(false)
const keyError = ref(false)

const [messageApi, contextHolder] = message.useMessage();
const selectWrapper = ref(null)

function getPopupContainer(node: HTMLElement) {
  return selectWrapper.value
}

function updateCurrentMap(item: string) {
  mapError.value = false
  globalSetting.setting.map.currentMap = item
  globalSetting.updateSetting(globalSetting.setting)
  map.value = globalSetting.setting.map.list.find((obj) => obj.type === item)
}

function updateKey() {
  if (!map.value.key?.trim()) {
    keyError.value = true
    return
  }
  keyError.value = false
  
  globalSetting.setting.map.list.forEach((obj) => {
    if (obj.type === currentMap.value) {
      obj.key = map.value.key
    }
  })
  globalSetting.updateSetting(globalSetting.setting)
}

onMounted(() => {
  map.value = globalSetting.setting.map.list.find((obj) => obj.type === currentMap.value)
})
</script>


<style scoped>
.input-wrapper {
  position: relative;
}

.error-message {
  position: absolute;
  left: 0;
  top: 100%;
  margin-top: 4px;
  color: #ff4d4f;
  font-size: 12px;
  line-height: 1.5;
  padding: 4px 8px;
  background-color: #fff2f0;
  border-radius: 2px;
  z-index: 1;
}

</style>