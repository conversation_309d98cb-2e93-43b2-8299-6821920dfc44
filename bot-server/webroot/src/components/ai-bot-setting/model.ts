export interface Setting {
    map: MapSettings;
    tts: TtsSettings;
    app: AppearanceSettingEntity;
}

export class MapSettings {
    currentMap: string
    list: MapSettingEntity[]
}

export class TtsSettings {
    currentTTS: string
    list: TTSSettingEntity[]
    enabled: boolean = false
    model: string = 'edge'
    volume: number = 50
    rate: number = 1
}

export interface MapSettingEntity {
    name: string;
    type: string;
    key?: string;
}

export class TTSSettingEntity {
    name: string;
    api: string;
    key?: string;
    enabled?: boolean;
    type?: string;
}

export class AppearanceSettingEntity {
    bg: string;
    cardColor: string;
    blurSize: number;
}