<template>
  <ResizablePanel 
    ref="resizablePanelRef"
    :initial-width="initialWidth"
    :min-width="minWidth"
    :max-width-percent="maxWidthPercent"
    :default-drag-mode="defaultDragMode"
    :remember-user-preference="rememberUserPreference"
    storage-key-prefix="conversation-panel"
    @resize="handleResize"
    @resize-start="handleResizeStart"
    @resize-end="handleResizeEnd"
    @mode-change="handleModeChange"
  >
    <ConversationUI />
  </ResizablePanel>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import ConversationUI from './ConversationUI.vue';
import ResizablePanel from '@/components/ai-bot-widget/ResizablePanel.vue';

// 定义组件属性
const props = defineProps({
  // 默认拖拽模式: 'preview' 预览模式，'direct' 实时模式
  defaultDragMode: {
    type: String,
    default: 'preview', // 默认使用实时模式
    validator: (value: string) => ['preview', 'direct'].includes(value)
  },
  // 是否记住用户的拖拽模式选择
  rememberUserPreference: {
    type: Boolean,
    default: true
  },
  // 初始宽度
  initialWidth: {
    type: String,
    default: '150px'
  },
  // 最小宽度 (px)
  minWidth: {
    type: Number,
    default: 150
  },
  // 最大宽度 (窗口宽度百分比)
  maxWidthPercent: {
    type: Number,
    default: 40
  }
});

// 引用子组件
const resizablePanelRef = ref(null);

// 发出事件
const emit = defineEmits(['modeChange', 'resize', 'resizeStart', 'resizeEnd']);

// 事件处理函数，只是简单地转发事件
const handleResize = (data) => {
  emit('resize', data);
};

const handleResizeStart = (data) => {
  emit('resizeStart', data);
};

const handleResizeEnd = (data) => {
  emit('resizeEnd', data);
};

const handleModeChange = (mode) => {
  emit('modeChange', mode);
};

// 公开方法，代理到内部 ResizablePanel 组件
defineExpose({
  setDragMode: (mode) => {
    if (resizablePanelRef.value) {
      resizablePanelRef.value.setDragMode(mode);
    }
  },
  getDragMode: () => {
    return resizablePanelRef.value ? resizablePanelRef.value.getDragMode() : 'direct';
  },
  getCurrentWidth: () => {
    return resizablePanelRef.value ? resizablePanelRef.value.getCurrentWidth() : 0;
  },
  setWidth: (width) => {
    if (resizablePanelRef.value) {
      resizablePanelRef.value.setWidth(width);
    }
  },
  toggleDragMode: () => {
    if (resizablePanelRef.value) {
      resizablePanelRef.value.toggleDragMode();
    }
  }
});
</script>

<style scoped>
/* 所有样式已移至 ResizablePanel 组件中 */
</style> 