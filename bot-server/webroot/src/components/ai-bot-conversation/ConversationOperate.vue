<template>
  <a-button 
    class="delete-btn" 
    @click.stop="deleteConv" 
    size="small" 
    type="text"
    :title="'删除会话'"
  >
    <template #icon>
      <icon>
        <icon-font type="shanchu"/>
      </icon>
    </template>
  </a-button>
</template>

<script setup lang="ts">
import {delConversation, getConversation} from "@/components/common/manageRequest";
import {useGptStore} from "@/components/store/gpt";

const gpt = useGptStore()
const props = defineProps<{
  id: string
}>()

function deleteConv() {
  let index = -1
  let id = ""
  if (props.id == gpt.CurrentChat.Current.Conversation.id) {
    index = gpt.CurrentChat.conversationList.findIndex(item => {
      return item.Conversation.id === props.id
    })
    if (index > -1 && index < gpt.CurrentChat.conversationList.length - 1) {
      index++
    } else {
      index = 0
    }
    id = gpt.CurrentChat.conversationList[index].Conversation.id
  }
  delConversation(props.id).then((data) => {
    getConversation().then(data => {
      gpt.setConversation(data)
      if (id != "") {
        gpt.CurrentChat.selectedKey = [id]
        gpt.SetCurrentChatById(id)
      }
    })
  })
}
</script>

<style scoped>
.delete-btn {
  opacity: 0;
  transition: opacity 0.2s ease;
  color: rgba(0, 0, 0, 0.45);
}

/*.delete-btn:hover {
  color: #ff4d4f;
}*/

:deep(.conversation-item:hover) .delete-btn {
  opacity: 1;
}


</style>