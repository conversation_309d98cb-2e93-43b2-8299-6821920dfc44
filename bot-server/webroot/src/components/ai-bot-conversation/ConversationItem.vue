<template>
  <div class="conversation-item my-1" :class="{
    'conversation-item-active': isSelected,
    'shake-animation': isShaking,
    'deleting': isDeleting,
    'creating': isCreating
  }" @click="handleItemClick">
    <div class="conversation-content">
      <div class="conversation-info">
        <div class="conversation-title" :title="title">{{ title }}</div>
      </div>
      <button class="delete-btn" @click.stop="handleDelete"
              :title="isSelected && isReplying ? '消息回复中，无法删除' : '删除会话'">
        <span class="close-icon">×</span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import {computed, defineEmits, defineProps, onMounted, ref} from 'vue'
import {delConversation, getConversation} from "@/components/common/manageRequest";
import {useGptStore} from "@/components/store/gpt";
import {theme} from 'ant-design-vue';

const {useToken} = theme;
const {token} = useToken();
const gpt = useGptStore()
const props = defineProps<{
  id: string
  title: string
  isSelected: boolean
  isShaking: boolean
  isReplying: boolean
}>()

const emit = defineEmits<{
  (e: 'click'): void
  (e: 'shake'): void
}>()

const isDeleting = ref(false)
const isCreating = ref(true)

onMounted(() => {
  // 组件挂载后立即开始创建动画
  setTimeout(() => {
    isCreating.value = false
  }, 300)
})

// 处理会话项点击
const handleItemClick = () => {
  // 只有在当前有会话正在回复时，才禁止选择其他会话
  if (gpt.ui.replying && !props.isSelected) {
    emit('shake')
    return
  }
  emit('click')
}

// 处理删除操作
const handleDelete = async () => {
  // 如果是当前选中的会话且正在回复中，显示抖动效果
  if (props.isSelected && props.isReplying) {
    emit('shake')
    return
  }

  // 开始删除动画
  isDeleting.value = true

  // 等待动画完成
  await new Promise(resolve => setTimeout(resolve, 300))

  // 如果不是当前回复中的会话，允许删除
  let index = -1
  let id = ""
  if (props.id == gpt.CurrentChat.Current.Conversation.id) {
    index = gpt.CurrentChat.conversationList.findIndex(item => {
      return item.Conversation.id === props.id
    })
    if (index > -1 && index < gpt.CurrentChat.conversationList.length - 1) {
      index++
    } else {
      index = 0
    }
    id = gpt.CurrentChat.conversationList[index].Conversation.id
  }

  delConversation(props.id).then((data) => {
    getConversation().then(data => {
      gpt.setConversation(data)
      // 检查是否还有剩余会话
      if (data.length === 0) {
        gpt.ui.showChat = false
      } else if (id != "") {
        gpt.CurrentChat.selectedKey = [id]
        gpt.SetCurrentChatById(id)
      }
    })
  })
}
</script>

<style scoped>
.conversation-item {
  width: calc(100% - 10px);
  height: 30px;
  padding: 0;
  cursor: pointer;
  border-radius: 20px;
  position: relative;
  display: flex;
  align-items: center;
  will-change: transform, opacity;
  transform: translateZ(0);
  box-sizing: border-box;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 1;
  transform-origin: center center;
  color: v-bind('token.colorTextBase');
}

.conversation-item:hover {
  background-color: v-bind('token.colorFillSecondary');
  border-color: v-bind('token.colorFillSecondary');
  color: v-bind('token.colorInfoTextHover');
}

.conversation-item.conversation-item-active {
  background-color: v-bind('token.colorPrimaryBg');
  border-color: v-bind('token.colorPrimaryBg');
  color: v-bind('token.colorPrimaryText');
}

.conversation-content {
  display: flex;
  align-items: center;
  gap: 10px;
  min-width: 0;
  flex: 1;
  height: 100%;
  padding: 0 4px 0 4px;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
}

.conversation-icon {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.3s;
  flex-shrink: 0;
  width: 28px;
  height: 28px;
  margin-left: 2px;
}

.conversation-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
  overflow: hidden;
}

.conversation-title {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 12px;
  padding-left: 4px;
  font-weight: 500;
  min-width: 0;
  transition: color 0.3s;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  max-width: 100%;
}

.delete-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  padding: 0;
  border: none;
  cursor: pointer;
  color: v-bind('token.colorTextSecondary');
  border-radius: 50%;
  background: transparent;
  opacity: 0;
  transition: opacity 0.2s, color 0.2s;
  margin-left: auto;
}

.conversation-item:hover .delete-btn {
  opacity: 1;
}

.delete-btn:hover {
  color: v-bind('token.colorErrorText');
  background-color: rgba(0, 0, 0, 0.05);
}

.close-icon {
  font-size: 16px;
  line-height: 1;
  display: block;
}

.conversation-item.conversation-item-active .delete-btn {
  background-color: transparent !important;
}

/* 抖动动画 */
@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  16.67% {
    transform: translateX(-2px);
  }
  33.33% {
    transform: translateX(2px);
  }
  50% {
    transform: translateX(-2px);
  }
  66.67% {
    transform: translateX(2px);
  }
  83.33% {
    transform: translateX(-2px);
  }
}

.shake-animation {
  animation: shake 0.25s ease-in-out;
}

.loading-icon {
  width: 28px;
  height: 28px;
  border-radius: 50%;
}

.avatar-image {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  object-fit: cover;
}

/* 删除动画 */
.deleting {
  opacity: 0;
  transform: translateX(-30px);
}

/* 新建会话动画 */
@keyframes create-animation {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.creating {
  animation: create-animation 0.3s ease-out forwards;
}
</style>