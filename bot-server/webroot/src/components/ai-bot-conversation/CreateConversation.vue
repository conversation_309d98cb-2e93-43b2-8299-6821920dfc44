<template>
  <a-modal
      v-model:open="mode"
      title="新建会话"
      @ok="add"
      @cancel="handleCancel"
      centered
  >
    <div class="input-wrapper">
      <a-input 
          ref="inputRef"
          v-model:value="inputValue" 
          placeholder="输入会话名称"
          :status="inputError ? 'error' : ''"
          @pressEnter="add"
      />
      <div v-if="inputError" class="error-message">
        会话名称不能为空
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { defineModel, ref, computed, nextTick, watch } from 'vue'
import { createConversation, getConversation } from "@/components/common/manageRequest";
import { useGptStore } from "@/components/store/gpt";
import { useAiBot } from "@/components/store/bot";

const gpt = useGptStore()
const bot = useAiBot()
const mode = defineModel({default: false})
const inputValue = ref('')
const inputError = ref(false)
const inputRef = ref()

// 监听 mode 的变化
watch(mode, (newVal) => {
  if (newVal) {
    nextTick(() => {
      inputRef.value?.focus()
    })
  }
})

// Modal 样式计算
const modalStyle = computed(() => ({
  backgroundColor: bot.theme === 'dark' ? '#1f1f1f' : '#ffffff',
  color: bot.theme === 'dark' ? 'rgba(255, 255, 255, 0.85)' : 'rgba(0, 0, 0, 0.85)',
  padding: '20px 24px'
}))

async function add() {
  if (inputValue.value.trim() === "") {
    inputError.value = true
    return
  }
  
  inputError.value = false
  let result = await createConversation(inputValue.value)
  if (result.code == 200) {
    getConversation().then(data => {
      gpt.setConversation(data)
      gpt.SetCurrentChatById(result.data)
      gpt.CurrentChat.selectedKey = [result.data]
    })
    resetAndClose()
  }
}

function handleCancel() {
  resetAndClose()
}

function resetAndClose() {
  inputValue.value = ''
  inputError.value = false
  mode.value = false
}
</script>

<style>

/* 添加错误消息样式 */
.input-wrapper {
  position: relative;
}

.error-message {
  position: absolute;
  left: 0;
  top: 100%;
  margin-top: 4px;
  color: #ff4d4f;
  font-size: 12px;
  line-height: 1.5;
  padding: 4px 8px;
  background-color: #fff2f0;
  border-radius: 2px;
  z-index: 1;
}
</style>