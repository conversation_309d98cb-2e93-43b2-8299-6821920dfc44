<template>
  <div class="conversation-container">
    <div class="conversation-list">
      <!-- 基于时间分组显示会话 -->
      <template v-for="(group, index) in groupedConversations" :key="index">
        <a-divider v-if="group.conversations.length > 0" orientation="left" class="time-divider">
          {{ group.label }}
        </a-divider>
        <ConversationItem
            v-for="conversation in group.conversations"
            :key="conversation.Conversation.id"
            :id="conversation.Conversation.id"
            :title="conversation.Conversation.title"
            :is-selected="isSelected(conversation.Conversation.id)"
            :is-shaking="shakingId === conversation.Conversation.id"
            :is-replying="gpt.ui.replying"
            :last-message-content="getLastMessageContent(conversation)"
            :last-message-time="getLastMessageTime(conversation)"
            @click="handleSelect(conversation)"
            @shake="handleShake(conversation.Conversation.id)"
        />
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import {computed, onMounted, ref} from "vue";
import {useGptStore} from "@/components/store/gpt";
import {IsEmpty} from "@/components/common/chatutils";
import ConversationItem from "@/components/ai-bot-conversation/ConversationItem.vue";
import {theme, Divider} from 'ant-design-vue';

const {useToken} = theme;
const {token} = useToken();
const gpt = useGptStore()
const shakingId = ref('');

// 判断是否为 MacOS 系统
const isMacOS = computed(() => {
  return process.platform === 'darwin';
});

// 处理抖动效果
const handleShake = (id: string) => {
  shakingId.value = id;
  setTimeout(() => {
    shakingId.value = '';
  }, 250);
};

// 基础会话过滤（搜索功能）
const filteredConversations = computed(() => {
  // 首先获取基础的会话列表
  let conversations = gpt.CurrentChat.conversationList;

  // 如果有搜索关键词，进行过滤
  if (gpt.ui.search) {
    conversations = conversations.filter(conversation =>
        conversation.Conversation.title.toLowerCase().includes(gpt.ui.search.toLowerCase())
    );
  }

  // 根据更新时间排序，较新的会话（时间较大）排在前面，较早的会话（时间较小）排在后面
  return conversations.slice().sort((a, b) => {
    // 使用正确的属性名updatedAt
    const updateAtA = a.Conversation.updatedAt || '';
    const updateAtB = b.Conversation.updatedAt || '';

    // 直接使用字符串比较，ISO 8601格式的时间字符串可以直接按字典序比较
    // 使用 B 比较 A，实现降序排列（较新的会话排在前面）
    return updateAtB.localeCompare(updateAtA);
  });
});

// 根据时间分组会话
const groupedConversations = computed(() => {
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate()); // 今天0点

  // 定义各个时间点
  const yesterday = new Date(today);
  yesterday.setDate(today.getDate() - 1); // 昨天

  const threeDaysAgo = new Date(today);
  threeDaysAgo.setDate(today.getDate() - 3); // 3天前

  const sevenDaysAgo = new Date(today);
  sevenDaysAgo.setDate(today.getDate() - 7); // 7天前

  const halfMonthAgo = new Date(today);
  halfMonthAgo.setDate(today.getDate() - 15); // 半个月前

  const halfYearAgo = new Date(today);
  halfYearAgo.setMonth(today.getMonth() - 6); // 半年前

  const oneYearAgo = new Date(today);
  oneYearAgo.setFullYear(today.getFullYear() - 1); // 一年前

  // 定义时间组
  const groups = [
    {id: 'today', label: '今天', conversations: []},
    {id: 'yesterday', label: '昨天', conversations: []},
    {id: 'three_days', label: '前3天', conversations: []},
    {id: 'seven_days', label: '7天前', conversations: []},
    {id: 'half_month', label: '半个月', conversations: []},
    {id: 'half_year', label: '半年', conversations: []},
    {id: 'one_year', label: '一年', conversations: []},
    {id: 'older', label: '更早', conversations: []}
  ];

  // 对会话进行分组
  filteredConversations.value.forEach(conversation => {
    if (!conversation.Conversation.updatedAt) return;

    const updateDate = new Date(conversation.Conversation.updatedAt);

    // 按照时间分组
    if (updateDate >= today) {
      groups[0].conversations.push(conversation); // 今天
    } else if (updateDate >= yesterday) {
      groups[1].conversations.push(conversation); // 昨天
    } else if (updateDate >= threeDaysAgo) {
      groups[2].conversations.push(conversation); // 前3天
    } else if (updateDate >= sevenDaysAgo) {
      groups[3].conversations.push(conversation); // 7天前
    } else if (updateDate >= halfMonthAgo) {
      groups[4].conversations.push(conversation); // 半个月
    } else if (updateDate >= halfYearAgo) {
      groups[5].conversations.push(conversation); // 半年
    } else if (updateDate >= oneYearAgo) {
      groups[6].conversations.push(conversation); // 一年
    } else {
      groups[7].conversations.push(conversation); // 更早
    }
  });

  // 过滤掉没有会话的分组
  const filteredGroups = groups.filter(group => group.conversations.length > 0);

  return filteredGroups;
});

// 检查是否选中
const isSelected = (id: string) => {
  if (!IsEmpty(gpt.CurrentChat.Current)) {
    return gpt.CurrentChat.Current.Conversation.id === id;
  }
  return false;
};

// 处理选择对话
const handleSelect = (conversation: any) => {
  // 如果当前正在回复中，显示点击的会话的抖动效果
  if (gpt.ui.replying) {
    handleShake(conversation.Conversation.id);
    return;
  }
  gpt.SetCurrentChatById(conversation.Conversation.id);
};

const getLastMessageContent = (conversation) => {
  // 根据网络请求数据结构访问 lastMsg 字段
  if (conversation.Conversation && conversation.Conversation.lastMsg) {
    const content = conversation.Conversation.lastMsg;
    // 截取消息内容，保留一部分
    return content.length > 30 ? content.substring(0, 30) + '...' : content;
  }
  return '';
}

const getLastMessageTime = (conversation) => {
  // 根据网络请求数据结构访问 updatedAt 字段
  if (conversation.Conversation && conversation.Conversation.updatedAt) {
    try {
      // 获取消息时间和当前时间
      const date = new Date(conversation.Conversation.updatedAt);
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

      // 创建当月第一天的日期对象
      const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

      // 时间部分格式（时:分 或 时:分:秒）
      const timeFormat = (date, includeSeconds = false) => {
        return includeSeconds
            ? `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`
            : `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
      };

      // 判断时间显示格式
      if (date >= today) {
        // 今天的消息，显示时:分:秒
        return timeFormat(date, true);
      } else if (date >= firstDayOfMonth) {
        // 当月内的消息，显示日日 时:分
        return `${date.getDate()}日 ${timeFormat(date)}`;
      } else {
        // 当月外的消息，显示年年年年年月月日日 时:分
        return `${date.getFullYear()}年${(date.getMonth() + 1)}月${date.getDate()}日 ${timeFormat(date)}`;
      }
    } catch (e) {
      console.error('时间格式化错误:', e);
      return '';
    }
  }
  return '';
}

onMounted(() => {
  setTimeout(() => {
    if (!IsEmpty(gpt.CurrentChat.Current)) {
      gpt.CurrentChat.selectedKey = [gpt.CurrentChat.Current.Conversation.id]
    }
  }, 500)
})
</script>

<style>
.conversation-card {
  width: 90% !important;
  overflow: hidden;
}

.conversation-card .ant-card-body {
  padding: 0 !important;
  height: 100% !important;
  overflow: hidden !important;
  background: transparent !important; /* 确保内容区域也是透明的 */
}
</style>

<style scoped>
.conversation-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  transition: all 0.3s;
  will-change: width;
  /* 移除右边框，由父组件 ResizableConversationPanel 控制 */
}

.conversation-list {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 4px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  scrollbar-width: none;
  -ms-overflow-style: none;
  min-height: 0;
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
  -webkit-backface-visibility: hidden;
  -webkit-perspective: 1000px;
}

.conversation-list::-webkit-scrollbar {
  display: none;
}

/* 时间分组标题样式 - 使用 Divider 替代 */
.time-divider {
  font-size: 10px !important;
  color: v-bind('token.colorTextBase') !important;
  margin:  0 !important;
}

.time-divider :deep(.ant-divider-inner-text) {
  font-size: 10px;
}
</style>