<template>
  <div class="flex flex-row size-full">
    <div class="flex flex-col justify-center">
      <OperateMenu/>
    </div>
    <div class="flex-1 flex items-center">
      <a-select
          v-model:value="selectedConversationId"
          show-search
          :placeholder="currentPlaceholder"
          :filter-option="false"
          :not-found-content="isLoading ? '加载中...' : '未找到匹配的会话'"
          :disabled="gpt.ui.replying"
          style="width: 50%"
          @search="handleSearch"
          @select="handleSelect"
          @clear="handleClear"
          allowClear
          class="conversation-search"
          :class="{ 'replying-state': gpt.ui.replying }"
      >
        <template #suffixIcon>
          <SearchOutlined v-if="!gpt.ui.replying"/>
          <a-spin v-else size="small" />
        </template>

        <!-- 在回复状态下显示特殊提示 -->
        <template v-if="gpt.ui.replying">
          <a-select-option key="replying-hint" :value="''" disabled>
            <div class="replying-hint">
              <div class="replying-text">
                <LoadingOutlined class="spinning" /> AI 正在回复中...
              </div>
              <a-button 
                type="primary" 
                size="small" 
                @click.stop="handleStopReplying"
                class="stop-reply-btn"
              >
                停止回复
              </a-button>
            </div>
          </a-select-option>
        </template>

        <!-- 按时间分组显示会话 -->
        <template v-for="(group, index) in groupedConversations" :key="index">
          <a-select-opt-group v-if="group.conversations.length > 0" :label="group.label">
            <a-select-option
                v-for="conversation in group.conversations"
                :key="conversation.Conversation.id"
                :value="conversation.Conversation.id"
                :title="conversation.Conversation.title"
                :disabled="gpt.ui.replying && conversation.Conversation.id !== selectedConversationId"
            >
              <div class="conversation-option"
                   :class="{ 
                     'shake-animation': shakingId === conversation.Conversation.id,
                     'current-replying': gpt.ui.replying && conversation.Conversation.id === selectedConversationId
                   }">
                <div class="conversation-title">
                  <LoadingOutlined v-if="gpt.ui.replying && conversation.Conversation.id === selectedConversationId" class="replying-icon" />
                  {{ conversation.Conversation.title }}
                </div>
                <div class="conversation-actions">
                <span class="conversation-time">
                  {{ getLastMessageTime(conversation) }}
                </span>
                  <a-button
                      type="text"
                      size="small"
                      class="delete-btn"
                      @click.stop="handleDelete(conversation.Conversation.id)"
                      :title="gpt.ui.replying && conversation.Conversation.id === selectedConversationId ? '回复中，无法删除' : '删除会话: ' + conversation.Conversation.title"
                      :disabled="gpt.ui.replying && conversation.Conversation.id === selectedConversationId"
                  >
                    <DeleteOutlined/>
                  </a-button>
                </div>
              </div>
            </a-select-option>
          </a-select-opt-group>
        </template>
      </a-select>
    </div>
  </div>

</template>

<script setup lang="ts">
import {computed, onMounted, ref, watch} from "vue";
import {DeleteOutlined, SearchOutlined, LoadingOutlined} from '@ant-design/icons-vue';
import {useGptStore} from "@/components/store/gpt";
import {IsEmpty} from "@/components/common/chatutils";
import {message} from 'ant-design-vue';
import {delConversation, getConversation} from "@/components/common/manageRequest";
import OperateMenu from "@/components/ai-bot-widget/OperateMenu.vue";

// 定义 props
interface Props {
  placeholder?: string;
  allowClear?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '搜索会话...',
  allowClear: true
});

// 移除 emits 定义，组件完全独立处理所有操作

const gpt = useGptStore();
const selectedConversationId = ref<string | undefined>(undefined);
const searchKeyword = ref('');
const isLoading = ref(false);
const shakingId = ref('');

// 动态占位符文本
const currentPlaceholder = computed(() => {
  if (gpt.ui.replying) {
    return 'AI 正在回复中，请等待...';
  }
  return props.placeholder;
});

// 停止回复功能
const handleStopReplying = () => {
  if (gpt.ui.currentLLMStream) {
    gpt.ui.currentLLMStream.Stop();
  }
  gpt.ui.replying = false;
  message.success('已停止 AI 回复');
};

// 处理抖动效果
const handleShake = (id: string) => {
  shakingId.value = id;
  setTimeout(() => {
    shakingId.value = '';
  }, 250);
};

// 初始化选中的对话
const initializeSelectedConversation = () => {
  if (gpt.CurrentChat.Current && gpt.CurrentChat.Current.Conversation) {
    selectedConversationId.value = gpt.CurrentChat.Current.Conversation.id;
  }
};

// 组件挂载时初始化
onMounted(() => {
  initializeSelectedConversation();
});

// 监听当前选中的会话变化
watch(() => gpt.CurrentChat.Current, (current) => {
  if (current && current.Conversation) {
    selectedConversationId.value = current.Conversation.id;
  } else {
    selectedConversationId.value = undefined;
  }
}, {immediate: true});

// 处理删除
const handleDelete = async (conversationId: string) => {
  try {
    // 如果是正在回复的会话，不允许删除
    if (gpt.ui.replying && conversationId === selectedConversationId.value) {
      message.warning('AI 正在回复中，无法删除当前会话。请先停止回复。');
      handleShake(conversationId);
      return;
    }

    // 如果删除的是当前选中的会话，需要确定下一个要选择的会话
    let nextConversationId = "";
    if (conversationId === gpt.CurrentChat.Current?.Conversation?.id) {
      const index = gpt.CurrentChat.conversationList.findIndex(item =>
          item.Conversation.id === conversationId
      );
      if (index > -1) {
        // 优先选择下一个会话，如果没有则选择前一个
        if (index < gpt.CurrentChat.conversationList.length - 1) {
          nextConversationId = gpt.CurrentChat.conversationList[index + 1].Conversation.id;
        } else if (index > 0) {
          nextConversationId = gpt.CurrentChat.conversationList[index - 1].Conversation.id;
        }
      }
    }

    // 调用删除API
    await delConversation(conversationId);

    // 重新获取会话列表并更新Store
    const data = await getConversation();
    gpt.setConversation(data);

    // 如果删除的是当前会话且还有其他会话，选择下一个会话
    if (nextConversationId && data.length > 0) {
      gpt.SetCurrentChatById(nextConversationId);
      selectedConversationId.value = nextConversationId;
    } else if (data.length === 0) {
      // 如果没有会话了，清空选择
      gpt.ui.showChat = false;
      selectedConversationId.value = undefined;
    } else if (conversationId === gpt.CurrentChat.Current?.Conversation?.id) {
      // 如果删除的是当前会话，但没有找到下一个会话，清空当前选择
      selectedConversationId.value = undefined;
    }
    
    message.success('会话已删除');
  } catch (error) {
    console.error('删除会话失败:', error);
    message.error('删除会话时发生错误，请重试。');
  }
};

// 检查是否选中
const isSelected = (id: string) => {
  if (!IsEmpty(gpt.CurrentChat.Current)) {
    return gpt.CurrentChat.Current.Conversation.id === id;
  }
  return false;
};

// 处理搜索
const handleSearch = (value: string) => {
  if (gpt.ui.replying) {
    return; // 回复中不允许搜索
  }
  searchKeyword.value = value;
  // emit('search', value); // Removed emit
};

// 处理选择
const handleSelect = (conversationId: string) => {
  // 如果当前正在回复中，不允许切换到其他会话
  if (gpt.ui.replying) {
    if (conversationId !== selectedConversationId.value) {
      handleShake(conversationId);
      message.warning('AI 正在回复中，无法切换会话。请先停止回复或等待回复完成。');
      return;
    }
    // 如果点击的是当前正在回复的会话，允许（不做任何操作）
    return;
  }

  selectedConversationId.value = conversationId;
  gpt.SetCurrentChatById(conversationId);
  // emit('select', conversationId); // Removed emit
};

// 处理清空
const handleClear = () => {
  if (gpt.ui.replying) {
    message.warning('AI 正在回复中，无法清空选择。请先停止回复。');
    return;
  }
  selectedConversationId.value = undefined;
  searchKeyword.value = '';
  // emit('clear'); // Removed emit
};

// 基础会话过滤（搜索功能）
const filteredConversations = computed(() => {
  // 首先获取基础的会话列表
  let conversations = gpt.CurrentChat.conversationList;

  // 如果有搜索关键词，进行过滤
  if (searchKeyword.value) {
    conversations = conversations.filter(conversation =>
        conversation.Conversation.title.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
        (conversation.Conversation.lastMsg && conversation.Conversation.lastMsg.toLowerCase().includes(searchKeyword.value.toLowerCase()))
    );
  }

  // 根据更新时间排序，较新的会话排在前面
  return conversations.slice().sort((a, b) => {
    const updateAtA = a.Conversation.updatedAt || '';
    const updateAtB = b.Conversation.updatedAt || '';
    return updateAtB.localeCompare(updateAtA);
  });
});

// 根据时间分组会话
const groupedConversations = computed(() => {
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

  // 定义各个时间点
  const yesterday = new Date(today);
  yesterday.setDate(today.getDate() - 1);

  const threeDaysAgo = new Date(today);
  threeDaysAgo.setDate(today.getDate() - 3);

  const sevenDaysAgo = new Date(today);
  sevenDaysAgo.setDate(today.getDate() - 7);

  const halfMonthAgo = new Date(today);
  halfMonthAgo.setDate(today.getDate() - 15);

  const halfYearAgo = new Date(today);
  halfYearAgo.setMonth(today.getMonth() - 6);

  const oneYearAgo = new Date(today);
  oneYearAgo.setFullYear(today.getFullYear() - 1);

  // 定义时间组
  const groups = [
    {id: 'today', label: '今天', conversations: []},
    {id: 'yesterday', label: '昨天', conversations: []},
    {id: 'three_days', label: '前3天', conversations: []},
    {id: 'seven_days', label: '7天前', conversations: []},
    {id: 'half_month', label: '半个月', conversations: []},
    {id: 'half_year', label: '半年', conversations: []},
    {id: 'one_year', label: '一年', conversations: []},
    {id: 'older', label: '更早', conversations: []}
  ];

  // 对会话进行分组
  filteredConversations.value.forEach(conversation => {
    if (!conversation.Conversation.updatedAt) return;

    const updateDate = new Date(conversation.Conversation.updatedAt);

    // 按照时间分组
    if (updateDate >= today) {
      groups[0].conversations.push(conversation);
    } else if (updateDate >= yesterday) {
      groups[1].conversations.push(conversation);
    } else if (updateDate >= threeDaysAgo) {
      groups[2].conversations.push(conversation);
    } else if (updateDate >= sevenDaysAgo) {
      groups[3].conversations.push(conversation);
    } else if (updateDate >= halfMonthAgo) {
      groups[4].conversations.push(conversation);
    } else if (updateDate >= halfYearAgo) {
      groups[5].conversations.push(conversation);
    } else if (updateDate >= oneYearAgo) {
      groups[6].conversations.push(conversation);
    } else {
      groups[7].conversations.push(conversation);
    }
  });

  // 过滤掉没有会话的分组
  return groups.filter(group => group.conversations.length > 0);
});

// 获取最后一条消息时间
const getLastMessageTime = (conversation: any) => {
  if (conversation.Conversation && conversation.Conversation.updatedAt) {
    try {
      const date = new Date(conversation.Conversation.updatedAt);
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

      const timeFormat = (date: Date) => {
        return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
      };

      if (date >= today) {
        return timeFormat(date);
      } else if (date >= firstDayOfMonth) {
        return `${date.getDate()}日 ${timeFormat(date)}`;
      } else {
        return `${date.getFullYear()}年${(date.getMonth() + 1)}月${date.getDate()}日`;
      }
    } catch (e) {
      console.error('时间格式化错误:', e);
      return '';
    }
  }
  return '';
};
</script>

<style scoped>

.conversation-option {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  min-height: 28px;
}

.conversation-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--ant-color-text);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  margin-right: 12px;
  display: flex;
  align-items: center;
  line-height: 1.4;
}

.conversation-time {
  font-size: 12px;
  color: var(--ant-color-text-tertiary);
  white-space: nowrap;
  display: flex;
  align-items: center;
  line-height: 1.2;
}

.conversation-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
  height: 100%;
}

.delete-btn {
  color: var(--ant-color-text-secondary);
  padding: 2px 4px;
  font-size: 12px;
  min-width: 24px;
  height: 24px;
  border-radius: 4px;
  opacity: 0.7;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-btn:hover {
  color: var(--ant-color-error);
  background-color: var(--ant-color-error-bg);
  opacity: 1;
}

.delete-btn:disabled {
  color: var(--ant-color-text-quaternary);
  opacity: 0.3;
  cursor: not-allowed;
}

.conversation-option:hover .delete-btn:not(:disabled) {
  opacity: 1;
}

/* 回复状态样式 */
.replying-state {
  background: var(--ant-color-warning-bg);
  border-color: var(--ant-color-warning-border);
}

.replying-hint {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 8px 4px;
}

.replying-text {
  display: flex;
  align-items: center;
  color: var(--ant-color-warning);
  font-size: 14px;
  font-weight: 500;
}

.spinning {
  margin-right: 8px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.stop-reply-btn {
  font-size: 12px;
  height: 24px;
  padding: 0 8px;
}

.current-replying {
  background: var(--ant-color-warning-bg-hover);
  border-radius: 4px;
}

.current-replying .conversation-title {
  color: var(--ant-color-warning);
  font-weight: 600;
}

.replying-icon {
  margin-right: 6px;
  color: var(--ant-color-warning);
  animation: spin 1s linear infinite;
}

/* 自定义选择器下拉菜单样式 */
:deep(.ant-select-dropdown) {
  max-height: 300px;
}

:deep(.ant-select-item-group-label) {
  font-size: 12px;
  color: var(--ant-color-text-secondary);
  font-weight: 500;
  padding: 6px 12px;
  background: var(--ant-color-bg-container);
  display: flex;
  align-items: center;
}

:deep(.ant-select-item-option) {
  padding: 6px 12px;
  min-height: 40px;
  display: flex;
  align-items: center;
}

:deep(.ant-select-item-option:hover) {
  background: var(--ant-color-bg-text-hover);
}

:deep(.ant-select-item-option-selected) {
  background: var(--ant-color-primary-bg);
  color: var(--ant-color-primary);
}

:deep(.ant-select-item-option-disabled) {
  background: var(--ant-color-bg-container-disabled);
  opacity: 0.6;
}

/* 确保选择器本身垂直居中 */
.conversation-search {
  width: 300px;
  display: flex;
  align-items: center;
}

/* 修复选中内容的垂直居中对齐 */
:deep(.ant-select-selector) {
  display: flex;
  align-items: center;
}

:deep(.ant-select-selection-search) {
  display: flex;
  align-items: center;
}

:deep(.ant-select-selection-item) {
  display: flex;
  align-items: center;
  line-height: 1.4;
}

:deep(.ant-select-selection-placeholder) {
  display: flex;
  align-items: center;
  line-height: 1.4;
}

/* 回复状态下的选择器样式 */
:deep(.ant-select-disabled .ant-select-selector) {
  background: var(--ant-color-warning-bg) !important;
  border-color: var(--ant-color-warning-border) !important;
  color: var(--ant-color-warning) !important;
}

/* 抖动动画 */
@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  16.67% {
    transform: translateX(-2px);
  }
  33.33% {
    transform: translateX(2px);
  }
  50% {
    transform: translateX(-2px);
  }
  66.67% {
    transform: translateX(2px);
  }
  83.33% {
    transform: translateX(-2px);
  }
}

.shake-animation {
  animation: shake 0.25s ease-in-out;
}

</style> 