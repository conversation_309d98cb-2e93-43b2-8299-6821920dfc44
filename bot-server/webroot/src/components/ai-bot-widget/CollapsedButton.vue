<template>
  <a-button
      @click="handleClick"
      type="link"
      tabindex="-1"
      class="collapsed-btn size-full"
  >
    <template #icon>
      <span class="icon iconfont" :class="bot.collapsed ? 'arrow-double-right' : 'arrow-double-left'"></span>
    </template>
  </a-button>
</template>

<script setup lang="ts">
import {useAiBot} from "@/components/store/bot";
import {theme} from 'ant-design-vue';

const {useToken} = theme;
const {token} = useToken();
const bot = useAiBot();

const handleClick = () => {
  bot.collapsed = !bot.collapsed;
};
</script>

<style scoped>
.collapsed-btn {
  color: v-bind('token.colorTextBase')
}
</style>