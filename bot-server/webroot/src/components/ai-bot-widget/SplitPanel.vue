<template>
  <div class="split-panel" :style="{ height: height }">
    <!-- 上面板 -->
    <div
        class="panel-top"
        :style="{
          height: `${100 - bottomPanelHeight}%`
        }"
    >
      <slot name="top"/>
    </div>

    <!-- 分隔条 -->
<!--    <div
        class="separator"
        :class="{ 'is-dragging': isDragging }"
        @mousedown="startDrag"
        @touchstart="startDrag"
        @mouseenter="handleMouseEnter"
        @mouseleave="handleMouseLeave"
    >
      <div class="separator-line" :class="{ 'is-hover': isHover, 'is-dragging': isDragging }"></div>
    </div>-->

    <!-- 下面板 -->
    <div
        class="panel-bottom"
        :style="{
          height: `${bottomPanelHeight}%`
        }"
    >
      <slot name="bottom"/>
    </div>
  </div>
</template>

<script setup lang="ts">
import {ref, watch} from 'vue'

interface Props {
  minBottom: number // 下面板最小比例
  maxBottom: number // 下面板最大比例
  height?: string // 容器高度
  panelRatio?: number // 新增：外部控制面板比例
}

const props = withDefaults(defineProps<Props>(), {
  minBottom: 25,
  maxBottom: 50,
  height: '100%',
  panelRatio: 25 // 默认值与 minBottom 相同
})

const emit = defineEmits<{
  (e: 'update:bottomHeight', value: number): void
}>()

// 内部状态，用于控制面板高度
const bottomPanelHeight = ref(props.minBottom)

// 拖拽和悬停相关状态
const isDragging = ref(false)
const isHover = ref(false)
const hoverTimer = ref<number | null>(null)
const startY = ref(0)
const startHeight = ref(0)

// 处理鼠标进入
const handleMouseEnter = () => {
  // 清除可能存在的定时器
  if (hoverTimer.value) {
    clearTimeout(hoverTimer.value)
  }
  // 设置新的定时器，延迟300ms后显示高亮效果
  hoverTimer.value = window.setTimeout(() => {
    isHover.value = true
  }, 300)
}

// 处理鼠标离开
const handleMouseLeave = () => {
  // 清除定时器
  if (hoverTimer.value) {
    clearTimeout(hoverTimer.value)
    hoverTimer.value = null
  }
  // 如果不是在拖拽中，则移除高亮效果
  if (!isDragging.value) {
    isHover.value = false
  }
}

// 开始拖拽
const startDrag = (e: MouseEvent | TouchEvent) => {
  isDragging.value = true
  isHover.value = true // 拖拽时保持高亮
  if (e instanceof MouseEvent) {
    startY.value = e.clientY
  } else {
    startY.value = e.touches[0].clientY
  }
  startHeight.value = bottomPanelHeight.value

  // 添加事件监听
  document.addEventListener('mousemove', onDrag)
  document.addEventListener('mouseup', stopDrag)
  document.addEventListener('touchmove', onDrag)
  document.addEventListener('touchend', stopDrag)
}

// 拖拽中
const onDrag = (e: MouseEvent | TouchEvent) => {
  if (!isDragging.value) return

  let clientY
  if (e instanceof MouseEvent) {
    clientY = e.clientY
  } else {
    clientY = e.touches[0].clientY
  }

  const container = (e.target as HTMLElement).closest('.split-panel')
  if (!container) return

  const containerRect = container.getBoundingClientRect()
  const delta = clientY - startY.value
  const deltaPercent = (delta / containerRect.height) * 100

  // 计算新的高度值
  let newBottomHeight = startHeight.value - deltaPercent

  // 限制范围
  newBottomHeight = Math.max(props.minBottom, Math.min(props.maxBottom, newBottomHeight))
  
  bottomPanelHeight.value = newBottomHeight
  emit('update:bottomHeight', newBottomHeight)
}

// 停止拖拽
const stopDrag = () => {
  isDragging.value = false
  isHover.value = false // 停止拖拽时移除高亮
  document.removeEventListener('mousemove', onDrag)
  document.removeEventListener('mouseup', stopDrag)
  document.removeEventListener('touchmove', onDrag)
  document.removeEventListener('touchend', stopDrag)
}

// 监听外部传入的面板比例变化
watch(() => props.panelRatio, (newRatio) => {
  if (newRatio !== undefined) {
    // 确保新的比例在限制范围内
    const validRatio = Math.max(props.minBottom, Math.min(props.maxBottom, newRatio))
    bottomPanelHeight.value = validRatio
    emit('update:bottomHeight', validRatio)
  }
}, { immediate: true })
</script>

<style scoped>
.split-panel {
  display: flex;
  flex-direction: column;
  width: 100%;
  position: relative;
  background: transparent;
  overflow: visible;
}

.panel-top,
.panel-bottom {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.separator {
  height: 2px;
  cursor: default;
  background: transparent;
  user-select: none;
  touch-action: none;
  z-index: 1;
  position: relative;
}

.separator:hover {
  background-color: transparent;
}

.separator.is-dragging {
  background-color: transparent;
}

.separator-line {
  display: none;
}

.separator-line::before,
.separator-line::after {
  display: none;
}

.separator-line.is-hover::before,
.separator-line.is-hover::after,
.separator-line.is-dragging::before,
.separator-line.is-dragging::after {
  display: none;
}
</style> 