import { markRaw, Component } from 'vue';
import GuideNavComponent from './GuideNavComponent.vue';
import WriterNavComponent from './WriterNavComponent.vue';
import ChatNavComponent from './ChatNavComponent.vue';
import DebugNavComponent from './DebugNavComponent.vue';
import AiBotDebugUI from '../../ai-bot-debug/AiBotDebugUI.vue';
import PersonalInformationNavComponent from './PersonalInformationNavComponent.vue';
import PaintingNavComponent from './PaintingNavComponent.vue';
// 可以根据需要导入更多组件

// 定义路由名称和组件UI类型对应的导航组件
interface NavComponentMap {
  [key: string]: Component | null;
}

// 路由名称对组件的映射
const routeNavComponents: NavComponentMap = {
  'guide': markRaw(GuideNavComponent),
  // 添加更多路由对应的导航组件
};

// UI类型对组件的映射
const uiNavComponents: NavComponentMap = {
  'WriterUI': markRaw(WriterNavComponent),
  'AiBotUI': markRaw(ChatNavComponent),
  'AiBotDebugUI': markRaw(DebugNavComponent),
  'AiPaintingUI': markRaw(PaintingNavComponent),
  'PersonalInformationSetting': markRaw(PersonalInformationNavComponent),
  // 添加更多UI类型对应的导航组件
};

// 导航组件管理器
export class NavComponentManager {
  // 获取路由对应的导航组件
  static getComponentByRoute(routeName: string): Component | null {
    return routeNavComponents[routeName] || null;
  }
  
  // 获取UI类型对应的导航组件
  static getComponentByUI(uiType: string): Component | null {
    return uiNavComponents[uiType] || null;
  }
  
  // 判断是否应该显示位置组件
  static shouldShowLocationComponent(routeName: string, uiType: string): boolean {
    // 特定路由下不显示位置组件
    if (['guide', 'loading', 'tray'].includes(routeName)) {
      return false;
    }
    
    // 特定UI类型下不显示位置组件
    if (['WriterUI', 'AiBotUI', 'AiPaintingUI'].includes(uiType)) {
      return false;
    }
    
    // 默认显示位置组件
    return true;
  }
  
  // 注册新的导航组件
  static registerRouteComponent(routeName: string, component: Component): void {
    routeNavComponents[routeName] = markRaw(component);
  }
  
  // 注册新的UI类型导航组件
  static registerUIComponent(uiType: string, component: Component): void {
    uiNavComponents[uiType] = markRaw(component);
  }
}

export default NavComponentManager; 