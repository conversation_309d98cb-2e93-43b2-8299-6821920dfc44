<template>
  <div class="painting-nav-component">
    <div class="nav-content">
      <!-- 占位空间，将模型选择器推到最右侧 -->
      <div class="nav-spacer"></div>
      
      <!-- 平台模型选择器 - 最右侧 -->
      <div class="model-selector-wrapper">
        <PlatformModelSelector 
          :ctx="plugin.currentPlugin" 
          :platform-filter="filterGiteeAIPlatforms" 
          :is-setting-show="false"
          :is-platform-show="true"
          :is-model-show="false"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAiPluginStore } from '@/components/store/plugin';
import PlatformModelSelector from "@/components/ai-bot-widget/nav/PlatformModelSelector.vue";
import { Platform } from "@/components/common/model/model";
import { PlatformType } from '@/components/ai-bot-platform/config';

const plugin = useAiPluginStore();

// 只显示GiteeAI平台的过滤器
const filterGiteeAIPlatforms = (platform: Platform) => {
  return platform.type === PlatformType.GiteeAI;
};
</script>

<style scoped>
.painting-nav-component {
  background: var(--ant-color-bg-container);
  border-bottom: 1px solid var(--ant-color-border);
  min-height: 60px;
  display: flex;
  align-items: center;
  padding: 0 24px;
}

.nav-content {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.nav-spacer {
  flex: 1;
}

.nav-title {
  display: flex;
  align-items: center;
  color: var(--ant-color-text);
}

.nav-title .icon {
  font-size: 20px;
  color: var(--ant-color-primary);
}

.title-text {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
  color: var(--ant-color-text);
}

.model-selector-wrapper {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 16px;
  flex-shrink: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .painting-nav-component {
    padding: 0 16px;
  }
  
  .title-text {
    font-size: 16px;
  }
  
  .nav-title .icon {
    font-size: 18px;
  }
  
  .model-selector-wrapper {
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .painting-nav-component {
    padding: 0 12px;
    min-height: 50px;
  }
  
  .title-text {
    font-size: 14px;
  }
  
  .nav-title .icon {
    font-size: 16px;
  }
  
  .model-selector-wrapper {
    gap: 8px;
  }
}
</style> 