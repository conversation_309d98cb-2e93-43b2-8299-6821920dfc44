<template>
  <div class="size-full flex flex-row">
    <!-- 会话搜索 -->
    <ConversationSearchDropdown  />
    <!-- 平台模型选择器 -->
    <PlatformModelSelector 
      :ctx="plugin.currentPlugin"
      :is-setting-show="true"
      :is-platform-show="true"
      :is-model-show="true"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, computed } from 'vue';
import { useGptStore } from "@/components/store/gpt";
import ConversationSearchDropdown from "@/components/ai-bot-conversation/ConversationSearchDropdown.vue";
import { useAiBot } from '@/components/store/bot';
import { useAiPluginStore } from '@/components/store/plugin';
import { usePlatform } from '@/components/store/platform';
import PlatformModelSelector from "./PlatformModelSelector.vue";

const ctx = useGptStore();
const searchValue = ref('');
const bot = useAiBot()
const plugin = useAiPluginStore()
const platform = usePlatform()

// 计算是否应该显示会话搜索组件
const shouldShowConversationSearch = computed(() => {
  // 检查是否显示聊天界面且有会话
  const hasConversations = ctx.CurrentChat.conversationList && ctx.CurrentChat.conversationList.length > 0;
  return ctx.ui.showChat && hasConversations;
});

// 搜索功能相关逻辑
// 将搜索关键词应用到会话过滤中
watch(searchValue, (newVal) => {
  ctx.ui.search = newVal;
});

// 同步全局搜索状态
onMounted(() => {
  searchValue.value = ctx.ui.search || '';
});
</script>

<style scoped>
.chat-nav-component {
  background: var(--ant-color-bg-container);
  border-bottom: 1px solid var(--ant-color-border);
  min-height: 60px;
  display: flex;
  align-items: center;
  gap: 16px;
}

/* 对话搜索框容器样式 */
.conversation-search-wrapper {
  width: 600px;

}

/* 响应式设计 */
@media (max-width: 1200px) {
  .conversation-search-wrapper {
    width: 450px;
    max-width: 55%;
  }
}

@media (max-width: 768px) {
  .conversation-search-wrapper {
    width: 400px;
    max-width: 50%;
  }
}

@media (max-width: 480px) {
  .conversation-search-wrapper {
    width: 300px;
    max-width: 45%;
  }
}
</style>
