<template>
    <div class="size-full flex flex-row-reverse items-center justify-between">
        <div class="flex flex-row items-center">
            <div class="resume-selector ">
                <a-select v-model:value="currentTemplateId" style="width: 100%" :loading="loading"
                    @change="handleTemplateChange" class="app-no-drag">
                    <a-select-option v-for="template in resumeTemplates" :key="template.id" :value="template.id">
                        {{ template.name }}
                    </a-select-option>
                    <a-select-option key="add-new" value="add-new">
                        <div class="add-option">
                            <plus-outlined /> 添加新简历
                        </div>
                    </a-select-option>
                </a-select>
            </div>
            <div class="flex flex-row " v-if="currentTemplate">
                <a-button type="text" class="app-no-drag mx-1" @click="handleRename">
                    <template #icon>
                        <edit-outlined />
                    </template>
                </a-button>
                <a-button type="text" class="app-no-drag mx-1" @click="handleDuplicate">
                    <template #icon>
                        <copy-outlined />
                    </template>
                </a-button>
                <a-button type="text" class="app-no-drag mx-1" @click="showDeleteConfirm"
                    :disabled="currentTemplate.isDefault || resumeTemplates.length <= 1">
                    <template #icon>
                        <delete-outlined />
                    </template>
                </a-button>
            </div>
        </div>

    </div>


    <!-- 重命名模态框 -->
    <a-modal v-model:visible="renameModalVisible" title="重命名简历" @ok="confirmRename" :confirm-loading="confirmLoading"
        @cancel="cancelRename">
        <a-input v-model:value="newTemplateName" placeholder="请输入简历名称" />
    </a-modal>

</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue';
import { PlusOutlined, EditOutlined, CopyOutlined, DeleteOutlined } from '@ant-design/icons-vue';
import { useInformation, type ResumeTemplate } from '@/components/store/information';
import { message, Modal } from 'ant-design-vue';

const information = useInformation();
const loading = ref(false);
const renameModalVisible = ref(false);
const confirmLoading = ref(false);
const newTemplateName = ref('');

// 获取简历模板数据
const resumeTemplates = computed(() => information.resumeTemplates);
const currentTemplateId = computed({
    get: () => information.currentTemplateId,
    set: (val) => {
        information.setCurrentTemplate(val);
    }
});
const currentTemplate = computed(() => information.currentTemplate);

// 初始化默认模板
onMounted(() => {
    loading.value = true;
    try {
        information.initDefaultTemplate();
    } finally {
        loading.value = false;
    }
});

// 处理模板变更
const handleTemplateChange = (value: string) => {
    if (value === 'add-new') {
        // 创建新模板
        createNewTemplate();
    } else {
        // 设置当前模板
        information.setCurrentTemplate(value);
    }
};

// 创建新模板
const createNewTemplate = () => {
    const newId = `template-${Date.now()}`;
    const newName = `简历 ${resumeTemplates.value.length + 1}`;

    const template: ResumeTemplate = {
        id: newId,
        name: newName,
        personalInfo: {}
    };

    information.addResumeTemplate(template);
    information.setCurrentTemplate(newId);

    // 重置选择器
    setTimeout(() => {
        currentTemplateId.value = newId;
    }, 0);

    message.success('新简历创建成功');
};

// 重命名相关
const handleRename = () => {
    if (currentTemplate.value) {
        newTemplateName.value = currentTemplate.value.name;
        renameModalVisible.value = true;
    }
};

const confirmRename = () => {
    if (!newTemplateName.value.trim()) {
        message.error('简历名称不能为空');
        return;
    }

    confirmLoading.value = true;
    setTimeout(() => {
        information.updateResumeTemplate(currentTemplateId.value, { name: newTemplateName.value });
        renameModalVisible.value = false;
        confirmLoading.value = false;
        message.success('重命名成功');
    }, 300);
};

const cancelRename = () => {
    renameModalVisible.value = false;
    newTemplateName.value = '';
};

// 复制模板
const handleDuplicate = () => {
    if (currentTemplate.value) {
        const newId = `template-${Date.now()}`;
        const newName = `${currentTemplate.value.name} 的副本`;

        const newTemplate: ResumeTemplate = {
            id: newId,
            name: newName,
            personalInfo: JSON.parse(JSON.stringify(currentTemplate.value.personalInfo || {}))
        };

        information.addResumeTemplate(newTemplate);
        information.setCurrentTemplate(newId);
        message.success('简历已复制');
    }
};

// 删除模板
const showDeleteConfirm = () => {
    if (currentTemplate.value?.isDefault || resumeTemplates.value.length <= 1) {
        message.warning('默认简历不能删除或至少需要保留一份简历');
        return;
    }

    Modal.confirm({
        title: '确定要删除这份简历吗？',
        content: '删除后无法恢复，请确认您的操作。',
        okText: '确认',
        cancelText: '取消',
        onOk() {
            const idToDelete = currentTemplateId.value;
            information.deleteResumeTemplate(idToDelete);
            message.success('简历已删除');
        }
    });
};
</script>

<style scoped>
.resume-selector {
    width: 240px;
}


.action-btn {
    padding: 0 8px;
}

.add-option {
    display: flex;
    align-items: center;
    color: #1890ff;
}

.add-option .anticon {
    margin-right: 4px;
}
</style>