<template>
  <div class="debug-nav-component size-full app-drag">
    <div class="app-no-drag h-full flex items-center px-4">
      <a-dropdown :trigger="['click']" class="app-no-drag">
        <a-button type="text">
          {{ currentItemLabel }}
        </a-button>
        <template #overlay>
          <a-menu @click="handleSelect">
            <template v-for="item in debugStore.menuItems" :key="item.key">
              <template v-if="item.children && item.children.length">
                <a-sub-menu :key="item.key" :title="item.label">
                  <a-menu-item v-for="child in item.children" :key="child.key">
                    {{ child.label }}
                  </a-menu-item>
                </a-sub-menu>
              </template>
              <template v-else>
                <a-menu-item :key="item.key">
                  {{ item.label }}
                </a-menu-item>
              </template>
            </template>
          </a-menu>
        </template>
      </a-dropdown>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { Menu as AMenu, MenuItem as AMenuItem, SubMenu as ASubMenu, Dropdown as ADropdown, Button as AButton } from 'ant-design-vue';
import { DownOutlined } from '@ant-design/icons-vue';
import { useAiDebugStore } from '@/components/store/debug';

// 初始化状态管理器
const debugStore = useAiDebugStore();

// 获取当前选中项的显示标签
const currentItemLabel = computed(() => {
  const currentItem = debugStore.getCurrentDebugItem();
  return currentItem ? currentItem.label : '选择调试项';
});

// 处理菜单选择事件
const handleSelect = (info: any) => {
  const key = info.key.toString();
  debugStore.setCurrentDebugKey(key);
};

// 组件挂载时，确保选中状态与store一致
onMounted(() => {
  // 初始化加载时不需要特别处理，计算属性会自动获取当前显示标签
});

// 监听当前选中项变化
watch(() => debugStore.currentDebugKey, () => {
  // 选中项改变时的处理逻辑（如果需要）
});
</script>

<style scoped>
.debug-nav-component {
  display: flex;
  align-items: center;
}
</style> 