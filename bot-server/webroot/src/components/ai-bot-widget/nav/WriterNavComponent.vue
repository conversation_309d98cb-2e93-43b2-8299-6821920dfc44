<template>
  <div class="writer-nav-component">
    <div class="flex items-center space-x-2">
      <!-- 文档标题显示 -->
      <div class="document-title truncate max-w-md">{{ documentTitle }}</div>
      
      <!-- 写作状态指示器 -->
      <div class="status-indicator" :class="{ 'saving': isSaving, 'saved': isSaved }">
        {{ statusText }}
      </div>
      
      <!-- 文档操作按钮 -->
      <div class="flex items-center space-x-2">
        <a-button type="text" size="small" @click="saveDocument" class="nav-btn">
          <template #icon><save-outlined /></template>
          保存
        </a-button>
        <a-button type="text" size="small" @click="exportDocument" class="nav-btn">
          <template #icon><export-outlined /></template>
          导出
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { SaveOutlined, ExportOutlined } from '@ant-design/icons-vue';

// 文档标题
const documentTitle = ref('未命名文档');

// 保存状态
const isSaving = ref(false);
const isSaved = ref(false);

// 状态文本
const statusText = computed(() => {
  if (isSaving.value) return '保存中...';
  if (isSaved.value) return '已保存';
  return '';
});

// 保存文档方法
const saveDocument = () => {
  isSaving.value = true;
  isSaved.value = false;
  
  // 模拟保存操作
  setTimeout(() => {
    isSaving.value = false;
    isSaved.value = true;
    
    // 3秒后隐藏"已保存"状态
    setTimeout(() => {
      isSaved.value = false;
    }, 3000);
  }, 800);
};

// 导出文档方法
const exportDocument = () => {
  // 导出逻辑实现
  console.log('Exporting document');
};

// 可以添加更多方法根据需要
</script>

<style scoped>
.writer-nav-component {
  -webkit-app-region: no-drag;
  display: flex;
  align-items: center;
  height: 100%;
}

.document-title {
  font-size: 12px;
  font-weight: 500;
  color: var(--color-text);
}

.status-indicator {
  font-size: 12px;
  transition: opacity 0.3s;
}

.status-indicator.saving {
  color: #faad14;
}

.status-indicator.saved {
  color: #52c41a;
}

.nav-btn {
  padding: 0 8px;
  height: 24px;
  line-height: 24px;
}
</style> 