<template>
  <div class="guide-nav-component">
    <div class="flex items-center space-x-3">
      <!-- 指南标题 -->
      <div class="guide-title">AI助手使用指南</div>
      
      <!-- 步骤指示器 -->
      <div class="steps-indicator">
        <span 
          v-for="step in totalSteps" 
          :key="step" 
          class="step-dot"
          :class="{ 'active': step === currentStep }"
          @click="goToStep(step)"
        ></span>
      </div>
      
      <!-- 导航按钮 -->
      <div class="flex items-center space-x-2">
        <a-button 
          type="text" 
          size="small" 
          @click="prevStep" 
          class="nav-btn"
          :disabled="currentStep === 1"
        >
          <template #icon><left-outlined /></template>
          上一步
        </a-button>
        <a-button 
          type="text" 
          size="small" 
          @click="nextStep" 
          class="nav-btn"
          :disabled="currentStep === totalSteps"
        >
          下一步
          <template #icon><right-outlined /></template>
        </a-button>
        <a-button 
          type="text" 
          size="small" 
          @click="skipGuide" 
          class="nav-btn"
        >
          跳过
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { LeftOutlined, RightOutlined } from '@ant-design/icons-vue';
import { useRouter } from 'vue-router';

const router = useRouter();

// 当前步骤
const currentStep = ref(1);
// 总步骤数
const totalSteps = ref(5);

// 前往指定步骤
const goToStep = (step: number) => {
  if (step >= 1 && step <= totalSteps.value) {
    currentStep.value = step;
    // 这里可以触发事件告知父组件步骤变化
    // emit('step-change', currentStep.value);
  }
};

// 前往上一步
const prevStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--;
    // emit('step-change', currentStep.value);
  }
};

// 前往下一步
const nextStep = () => {
  if (currentStep.value < totalSteps.value) {
    currentStep.value++;
    // emit('step-change', currentStep.value);
  }
};

// 跳过指南
const skipGuide = () => {
  // 跳转到主界面
  router.push('/');
};
</script>

<style scoped>
.guide-nav-component {
  -webkit-app-region: no-drag;
  display: flex;
  align-items: center;
  height: 100%;
}

.guide-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-text);
}

.steps-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
}

.step-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.15);
  cursor: pointer;
  transition: all 0.3s;
}

.step-dot.active {
  background-color: #1890ff;
  transform: scale(1.2);
}

.nav-btn {
  padding: 0 8px;
  height: 24px;
  line-height: 24px;
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .step-dot {
    background-color: rgba(255, 255, 255, 0.15);
  }
}
</style> 