<template>
  <div class="flex flex-row size-full">
    <!-- 平台选择器 -->
    <div v-if="isPlatformShow" class="flex items-center min-w-0 flex-shrink-0 mx-1" ref="selectWrapper">
      <a-select
          v-model:value="displayModelValue"
          style="width: 200px"
          :get-popup-container="getPopupContainer"
          @select="select"
          size="small"
          placeholder="选择平台"
          :disabled="filteredPlatforms.length === 0"
      >
        <a-select-option
            class="my-1"
            v-for="platform in filteredPlatforms"
            :label="platform.name"
            :key="platform.id"
            :value="platform.id"
        >
          <div class="flex items-center gap-2">
            <img :src="platform.icon" class="w-5 h-5 rounded-full" :alt="platform.name"/>
            <span>{{ platform.name }}</span>
          </div>
        </a-select-option>
      </a-select>
    </div>
    
    <!-- 平台快速配置面板 -->
    <div v-if="isModelShow" class="flex items-center flex-1 min-w-0 mx-1">
      <component
          v-if="displayPlatform!=null"
          ref="quackConfig"
          :key="displayPlatform.id"
          :is="displayPlatform.quickConfigView"
          :ctx="displayPlatform"
          :model-filter="createModelFilter"
          class="w-full"
      />
      <div v-else-if="filteredPlatforms.length === 0" class="text-gray-400 text-sm">
        没有可用的平台
      </div>
    </div>
    
    <!-- 设置按钮 -->
    <div v-if="isSettingShow" class="flex items-center flex-shrink-0 mx-1">
      <a-button @click="open = !open" size="small" :disabled="displayPlatform == null">
        <template #icon>
          <setting-outlined/>
        </template>
      </a-button>
    </div>
    
    <!-- 平台配置弹窗 -->
    <PlatformConfig v-model="open" :platform="displayPlatform" @save="save"/>
  </div>
</template>

<script setup lang="ts">
import {Platform, Plugin} from "@/components/common/model/model";
import {computed, defineComponent, defineProps, onMounted, ref, watch} from 'vue'
import {getPlatforms} from "@/components/common/manageRequest";
import {usePlatform} from "@/components/store/platform";
import PlatformConfig from "@/components/ai-bot-platform/PlatformConfig.vue";
import {useAiPluginStore} from "@/components/store/plugin";
import {useAiBot} from "@/components/store/bot";
import {SettingOutlined} from "@ant-design/icons-vue";
import { BaseModelInfo } from "@/components/ai-bot-platform/base/types";

const bot = useAiBot()

const props = defineProps<{
  // 当前插件
  ctx: Plugin,
  // 平台过滤函数
  platformFilter?: (platform: Platform) => boolean,
  // 平台对应 模型过滤函数
  modelFilter?: (platform: Platform, model: BaseModelInfo) => boolean,
  // 控制设置按钮的显示
  isSettingShow?: boolean,
  // 控制平台选择器的显示
  isPlatformShow?: boolean,
  // 控制模型选择器的显示
  isModelShow?: boolean,
}>()

// 默认选中当前插件默认平台
const model = ref<string>('')
const platforms = ref<Platform[]>([])
const platform = ref<Platform>()
const open = ref(false)
const plat = usePlatform()
const plugin = useAiPluginStore()
const quackConfig = ref()
const selectWrapper = ref<HTMLElement | null>(null)
const isSettingShow = computed(() => {
  return props.isSettingShow !== undefined ? props.isSettingShow : true
})
const isPlatformShow = computed(() => {
  return props.isPlatformShow !== undefined ? props.isPlatformShow : true
})
const isModelShow = computed(() => {
  return props.isModelShow !== undefined ? props.isModelShow : true
})

// 计算过滤后的平台列表
const filteredPlatforms = computed(() => {
  if (props.platformFilter && typeof props.platformFilter === 'function') {
    return plat.platforms.filter(props.platformFilter)
  }
  return plat.platforms
})

// 计算当前应该显示的选中平台（只在过滤后的列表中选择）
const displayPlatform = computed(() => {
  if (filteredPlatforms.value.length === 0) {
    return null // 没有可显示的平台时，不显示任何选中状态
  }
  
  // 如果当前选中的平台在过滤列表中，显示它
  if (platform.value && filteredPlatforms.value.find(p => p.id === platform.value?.id)) {
    return platform.value
  }
  
  return null
})

// 计算当前应该显示的选中模型值
const displayModelValue = computed(() => {
  if (filteredPlatforms.value.length === 0) {
    return '' // 没有可显示的平台时，不显示任何选中值
  }
  
  // 如果当前选中的平台在过滤列表中，显示对应的model值
  if (platform.value && filteredPlatforms.value.find(p => p.id === platform.value?.id)) {
    return model.value
  }
  
  return ''
})

// 为当前平台创建模型过滤器
const createModelFilter = computed(() => {
  if (!props.modelFilter || !displayPlatform.value) {
    return undefined
  }
  
  // 返回一个符合 ModelFilter 类型的函数
  return (model: BaseModelInfo) => {
    return props.modelFilter!(displayPlatform.value!, model)
  }
})

const VNodes = defineComponent({
  props: {
    vnodes: {
      type: Object,
      required: true,
    },
  },
  render() {
    return this.vnodes;
  },
});

// 修改当前插件所配置的平台
function select(value) {
  // 更新当前的操作平台 用于触发 快速面板的配置刷新
  platform.value = plat.platforms.find(p => p.id === value);
  plat.currentPlatform = platform.value;
  let pram = {...props.ctx}
  pram.platformID = value
  // plugin.updatePlugin(pram)
  // 修改 统一配置所有插件 使用的基座
  plugin.updateAllPlugin(platform.value)
  // 更新当前插件配置
  plugin.currentPlugin = pram
}

/*
* save 平台配置保存后触发 平台对应的快速配置面板 刷新
* */
function save() {
  // 如果没有可显示的平台，不进行刷新操作
  if (filteredPlatforms.value.length === 0) {
    return
  }
  
  platform.value = null
  let p = plat.platforms.find(p => p.id === props.ctx.platformID)
  setTimeout(() => {
    platform.value = p
  }, 200)
}

watch(() => plugin.currentPlugin.platformID, (newVal) => {
  if (newVal && plat.platforms.find(p => p.id === newVal)) {
    model.value = newVal;
    platform.value = plat.platforms.find(p => p.id === newVal);
  }
}, {
  deep: true,
})

// 监听平台列表变化，重新初始化选中的模型
watch(() => plat.platforms, () => {
  if (plat.platforms.length > 0 && !model.value) {
    initializeSelectedModel();
  }
}, {
  deep: true,
})

// 监听过滤后的平台列表变化，重新初始化选中的模型
watch(() => filteredPlatforms.value, (newPlatforms, oldPlatforms) => {
  // 如果从有平台变为没有平台，清空显示状态
  if (newPlatforms.length === 0 && oldPlatforms && oldPlatforms.length > 0) {
    model.value = '';
    return;
  }
  
  // 如果有可用平台但当前没有选中，初始化选中的模型
  if (newPlatforms.length > 0 && !model.value) {
    initializeSelectedModel();
  }
  
  // 如果当前选中的平台不在新的过滤列表中，重新选择
  if (newPlatforms.length > 0 && model.value && !newPlatforms.find(p => p.id === model.value)) {
    initializeSelectedModel();
  }
}, {
  deep: true,
})

function getPopupContainer() {
  return selectWrapper.value || document.body
}

// 初始化选中的模型
function initializeSelectedModel() {
  const availablePlatforms = filteredPlatforms.value;
  if (availablePlatforms.length === 0) {
    // 如果没有可显示的平台，清空当前显示的选中状态，但不影响全局状态
    model.value = '';
    return;
  }
  
  let selectedPlatformId = '';
  
  // 1. 优先使用当前插件配置的平台ID（但要确保在过滤后的列表中）
  if (props.ctx.platformID && availablePlatforms.find(p => p.id === props.ctx.platformID)) {
    selectedPlatformId = props.ctx.platformID;
  }
  // 2. 如果当前插件没有配置或配置无效，使用当前插件store中的平台ID（但要确保在过滤后的列表中）
  else if (plugin.currentPlugin.platformID && availablePlatforms.find(p => p.id === plugin.currentPlugin.platformID)) {
    selectedPlatformId = plugin.currentPlugin.platformID;
  }
  // 3. 如果都没有，选择第一个可用的过滤后平台
  else if (availablePlatforms.length > 0) {
    selectedPlatformId = availablePlatforms[0].id;
  }
  
  // 设置选中的模型和平台
  if (selectedPlatformId) {
    model.value = selectedPlatformId;
    platform.value = plat.platforms.find(p => p.id === selectedPlatformId);
    plat.currentPlatform = platform.value;
    // 如果当前插件的平台ID与选中的不一致，更新插件配置
    if (props.ctx.platformID !== selectedPlatformId) {
      let updatedPlugin = {...props.ctx};
      updatedPlugin.platformID = selectedPlatformId;
      plugin.currentPlugin = updatedPlugin;
    }
  }
}

onMounted(async () => {
  platforms.value = await getPlatforms()
  // 解析平台配置
  plat.platforms = platforms.value.map(p => {
    let config = {...p}
    config.settings = JSON.parse(config.config)
    return config
  })
  
  // 初始化选中的模型
  initializeSelectedModel();
})

</script>

<style scoped>

</style>
