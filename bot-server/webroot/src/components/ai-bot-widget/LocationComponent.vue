<template>
  <div class="location-wrapper flex items-center cursor-pointer" :title="locationTitle">
    <a-button
        type="text"
        class="location-btn"
        tabindex="-1"
        :loading="loading"
        :class="{
        'error-btn': error || networkError,
        'success-btn': location && !loading && !error && !networkError
      }"
        @click="getLocation"
    >
      <template #icon>
        <environment-outlined/>
      </template>
    </a-button>
    <div v-if="showDetails || location" class="location-text text-sm">
      {{ locationText }}
    </div>
  </div>
</template>

<script setup lang="ts">
import {computed, onMounted, onUnmounted, ref} from 'vue'
import {EnvironmentOutlined} from '@ant-design/icons-vue'
import axios from 'axios'

// ipinfo.io API 地址
const IP_INFO_API_URL = 'https://ipinfo.io/json'

// 基础状态变量
const location = ref(null)
const showDetails = ref(false)
const loading = ref(false)
const error = ref(null)
const networkError = ref(false)
const locationDetail = ref('')

// 计算属性
const locationText = computed(() => {
  if (loading.value) return '正在获取位置...'
  if (networkError.value) return '网络连接错误'
  if (error.value) return '无法获取位置'
  if (location.value) {
    if (locationDetail.value) {
      return locationDetail.value
    }
    return `${location.value.latitude.toFixed(4)}, ${location.value.longitude.toFixed(4)}`
  }
  return '获取位置'
})

// 提示信息
const locationTitle = computed(() => {
  if (loading.value) return '正在获取您的位置信息'
  if (networkError.value) return '网络连接错误，请检查您的网络设置'
  if (error.value) return `无法获取位置: ${error.value}`
  if (location.value) {
    if (locationDetail.value) {
      return `${locationDetail.value} (${location.value.latitude}, ${location.value.longitude})`
    }
    return `纬度: ${location.value.latitude}, 经度: ${location.value.longitude}`
  }
  return '点击获取您当前的地理位置'
})

// 获取位置方法
const getLocation = async () => {
  // 如果正在加载，不重复请求
  if (loading.value) return

  // 检查网络连接
  if (!navigator.onLine) {
    networkError.value = true
    error.value = '网络连接不可用'
    showDetails.value = true
    setTimeout(() => {
      showDetails.value = false
    }, 3000)
    return
  }

  // 重置错误状态
  networkError.value = false
  error.value = null

  // 开始请求位置
  loading.value = true
  showDetails.value = true

  try {
    // 使用 ipinfo.io 进行定位
    const response = await axios.get(IP_INFO_API_URL)

    if (response.data) {
      const ipData = response.data

      // 从返回数据中解析位置信息
      // ipinfo 返回的经纬度格式为 "37.3860,-122.0838"
      if (ipData.loc) {
        const [lat, lon] = ipData.loc.split(',').map(coord => parseFloat(coord));

        location.value = {
          latitude: lat,
          longitude: lon
        }
      } else {
        throw new Error('无法获取位置坐标')
      }

      // 显示基本位置信息
      locationDetail.value = `${ipData.country || ''} ${ipData.region || ''} ${ipData.city || ''}`.trim()

      loading.value = false

      // 已成功获取位置，保持显示状态
      showDetails.value = true

      // 错误状态下才自动隐藏
    } else {
      throw new Error('获取位置信息失败')
    }
  } catch (e) {
    console.error('尝试获取位置时发生异常:', e)
    loading.value = false
    error.value = e.message || '获取位置时发生错误'
    showDetails.value = true

    // 错误信息显示几秒后自动隐藏
    setTimeout(() => {
      showDetails.value = false
    }, 3000)
  }
}

// 网络状态处理
const handleOnline = () => {
  networkError.value = false
}

const handleOffline = () => {
  networkError.value = true
  if (loading.value) {
    loading.value = false
    error.value = '网络连接已断开'
    showDetails.value = true
  }
}

// 注册和移除网络状态监听器
onMounted(() => {
  window.addEventListener('online', handleOnline)
  window.addEventListener('offline', handleOffline)

  // 组件加载后自动获取一次位置
  getLocation()
})

onUnmounted(() => {
  window.removeEventListener('online', handleOnline)
  window.removeEventListener('offline', handleOffline)
})
</script>

<style scoped>
.location-wrapper {
  -webkit-app-region: no-drag;
  height: 100%;
  position: relative;
}

.location-btn {
  padding: 0 8px;
  color: var(--location-color, #f0f0f0);
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.location-btn :deep(.anticon) {
  font-size: 16px;
}

/* 状态样式 */
.error-btn {
  color: #ff4d4f !important;
}

.success-btn {
  color: #52c41a !important;
}

.location-text {
  white-space: nowrap;
  margin-left: 4px;
  color: var(--location-color, #f0f0f0);
  font-weight: 500;
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .location-wrapper {
    --location-color: #ffffff;
  }
}

/* 亮色主题适配 */
@media (prefers-color-scheme: light) {
  .location-wrapper {
    --location-color: #505050;
  }
}
</style> 