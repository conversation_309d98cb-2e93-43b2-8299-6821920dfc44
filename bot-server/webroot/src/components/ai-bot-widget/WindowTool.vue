<template>
  <div class="w-full flex flex-row justify-center items-center gap-0.5 mt-2">
    <a-input
        class="conversation-search"
        v-model:value="searchValue"
        placeholder="搜索对话"
        :theme="bot.theme"/>
    <div class="flex">
      <OperateMenu/>
    </div>
  </div>
</template>
<script setup lang="ts">

import OperateMenu from "@/components/ai-bot-widget/OperateMenu.vue";
import {useAiBot} from "@/components/store/bot";
import {ref} from "vue";

const bot = useAiBot()
const searchValue = ref('');
</script>


<style scoped>

</style>