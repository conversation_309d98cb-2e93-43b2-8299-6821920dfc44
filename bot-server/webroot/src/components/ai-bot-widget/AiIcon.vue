<template>
  <span class="ai-icon icon iconfont" :class="[iconClass, {'animate-enabled': animated}]" ref="iconRef"></span>
</template>

<script setup lang="ts">
import { ref, defineProps } from 'vue';

const props = defineProps({
  // 图标的类名
  iconClass: {
    type: String,
    required: true
  },
  // 是否在悬停时启用动画
  animated: {
    type: Boolean,
    default: true
  }
});

const iconRef = ref(null);

// 暴露触发动画的方法给父组件使用
const startAnimation = () => {
  if (props.animated && iconRef.value) {
    iconRef.value.style.animation = 'none';
    // 触发重绘
    void iconRef.value.offsetWidth;
    // 重新应用动画
    iconRef.value.style.animation = 'iconBounce 0.6s ease-in-out';
  }
};

// 将方法暴露给父组件
defineExpose({
  startAnimation
});
</script>

<style scoped>
.ai-icon.icon.iconfont {
  font-size: 16px;
  color: inherit;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  margin: 0 auto;
}

.animate-enabled:hover {
  animation: iconBounce 0.6s ease-in-out;
}

@keyframes iconBounce {
  0% {
    transform: scale(1);
  }

  25% {
    transform: scale(1.2);
  }

  50% {
    transform: scale(0.9);
  }

  75% {
    transform: scale(1.1);
  }

  100% {
    transform: scale(1);
  }
}
</style> 