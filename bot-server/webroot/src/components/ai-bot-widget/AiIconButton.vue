<template>
  <button
      class="ai-icon-button"
      :class="{
        'disabled': disabled
      }"
      :type="htmlType"
      :disabled="disabled"
      @click="handleClick"
  >
    <div class="icon-container">
      <slot></slot>
    </div>
  </button>
</template>

<script setup lang="ts">
import {theme} from 'ant-design-vue';

const {useToken} = theme;
const {token} = useToken();


interface Props {
  disabled?: boolean;
  htmlType?: 'button' | 'submit' | 'reset';
  size?: number;
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  htmlType: 'button',
  size: 32
});

const emit = defineEmits(['click']);

const handleClick = (event: MouseEvent) => {
  if (!props.disabled) {
    emit('click', event);
  }
};
</script>

<style scoped>

.ai-icon-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: v-bind('`${props.size}px`');
  height: v-bind('`${props.size}px`');
  padding: 0;
  border: none;
  border-radius: 6px;
  background: transparent;
  cursor: pointer;
  transition: all 0.2s;
  outline: none;

  -webkit-tap-highlight-color: transparent;
  user-select: none;
}

.icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

/* 确保图标容器内的所有元素都居中 */
.icon-container :deep(*) {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

.ai-icon-button:hover {
  background-color: rgba(0, 0, 0, 0.04);
  color: v-bind('token.colorPrimaryText');
}

.ai-icon-button:active {
  background-color: rgba(0, 0, 0, 0.08);
}

/* 禁用状态 */
.ai-icon-button.disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.ai-icon-button.disabled:hover {
  background-color: transparent;
}

/* 图标基础样式 */
.ai-icon-button :deep(.icon),
.ai-icon-button :deep(.iconfont) {
  font-size: v-bind('`${props.size/2}px`');
  line-height: 1;
  width: v-bind('`${props.size/2}px`');
  height: v-bind('`${props.size/2}px`');
}
</style> 