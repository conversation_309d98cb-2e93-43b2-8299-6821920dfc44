<template>
  <a-button
      @click="handleThemeToggle"
      type="link"
      tabindex="-1"
      class="theme-btn size-full"

  >
    <template #icon>
      <span class="icon iconfont" :class="bot.theme === 'dark' ? 'light' : 'dark'"></span>
    </template>
  </a-button>
</template>

<script setup lang="ts">
import {useAiBot} from "@/components/store/bot";
import {theme} from 'ant-design-vue';

const {useToken} = theme;
const {token} = useToken();
const bot = useAiBot();

const handleThemeToggle = () => {
  const newTheme = bot.theme === 'light' ? 'dark' : 'light';
  bot.setTheme(newTheme);
};
</script>

<style scoped>
.theme-btn {
  color: v-bind('token.colorTextBase');
}
</style> 