<template>
    <div class="editor-container-wrapper size-full flex flex-col">
        <div ref="menuBarContainer"></div>
        <div ref="editorToolbar"></div>
        <div class="size-full flex flex-row overflow-auto">
            <div class="editor-area size-full">
                <div class="editor-container size-full">
                    <div id="editor-content" class="size-full" ref="editorContainer"></div>
                </div>
            </div>

        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
// 使用DecoupledEditor实现文档界面
import { DecoupledEditor } from '@ckeditor/ckeditor5-editor-decoupled';
import { Essentials } from '@ckeditor/ckeditor5-essentials';
import { Paragraph } from '@ckeditor/ckeditor5-paragraph';
import { Bold, Italic, Strikethrough, Underline } from '@ckeditor/ckeditor5-basic-styles';
import { Heading } from '@ckeditor/ckeditor5-heading';
import { Image, ImageCaption, ImageResize, ImageStyle } from '@ckeditor/ckeditor5-image';
import { List } from '@ckeditor/ckeditor5-list';
import { Link } from '@ckeditor/ckeditor5-link';
import { Table, TableToolbar } from '@ckeditor/ckeditor5-table';
import { Alignment } from '@ckeditor/ckeditor5-alignment';
import { Font } from '@ckeditor/ckeditor5-font';
import { Indent } from '@ckeditor/ckeditor5-indent';
import { TextTransformation } from '@ckeditor/ckeditor5-typing';
import { WordCount } from '@ckeditor/ckeditor5-word-count';
import { Base64UploadAdapter } from '@ckeditor/ckeditor5-upload';
import { PageBreak } from '@ckeditor/ckeditor5-page-break';
import { theme } from 'ant-design-vue';
const { useToken } = theme;
const { token } = useToken();
let editor: any = null;

const description = defineModel('description', { required: true, default: '' });
// 编辑器相关
const editorContainer = ref<HTMLElement | null>(null);
const editorToolbar = ref<HTMLElement | null>(null);
const props = defineProps({
    description: {
        type: String,
        required: true,
    },
});

// 初始化编辑器
onMounted(async () => {
    if (editorContainer.value && editorToolbar.value) {
        try {
            // 创建自定义编辑器实例，使用DecoupledEditor
            editor = await DecoupledEditor.create(editorContainer.value, {
                licenseKey: 'GPL',
                plugins: [
                    Essentials, Paragraph,
                    Bold, Italic, Underline, Strikethrough,
                    Heading,
                    Image, ImageCaption, ImageResize, ImageStyle,
                    List,
                    Link,
                    Table, TableToolbar,
                    Alignment,
                    Font,
                    Indent,
                    TextTransformation,
                    WordCount,
                    Base64UploadAdapter,
                    PageBreak,
                ],
                toolbar: {
                    items: [
                        'heading',
                        '|',
                        'alignment',
                        'bold', 'italic', 'underline', 'strikethrough',
                        '|',
                        'fontFamily', 'fontSize', 'fontColor',
                        '|',
                        'bulletedList', 'numberedList',
                        '|',
                        'link', 'insertImage', 'insertTable'
                    ],
                    shouldNotGroupWhenFull: false
                },
                placeholder: '开始编辑文档...',
                heading: {
                    options: [
                        { model: 'paragraph', title: '正文', class: 'ck-heading_paragraph' },
                        { model: 'heading1', view: 'h1', title: '标题 1', class: 'ck-heading_heading1' },
                        { model: 'heading2', view: 'h2', title: '标题 2', class: 'ck-heading_heading2' },
                        { model: 'heading3', view: 'h3', title: '标题 3', class: 'ck-heading_heading3' },
                        { model: 'heading4', view: 'h4', title: '标题 4', class: 'ck-heading_heading4' }
                    ]
                },
                fontSize: {
                    options: [
                        12, 14, 16, 18, 20, 22, 24, 26, 28
                    ],
                    supportAllValues: true
                },
                fontFamily: {
                    options: [
                        'default',
                        'Arial, Helvetica, sans-serif',
                        'Times New Roman, Times, serif',
                        '宋体, SimSun, sans-serif',
                        '微软雅黑, Microsoft YaHei, sans-serif'
                    ]
                },
                language: 'zh-cn',
            } as any);
            // 将编辑器工具栏添加到工具栏容器
            editorToolbar.value.appendChild(editor.ui.view.toolbar.element);
        } catch (error) {
            console.error('初始化编辑器失败:', error);
        }
    }
});

</script>

<style scoped>
.editor-container-wrapper {
    border: 1px solid v-bind('token.colorBorder');
    border-radius: 4px;
    padding: 1px;
}
#editor-content {
  width: auto;
  height: auto;
  background: v-bind('token.colorBgContainer');
  border: none;
  outline: none;
  box-shadow: none;
  overflow: auto;
  min-height: 150px;
}
:global(.ck.ck-toolbar ) {
    border-radius: 0 !important;
    border: none !important;
}
</style>