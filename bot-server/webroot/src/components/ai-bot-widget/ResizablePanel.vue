<template>
  <div 
    class="resizable-panel" 
    ref="panelRef"
    :class="{ 
      'panel-resizing': isResizing,
      'panel-widening': isWidening,
      'panel-narrowing': isNarrowing,
      'transition-enabled': shouldTransition,
      'resize-edge-left': props.resizeEdge === 'left',
      'resize-edge-right': props.resizeEdge === 'right'
    }"
  >
    <div class="panel-content">
      <slot></slot>
    </div>

    <div 
      class="resize-handle" 
      @mousedown="startResize"
      :class="{ 
        'resizing': isResizing,
        'left-edge': props.resizeEdge === 'left',
        'right-edge': props.resizeEdge === 'right'
      }"
    >
      <div class="resize-handle-indicator"></div>
    </div>
    
    <!-- 拖拽时的预览指示器，只在拖拽时显示 -->
    <div 
      v-if="isDragging" 
      class="resize-preview" 
      :style="{ 
        left: `${previewPosition}px`,
        height: containerHeight + 'px',
        top: containerTop + 'px'
      }"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, computed, nextTick } from 'vue';
import { theme } from 'ant-design-vue';

// 定义组件属性
const props = defineProps({
  // 默认拖拽模式: 'preview' 预览模式，'direct' 实时模式
  defaultDragMode: {
    type: String,
    default: 'direct', // 默认使用实时模式
    validator: (value: string) => ['preview', 'direct'].includes(value)
  },
  // 是否记住用户的拖拽模式选择
  rememberUserPreference: {
    type: Boolean,
    default: true
  },
  // 初始宽度
  initialWidth: {
    type: String,
    default: '150px'
  },
  // 最小宽度 (px)
  minWidth: {
    type: Number,
    default: 150
  },
  // 最大宽度 (窗口宽度百分比)
  maxWidthPercent: {
    type: Number,
    default: 40
  },
  // 本地存储键名前缀，用于保存面板设置
  storageKeyPrefix: {
    type: String,
    default: 'resizable-panel'
  },
  // 拖拽边缘位置: 'right' 右侧边缘，'left' 左侧边缘
  resizeEdge: {
    type: String,
    default: 'right',
    validator: (value: string) => ['right', 'left'].includes(value)
  }
});

const { useToken } = theme;
const { token } = useToken();

// 拖拽状态
const isResizing = ref(false);
const isDragging = ref(false);
const panelRef = ref<HTMLElement | null>(null);
const startWidth = ref(0);
const startX = ref(0);
const previewPosition = ref(0);
const containerHeight = ref(0);
const containerTop = ref(0);

// 方向状态（用于动画）
const isWidening = ref(false);  // 变宽
const isNarrowing = ref(false); // 变窄
const lastDirection = ref<'widening' | 'narrowing' | null>(null);
const shouldTransition = ref(false); // 是否启用过渡动画

// 拖拽模式控制
const isPreviewMode = ref(props.defaultDragMode === 'preview');

// 计算拖拽模式提示文本
const dragModeTitle = computed(() => {
  return isPreviewMode.value ? '当前：预览模式（点击切换）' : '当前：实时模式（点击切换）';
});

// 发出事件
const emit = defineEmits(['modeChange', 'resize', 'resizeStart', 'resizeEnd']);

// 切换拖拽模式
const toggleDragMode = () => {
  isPreviewMode.value = !isPreviewMode.value;
  
  // 发出模式变化事件
  emit('modeChange', isPreviewMode.value ? 'preview' : 'direct');
  
  // 如果需要记住用户偏好，保存到本地存储
  if (props.rememberUserPreference) {
    localStorage.setItem(`${props.storageKeyPrefix}-drag-mode`, isPreviewMode.value ? 'preview' : 'direct');
  }
};

// 默认宽度，可以通过 localStorage 保存/读取用户上次设置的宽度
const defaultWidth = ref(props.initialWidth);

// 计算最大宽度（像素）
const maxWidthPx = computed(() => {
  return window.innerWidth * (props.maxWidthPercent / 100);
});

// 开始调整大小
const startResize = (event: MouseEvent) => {
  event.preventDefault();
  
  // 重置方向状态
  isWidening.value = false;
  isNarrowing.value = false;
  shouldTransition.value = false; // 拖拽开始时禁用过渡
  
  isResizing.value = true;
  isDragging.value = true;
  startX.value = event.clientX;
  
  // 获取面板当前宽度和位置
  if (panelRef.value) {
    const rect = panelRef.value.getBoundingClientRect();
    startWidth.value = panelRef.value.offsetWidth;
    
    // 根据拖拽边缘位置初始化预览位置
    if (props.resizeEdge === 'right') {
      // 右侧拖拽时，预览位置为右边界
      previewPosition.value = rect.right;
    } else {
      // 左侧拖拽时，预览位置为左边界
      previewPosition.value = rect.left;
    }
    
    // 获取容器高度和顶部位置，用于限制预览线
    containerHeight.value = rect.height;
    containerTop.value = rect.top;
  }
  
  // 添加事件监听
  document.addEventListener('mousemove', handleMouseMove);
  document.addEventListener('mouseup', handleMouseUp);
  
  // 添加调整中样式
  document.body.style.cursor = 'ew-resize';
  document.body.style.userSelect = 'none';
  
  // 发出调整开始事件
  emit('resizeStart', { 
    width: startWidth.value, 
    mode: isPreviewMode.value ? 'preview' : 'direct' 
  });
};

// 处理鼠标移动
const handleMouseMove = (event: MouseEvent) => {
  if (!isResizing.value) return;
  
  const deltaX = event.clientX - startX.value;
  let newWidth;
  
  if (props.resizeEdge === 'right') {
    // 右侧拖拽，宽度增加时面板变宽
    newWidth = startWidth.value + deltaX;
  } else {
    // 左侧拖拽，宽度减少时面板变宽
    newWidth = startWidth.value - deltaX;
  }
  
  // 限制最小宽度
  if (newWidth < props.minWidth) {
    newWidth = props.minWidth;
  }
  
  // 限制最大宽度
  if (newWidth > maxWidthPx.value) {
    newWidth = maxWidthPx.value;
  }
  
  // 确定方向（变宽或变窄）
  const currentDirection = props.resizeEdge === 'right' 
    ? (deltaX > 0 ? 'widening' : deltaX < 0 ? 'narrowing' : null)
    : (deltaX < 0 ? 'widening' : deltaX > 0 ? 'narrowing' : null);
  
  // 只有当方向改变时才更新方向状态
  if (currentDirection && currentDirection !== lastDirection.value) {
    isWidening.value = currentDirection === 'widening';
    isNarrowing.value = currentDirection === 'narrowing';
    lastDirection.value = currentDirection;
  }
  
  // 存储计算后的新宽度，用于后续应用
  const finalWidth = newWidth;
  
  if (panelRef.value) {
    // 计算面板的左边界和右边界
    const rect = panelRef.value.getBoundingClientRect();
    
    if (props.resizeEdge === 'right') {
      // 右侧拖拽，预览位置为左边界+新宽度
      previewPosition.value = rect.left + finalWidth;
    } else {
      // 左侧拖拽，预览位置为右边界减去新宽度
      previewPosition.value = rect.right - finalWidth;
    }
    
    // 在直接模式下，实时更新容器宽度
    if (!isPreviewMode.value) {
      panelRef.value.style.width = `${finalWidth}px`;
    }
    
    // 无论是否为预览模式，都发出resize事件
    emit('resize', { 
      width: finalWidth, 
      previewOnly: isPreviewMode.value 
    });
  }
};

// 处理鼠标抬起 - 应用最终宽度
const handleMouseUp = () => {
  if (isResizing.value && panelRef.value) {
    // 启用过渡动画，将在下一帧生效
    shouldTransition.value = true;
    
    // 计算最终应用的宽度 - 从当前预览位置反向计算出宽度
    let finalWidth = startWidth.value;
    if (isDragging.value) {
      const rect = panelRef.value.getBoundingClientRect();
      
      if (props.resizeEdge === 'right') {
        // 右侧拖拽时，宽度为预览位置减去左边界
        finalWidth = previewPosition.value - rect.left;
      } else {
        // 左侧拖拽时，宽度为右边界减去预览位置
        finalWidth = rect.right - previewPosition.value;
      }
      
      // 确保宽度在限制范围内
      if (finalWidth < props.minWidth) finalWidth = props.minWidth;
      if (finalWidth > maxWidthPx.value) finalWidth = maxWidthPx.value;
    }
    
    // 应用最终宽度并发出事件
    nextTick(() => {
      if (panelRef.value) {
        panelRef.value.style.width = `${finalWidth}px`;
        emit('resizeEnd', { width: finalWidth });
      }
    });
    
    // 保存当前宽度到本地存储
    localStorage.setItem(`${props.storageKeyPrefix}-width`, `${finalWidth}px`);
    
    // 在动画结束后重置状态
    setTimeout(() => {
      isWidening.value = false;
      isNarrowing.value = false;
      shouldTransition.value = false;
    }, 300); // 与 CSS 过渡时间匹配
  }
  
  // 重置状态
  isResizing.value = false;
  isDragging.value = false;
  
  // 移除事件监听
  document.removeEventListener('mousemove', handleMouseMove);
  document.removeEventListener('mouseup', handleMouseUp);
  
  // 恢复光标样式
  document.body.style.cursor = '';
  document.body.style.userSelect = '';
};

// 组件挂载时，尝试从本地存储中恢复宽度和拖拽模式
onMounted(() => {
  // 恢复面板宽度
  const savedWidth = localStorage.getItem(`${props.storageKeyPrefix}-width`);
  if (savedWidth && panelRef.value) {
    panelRef.value.style.width = savedWidth;
  } else if (panelRef.value) {
    panelRef.value.style.width = defaultWidth.value;
  }
  
  // 如果需要记住用户偏好，尝试恢复拖拽模式
  if (props.rememberUserPreference) {
    const savedDragMode = localStorage.getItem(`${props.storageKeyPrefix}-drag-mode`);
    if (savedDragMode) {
      isPreviewMode.value = savedDragMode === 'preview';
    }
  }
  
  // 添加窗口大小变化监听
  window.addEventListener('resize', handleWindowResize);
  
  // 在组件挂载完成后，获取初始容器高度和顶部位置
  nextTick(() => {
    if (panelRef.value) {
      const rect = panelRef.value.getBoundingClientRect();
      containerHeight.value = rect.height;
      containerTop.value = rect.top;
    }
  });
});

// 监听窗口大小变化，确保宽度不超过最大值并更新容器高度
const handleWindowResize = () => {
  if (panelRef.value) {
    const currentWidth = panelRef.value.offsetWidth;
    if (currentWidth > maxWidthPx.value) {
      panelRef.value.style.width = `${maxWidthPx.value}px`;
      localStorage.setItem(`${props.storageKeyPrefix}-width`, `${maxWidthPx.value}px`);
    }
    
    // 更新容器高度和位置
    const rect = panelRef.value.getBoundingClientRect();
    containerHeight.value = rect.height;
    containerTop.value = rect.top;
  }
};

// 组件卸载前，移除事件监听
onBeforeUnmount(() => {
  document.removeEventListener('mousemove', handleMouseMove);
  document.removeEventListener('mouseup', handleMouseUp);
  window.removeEventListener('resize', handleWindowResize);
});

// 公开方法给父组件
defineExpose({
  // 设置拖拽模式
  setDragMode: (mode: 'preview' | 'direct') => {
    isPreviewMode.value = mode === 'preview';
    if (props.rememberUserPreference) {
      localStorage.setItem(`${props.storageKeyPrefix}-drag-mode`, mode);
    }
  },
  // 获取当前拖拽模式
  getDragMode: () => isPreviewMode.value ? 'preview' : 'direct',
  // 获取当前宽度
  getCurrentWidth: () => panelRef.value?.offsetWidth || 0,
  // 设置宽度
  setWidth: (width: number | string) => {
    if (panelRef.value) {
      const widthValue = typeof width === 'number' ? `${width}px` : width;
      panelRef.value.style.width = widthValue;
      localStorage.setItem(`${props.storageKeyPrefix}-width`, widthValue);
    }
  },
  // 切换拖拽模式
  toggleDragMode
});
</script>

<style scoped>
.resizable-panel {
  position: relative;
  height: 100%;
  width: v-bind('defaultWidth');
  overflow: hidden;
}

/* 拖拽过程中的样式 */
.resizable-panel.panel-resizing {
  /* 移除阴影效果 */
}

.resize-edge-right {
  border-right: 1px solid v-bind('token.colorFill'); /* 右侧边框 */
}

.resize-edge-left {
  border-left: 1px solid v-bind('token.colorFill'); /* 左侧边框 */
}

/* 添加方向性过渡动画 */
.resizable-panel.transition-enabled {
  transition: width 0.3s cubic-bezier(0.25, 1, 0.5, 1);
}

/* 变宽时的过渡特效 */
.resizable-panel.panel-widening.transition-enabled {
  transition: width 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275); /* 弹性缓动 */
}

/* 变窄时的过渡特效 */
.resizable-panel.panel-narrowing.transition-enabled {
  transition: width 0.3s cubic-bezier(0.6, -0.28, 0.735, 0.045); /* 急停缓动 */
}

.panel-content {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.resize-handle {
  position: absolute;
  top: 0;
  width: 16px; /* 增加宽度以便于抓取 */
  height: 100%;
  cursor: ew-resize;
  z-index: 10;
  display: flex;
  justify-content: center;
}

.resize-handle.right-edge {
  right: -8px; /* 向右偏移，使手柄居中于边框 */
}

.resize-handle.left-edge {
  left: -8px; /* 向左偏移，使手柄居中于边框 */
}

.resize-handle-indicator {
  width: 4px;
  height: 100%;
  opacity: 0;
  background-color: transparent;
  transition: opacity 0.2s ease, width 0.2s ease;
  border-radius: 2px;
}

.resize-handle:hover .resize-handle-indicator {
  opacity: 0;
  width: 5px;
}

.resize-handle.resizing .resize-handle-indicator {
  opacity: 0;
  width: 6px;
}

/* 拖拽预览指示器 */
.resize-preview {
  position: fixed;
  width: 3px;
  background-color: v-bind('token.colorPrimary');
/*   box-shadow: 0 0 8px v-bind('token.colorPrimary');
 */  z-index: 1000;
  pointer-events: none; /* 防止指示器干扰鼠标事件 */
  animation: pulse 1.5s infinite;
  transform: translateX(-1px); /* 微调位置，使其刚好在边界上 */
}

@keyframes pulse {
  0% { opacity: 0.7; }
  50% { opacity: 1; }
  100% { opacity: 0.7; }
}
</style> 