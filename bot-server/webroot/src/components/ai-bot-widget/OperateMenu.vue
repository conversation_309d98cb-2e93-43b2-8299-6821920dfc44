<template>
  <a-dropdown trigger="click">
    <AiIconButton>
      <span class="icon iconfont caidan1"></span>
    </AiIconButton>
    <template #overlay>
      <a-menu :items="menuItems" @click="handleMenuItemClick"></a-menu>
    </template>
  </a-dropdown>

  <CreateConversation v-model="openNewConversation"/>
  <CreatePlatform v-model="openNewPlatform"/>
  <AiBotSetting v-model="openSetting"/>
</template>

<script setup lang="ts">
import { computed, h, onMounted, onUnmounted, ref } from "vue";
import CreateConversation from "@/components/ai-bot-conversation/CreateConversation.vue";
import CreatePlatform from "@/components/ai-bot-platform/CreatePlatform.vue";
import AiBotSetting from "@/components/ai-bot-setting/AiBotSetting.vue";
import { useAiBot } from "@/components/store/bot";
import AiIconButton from "@/components/ai-bot-widget/AiIconButton.vue";

// 添加 electron 类型声明
declare global {
  interface Window {
    electron?: {
      openDevTools: () => void;
    };
  }
}

const openNewConversation = ref(false)
const openNewPlatform = ref(false)
const openPluginCenter = ref(false)
const openSetting = ref(false)
const bot = useAiBot()
const isMac = ref(false)

// 菜单点击处理函数
const handleMenuItemClick = (menuInfo: {key: string}) => {
  handleMenuClick(menuInfo.key)
}

const handleMenuClick = (type: string) => {
  switch (type) {
    case 'conversation':
      openNewConversation.value = true
      break
    case 'platform':
      openNewPlatform.value = true
      break
    case 'plugin':
      openPluginCenter.value = true
      break
    case 'setting':
      openSetting.value = true
      break
    case 'reload':
      window.location.reload()
      break
    case 'devTools':
      if (window.electron) {
        window.electron.openDevTools()
      }
      break
  }
}

// 修改快捷键处理函数
const handleKeydown = (event: KeyboardEvent) => {
  // 检查是否按下了 Cmd+N (Mac) 或 Ctrl+N (Windows/Linux)
  if ((event.metaKey || event.ctrlKey) && event.key === 'n') {
    event.preventDefault()
    openNewConversation.value = !openNewConversation.value
  }
  // 检查是否按下了 Cmd+, (Mac) 或 Ctrl+, (Windows/Linux)
  if ((event.metaKey || event.ctrlKey) && event.key === ',') {
    event.preventDefault()
    openSetting.value = !openSetting.value
  }
  // 重新加载快捷键
  if ((event.metaKey || event.ctrlKey) && event.key === 'r') {
    event.preventDefault()
    window.location.reload()
  }
  // 开发者工具快捷键
  if ((event.metaKey || event.ctrlKey) && event.shiftKey && event.key === 'i') {
    event.preventDefault()
    if (window.electron) {
      window.electron.openDevTools()
    }
  }
}

// 组件挂载时添加事件监听
onMounted(() => {
  window.addEventListener('keydown', handleKeydown);
  // 优先使用新的 API
  if ('userAgentData' in navigator) {
    isMac.value = (navigator as any).userAgentData.platform === 'macOS'
  } else {
    // 降级使用 userAgent
    isMac.value = /macintosh|mac os x/i.test(navigator.userAgent)
  }
})

// 组件卸载时移除事件监听
onUnmounted(() => {
  window.removeEventListener('keydown', handleKeydown);
})

// 计算快捷键显示
const shortcutKeys = computed(() => ({
  newConversation: isMac.value ? '⌘N' : 'Ctrl+N',
  settings: isMac.value ? '⌘,' : 'Ctrl+,',
  reload: isMac.value ? '⌘R' : 'Ctrl+R',
  devTools: isMac.value ? '⌘⌥I' : 'Ctrl+Shift+I'
}))

// 使用Ant Design Vue的方式构建菜单项
const menuItems = computed(() => [
  {
    key: 'conversation',
    label: '新增会话',
    icon: () => h('span', { class: 'icon iconfont zaixianzixun_mian' }),
    title: '新增会话',
    children: undefined,
  },
  {
    key: 'view',
    label: '视图',
    icon: () => h('span', { class: 'icon iconfont iconset0334' }),
    children: [
      {
        key: 'reload',
        label: '重新加载',
        icon: () => h('span', { class: 'icon iconfont shuaxin' }),
        title: '重新加载',
        children: undefined,
      },
      {
        key: 'devTools',
        label: '开发者工具',
        icon: () => h('span', { class: 'icon iconfont icon_kaifazhegongju' }),
        title: '开发者工具',
        children: undefined,
      }
    ]
  }
])
</script>

<style scoped>
/* 保留图标样式 */
.icon.iconfont {
  font-size: 16px;
  color: inherit;
  margin-right: 8px;
}

/* 自定义下拉菜单样式 */
:deep(.ant-dropdown-menu) {
  border-radius: 8px;
}

:deep(.ant-dropdown-menu-item) {
  padding: 4px 12px;
  border-radius: 6px;
  margin: 2px;
}

/* 移除下拉菜单箭头 */
:deep(.ant-dropdown-arrow),
:deep(.ant-dropdown-arrow::before) {
  display: none !important;
  content: none !important;
}

/* 快捷键提示样式 - 需要通过插槽自定义 */
.shortcut-hint {
  margin-left: auto;
  padding-left: 12px;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
}
</style>