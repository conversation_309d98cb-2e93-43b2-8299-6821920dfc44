<template>
  <div 
    class="menu-list-item"
    :class="{ active: isActive }" 
    @click="handleClick"
    :title="text">
    <div class="menu-item-content">
      <div class="menu-icon" v-if="icon">
        <slot name="icon">
          <a-avatar :src="icon" size="small" v-if="icon" />
        </slot>
      </div>
      <span class="menu-text">{{ text }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { theme } from 'ant-design-vue';

const { useToken } = theme;
const { token } = useToken();

defineProps<{
  text: string;
  isActive: boolean;
  icon?: string;
}>();

const emit = defineEmits<{
  (e: 'click'): void;
}>();

const handleClick = () => {
  emit('click');
};
</script>

<style scoped>
.menu-list-item {
  width: calc(100% - 10px);
  height: 30px;
  padding: 0 10px;
  margin: 2px 3px;
  cursor: pointer;
  font-size: 14px;
  border-radius: 20px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: v-bind('token.colorTextBase');
}

.menu-item-content {
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
}

.menu-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-right: 8px;
}

.menu-text {
  flex: 1;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  user-select: none;
}

.menu-list-item:hover {
  background-color: v-bind('token.colorFillSecondary');
  border-color: v-bind('token.colorFillSecondary');
  color: v-bind('token.colorInfoTextHover');
}

.menu-list-item.active {
  background-color: v-bind('token.colorPrimaryBg');
  border-color: v-bind('token.colorPrimaryBg');
  color: v-bind('token.colorPrimaryText');
  font-weight: 500;
}
</style> 