<template>
  <component :is="navComponent" v-if="navComponent" />
  <div v-else class="toolbar w-full flex items-center justify-between px-4 py-2">
    <div v-if="ctx.ui.showChat" class="text-sm font-medium truncate">
      {{ ctx.CurrentChat.Current.Conversation.title }}
    </div>
    <PlatformConfig v-model="open" :platform="platform" @save="save"/>
  </div>
</template>

<script setup lang="ts">
import {useGptStore} from "@/components/store/gpt";
import {onMounted, ref, watch, computed, shallowRef} from 'vue';
import {usePlatform} from "@/components/store/platform";
import {useAiPluginStore} from "@/components/store/plugin";
import {getPlatforms} from "@/components/common/manageRequest";
import PlatformConfig from "@/components/ai-bot-platform/PlatformConfig.vue";
import {theme} from 'ant-design-vue';
import {useAiBot} from "@/components/store/bot";
import { useRoute } from 'vue-router';
import NavComponentManager from "@/components/ai-bot-widget/nav/NavComponentManager";

const {useToken} = theme;
const {token} = useToken();
const ctx = useGptStore();
const plat = usePlatform();
const plugin = useAiPluginStore();
const model = ref<string>("");
const platform = ref();
const open = ref(false);
const selectWrapper = ref<HTMLElement | null>(null);
const bot = useAiBot();
const route = useRoute();

// 使用计算属性确定当前要显示的导航组件
const navComponent = computed(() => {
  const routeName = route.name as string;
  const uiType = bot.ui;
  
  // 首先检查是否有与路由相关的导航组件
  let component = NavComponentManager.getComponentByRoute(routeName);
  
  // 如果没有路由相关的组件，则检查是否有与UI类型相关的导航组件
  if (!component && routeName !== 'guide' && routeName !== 'loading') {
    component = NavComponentManager.getComponentByUI(uiType);
  }
  
  return component;
});

function select(value) {
  platform.value = plat.platforms.find(p => p.id === value);
  let pram = {...plugin.currentPlugin}
  pram.platformID = value
  plugin.updatePlugin(pram)
  plugin.currentPlugin = pram
}

function save() {
  platform.value = null
  let p = plat.platforms.find(p => p.id === plugin.currentPlugin?.platformID)
  setTimeout(() => {
    platform.value = p
  }, 200)
}

watch(() => plugin.currentPlugin?.platformID, (newVal) => {
  if (newVal) {
    model.value = newVal
    platform.value = plat.platforms.find(p => p.id === model.value);
  }
}, {
  deep: true,
})

function getPopupContainer() {
  return selectWrapper.value
}

onMounted(async () => {
  const platforms = await getPlatforms()
  plat.platforms = platforms.map(p => {
    let config = {...p}
    config.settings = JSON.parse(config.config)
    return config
  })
  if (plugin.currentPlugin) {
    platform.value = plat.platforms.find(p => p.id === plugin.currentPlugin.platformID)
    model.value = plugin.currentPlugin.platformID
  }
})
</script>

<style scoped>
.toolbar {
  position: relative;
  z-index: 1;
  border-bottom: 1px solid v-bind('token.colorFill');
}

.toolbar .text-sm {
  max-width: 80%;
}


</style> 