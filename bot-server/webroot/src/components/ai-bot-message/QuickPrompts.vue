<template>
  <div class="quick-prompts-container">
    <div class="prompts-content">
      <a-card class="prompt-card"
              size="small"
              v-for="(card, index) in displayCards"
              :key="index"
              hoverable
              :title="card.title"
              :style="{ animationDelay: index * 100 + 'ms' }"
      >
        <div class="card-content">
          <div class="card-desc">{{ card.description }}</div>
        </div>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import {useGptStore} from "@/components/store/gpt";
import {onMounted, ref} from 'vue';
import {theme} from 'ant-design-vue';

const {useToken} = theme;
const {token} = useToken();
const ctx = useGptStore();

// 定义四个类别的内容
const categories = [
  {
    title: "代码优化",
    items: [
      "如何优化这段代码的性能？",
      "这段代码有什么可以改进的地方？",
      "帮我重构这个函数使其更简洁"
    ]
  },
  {
    title: "学习指导",
    items: [
      "如何学习这个技术框架？",
      "这个概念能详细解释一下吗？",
      "学习路线应该怎么规划？"
    ]
  },
  {
    title: "问题分析",
    items: [
      "这个错误是什么原因？",
      "如何解决这个技术难题？",
      "帮我分析下性能瓶颈"
    ]
  },
  {
    title: "技术支持",
    items: [
      "如何实现这个功能？",
      "这个问题有什么解决方案？",
      "能帮我调试这段代码吗？"
    ]
  }
];

// 随机选择每个类别中的一个问题
const getRandomItem = (items: string[]) => {
  const randomIndex = Math.floor(Math.random() * items.length);
  return items[randomIndex];
};

// 生成展示卡片
const displayCards = ref([]);

onMounted(() => {
  displayCards.value = categories.map(category => ({
    title: category.title,
    description: getRandomItem(category.items)
  }));
});
</script>

<style scoped>
.quick-prompts-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
}

.prompts-content {
  width: 100%;
  max-width: 700px;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  align-content: center;
  justify-items: center;
}

.prompt-card {
  width: 95%;
  height: 100px;
  animation: fadeInUp 0.5s ease-out forwards;
  opacity: 0;
  transform: translateY(20px);
}

.prompt-card :deep(.ant-card-head) {
  min-height: 36px;
  padding: 0 12px;
  font-size: 14px;
  border-bottom: 1px solid v-bind('token.colorBorderSecondary');
}

.prompt-card :deep(.ant-card-head-title) {
  padding: 6px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.prompt-card :deep(.ant-card-body) {
  padding: 12px;
  height: calc(100% - 36px);
}

.card-content {
  height: 100%;
  display: flex;
  align-items: center;
}

.card-desc {
  font-size: 13px;
  color: v-bind('token.colorTextSecondary');
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
