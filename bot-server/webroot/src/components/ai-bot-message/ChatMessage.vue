<template>
  <div class="chat-message">
    <div class="chat-message-body">
      <div v-if="isExporting" class="loading-overlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在生成图片...</div>
      </div>
      <div class="chat-header" :class="message.role">
        <template v-if="message.role === RoleType.Assistant">
          <!--          <img class="avatar-img" :src="header" alt="avatar"/>-->
          <a-avatar :src="header">
          </a-avatar>
          <span class="user-name">{{ name }}</span>
          <div class="action-buttons">
            <button class="action-btn" @click="handleCopyMessage">
              <i class="icon iconfont copy"></i>
            </button>
            <div class="export-dropdown">
              <button class="action-btn" @click="toggleExportMenu">
                <i class="icon iconfont daochu"></i>
              </button>
              <div class="export-menu" v-show="showExportMenu">
                <div class="export-item" @click="handleExportImage">
                  <i class="icon iconfont tupian"></i>
                  <span>导出图片</span>
                </div>
                <div class="export-item" @click="handleExportPDF">
                  <i class="icon iconfont pdf"></i>
                  <span>导出PDF</span>
                </div>
              </div>
            </div>
            <button class="action-btn" @click="handleDeleteMessage">
              <i class="icon iconfont delete"></i>
            </button>
          </div>
        </template>
        <template v-else>
          <div class="action-buttons">
            <button class="action-btn" @click="handleDeleteMessage">
              <i class="icon iconfont delete"></i>
            </button>
            <button class="action-btn" @click="handleCopyMessage">
              <i class="icon iconfont copy"></i>
            </button>
          </div>
          <a-avatar class="user-avatar" :style="{ verticalAlign: 'middle' }">
            U
          </a-avatar>
        </template>
      </div>
      <div ref="bodyRef" class="chat-body size-full" :class="message.role">
        <component :is="scene" :key="message.id" :message="message" :index="index"/>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {computed, onBeforeMount, onMounted, ref} from "vue";
import {useGptStore} from "@/components/store/gpt";
import {MessageItem, RoleType} from "@/components/common/model/chat";
import {deleteMessage as deleteMessageRequest} from '@/components/common/manageRequest'
import {
  copyMessage as copyMessageAction,
  deleteMessage as deleteMessageAction,
  exportAsImage as exportImageAction,
  exportAsPDF as exportPDFAction
} from './actions';
import {sceneMap} from "@/components/ai-bot-message/scene/scene";
import {theme} from 'ant-design-vue';

const {useToken} = theme;
const {token} = useToken();

const props = defineProps<
    {
      message: MessageItem,
      index: number
    }
>()

// 渲染消息文本存储
const info = ref({...props.message!})
const ctx = useGptStore()
const bodyRef = ref<HTMLElement>()
const name = ref(info.value.role)
const send = ref(false)
const fold = ref('300px')
const header = ref('')
const showExportMenu = ref(false)
const isExporting = ref(false)

if (info.value.content != '') {
  fold.value = 'none'
}
switch (info.value.role) {
  case RoleType.User:
    send.value = true
    name.value = ""
    header.value = ctx.defaultAvatar
    break
  case RoleType.Assistant:
    send.value = false
    name.value = 'AI助手'
    header.value = ctx.gptDefaultAvatar
    break
}

const scene = computed(() => {
  return sceneMap.get(info.value.messageType)
})

// 复制消息内容
const handleCopyMessage = () => {
  copyMessageAction(bodyRef.value);
};

// 删除消息
const handleDeleteMessage = async () => {
  await deleteMessageAction(props.message, deleteMessageRequest);
};

// 导出图片
const handleExportImage = async () => {
  showExportMenu.value = false;
  isExporting.value = true;
  try {
    await exportImageAction(bodyRef.value!);
  } finally {
    isExporting.value = false;
  }
};

const toggleExportMenu = () => {
  showExportMenu.value = !showExportMenu.value
}

const handleExportPDF = async () => {
  showExportMenu.value = false;
  isExporting.value = true;
  try {
    await exportPDFAction(bodyRef.value!);
  } finally {
    isExporting.value = false;
  }
}

// 点击其他地方关闭菜单
onMounted(() => {
  document.addEventListener('click', (e: MouseEvent) => {
    const target = e.target as HTMLElement
    if (!target.closest('.export-dropdown')) {
      showExportMenu.value = false
    }
  })

  // 监听鼠标离开操作按钮区域
  const actionButtons = document.querySelector('.action-buttons')
  if (actionButtons) {
    actionButtons.addEventListener('mouseleave', () => {
      showExportMenu.value = false
    })
  }
})


onBeforeMount(() => {
  info.value.ex = JSON.parse(info.value.ex)
})


</script>


<style>
.chat-message {
  display: flex;
  flex-direction: column;
  padding: 2px;
}

.chat-message-body {
  flex: 1;
  position: relative;
  padding: 2px;
  display: flex;
  flex-direction: column;
}

.chat-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
  padding: 1px 1px;
  width: 100%;
}

.chat-header.user {
  justify-content: flex-end;
}

.chat-header.assistant {
  justify-content: flex-start;
}

.chat-header.assistant > div {
  width: 100%;
}

.user-avatar {
  background-color: v-bind('token.colorPrimary');
}


.user-name {
  font-size: 14px;
  font-weight: 500;
  margin: 0;
  white-space: nowrap;
}

.chat-body {
  width: 100%;
  display: flex;
  flex-direction: row;
  overflow: hidden;
  padding: 2px;
  border-radius: 5px;
}

.chat-body.user {
  justify-content: flex-end;
}

.chat-body.assistant {
  min-width: 100%;
  width: 100%;
  justify-content: flex-start;
  background-color: v-bind('token.colorBgElevated');
  border: 1px solid v-bind('token.colorBorder');
}

/*
 统一修改 消息下面的配色，代码块部分除外
*/
.chat-body.assistant * :not(.code-block *) {
  color: v-bind('token.colorTextBase');
}

.chat-body * {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.action-buttons {
  display: flex;
  flex-direction: row;
  gap: 4px;
  padding: 2px 4px;
  border-radius: 4px;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s ease;
}

.chat-header:hover .action-buttons {
  visibility: visible;
  opacity: 1;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 4px;
  background: transparent;
  cursor: pointer;
  padding: 0;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background-color: v-bind('token.colorPrimaryBgHover');
  color: v-bind('token.colorPrimaryTextHover');
}

.action-btn i {
  font-size: 16px;
  color: #666;
  opacity: 0.6;
  transition: opacity 0.2s;
}

.action-btn:hover i {
  opacity: 1;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* 保留动画和基础样式 */
.action-btn .download {
  font-size: 16px;
}

/* 加载动画相关样式 */
.loading-overlay,
.loading-spinner,
.loading-text,
.global-loading-overlay {
  /* 这些样式现在由 JavaScript 动态添加 */
  display: none;
}

/* 预览相关样式 */
.preview-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.preview-image {
  max-width: 90vw;
  max-height: 90vh;
  object-fit: contain;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-radius: 8px;
  background: white;
}

.preview-download-btn {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  padding: 10px 24px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.preview-download-btn:hover {
  background: #40a9ff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.preview-tips {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  color: white;
  font-size: 14px;
  text-align: center;
  background: rgba(0, 0, 0, 0.5);
  padding: 8px 16px;
  border-radius: 4px;
  z-index: 10000;
}

/* Markdown 内容样式优化 */
.chat-body :deep(.markdown-body) {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

.chat-body :deep(.markdown-body):hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.chat-body :deep(.markdown-body pre) {
  background-color: #f6f8fa;
  border-radius: 6px;
  padding: 16px;
  margin: 8px 0;
}

.export-dropdown {
  position: relative;
}

.export-menu {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: white;
  border-radius: 2px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  min-width: 100px;
  z-index: 1000;
  margin-top: 4px;
  overflow: hidden;
}

.export-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.export-item:hover {
  background-color: #f5f5f5;
}

.export-item i {
  font-size: 14px;
  color: #666;
}

.export-item span {
  font-size: 12px;
  color: #333;
}
</style>