import domToImage from 'dom-to-image';
import { useAiBot } from '@/components/store/bot';

/**
 * 生成图片数据 URL
 * @param element 要转换为图片的 DOM 元素
 * @param scale 缩放比例，默认为 1.5
 * @returns 图片的数据 URL
 */
export const generateImageData = async (element: HTMLElement, scale: number = 1.5) => {
    const width = element.offsetWidth * scale;
    const height = element.offsetHeight * scale;
    
    // 获取当前主题信息
    const botStore = useAiBot();
    const isDarkMode = botStore.theme === 'dark';
    
    // 根据当前主题设置背景色
    const bgColor = isDarkMode ? '#1f1f1f' : '#ffffff';
    
    // 克隆元素进行处理
    const cloneContainer = document.createElement('div');
    cloneContainer.style.position = 'absolute';
    cloneContainer.style.left = '-9999px';
    cloneContainer.style.top = '-9999px';
    
    // 克隆原始元素
    const clone = element.cloneNode(true) as HTMLElement;
    
    // 确保克隆元素的宽度与原始元素一致
    clone.style.width = `${element.offsetWidth}px`;
    
    // 添加到DOM中以便计算样式
    document.body.appendChild(cloneContainer);
    cloneContainer.appendChild(clone);
    
    // 处理深色模式的样式调整
    if (isDarkMode) {
        // 递归地为克隆元素及其子元素应用计算样式
        applyStyles(element, clone);
    }
    
    // 优化选项
    const options = {
        quality: 0.95,
        bgcolor: bgColor, // 根据主题使用相应的背景色
        width: width,
        height: height,
        cacheBust: true,
        style: {
            transform: `scale(${scale})`,
            transformOrigin: 'top left',
            // 添加性能优化
            'image-rendering': 'optimizeQuality',
        },
        filter: (node: Element) => {
            // 过滤不需要的元素
            if (node.classList?.contains('action-buttons')) return false;
            // 过滤隐藏的元素
            if (node instanceof HTMLElement) {
                const style = window.getComputedStyle(node);
                return style.display !== 'none' && style.visibility !== 'hidden';
            }
            return true;
        }
    };

    try {
        // 使用处理后的克隆元素生成图片
        const dataUrl = isDarkMode 
            ? await domToImage.toPng(clone, options)
            : await domToImage.toPng(element, options);
        
        return dataUrl;
    } catch (error) {
        console.error('生成图片失败:', error);
        throw error;
    } finally {
        // 清理临时元素
        if (cloneContainer.parentNode) {
            cloneContainer.parentNode.removeChild(cloneContainer);
        }
    }
};

/**
 * 递归地将源元素的计算样式应用到目标元素
 * @param source 源元素
 * @param target 目标元素
 */
function applyStyles(source: HTMLElement, target: HTMLElement) {
    // 获取源元素的计算样式
    const computedStyle = window.getComputedStyle(source);
    
    // 应用关键样式属性
    const criticalStyles = [
        'color', 'backgroundColor', 'borderColor', 'borderWidth', 'borderStyle',
        'fontFamily', 'fontSize', 'fontWeight', 'textAlign', 'paddingTop', 'paddingRight',
        'paddingBottom', 'paddingLeft', 'marginTop', 'marginRight', 'marginBottom', 'marginLeft'
    ];
    
    criticalStyles.forEach(style => {
        target.style[style as any] = computedStyle[style as any];
    });
    
    // 为特定的Markdown元素应用样式
    if (target.classList.contains('markdown-body')) {
        target.style.backgroundColor = computedStyle.backgroundColor;
        target.style.color = computedStyle.color;
    }
    
    // 为代码块应用正确的样式
    const codeBlocks = target.querySelectorAll('pre, code');
    codeBlocks.forEach((block) => {
        if (block instanceof HTMLElement) {
            const originalBlock = source.querySelector(`#${block.id}`) || 
                                 source.querySelector(`.${Array.from(block.classList).join('.')}`);
            
            if (originalBlock instanceof HTMLElement) {
                const codeStyle = window.getComputedStyle(originalBlock);
                block.style.backgroundColor = codeStyle.backgroundColor;
                block.style.color = codeStyle.color;
                block.style.borderColor = codeStyle.borderColor;
            }
        }
    });
    
    // 递归处理子元素
    Array.from(source.children).forEach((child, index) => {
        if (child instanceof HTMLElement && index < target.children.length && target.children[index] instanceof HTMLElement) {
            applyStyles(child, target.children[index] as HTMLElement);
        }
    });
}