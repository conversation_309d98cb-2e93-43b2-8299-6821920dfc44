import {message as antMessage} from 'ant-design-vue';
import {MessageItem} from '@/components/common/model/chat';
import {useGptStore} from '@/components/store/gpt';
import {generateImageData} from './utils/imageUtils';
import {useAiBot} from '@/components/store/bot';

// 复制消息内容
export const copyMessage = (id: string, messageElement: HTMLElement | null) => {
    if (!messageElement) return;

    // 获取纯文本内容
    let text = messageElement.innerText || messageElement.textContent || '';
    text = text.replace(/\s+/g, ' ').trim();

    // 创建临时文本区域
    const textarea = document.createElement('textarea');
    textarea.value = text;
    textarea.style.position = 'fixed';
    textarea.style.opacity = '0';
    document.body.appendChild(textarea);

    try {
        textarea.select();
        textarea.setSelectionRange(0, textarea.value.length);

        const successful = document.execCommand('copy');
        if (successful) {
            antMessage.success({
                key: id,
                content: '已复制到剪贴板'
            });
        } else {
            navigator.clipboard.writeText(text).then(() => {
                antMessage.success({
                    key: id,
                    content: '已复制到剪贴板'
                });
            }).catch(err => {
                console.error('复制失败:', err);
                antMessage.error({
                    key: id,
                    content: '复制失败，请手动复制'
                });
            });
        }
    } catch (err) {
        console.error('复制出错:', err);
        navigator.clipboard.writeText(text).then(() => {
            antMessage.success('已复制到剪贴板');
        }).catch(() => {
            antMessage.error('复制失败，请手动复制');
        });
    } finally {
        document.body.removeChild(textarea);
    }
};

// 删除消息
export const deleteMessage = async (message: MessageItem, deleteRequest: Function) => {
    try {
        const result = await deleteRequest(message.id!);
        if (result.code === 200) {
            const ctx = useGptStore();
            ctx.removeMessage(message.id!);
            antMessage.success('删除成功');
            return true;
        } else {
            antMessage.error(result.msg || '删除失败');
            return false;
        }
    } catch (error) {
        console.error('删除消息失败:', error);
        antMessage.error('删除失败');
        return false;
    }
};

// 导出图片
export const exportAsImage = async (messageElement: HTMLElement | null) => {
    if (!messageElement) {
        antMessage.error('无法获取消息元素');
        return;
    }

    // 创建预览容器和加载动画
    const previewContainer = createPreviewContainer();
    document.body.appendChild(previewContainer);

    // 添加导出中的加载样式
    messageElement.classList.add('exporting-image');
    
    try {
        const dataUrl = await generateImageData(messageElement);
        if (dataUrl) {
            showImagePreview(previewContainer, dataUrl);
        }
    } catch (error) {
        console.error('导出图片失败:', error);
        antMessage.error('导出图片失败，请重试');
        if (document.body.contains(previewContainer)) {
            document.body.removeChild(previewContainer);
        }
    } finally {
        // 移除导出中的样式
        messageElement.classList.remove('exporting-image');
    }
};

// 创建预览容器
const createPreviewContainer = () => {
    const previewContainer = document.createElement('div');
    previewContainer.className = 'preview-container';
    previewContainer.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    `;

    // 创建加载动画
    const loadingContent = document.createElement('div');
    loadingContent.style.cssText = `
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    `;

    const spinner = document.createElement('div');
    spinner.className = 'loading-spinner';
    spinner.style.cssText = `
        width: 50px;
        height: 50px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-top: 4px solid #ffffff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    `;

    const loadingText = document.createElement('div');
    loadingText.style.cssText = `
        margin-top: 16px;
        font-size: 16px;
        color: #ffffff;
    `;
    loadingText.innerText = '正在生成图片...';

    // 添加动画样式
    const style = document.createElement('style');
    style.textContent = `
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    `;
    document.head.appendChild(style);

    loadingContent.appendChild(spinner);
    loadingContent.appendChild(loadingText);
    previewContainer.appendChild(loadingContent);

    return previewContainer;
};

// 显示图片预览
const showImagePreview = (previewContainer: HTMLElement, dataUrl: string) => {
    // 获取当前主题
    const botStore = useAiBot();
    const isDarkMode = botStore.theme === 'dark';
    
    // 创建图片元素
    const img = document.createElement('img');
    img.className = 'preview-image';
    img.style.cssText = `
        display: none;
        max-width: 90vw;
        max-height: 90vh;
        object-fit: contain;
        box-shadow: 0 4px 12px rgba(0, 0, 0, ${isDarkMode ? '0.5' : '0.15'});
        border-radius: 8px;
        background: ${isDarkMode ? '#1f1f1f' : 'white'};
    `;
    img.src = dataUrl;

    const downloadBtn = document.createElement('button');
    downloadBtn.className = 'preview-download-btn';
    downloadBtn.innerText = '下载图片';
    downloadBtn.style.cssText = `
        position: fixed;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        padding: 10px 24px;
        background: #1890ff;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 16px;
        font-weight: 500;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        z-index: 10000;
    `;

    const tipsDiv = document.createElement('div');
    tipsDiv.className = 'preview-tips';
    tipsDiv.style.cssText = `
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        color: white;
        font-size: 14px;
        text-align: center;
        background: rgba(0, 0, 0, 0.5);
        padding: 8px 16px;
        border-radius: 4px;
        z-index: 10000;
    `;
    tipsDiv.innerText = '点击"下载图片"按钮保存，点击背景关闭预览';

    // 图片加载完成时的处理
    img.onload = () => {
        // 清除加载动画
        previewContainer.innerHTML = '';
        // 添加预览界面元素
        previewContainer.appendChild(tipsDiv);
        previewContainer.appendChild(img);
        previewContainer.appendChild(downloadBtn);
        img.style.display = 'block';
    };

    // 点击背景关闭预览
    previewContainer.onclick = (e) => {
        if (e.target === previewContainer) {
            document.body.removeChild(previewContainer);
        }
    };

    // 下载按钮点击事件
    downloadBtn.onclick = async (e) => {
        e.stopPropagation();
        try {
            const link = document.createElement('a');
            link.href = dataUrl;
            link.download = `chat-export-highres-${Date.now()}.png`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            document.body.removeChild(previewContainer);
            antMessage.success('高清图片已开始下载');
        } catch (error) {
            console.error('保存图片失败:', error);
            antMessage.error('保存图片失败，请重试');
        }
    };
};

// 导出PDF
export const exportAsPDF = async (messageElement: HTMLElement | null) => {
    if (!messageElement) {
        antMessage.error('无法获取消息元素');
        return;
    }

    try {
        // 使用全局变量
        const html2pdf = (window as any).html2pdf;
        if (!html2pdf) {
            throw new Error('html2pdf.js未加载');
        }
        
        // 创建一个临时容器来设置A4尺寸
        const tempContainer = document.createElement('div');
        tempContainer.style.cssText = `
            width: 210mm;
            padding: 20mm;
            background-color: white;
            position: absolute;
            left: -9999px;
            top: -9999px;
        `;


        // 显示加载提示
        antMessage.loading({ content: '正在生成PDF...', key: 'pdfExport', duration: 0 });

        try {
            // 先使用dom-to-image生成图片
            const dataUrl = await generateImageData(messageElement, 2);
            
            // 创建图片容器
            const imgContainer = document.createElement('div');
            imgContainer.style.cssText = `
                width: 210mm;
                background-color: white;
                display: flex;
                justify-content: center;
                align-items: center;
            `;
            
            // 创建图片元素
            const img = document.createElement('img');
            img.src = dataUrl;
            img.style.cssText = `
                max-width: 100%;
                height: auto;
                display: block;
            `;
            
            imgContainer.appendChild(img);
            
            // 配置PDF选项
            const opt = {
                margin: [10, 10, 10, 10],
                filename: `chat-export-${Date.now()}.pdf`,
                image: { type: 'jpeg', quality: 0.98 },
                html2canvas: { 
                    scale: 4,
                    useCORS: true,
                    letterRendering: true
                },
                jsPDF: { 
                    unit: 'mm', 
                    format: 'a4', 
                    orientation: 'portrait'
                }
            };

            const worker = html2pdf();
            await worker.set(opt).from(img).save();
            antMessage.success({ content: 'PDF已开始下载', key: 'pdfExport' });
        } finally {
            // 清理临时元素
            document.body.removeChild(tempContainer);
        }
    } catch (error) {
        console.error('导出PDF失败:', error);
        antMessage.error({ content: '导出PDF失败，请重试', key: 'pdfExport' });
    }
}; 