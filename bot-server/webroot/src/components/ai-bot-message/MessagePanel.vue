<template>
  <div class="size-full" style="overflow: hidden;">
    <div class="size-full" style="overflow: hidden;">
      <div class="size-full flex-grow relative custom-scrollbar" style="overflow: auto;">
        <template v-if="ctx.CurrentChat.messageList.length === 0">
          <QuickPrompts/>
        </template>
        <DynamicScroller v-else class="size-full " ref="scroller"
                         :items="ctx.CurrentChat.messageList"
                         :min-item-size="10"
                         @scroll="handleScroll"
        >
          <template v-slot="{ item, index, active }">
            <DynamicScrollerItem
                :item="item"
                :active="active"
                :data-index="index"
            >
              <template v-if="item.messageType==MessageScene.ClearScene" :key="item.id">
                <ClearMessage/>
              </template>
              <template v-else>
                <!-- <ChatMessage :key="item.id" :message="item" :index="index"/> -->
                <AgentMessage :key="item.id" :message="item" :index="index"/>
              </template>
            </DynamicScrollerItem>
          </template>
        </DynamicScroller>

        <!-- 悬浮回到底部按钮 -->
        <div v-if="showScrollButton"
             class="scroll-to-bottom-btn"
             @click="MoveScrollBottom">
          <a-button shape="round" class="scroll-bottom-button">
            <template #icon>
              <span class="icon iconfont arrow-2"></span>
            </template>
          </a-button>
        </div>
      </div>
    </div>
  </div>

</template>


<script setup lang="ts">
import {onMounted, onUnmounted, ref, watch, computed, provide} from "vue";
import emitter from "@/plugins/event";
import {ScrollToBottom, SetScrollPosition} from "@/plugins/evenKey";
import {useGptStore} from "@/components/store/gpt";
import ChatMessage from "@/components/ai-bot-message/ChatMessage.vue";
import QuickPrompts from "@/components/ai-bot-message/QuickPrompts.vue";
import MessageToolbar from "@/components/ai-bot-message/MessageToolbar.vue";
import {MessageScene} from "@/components/common/model/chat";
import ClearMessage from "@/components/ai-bot-message/ClearMessage.vue";
import {theme} from 'ant-design-vue';
import AgentMessage from "./AgentMessage.vue";

const {useToken} = theme;
const {token} = useToken();
const scroller = ref()
const ctx = useGptStore()
const h = ref(0)
const showScrollButton = ref(false)

// 用于记录是否在底部的状态
const isAtBottom = ref(true)

// 滚动条比例 (0-1之间的值，表示当前滚动位置占总可滚动区域的比例)
const scrollRatio = ref(0)

provide('scrollRatio', scrollRatio)

// 上一次消息列表长度
const prevMessageLength = ref(0)

defineExpose({
  MoveScrollBottom,
})

/*
* @description: 监听滚动事件
* */
function handleScroll(event: any) {
  if (scroller.value) {
    const {scrollTop, scrollHeight, clientHeight} = event.target
    // 如果滚动条不在底部，显示按钮
    // 添加一个小的阈值（20px）以允许轻微滚动不显示按钮
    const atBottom = scrollTop >= scrollHeight - clientHeight - 20
    showScrollButton.value = !atBottom
    isAtBottom.value = atBottom
    
    // 计算滚动比例
    const maxScrollTop = scrollHeight - clientHeight
    scrollRatio.value = maxScrollTop > 0 ? scrollTop / maxScrollTop : 1
    
    // 保存当前滚动位置到 store 中持久化
    if (ctx.CurrentChat.Current?.Conversation?.id) {
      ctx.saveScrollPosition(ctx.CurrentChat.Current.Conversation.id, scrollTop);
    }
  }
}

/**
 * @description: 设置滚动条位置根据比例
 * @param ratio 滚动比例 0-1之间的值
 */
function handleSetScrollPosition(ratio: number) {
  if (scroller.value) {
    const scrollerEl = scroller.value.$el;
    const { scrollHeight, clientHeight } = scrollerEl;
    
    // 计算最大可滚动高度
    const maxScrollTop = scrollHeight - clientHeight;
    
    // 确保比例在0-1之间
    const clampedRatio = Math.max(0, Math.min(1, ratio));
    
    // 根据比例计算精确的滚动位置
    const targetScrollTop = Math.round(maxScrollTop * clampedRatio);
    
    // 设置滚动位置，使用平滑滚动
    scrollerEl.scrollTo({
      top: targetScrollTop,
      behavior: 'smooth'
    });
    
    // 更新内部状态
    scrollRatio.value = clampedRatio;
    
    // 判断是否在底部，添加小容差以提高用户体验
    const isNearBottom = clampedRatio >= 0.98; // 98%以上视为在底部
    isAtBottom.value = isNearBottom;
    showScrollButton.value = !isNearBottom;
    
    // 保存滚动位置到持久化存储
    if (ctx.CurrentChat.Current?.Conversation?.id) {
      ctx.saveScrollPosition(ctx.CurrentChat.Current.Conversation.id, targetScrollTop);
    }
  }
}

/*
* @description: 移动滚动条到最底部
* */
function MoveScrollBottom() {
  setTimeout(() => {
    if (ctx.ui.showChat) {
      if (scroller.value) {
        // 获取滚动元素
        const scrollerEl = scroller.value.$el;
        // 获取滚动目标位置（底部）
        const scrollTarget = scrollerEl.scrollHeight - scrollerEl.clientHeight;
        
        // 保留平滑滚动效果
        scrollerEl.scrollTo({
          top: scrollTarget,
          behavior: 'smooth'
        });
        
        // 设置一个备份机制，确保滚动完成
        const currentScrollTop = scrollerEl.scrollTop;
        const scrollDistance = scrollTarget - currentScrollTop;
        
        // 根据滚动距离动态调整时间
        const animationTime = Math.max(500, Math.min(1000, scrollDistance / 3));
        
        // 动画进行中隐藏按钮
        showScrollButton.value = false;
        isAtBottom.value = true;
        
        // 确保最终位置正确 - 添加额外检查
        setTimeout(() => {
          // 再次检查是否真的滚动到底部，如果没有则强制滚动
          const finalScrollTop = scrollerEl.scrollTop;
          const finalTarget = scrollerEl.scrollHeight - scrollerEl.clientHeight;
          if (finalScrollTop < finalTarget - 10) {
            scrollerEl.scrollTop = finalTarget;
          }
          
          // 始终保存最新的滚动位置到 store 中持久化
          if (ctx.CurrentChat.Current?.Conversation?.id) {
            // 使用实际的滚动位置，而不是理论上的目标位置
            const actualPosition = scrollerEl.scrollTop;
            ctx.saveScrollPosition(ctx.CurrentChat.Current.Conversation.id, actualPosition);
          }
        }, animationTime);
      }
    }
  }, 100)
}

// 恢复保存的滚动位置
function restoreScrollPosition() {
  const conversationId = ctx.CurrentChat.Current?.Conversation?.id;
  if (conversationId) {
    const savedPosition = ctx.getScrollPosition(conversationId);
    
    setTimeout(() => {
      if (scroller.value) {
        const scrollerEl = scroller.value.$el;
        
        // 如果有保存的滚动位置，恢复到该位置
        if (savedPosition !== undefined) {
          // 使用非平滑滚动立即恢复位置
          scrollerEl.scrollTop = savedPosition;
          
          // 检查滚动位置是否需要显示回到底部按钮
          const {scrollHeight, clientHeight} = scrollerEl;
          const atBottom = savedPosition >= scrollHeight - clientHeight - 20;
          showScrollButton.value = !atBottom;
          isAtBottom.value = atBottom;
        } 
        // 如果没有保存的滚动位置，直接滚动到底部
        else {
          // 立即滚动到底部（非平滑）
          const {scrollHeight, clientHeight} = scrollerEl;
          scrollerEl.scrollTop = scrollHeight - clientHeight;
          showScrollButton.value = false;
          isAtBottom.value = true;
        }
      }
    }, 300); // 给一点时间让组件渲染完成
  }
}

// 监听当前聊天变化，恢复滚动位置
watch(() => ctx.CurrentChat.Current?.Conversation?.id, (newId, oldId) => {
  if (newId && newId !== oldId) {
    // 重置底部标志和消息长度记录
    isAtBottom.value = true;
    prevMessageLength.value = 0;
    // 恢复保存的滚动位置
    restoreScrollPosition();
  }
}, { immediate: true });

// 监听消息列表变化
watch(() => ctx.CurrentChat.messageList.length, (newLength, oldLength) => {
  // 记录新消息长度
  const currentLength = newLength;
  
  // 如果是首次加载消息（prevMessageLength为0），或者消息数量变少了（可能是删除了消息）
  if (prevMessageLength.value === 0 || currentLength < prevMessageLength.value) {
    prevMessageLength.value = currentLength;
    return;
  }
  
  // 如果有新消息到达
  if (currentLength > prevMessageLength.value) {
    setTimeout(() => {
      if (scroller.value) {
        const scrollerEl = scroller.value.$el;
        
        // 如果用户之前在底部，自动滚动到底部查看新消息
        if (isAtBottom.value) {
          MoveScrollBottom();
        } 
        // 否则保持相对位置，不滚动到底部
        else {
          // 可以在这里提供一个提示，告诉用户有新消息
          // 已经有"回到底部"按钮了，不需要额外处理
        }
      }
      
      // 更新上一次消息长度记录
      prevMessageLength.value = currentLength;
    }, 100);
  }
});

// 初始化时检查滚动位置
onMounted(() => {
  if (scroller.value) {
    setTimeout(() => {
      const scrollerEl = scroller.value.$el
      const {scrollTop, scrollHeight, clientHeight} = scrollerEl
      const atBottom = scrollTop >= scrollHeight - clientHeight - 20;
      showScrollButton.value = !atBottom
      isAtBottom.value = atBottom
      
      // 记录初始消息长度
      prevMessageLength.value = ctx.CurrentChat.messageList.length;
      
      // 恢复滚动位置
      restoreScrollPosition();
      
      // 监听窗口大小改变事件
      window.addEventListener('resize', handleResize);
    }, 200)
  }
})

// 处理窗口大小变化
function handleResize() {
  // 如果用户原本在底部，窗口大小变化后保持在底部
  if (isAtBottom.value && scroller.value) {
    setTimeout(() => {
      MoveScrollBottom();
    }, 100);
  } 
  // 否则保存当前滚动位置
  else {
    saveCurrentScrollPosition();
  }
}

// 保存当前滚动位置
function saveCurrentScrollPosition() {
  if (scroller.value && ctx.CurrentChat.Current?.Conversation?.id) {
    const scrollerEl = scroller.value.$el;
    const scrollTop = scrollerEl.scrollTop;
    ctx.saveScrollPosition(ctx.CurrentChat.Current.Conversation.id, scrollTop);
  }
}

emitter.on(ScrollToBottom, MoveScrollBottom)
emitter.on(SetScrollPosition, handleSetScrollPosition)
onUnmounted(() => {
  // 移除窗口大小改变事件监听
  window.removeEventListener('resize', handleResize);
  
  // 在组件卸载前保存滚动位置
  saveCurrentScrollPosition();
  emitter.off(ScrollToBottom, MoveScrollBottom)
  emitter.off(SetScrollPosition, handleSetScrollPosition)
})

</script>

<style>

.message-card {
  width: 100% !important;
  height: 100% !important;
  overflow: hidden !important;
}

.message-card .ant-card-body {
  padding: 0 !important;
  height: 100%;
  border-radius: 5px !important;
}

.message-card .vue-recycle-scroller {
  border-radius: 5px !important;
}

.scroller {
  padding-bottom: 2px;
  scroll-behavior: auto; /* 确保其他滚动行为不受影响 */
}


.toolbar .text-sm {
  max-width: 80%;
}


/* 悬浮回到底部按钮样式 */
.scroll-to-bottom-btn {
  position: absolute;
  bottom: 10px;
  right: 20px;
  z-index: 10;
  cursor: pointer;
  transition: opacity 0.3s;
  animation: fadeIn 0.3s;
}

.scroll-bottom-button {
  padding: 2px 16px;
  display: flex;
  align-items: center;
  background-color: v-bind('token.colorBgBase');
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 自定义滚动条样式 - 默认隐藏，活跃时显示，不影响布局 */
.custom-scrollbar {
  /* 使用 scrollbar-gutter 保持滚动条空间稳定 */
  scrollbar-gutter: stable;
  /* 保持滚动条位置稳定，不会在显示/隐藏时导致布局变化 */
  overflow-y: scroll;
  /* 防止水平滚动 */
  overflow-x: hidden;
  padding-left: 10px;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 5px;
  /* 背景透明 */
  background-color: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: v-bind('token.colorPrimary');
  border-radius:5px;
  transition: background-color 0.3s ease;
}

.custom-scrollbar:hover::-webkit-scrollbar-thumb {
  background-color: v-bind('token.colorPrimary');
}

.custom-scrollbar::-webkit-scrollbar-track {
  background-color: transparent;
}


</style>