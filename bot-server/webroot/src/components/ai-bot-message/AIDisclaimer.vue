<template>
    <div class="ai-disclaimer ml-2 mb-2" v-if="visible">
        <span class="disclaimer-text">{{ disclaimerText }}</span>
    </div>
</template>

<script setup lang="ts">
import { theme } from 'ant-design-vue';

const { useToken } = theme;
const { token } = useToken();
// 定义组件属性
const props = defineProps({
    visible: {
        type: Boolean,
        default: true  // 默认显示免责声明
    },
    disclaimerText: {
        type: String,
        default: '本回答由 AI 生成，内容仅供参考，请仔细甄别。'
    }
});
</script>

<style scoped>
.ai-disclaimer {
    width: auto;
    text-align: left;
    position: relative;
    display: flex;
}

.disclaimer-text {
    font-size: 12px;
    color: v-bind('token.colorWarning') !important;
    padding: 3px 10px;
    white-space: nowrap;
    border: none;
    border-left: 2px solid v-bind('token.colorWarning');
    background-color: v-bind('token.colorWarningBg');
}
</style>