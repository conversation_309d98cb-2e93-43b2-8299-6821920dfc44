<template>
  <div class="clear-message-container">
    <div class="clear-message">
      <span class="clear-text">新的对话已开始</span>
    </div>
  </div>
</template>

<script setup lang="ts">
// 简化组件，不需要任何props数据
import {theme} from 'ant-design-vue';

const {useToken} = theme;
const {token} = useToken();
</script>

<style scoped>
.clear-message-container {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 12px 0;
  overflow: visible;
}

.clear-message {
  position: relative;
  text-align: center;
  margin: 0 auto;
  width: auto;
  max-width: 80%;
}

.clear-message::before,
.clear-message::after {
  content: '';
  position: absolute;
  top: 50%;
  height: 1px;
  background-color: v-bind('token.colorBgElevated');
  width: 110px; /* 缩短横线长度 */
  max-width: 16%; /* 限制最大宽度，适应不同屏幕 */
}

.clear-message::before {
  right: 100%;
  margin-right: 15px;
}

.clear-message::after {
  left: 100%;
  margin-left: 15px;
}

.clear-text {
  position: relative;
  display: inline-block;
  padding: 0 12px;
  font-size: 14px;
  color: v-bind('token.colorInfoText');
  background-color: v-bind('token.colorBgElevated');
  border-radius: 12px; /* 添加圆角 */
  z-index: 2;
  line-height: 24px; /* 增加行高，使文本垂直居中 */
  border: 1px solid v-bind('token.colorBgElevated'); /* 添加细边框 */
  user-select: none;
}

/* 深色模式 */
@media (prefers-color-scheme: dark) {
  .clear-message::before,
  .clear-message::after {
    background-color: #444;
  }

  .clear-text {
    color: v-bind('token');
    background-color: v-bind('token.colorBgContainer'); /* 深色模式下的背景色 */
    border-color: #333; /* 深色模式下的边框颜色 */
  }
}
</style>