/* 1. 变量定义 */
:root {
    --md-msg-gpt-bg: rgb(255, 255, 255);
    --md-msg-user-bg: #1890ff;

    --md-msg-title1: 1em;
    --md-msg-title2: 1em;
    --md-msg-title3: 1em;
    --md-msg-title4: 1em;

    --md-msg-font-bold: bold;
    --md-msg-font-normal: 50px;

    --md-msg-a: #3451b2;
    --md-msg-inline-code-bg: rgba(62, 159, 100, 0.98);
    --md-msg-inline-code-bg-dark: #000000;

    --md-msg-block-code-bg: rgba(2, 23, 43, 0.74);
    --md-msg-block-code-border: transparent;
    --md-msg-block-code-color: var(--md-msg-block-code-bg);
    --md-msg-block-code-color-hover: var(--md-msg-block-code-bg);

    --code-block-height: 25px
}

/* 2. 基础布局样式 */
.markdown-message {
    word-wrap: break-word;
    padding: 5px;
    border-radius: 5px;
    color: #24292f;
}


/* 段落布局 */
.markdown-message p {
    min-width: 100% !important;
    word-wrap: break-word;
    overflow-wrap: break-word;
    margin: 8px 0;
    padding: 0;
    line-height: 1.6;
    text-align: left;
    display: block;
    position: relative;
}


.markdown-message p:first-child {
    margin-top: 0;
}

.markdown-message p:last-child {
    margin-bottom: 0;
}

.markdown-message p > code,
.markdown-message p > a {
    display: inline;
    vertical-align: baseline;
}





