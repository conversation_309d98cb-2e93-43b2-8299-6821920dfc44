/* 内联代码块样式 */
.inline-block {
    display: inline;
    font-size: 12px;
    align-content: center;
    border-radius: 4px;
    padding: 1px 3px;
    background-color: var(--md-msg-block-code-bg);
    color: #ffffff;
    transition: color .25s, background-color .5s;
}

/* 代码块主容器 */
.code-block {
    width: 100%;
    display: flex;
    flex-direction: column;
    border-radius: 10px;
    background: var(--md-msg-block-code-bg);
}


.code-block pre {
    margin: 8px 4px;
    border: none;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
    padding: 0;
}

/* 代码工具栏 */
.code-toolbar,
.code-block .code-toolbar {
    display: flex;
    align-items: center;
    padding: 4px 8px;
    background: transparent;
    color: #ffffff;
}

/* 窗口按钮 */
.window-button {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    border: none;
    padding: 0;
    margin-right: 6px;
    cursor: default;
}

.window-button.close {
    background: #ff5f56;
}

.window-button.minimize {
    background: #ffbd2e;
}

.window-button.maximize {
    background: #27c93f;
}


/* 语言标签 */
.language-label {
    font-size: 12px;
    color: white;
    display: flex;
    align-items: center;
    opacity: 0.8;
    margin-left: auto;
    margin-right: 8px;
}

/* 复制按钮 */
.copy-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    padding: 0;
    margin: 0;
    background: none;
    color: white;
    border: none;
    outline: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.copy-button:hover {
    background: rgba(0, 0, 0, 0.04);
}

.copy-button .copy-icon {
    color: #ffffff;
    opacity: 0.6;
    transition: all 0.2s ease;
}

.copy-button:hover .copy-icon {
    opacity: 0.8;
}

.copy-button.copied {
    background: rgba(24, 144, 255, 0.1);
}

.copy-button.copied .copy-icon {
    color: #1890ff;
    opacity: 1;
}

/* 行号容器 */
.code-block .line-numbers,
.code-block .code-content .line-numbers {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 0 12px;
    opacity: 0.6;
    user-select: none;
    position: relative;
}

.code-block .code-content .line-number {
    display: block;
    text-align: right;
    align-content: center;
    color: #ffffff;
    padding-right: 8px;
    min-width: 20px;
    height: var(--code-block-height);
}

.code-block .code-content .line-numbers::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 1px;
    height: 100%;
    background-size: 1px 6px;
    opacity: 0.5;
}

/* 代码行 */
.code-block .code-content .code-line {
    display: block;
    white-space: pre;
    height: var(--code-block-height);
    align-content: center;
    color: #d4d4d4;
}

.code-block .markdown-code-block {
    flex: 1;
    padding: 0 12px;
    margin: 0;
    overflow-x: auto;
    border: none;
    color: #ffffff;
}


/* 代码内容区域 */
.code-block .code-content {
    display: flex;
    background: transparent;
}

.code-block .code-content {
    background: transparent;
}


.code-block .markdown-code-block {
    background: transparent;
    padding: 0 !important;
    border: none;
    border-radius: 0 !important;
    box-shadow: none !important;
}

/* 代码高亮相关 */
:not(pre) > code[class*=language-],
.token.operator,
.token {
    background: transparent;
    text-shadow: none !important;
}

:not(pre) > code[class*=language-] {
    border: none !important;
}

.token {
    background: transparent !important;
}

/* 滚动条样式 */
.markdown-code-block::-webkit-scrollbar,
.code-content::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    background-color: var(--md-msg-block-code-bg);
}

.markdown-code-block::-webkit-scrollbar-thumb,
.code-content::-webkit-scrollbar-thumb {
    background-color: var(--md-msg-block-code-bg);
    border-radius: 4px;
}

.markdown-code-block::-webkit-scrollbar-track,
.code-content::-webkit-scrollbar-track {
    background-color: var(--md-msg-block-code-bg);
    border-radius: 4px;
}

/* Firefox 滚动条 */
.markdown-code-block,
.code-content {
    scrollbar-width: thin;
    scrollbar-color: #4a4a4a var(--md-msg-block-code-bg);
}