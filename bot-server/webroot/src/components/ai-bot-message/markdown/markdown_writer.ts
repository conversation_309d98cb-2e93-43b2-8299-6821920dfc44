import Markdown from "@/components/ai-bot-message/markdown/markdown";


class MarkdownWriter extends Markdown {

    constructor() {
        super();
        // 添加自定义容器插件
        this.setupCustomContainers();
    }

    public setupCustomContainers() {
    }

    public render(text: string): string {
        return this.md.render(text)
    }

    /**
     * 将 Markdown 文本转换为纯文本
     * @param text Markdown 格式的文本
     * @returns 处理后的纯文本
     */
    public toPlainText(text: string): string {
        // 首先将 Markdown 转换为 HTML
        const html = this.md.render(text);

        // 创建一个临时的 div 元素来解析 HTML
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = html;

        // 处理特殊的 Markdown 元素
        // 移除代码块的语言标签
        const preTags = tempDiv.getElementsByTagName('pre');
        for (const pre of Array.from(preTags)) {
            const code = pre.querySelector('code');
            if (code) {
                pre.textContent = code.textContent || '';
            }
        }

        // 处理链接，只保留文本内容
        const links = tempDiv.getElementsByTagName('a');
        for (const link of Array.from(links)) {
            link.replaceWith(link.textContent || '');
        }

        // 处理图片，用图片的 alt 文本替换
        const images = tempDiv.getElementsByTagName('img');
        for (const img of Array.from(images)) {
            img.replaceWith(img.alt || '');
        }

        // 获取纯文本内容
        let plainText = tempDiv.textContent || '';

        // 清理多余的空白字符
        plainText = plainText.replace(/\s+/g, ' ').trim();

        return plainText;
    }
}

export default MarkdownWriter

function commandRender(tokens: any, idx: any) {
    throw new Error("Function not implemented.");
}
