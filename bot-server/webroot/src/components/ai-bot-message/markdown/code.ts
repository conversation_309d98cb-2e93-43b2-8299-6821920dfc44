export function getLineNumbers(codeText) {

    var NEW_LINE_EXP = /\n(?!$)/g;
    let lines = codeText.split(NEW_LINE_EXP);
    const maxLineNumber = 1 + lines.length - 1;
    const digits = String(maxLineNumber).length;

    const lineNumbers = lines.map((_, index) => {
        const lineNumber = 1 + index;
        const paddedLineNumber = lineNumber.toString().padStart(digits, '')
        return `<span class="line-number">${paddedLineNumber}</span>`;
    }).join('');

    return ` <div class="line-numbers">
                <div style="display: flex;flex-direction: column;height: 100%">${lineNumbers}</div>
             </div>`
}


export function getCodeLine(code) {
    let lines = code.split('\n');
    lines = lines.slice(0, lines.length - 1)
    return lines.map(line => {
            return `<div class="code-line">${line}</div>`
        }
    ).join('')
}


export function getCodeToolbar(language: string): string {
    // 定义复制图标
    const copyIcon = `<svg class="copy-icon" viewBox="0 0 24 24" width="12" height="12">
        <path fill="currentColor" d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
    </svg>`;

    return `
    <div class="code-toolbar">
        <button class="window-button close"></button>
        <button class="window-button minimize"></button>
        <button class="window-button maximize"></button>
        <span class="language-label">${language}</span>
        <button class="copy-button" onclick="window.copyCode(this)" title="复制代码">
            ${copyIcon}
        </button>
    </div>
    `;
}