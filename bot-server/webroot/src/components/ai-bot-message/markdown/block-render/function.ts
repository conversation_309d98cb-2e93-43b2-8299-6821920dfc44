/*
* functionRender 数据 拿到一般都是正常的完整数据
* */
import {FuncCallRender} from "@/components/common/model/render";
import {h, render} from "vue";
import FunctionRender from "@/components/ai-bot-message/widget/md-render/FunctionRender.vue";

export function functionRender(tokens, idx) {
    console.log(tokens)
    let value = tokens[idx].info.trim().match(/^function$/);
    if (value) {
        console.log("token:", tokens[idx], idx)
        let token = tokens[idx]
        switch (token.info) {
            case  "function":
                if (token.nesting == 1) {
                    let close = null
                    let end = idx
                    // 找到完整的标签
                    for (let i = idx; i < tokens.length; i++) {
                        let t = tokens[i]
                        if (t.type == "container_function_close") {
                            close = t
                            end = i
                            break
                        }
                    }

                    let value = tokens.slice(idx)
                    let inlineToken = value.find(token => token.type === 'inline');
                    let content = inlineToken.content;
                    console.log("find inlineToken:", inlineToken)
                    // 此处的 清空操作，是为了 不让 md 解析器继续 对自定义容器块内的数据进行渲染，
                    // 会出现非预期效果，把原始json渲染出来
                    for (let i = idx; i <= end; i++) {
                        tokens[i].content = '';
                        tokens[i].hidden = true
                        tokens[i].type = 'skip'
                    }
                    if (content) {
                        try {
                            // 清理内容，去除可能的前后空格和换行符
                            let cleanContent = content.trim();
                            let parse: FuncCallRender = JSON.parse(cleanContent);
                            console.log("functionRender :", parse)

                            // 创建虚拟DOM
                            const vnode = h(FunctionRender, {functionCall: parse});

                            // 创建容器元素
                            const container = document.createElement('div');
                            container.className = 'function-container my-1';

                            // 将虚拟DOM渲染到容器中
                            render(vnode, container);

                            // 返回容器的HTML
                            return container.outerHTML;
                        } catch (error) {
                            console.error("无法解析JSON内容:", error, content);
                            return ``
                        }
                    }
                }
        }
    }
}