import {h, render} from "vue";
import FileBlockMD from "@/components/ai-bot-editor/editor/widget/file-widget/FileBlockMD.vue";
import {ipc<PERSON>enderer} from 'electron';

function generateUID() {
    return 'file_' + Date.now().toString(36) + Math.random().toString(36).substr(2);
}

export function fileRender(tokens, idx) {
    if (tokens[idx].nesting === 1) {
        let inlineToken = tokens.find(token => token.type === 'inline');
        let content = inlineToken.content;
        tokens.forEach((token) => {
            if (token.type != 'div') {
                token.hidden = true
                token.type = 'skip'
                token.content = ''
            }
        })
        console.log(content)
        // 解析 JSON 内容
        const fileInfo = JSON.parse(content);
        const uid = generateUID();

        // 创建容器元素
        const container = document.createElement('div');

        // 创建 FileBlockMD 组件
        const vnode = h(FileBlockMD, {
            uid: uid,
            name: fileInfo.name,
            path: fileInfo.path
        });

        // 渲染组件到容器
        render(vnode, container);

        // 异步获取图标
        getIcon(fileInfo.path).then(iconDataUrl => {
            if (iconDataUrl) {
                const fileBlock = document.querySelector(`[data-file-uid="${uid}"]`);
                if (fileBlock) {
                    const iconElement = fileBlock.querySelector('.file-icon') as HTMLElement;
                    if (iconElement) {
                        iconElement.style.backgroundImage = `url(${iconDataUrl})`;
                    }
                }
            }
        }).catch(error => {
            console.error('获取文件图标失败:', error);
        });

        return container.outerHTML;
    } else {
        return ``
    }
}

async function getIcon(path) {
    if (!path) return null;
    try {
        return await ipcRenderer.invoke('get-file-icon', path);
    } catch (error) {
        console.error('获取系统图标失败:', error);
        return null;
    }
}