import { McpMarkdown } from "@/components/ai-bot-editor/editor/editor-plugins/CustomDataProcessor/augmentation";
import { h, render } from "vue";
import McpInstanceRender from "../../widget/md-render/McpInstanceRender.vue";

export function mcpInstanceRender(tokens, idx) {
    // console.log(tokens)
    let value = tokens[idx].info.trim().match(/^mcp$/);
    if (value) {
        console.log("token:", tokens[idx], idx)
        let token = tokens[idx]
        switch (token.info) {
            case  "mcpinstance":
                let content = ''
                if (token.nesting == 1) {
                    // 找到完整的标签
                    for (let i = idx; i < tokens.length; i++) {
                        if (tokens[i].type == 'inline') {
                            console.log("inline:", tokens[i].content)
                            content += tokens[i].content
                            tokens[i].content = '';
                            tokens[i].hidden = true
                            tokens[i].type = 'skip'
                        }

                        if (tokens[i].type == "container_mcp_close") {
                            break
                        }
                    }
                    if (content) {
                        try {
                            // console.log(content)
                            let parse: McpMarkdown = JSON.parse(content);

                            console.log("mcpRender :", parse)
                            // 生成随机ID
                            const containerId = `function-container-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

                            // 创建虚拟DOM
                            const vnode = h(McpInstanceRender, {mcp: parse});

                            // 创建容器元素
                            const container = document.createElement('div');
                            container.className = 'function-container my-1';

                            // 将虚拟DOM渲染到容器中
                            render(vnode, container);

                            // 返回容器的HTML
                            return container.outerHTML;
                        } catch (error) {
                            // console.log('err: ', content)
                            console.error("无法解析JSON内容:", error, content);
                            return `<span></span>`
                        }
                    }
                }
        }
    }
    return ``
}