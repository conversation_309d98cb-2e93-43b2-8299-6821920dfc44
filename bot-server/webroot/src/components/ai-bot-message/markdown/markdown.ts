import markdownit from "markdown-it";
import { escapeHtml } from "markdown-it/lib/common/utils.mjs";
import { getCodeLine, getCodeToolbar, getLineNumbers } from "@/components/ai-bot-message/markdown/code";
import Prism from 'prismjs'
import { McpMarkdown, MentionMarkdown } from "@/components/ai-bot-editor/editor/editor-plugins/CustomDataProcessor/augmentation";
import { DataType } from "@/components/common/model/enum";
import { full as emoji } from 'markdown-it-emoji'
import { createVNode, h, render } from "vue";
import ThinkingRender from "@/components/ai-bot-message/widget/md-render/ThinkingRender.vue";
import { thinkingRender } from "@/components/ai-bot-message/markdown/block-render/thinking";
import { functionRender } from "@/components/ai-bot-message/markdown/block-render/function";
import { mcpRender } from "@/components/ai-bot-message/markdown/block-render/mcp";
import McpInstanceRender from "@/components/ai-bot-message/widget/md-render/McpInstanceRender.vue";

import loadLanguages from 'prismjs/components/';
import markdownItAbbr from 'markdown-it-abbr'; // 缩写
import markdownItDeflist from 'markdown-it-deflist'; // 定义列表
import markdownItIns from 'markdown-it-ins'; // 插入文本
import markdownItMark from 'markdown-it-mark'; // 标记
import markdownItSub from 'markdown-it-sub'; // 下标
import markdownItSup from 'markdown-it-sup'; // 上标
import markdownItFootnote from 'markdown-it-footnote'; // 脚注
import markdownItAnchor from 'markdown-it-anchor';
import markdownItKatex from 'markdown-it-katex';
import markdownItContainer from 'markdown-it-container';
import cjk_breaks from 'markdown-it-cjk-breaks';
import iterator from 'markdown-it-for-inline';
// loadLanguages([
//     'python',
//     'go',
//     'java',
//     'css',
//     'javascript',
//     'ruby',
//     'bash',
//     'sql',
//     'yaml',
//     'cpp',
//     'csharp',
//     'php',
//     'json',
//     'xml',
//     'plain',
//     'html',
//     'sh'
// ])


class Markdown {
    public md: markdownit

    constructor() {
        this.md = markdownit({
            html: true,
            linkify: true,  // 启用链接识别
            typographer: true, // 美化引号、破折号等
            highlight: this.highlight.bind(this)
        })
        // 确保在构造函数中初始化样式
        this.initializeStyles();
        this.initCustomContainer();
    }

    private initializeStyles() {
        if (this.md) {
            // 添加插件
            this.md.use(markdownItAbbr)
            this.md.use(markdownItDeflist)
            this.md.use(markdownItIns)
            this.md.use(markdownItMark)
            this.md.use(markdownItSub)
            this.md.use(markdownItSup)
            this.md.use(markdownItFootnote)
            this.md.use(markdownItAnchor)
            this.md.use(emoji)
            this.md.use(cjk_breaks)

            // 配置 外部打开 连接
            this.md.use(iterator, 'url_new_win', 'link_open', function (tokens, idx) {
                tokens[idx].attrPush(['target', '_blank']);
            });
            // 配置链接识别选项
            // this.md.linkify.set({fuzzyLink: false});

            // 直接在构造函数中设置链接处理规则
            this.md.renderer.rules.link_open = (tokens, idx, options, env, self) => {
                const token = tokens[idx];
                const hrefIndex = token.attrIndex('href');
                let href = '';
                if (hrefIndex >= 0) {
                    href = token.attrs[hrefIndex][1];
                }
                return `<a class="md-link" data-href="${href}" ${self.renderAttrs(token)}>`;
            };

            // 添加处理'skip'类型token的规则
            this.md.renderer.rules.skip = function (tokens, idx) {
                return ``;
            };

            // 处理内联代码
            this.md.renderer.rules.code_inline = (tokens, idx, options, env, slf) => {
                const token = tokens[idx]
                return '<code class="inline-block"' + slf.renderAttrs(token) + '>' + escapeHtml(token.content) + '</code>'
            }
        }
    }

    private initCustomContainer() {
        this.md.use(markdownItContainer, 'thinking', {
            validate: function (params) {
                return params.trim().match(/^thinking$/);
            },
            render: thinkingRender
        })
        this.md.use(markdownItContainer, 'function', {
            validate: function (params) {
                return params.trim().match(/^function$/);
            },
            render: functionRender
        })

        this.md.use(markdownItContainer, 'mcpinstance', {
            validate: function (params) {
                return params.trim().match(/^mcpinstance$/);
            },
            render: this.mcpInstanceRender
        })

        this.md.use(markdownItContainer, 'mcp', {
            validate: function (params) {
                return params.trim().match(/^mcp$/);
            },
            render: mcpRender
        })

        this.md.use(markdownItContainer, 'mention', {
            validate: function (params) {

                return params.trim().match(/^mention$/);
            },
            render: this.mentionRender
        })


        // 添加跳过隐藏token的规则
        this.md.renderer.rules.text = (tokens, idx, options, env, self) => {
            const token = tokens[idx];
            if (token.hidden) {
                return '';
            }
            return token.content;
        };
    }

    public render(text: string): string {
        return this.md.render(text);
    }

    public highlight(str, lang): string {
        let lineNumbers = getLineNumbers(str);
        let toolbar = getCodeToolbar(lang);
        let langClass = 'language-' + lang;
        let codeLine = ''
        if (lang && Prism.languages[lang]) {
            let code = Prism.highlight(str, Prism.languages[lang], lang)
            codeLine = getCodeLine(code);
        } else {
            codeLine = getCodeLine(escapeHtml(str));
        }
        return `<pre class="code-block">
                        ${toolbar}
                        <div class="code-content">
                            ${lineNumbers}
                            <code class="${langClass} markdown-code-block">
                                ${codeLine}
                            </code>
                        </div>
                    </pre>`;
    }


    public mentionRender(tokens, idx) {
        console.log(tokens)
        var mention = tokens[idx].info.trim().match(/^mention$/);
        if (mention) {
            let token = tokens[idx]
            let start = token.map[0]
            let end = token.map[1]
            let mentions = tokens.slice(start, end + 1)
            let inlineToken = mentions.find(token => token.type === 'inline');
            let content = inlineToken.content;
            // 此处的 清空操作，是为了 不让 md 解析器继续 对自定义容器块内的数据进行渲染，
            // 会出现非预期效果，把原始json渲染出来
            for (let i = start; i <= end; i++) {
                tokens[i].content = '';
                tokens[i].hidden = true
                tokens[i].type = 'skip'
            }
            console.log("mentions:", content)
            if (content) {
                try {
                    // 清理内容，去除可能的前后空格和换行符
                    let cleanContent = content.trim();
                    let parse: MentionMarkdown = JSON.parse(cleanContent);
                    console.log("mentionRender :", parse)
                    switch (parse.dataType) {
                        case DataType.Image:
                            return `<img src='${parse.resource}' alt='${parse.title}'/ >`
                        // todo 其他自定义 数据解析
                        default:
                            return `<span class="mention">${content}</span>`
                    }
                } catch (error) {
                    console.error("无法解析JSON内容:", error, content);
                    return `<span class="mention error">无法解析内容: ${content}</span>`
                }
            }
        }
    }

    public mcpInstanceRender(tokens, idx) {
        console.log(tokens)
        let value = tokens[idx].info.trim().match(/^mcpinstance$/);
        if (value) {
            console.log("token:", tokens[idx], idx)
            let token = tokens[idx]
            switch (token.info) {
                case "mcpinstance":
                    let content = ''
                    if (token.nesting == 1) {
                        // 找到完整的标签
                        for (let i = idx; i < tokens.length; i++) {
                            if (tokens[i].type == 'inline') {
                                console.log("inline:", tokens[i].content)
                                content += tokens[i].content
                                tokens[i].content = '';
                                tokens[i].hidden = true
                                tokens[i].type = 'skip'
                            }
                            if (tokens[i].type == "container_mcpinstance_close") {
                                break
                            }
                        }
                        if (content) {
                            try {
                                console.log("content:", content)
                                let parse: McpMarkdown = JSON.parse(content);

                                console.log("mcpInstanceRender :", parse)
                                // 生成随机ID
                                const containerId = `function-container-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

                                // 创建虚拟DOM
                                const vnode = h(McpInstanceRender, { mcp: parse });

                                // 创建容器元素
                                const container = document.createElement('div');
                                container.className = 'mcpinstance-container';

                                // 将虚拟DOM渲染到容器中
                                render(vnode, container);

                                // 返回容器的HTML
                                return container.outerHTML;
                            } catch (error) {
                                // console.log('err: ', content)
                                console.error("无法解析JSON内容:", error, content);
                                return `<span></span>`
                            }
                        }
                    }
            }
        }
        return ``
    }
}

export default Markdown;