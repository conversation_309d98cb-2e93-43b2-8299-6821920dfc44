<template>
  <SceneBase :message="message" :index="index">
    <AiUserMarkDownRender :message="message" :index="index"/>
  </SceneBase>
</template>


<script setup lang="ts">
import {MessageItem} from "@/components/common/model/chat";
import AiUserMarkDownRender from "@/components/ai-bot-message/scene/md-ui/AiUserMarkDownRender.vue";
import SceneBase from "@/components/ai-bot-message/scene/SceneBase.vue";

defineProps<{
  // 消息
  message: MessageItem,
  index: number
}>()


</script>

<style scoped>

</style>