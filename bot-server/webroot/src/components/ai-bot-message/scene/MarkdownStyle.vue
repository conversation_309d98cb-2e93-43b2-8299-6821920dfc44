<template>
  <slot></slot>
</template>
<script setup lang="ts">
import {theme} from "ant-design-vue";
import {onMounted} from "vue";

const {useToken} = theme;
const {token} = useToken();

// 自动为表格添加容器处理函数
const wrapTablesWithContainer = () => {
  const markdownMessages = document.querySelectorAll('.markdown-message');
  markdownMessages.forEach(message => {
    const tables = message.querySelectorAll('table');
    tables.forEach(table => {
      // 检查表格是否已经在容器中
      if (table.parentElement && !table.parentElement.classList.contains('table-container')) {
        // 创建容器并包裹表格
        const container = document.createElement('div');
        container.className = 'table-container';
        table.parentNode!.insertBefore(container, table);
        container.appendChild(table);
      }
    });
  });
};

// 组件挂载后处理表格
onMounted(() => {
  wrapTablesWithContainer();
  
  // 监听内容变化，为新增的表格添加容器
  const observer = new MutationObserver((mutations) => {
    mutations.forEach(mutation => {
      if (mutation.addedNodes.length) {
        wrapTablesWithContainer();
      }
    });
  });
  
  // 开始监听 DOM 变化
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
});
</script>


<style>
/* 1. 变量定义 */
:root {
  --md-msg-block-code-bg: v-bind('token.colorBgElevated');
  --md-msg-block-code-border: v-bind('token.colorBorder');
  --md-msg-block-code-color: var(--md-msg-block-code-bg);
  --md-msg-block-code-color-hover: var(--md-msg-block-code-bg);
  --code-block-height: 25px
}

/* 2. 基础布局样式 */
.markdown-message {
  word-wrap: break-word;
  padding: 5px;
  border-radius: 5px;
}

.markdown-message strong {
  font-weight: 600;
}

.markdown-message h1, h2, h3, h4 {
  margin: 48px 0 16px;
  padding-top: 10px;
  padding-bottom: 10px;
  letter-spacing: -.02em;
  font-weight: 600;
  outline: none;
}

.markdown-message h1 {
  line-height: 40px;
  font-size: 32px;
}

.markdown-message h2 {
  line-height: 32px;
  font-size: 24px;
}

.markdown-message h3 {
  line-height: 24px;
  font-size: 16px;
}

.markdown-message h4 {
  letter-spacing: -.02em;
  line-height: 16px;
}

.markdown-message a {
  color: v-bind('token.colorLink') !important;
  text-decoration: none;
  transition: color 0.3s, text-decoration 0.3s;
  position: relative;
}

.markdown-message a:hover {
  color: v-bind('token.colorLinkHover') !important;
  text-decoration: underline;
  text-underline-offset: 2px;
}

.markdown-message a:active {
  color: v-bind('token.colorLinkActive') !important;
}

.markdown-message a::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background-color: transparent;
  transition: background-color 0.3s;
}

.markdown-message a:focus {
  outline: 1px solid v-bind('token.colorPrimaryBorder');
  outline-offset: 2px;
}

/* 段落布局 */
.markdown-message p {
  display: block;
  margin-block-start: 1em;
  margin-block-end: 1em;
  margin-inline-start: 0px;
  margin-inline-end: 0px;
  unicode-bidi: isolate;
}

.markdown-message ol, ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.markdown-message li {
  display: list-item;
  text-align: -webkit-match-parent;
  unicode-bidi: isolate;
}

.markdown-message hr {
  margin-top: 10px;
  margin-bottom: 10px;
  background-color: v-bind('token.colorSplit');
  border-color: v-bind('token.colorSplit');
}


.markdown-message p:first-child {
  margin-top: 0;
}

.markdown-message p:last-child {
  margin-bottom: 0;
}

.markdown-message p > code,
.markdown-message p > a {
  display: inline;
  vertical-align: baseline;
}

.markdown-message table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  margin: 0;
  border: none;
  display: table;
}

/* 斑马纹样式 */
.markdown-message tr:nth-child(even) {
  background-color: v-bind('token.colorFillQuaternary');
}

.markdown-message thead tr {
  background-color: v-bind('token.colorFillAlter');
}

.markdown-message th {
  color: v-bind('token.colorTextHeading');
  font-weight: 500;
  text-align: left;
  padding: 12px 16px;
  border-bottom: 1px solid v-bind('token.colorBorderSecondary');
  transition: background 0.3s ease;
}

.markdown-message td {
  padding: 12px 16px;
  border-bottom: 1px solid v-bind('token.colorBorderSecondary');
  transition: background 0.3s ease;
}

.markdown-message tr:hover > td {
  background-color: v-bind('token.colorBgTextHover');
}

.markdown-message tr:last-child > td {
  border-bottom: none;
}

/* 响应式表格样式 */
@media screen and (max-width: 768px) {
  .markdown-message table {
    font-size: 12px;
  }
  
  .markdown-message th,
  .markdown-message td {
    padding: 8px 12px;
  }
}

/* 表格容器，解决横向滚动问题 */
.markdown-message .table-container {
  width: 100%;
  overflow-x: auto;
  margin: 16px 0;
  border: 1px solid v-bind('token.colorBorderSecondary');
  border-radius: v-bind('token.borderRadius');
}

.markdown-message table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  margin: 0;
  border: none;
  display: table;
}

/* 表格滚动条样式 */
.markdown-message .table-container::-webkit-scrollbar {
  height: 8px;
}

.markdown-message .table-container::-webkit-scrollbar-thumb {
  background-color: v-bind('token.colorTextQuaternary');
  border-radius: 4px;
}

.markdown-message .table-container::-webkit-scrollbar-track {
  background-color: v-bind('token.colorFillQuaternary');
  border-radius: 4px;
}

</style>

<!--
 定义代码块颜色适配，全局统一定义
-->
<style>
/* 内联代码块样式 */
.inline-block {
  display: inline;
  font-size: 12px;
  align-content: center;
  border-radius: 4px;
  padding: 1px 3px;
  background-color: v-bind('token.colorInfoBg');
  transition: color .25s, background-color .5s;
}

/* 代码块主容器 */
.code-block {
  width: 100%;
  display: flex;
  flex-direction: column;
  margin-bottom: 10px;
  border-radius: 10px;
  border-style: solid;
  border-width: 1px;
  border-color: v-bind('token.colorBorder');
  background: v-bind('token.colorBgElevated');
}


.code-block pre {
  margin: 8px 4px;
  border: none;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
  padding: 0;
}

/* 代码工具栏 */
.code-block .code-toolbar {
  position: sticky !important;
  top:0;
  display: flex;
  align-items: center;
  padding: 4px 8px;
  background: transparent;
}

/* 窗口按钮 */
.window-button {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  border: none;
  padding: 0;
  margin-right: 6px;
  cursor: default;
}

.window-button.close {
  background: #ff5f56;
}

.window-button.minimize {
  background: #ffbd2e;
}

.window-button.maximize {
  background: #27c93f;
}


/* 语言标签 */
.language-label {
  font-size: 12px;
  color: v-bind('token.colorTextBase');
  display: flex;
  align-items: center;
  opacity: 0.8;
  margin-left: auto;
  margin-right: 8px;
}

/* 复制按钮 */
.copy-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  padding: 0;
  margin: 0;
  background: none;
  color: v-bind('token.colorInfoBg');
  border: none;
  border-radius: 5px;
  outline: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.copy-button:hover {
  background: v-bind('token.colorInfoBgHover');
}

.copy-button .copy-icon {
  color: v-bind('token.colorInfoText');
  opacity: 0.6;
  transition: all 0.2s ease;
}

.copy-button:hover .copy-icon {
  opacity: 0.8;
}

.copy-button.copied .copy-icon {
  color: v-bind('token.colorInfoTextActive');
  opacity: 1;
}

/* 行号容器 */
.code-block .line-numbers,
.code-block .code-content .line-numbers {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 0 5px;
  opacity: 0.6;
  user-select: none;
  position: relative;
  border-right: 1px solid v-bind('token.colorBorder');
}

.code-block .code-content .line-number {
  display: block;
  text-align: left;
  align-content: center;
  color: v-bind('token.colorTextSecondary');
  padding-left: 2px;
  min-width: 20px;
  height: var(--code-block-height);
}

.code-block .code-content .line-numbers::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 1px;
  height: 100%;
  background-size: 1px 6px;
  opacity: 0.5;
}

/* 代码行 */
.code-block .code-content .code-line {
  display: block;
  white-space: pre;
  height: var(--code-block-height);
  align-content: center;
}

.code-block .markdown-code-block {
  flex: 1;
  padding: 0 12px;
  margin: 0;
  overflow-x: auto;
  border: none;
}


/* 代码内容区域 */
.code-block .code-content {
  display: flex;
  background: transparent;
}

.code-block .code-content {
  background: transparent;
}


.code-block .markdown-code-block {
  background: transparent;
  border-top: solid 1px v-bind('token.colorBorder');
  border-radius: 0 !important;
  box-shadow: none !important;
}

/* 代码高亮相关 */
/*:not(pre)>code[class*=language-],
.token.operator,
.token {
  background: transparent;
  text-shadow: none !important;
}

:not(pre)>code[class*=language-] {
  border: none !important;
}*/

/*.token {
  background: transparent !important;
}*/

/* 滚动条样式 */
.markdown-code-block::-webkit-scrollbar,
.code-content::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  background-color: v-bind('token.colorFillQuaternary');
}

.markdown-code-block::-webkit-scrollbar-thumb,
.code-content::-webkit-scrollbar-thumb {
  background-color: v-bind('token.colorTextQuaternary');
  border-radius: 4px;
  transition: background-color 0.3s;
}

.markdown-code-block::-webkit-scrollbar-thumb:hover,
.code-content::-webkit-scrollbar-thumb:hover {
  background-color: v-bind('token.colorTextTertiary');
}

.markdown-code-block::-webkit-scrollbar-track,
.code-content::-webkit-scrollbar-track {
  background-color: v-bind('token.colorFillQuaternary');
  border-radius: 4px;
}

/* Firefox 滚动条 */
.markdown-code-block,
.code-content {
  scrollbar-width: thin;
  scrollbar-color: v-bind('token.colorTextQuaternary') v-bind('token.colorFillQuaternary');
}

/* md消息 选中样式 */
.markdown-message ::selection,
.markdown-message ::selection,
.markdown-message ::selection {
  color: v-bind('token.colorTextLightSolid');
  background-color: v-bind('token.colorPrimary');
}

.markdown-message ::-moz-selection,
.markdown-message ::-moz-selection,
.markdown-message ::-moz-selection {
  color: v-bind('token.colorTextLightSolid');
  background-color: v-bind('token.colorPrimary');
}
</style>


<!-- 函数调 md渲染用样式 -->
<style>
.function-call-container {
  padding: 4px;
  border-radius: 4px;
  border: 1px solid v-bind('token.colorBorder');
  background-color: v-bind('token.colorBgContainer');
  margin: 4px 0;
}

.function-header {
  font-weight: bold;
  color: v-bind('token.colorPrimary');
  cursor: pointer;
  display: flex;
  align-items: center;
  padding: 2px 0;
}

.toggle-icon {
  font-size: 12px;
  margin-right: 4px;
}

.function-value {
  /* margin-top: 8px; */
  padding: 8px;
  background-color: v-bind('token.colorBgContainer');
  /* border-radius: 4px; */
  /* white-space: pre-wrap; */
  word-break: break-all;
  font-family: monospace;

  max-height: 0;
  overflow: hidden;
  opacity: 0;
  transition: max-height 0.3s ease-in-out, opacity 0.3s ease-in-out, padding 0.3s ease-in-out;
  padding-top: 0;
  padding-bottom: 0;
}

.function-value.expanded {
  max-height: 1000px; /* 足够大的值以容纳内容 */
  opacity: 1;
  padding: 8px;
}
</style>
