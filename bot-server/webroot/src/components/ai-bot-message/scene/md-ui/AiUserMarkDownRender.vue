<template>
  <div class="markdown-message user-message size-full flex" v-html="render">
  </div>
</template>

<script setup lang="ts">
import {computed} from "vue";
import {MessageItem} from "@/components/common/model/chat";
import {sceneRenderMap} from "@/components/ai-bot-message/scene/scene";
import {theme} from "ant-design-vue";

const {useToken} = theme;
const {token} = useToken();
const props = defineProps<{
  // 消息
  message: MessageItem,
  index: number
}>()

const render = computed(() => {
  let markdown = sceneRenderMap.get(props.message.messageType);
  return markdown.render(props.message.content)
})
</script>


<style scoped>
.user-message *{
  color: rgb(255, 255, 255) !important;
}
.user-message {
  padding: 6px;
  border-radius: 5px;
  height: 100%;
  background-color: v-bind('token.colorPrimary');
  color: rgb(255, 255, 255);
}

:deep(a) {
  color: rgb(255, 255, 255) !important;
  text-decoration: underline;
  text-underline-offset: 2px;
  transition: all 0.3s;
}

:deep(a:hover) {
  color: rgb(0, 0, 0) !important;
  text-decoration: underline;
  text-shadow: none;
}

:deep(a:active) {
  color: rgba(0, 0, 0, 0.8) !important;
}
:deep(p) {
  display: inline-block !important;
  margin: 0 !important;
  padding: 0 !important;
}

</style>