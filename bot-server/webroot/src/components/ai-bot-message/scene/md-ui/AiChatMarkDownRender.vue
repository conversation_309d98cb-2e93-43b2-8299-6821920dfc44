<template>
  <div class="markdown-message gpt-message" v-html="render">
  </div>
</template>

<script setup lang="ts">
import {computed} from "vue";
import {MessageItem} from "@/components/common/model/chat";
import {sceneRenderMap} from "@/components/ai-bot-message/scene/scene";

const props = defineProps<{
  // 消息
  message: MessageItem,
  index: number
}>()


const render = computed(() => {
  let markdown = sceneRenderMap.get(props.message.messageType);
  return markdown.render(props.message.content)
})

</script>


<style scoped>

.gpt-message {
  min-width: 100% !important;
}
</style>