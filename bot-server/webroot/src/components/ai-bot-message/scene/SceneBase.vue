<template>
  <MarkdownStyle>
    <div class="h-full flex flex-col scene-render" :class="message.role">
      <slot></slot>
      <AIDisclaimer v-if="message.role == RoleType.Assistant"/>
    </div>
  </MarkdownStyle>
</template>
<script setup lang="ts">
import {theme} from "ant-design-vue";
import {MessageItem, RoleType} from "@/components/common/model/chat";
import AIDisclaimer from "@/components/ai-bot-message/AIDisclaimer.vue";
import MarkdownStyle from "@/components/ai-bot-message/scene/MarkdownStyle.vue";

const {useToken} = theme;
const {token} = useToken();

defineProps<{
  // 消息
  message: MessageItem,
  index: number
}>()
</script>

<style scoped>
.scene-render.assistant {
  width: 100%;
}
</style>

