<template>
    <AgentSceneBase :message="message" :index="index">
        <AgentChatRender :message="message" :index="index"/>
    </AgentSceneBase>
</template>

<script setup lang="ts">
import {MessageItem} from "@/components/common/model/chat";
import AgentSceneBase from "@/components/ai-bot-message/scene/agent/AgentSceneBase.vue";
import AgentChatRender from "@/components/ai-bot-message/scene/agent/AgentChatRender.vue";
defineProps<{
    message: MessageItem,
    index: number
}>()

</script>

<style scoped>

</style>