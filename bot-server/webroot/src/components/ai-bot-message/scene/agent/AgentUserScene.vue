<template>
    <AgentSceneBase :message="message" :index="index">
        <AiUserMarkDownRender :message="message" :index="index"/>
    </AgentSceneBase>
</template>

<script setup lang="ts">
import {MessageItem} from "@/components/common/model/chat";
import AgentSceneBase from "@/components/ai-bot-message/scene/agent/AgentSceneBase.vue";
import AiUserMarkDownRender from "@/components/ai-bot-message/scene/md-ui/AiUserMarkDownRender.vue";
defineProps<{
    message: MessageItem,
    index: number
}>()

</script>

<style scoped>

</style>