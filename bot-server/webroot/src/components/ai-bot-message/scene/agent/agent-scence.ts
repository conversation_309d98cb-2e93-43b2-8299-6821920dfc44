import {MessageScene} from "@/components/common/model/chat";
import Markdown from "@/components/ai-bot-message/markdown/markdown";

export const sceneMap: Map<string, string> = new Map([
    [MessageScene.RenderScene, "AgentRenderScene"],
    [MessageScene.UserScene, "AgentUserScene"],
    [MessageScene.AgentScene, "AgentChatScene"],
]);

const defaultRender = new Markdown();

export const sceneRenderMap: Map<string, Markdown> = new Map([
    [MessageScene.UserScene, defaultRender],
    [MessageScene.ChatScene, defaultRender],
    [MessageScene.AgentScene, defaultRender],
])