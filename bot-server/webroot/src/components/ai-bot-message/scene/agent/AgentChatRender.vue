<template>
    <AgentRender :message="message" :index="index" />
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import { MessageItem } from "@/components/common/model/chat";
import { sceneRenderMap } from "@/components/ai-bot-message/scene/agent/agent-scence";
import { AgentItem, AgentItemType } from "@/components/common/model/agent";
import AgentRender from "@/components/ai-bot-message/widget/agent/AgentRender.vue";
const props = defineProps<{
    // 消息
    message: MessageItem,
    index: number
}>()
let markdown = sceneRenderMap.get(props.message.messageType);

const agent = ref<AgentItem[]>([])

// 解析消息
if (props.message.content) {
    agent.value = JSON.parse(props.message.content) as AgentItem[]
}

function getAgentMdRender(item: AgentItem) {
    if (item.type == AgentItemType.Markdown || item.type == AgentItemType.Thinking) {
        return markdown.render(item.data)
    }
    return ''
}

</script>


<style scoped>
.gpt-message {
    min-width: 100% !important;
}
</style>