<template>
  <template v-if="!isError && message.content !== ''">
    <slot></slot>
  </template>
  <template v-else-if="!isError">
    <AsyncTyping :message="message" :index="index"/>
  </template>
  <template v-else>
    <ErrMessage :message="message" :index="index"/>
  </template>
</template>


<script setup lang="ts">
import {computed, ref, watch} from "vue";
import {MessageItem, RoleType} from "@/components/common/model/chat";
import {LLmStatus} from "@/components/common/stream/llm_stream";
import ErrMessage from "@/components/ai-bot-message/widget/ErrMessage.vue";
import AsyncTyping from "@/components/ai-bot-message/widget/typing/AsyncTyping.vue";
import {theme} from 'ant-design-vue';
import {useAiBot} from "@/components/store/bot";

const {useToken} = theme;
const {token} = useToken();
let bot = useAiBot();
const props = defineProps<{
  // 消息
  message: MessageItem,
  index: number
}>()

// 渲染消息文本存储
const info = ref({...props.message!})
// 标识当前消息是否是用户发送的
const send = ref(false)
// 获取消息发送标识
send.value = props.message.role === RoleType.User
info.value.ex = JSON.parse(info.value.ex)
const isError = computed(() => {
  if (props.message.ex) {
    let data = JSON.parse(props.message.ex);
    if (data.debugINFO) {
      return data.debugINFO.status === LLmStatus.Error
    }
  }
  return false
})


watch(() => props.message, (value) => {
  if (value.content !== "") {
    info.value = {...value}
  }
})
</script>


