<template>
  <BaseMessage :message="message" :index="index">
    <component :is="scene" :key="message.id" :message="message" :index="index"/>
  </BaseMessage>
</template>

<script setup lang="ts">

import BaseMessage from "@/components/ai-bot-message/scene/BaseMessage.vue";
import {MessageItem} from "@/components/common/model/chat";
import {computed, ref} from "vue";
import {sceneMap} from "@/components/ai-bot-message/scene/scene";

const props = defineProps<
    {
      message: MessageItem,
      index: number
    }
>()
// 渲染消息文本存储
const info = ref({...props.message!})
const scene = computed(() => {
  console.log(sceneMap.get(info.value.messageType))
  return sceneMap.get(info.value.messageType)
})
</script>


<style scoped>

</style>