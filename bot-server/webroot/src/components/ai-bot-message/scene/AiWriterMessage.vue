<template>
  <BaseMessage :message="message" :index="index">

  </BaseMessage>
</template>


<script setup lang="ts">
import {MessageItem} from "@/components/common/model/chat";
import Markdown from "@/components/ai-bot-message/markdown/markdown";
import BaseMessage from "@/components/ai-bot-message/scene/BaseMessage.vue";

defineProps<{
  // 消息
  message: MessageItem,
  index: number
}>()

</script>

