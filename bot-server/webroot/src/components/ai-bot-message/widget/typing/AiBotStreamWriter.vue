<template>
  <AsyncTyping :message="message" :index="index" @begin-typing="handleBeginTyping"/>
</template>

<script setup lang="ts">
import {MessageItem} from "@/components/common/model/chat";
import {onMounted, ref} from "vue";
import AsyncTyping from "@/components/ai-bot-message/widget/typing/AsyncTyping.vue";
import emitter from "@/plugins/event";
import {ScrollToBottom} from "@/plugins/evenKey";

const props = defineProps<{
  // 消息
  message: MessageItem,
  index: number
}>()

const emit = defineEmits(['begin-typing'])

const isTyping = ref(false)

function handleBeginTyping() {
  isTyping.value = true
  emit('begin-typing')
}

onMounted(() => {
  emitter.emit(ScrollToBottom)
})

</script>


<style scoped>

</style>