<template>
  <div ref="typingBox" class="w-full">
    <MarkdownStyle>
      <div ref="typing"
           class="markdown-message typing w-full render"
           :class="message.role"
           v-if="info.content!=''"
           v-html="info.content"
      >
      </div>
    </MarkdownStyle>
    <div class="w-full">
      <MessageLoading/>
    </div>

    <!--  打字光标 暂时注释    -->
    <!--      <div ref="cursor" v-show="showCursor"
               class="typing-cursor">
          </div>-->
    <!--      <typing-loading/>-->
  </div>
</template>

<script setup lang="ts">
import {onBeforeMount, onMounted, reactive, ref, watch} from "vue";
import {useGptStore} from "@/components/store/gpt";
import emitter from "@/plugins/event";
import {ScrollToBottom} from "@/plugins/evenKey";
import {MessageItem, MessageScene} from "@/components/common/model/chat";
import {DataType, LLMData, LLMStream} from "@/components/common/stream/llm_stream";
import {getMessage} from "@/components/common/manageRequest";
import MessageLoading from "@/components/ai-bot-message/widget/MessageLoading.vue";
import {sceneRenderMap} from "@/components/ai-bot-message/scene/scene";
import MarkdownStyle from "@/components/ai-bot-message/scene/MarkdownStyle.vue";

const props = defineProps<{
  // 消息
  message: MessageItem,
  index: number
}>()

const scene = ref<MessageScene>()
const isLoading = ref(true)
const typingBox = ref<HTMLElement>()
const typing = ref<HTMLElement>()
const cursor = ref<HTMLElement>()
// 是否显示打字光标
const showCursor = ref(false)
// 打字机 光标显示位置
const pos = reactive({
  x: 0,
  y: 0,
})
const ctx = useGptStore()
// 渲染消息文本存储
const info = ref({...props.message!})

// 存储流消息拼接文本
const content = ref('')
const emit = defineEmits(['begin-typing'])

function beginTyping() {
  llmStream()
}


async function llmStream() {
  if (info.value.llmStream) {
    let markdown = null
    let llmStream = info.value.llmStream
    ctx.ui.currentLLMStream = llmStream
    // Begin 数据中携带了本次消息的 渲染场景 scene 需要在此处初始化 渲染器
    llmStream.setBegin((data: LLMData) => {
      info.value.content = ''  // 确保内容为空字符串而不是 undefined
      if (data.type == DataType.Render) {
        // 加载 渲染器
        markdown = sceneRenderMap.get(data.scene);
        console.log(markdown)
        if (markdown == null) {
          console.error('sceneRenderMap loading error', data)
          setTimeout(async () => {
            await end()
          }, 200)
        }
      }
    })
    llmStream.setData((data: LLMData) => {
      if (data != null) {
        content.value += data.content
        // 渲染 md 消息进行展示
        info.value.content = markdown.render(content.value);
        // 发送滚动条滚动指令
        emitter.emit(ScrollToBottom)
        // 将消息传递给语音管理器
      }
    })
    llmStream.setError(async (data: LLMData) => {
      setTimeout(async () => {
        await end()
      }, 200)
    })
    llmStream.setFinish((data: LLMData) => {
      setTimeout(async () => {
        await end()
      }, 200)
    })
    llmStream.setCancel((data: LLMData) => {
      setTimeout(async () => {
        // 结束语音播报
        await end()
      }, 500)
    })
    await llmStream.listen()
  }
}

async function end() {
  await updateStopSelfMessage()
  // 关闭正在回复状态
  ctx.ui.replying = false
  // 打字结束取消光标展示
  showCursor.value = false
  setTimeout(() => {
    emitter.emit(ScrollToBottom)
  }, 100)
}

async function updateStopSelfMessage() {
  let message = await getMessage(props.message.id);
  ctx.UpdateConversationLastMsg(props.message.conversationID, message)
  ctx.CurrentChat.messageList[props.index] = message
}


/*
* @description: 找到打字容器最后一个字符位置
* */
function getLastTextNode(dom: HTMLElement) {
  if (dom.childNodes.length === 0) {
    return null;
  }
  if (dom.childNodes.length === 1) {
    return dom.childNodes[0];
  }
  for (let i = dom.childNodes.length - 1; i >= 0; i--) {
    let node = dom.childNodes[i];
    if (node.nodeType === Node.TEXT_NODE && /\S/.test(node.nodeValue)) {
      node.nodeValue = node.nodeValue.replace(/\s+$/, '')
      return node;
    } else if (node.nodeType === Node.ELEMENT_NODE) {
      let last = getLastTextNode(node as HTMLElement);
      if (last) {
        return last;
      }
    }
  }
  return null;
}

/*
* @description: 计算打字容器光标显示位置
* */
function updateCursor() {
  let el = typing.value
  if (!el) {
    return
  }
  let lastTextNode = getLastTextNode(typing.value);
  // 创建一个不可显示字符
  let textNode = document.createTextNode("\u200b")
  // 根据最后一个节点的情况 把字符添加到容器或者最后一个节点上 用于占位
  if (lastTextNode) {
    lastTextNode.parentElement.appendChild(textNode)
  } else {
    typing.value.appendChild(textNode)
  }
  // 计算占位的距离
  const domRect = typing.value.getBoundingClientRect()
  let range = document.createRange()
  range.setStart(textNode, 0)
  range.setEnd(textNode, 0)
  let rect = range.getBoundingClientRect()
  // 更新光标位置到占位符号
  pos.x = rect.left - domRect.left
  pos.y = rect.top - domRect.top
  // 移除占位符号
  textNode.remove()
}

watch(() => info.value.content, (value) => {
  if (value !== '' && isLoading.value == true) {
    isLoading.value = false
  }
})

onBeforeMount(() => {
  info.value.ex = JSON.parse(info.value.ex)
})
/*
* @description: 如果消息是gpt消息则加载周期事件
* */
onMounted(() => {
  // 初始化默认渲染数据
  if (info.value.MultipleStream?.get(info.value.id)) {
    setTimeout(async () => {
      // 聊天默认 通过 id 获取请求
      const response = await info.value.MultipleStream.get(info.value.id)()
      info.value.llmStream = new LLMStream(response)
      // updateCursor()
      beginTyping()
    }, 1000)
  }
})
// onUpdated(updateCursor)

</script>


<style scoped>


.typing-cursor {
  content: '';
  position: absolute;
  width: 10px;
  height: 18px;
  background: black;
  animation: toggle 0.5s linear infinite;
  opacity: 0;
  left: calc(v-bind('pos.x') * 1px);
  top: calc(v-bind('pos.y') * 1px);
}

.error-message {
  border-left: 4px solid #ff4d4f;
  padding-left: 12px;
  background-color: #fff2f0;
}

@keyframes toggle {
  30% {
    opacity: 1;
  }
}
</style>