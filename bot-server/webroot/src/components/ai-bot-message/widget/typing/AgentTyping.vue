<template>
    <div ref="typingBox" class="w-full">
        <div ref="typing" class="markdown-message typing w-full render" :class="message.role" v-if="agent.length > 0">
            <template v-for="(item, index) in agent" :key="index">
                <template v-if="item.type == AgentItemType.Markdown">
                    <MarkdownStyle>
                        <div class="markdown-message typing w-full" :class="message.role"
                            v-html="getAgentMdRender(item)">
                        </div>
                    </MarkdownStyle>
                </template>
                <template v-if="item.type == AgentItemType.Thinking">
                    <AgentThinking :data="item" :md="getAgentMdRender(item)" />
                </template>
                <template v-if="item.type == AgentItemType.McpCall">
                    <AgentMcpCall :data="item" />
                </template>
            </template>
        </div>
        <div class="w-full" v-if="agent.length == 0 || (agent.length > 0 && agent[0].data == '')">
            <MessageLoading />
        </div>
    </div>
</template>

<script setup lang="ts">
import { onBeforeMount, onMounted, provide, reactive, ref, watch } from "vue";
import { useGptStore } from "@/components/store/gpt";
import emitter from "@/plugins/event";
import { ScrollToBottom } from "@/plugins/evenKey";
import { MessageItem, MessageScene } from "@/components/common/model/chat";
import { DataType, LLMData, LLMStream } from "@/components/common/stream/llm_stream";
import { getMessage, updateMessage } from "@/components/common/manageRequest";
import MessageLoading from "@/components/ai-bot-message/widget/MessageLoading.vue";

import MarkdownStyle from "@/components/ai-bot-message/scene/MarkdownStyle.vue";
import { AgentItem, AgentItemType } from "@/components/common/model/agent";
import Markdown from "@/components/ai-bot-message/markdown/markdown";
import AgentMcpCall from "@/components/ai-bot-message/widget/agent/AgentMcpCall.vue";
import AgentThinking from "@/components/ai-bot-message/widget/agent/AgentThinking.vue";
import { sceneRenderMap } from "../../scene/agent/agent-scence";
const props = defineProps<{
    // 消息
    message: MessageItem,
    index: number
}>()

const isLoading = ref(true)
const typingBox = ref<HTMLElement>()
const typing = ref<HTMLElement>()

const ctx = useGptStore()
// 渲染消息文本存储
const info = ref({ ...props.message! })

const emit = defineEmits(['begin-typing'])

const agent = ref<AgentItem[]>([])

const markdown = ref<Markdown>()

// 提供 isTyping 状态
const isTyping = provide('isTyping', ref(true))


function getAgentMdRender(item: AgentItem) {
    if (item.type == AgentItemType.Markdown || item.type == AgentItemType.Thinking) {
        return markdown.value.render(item.data)
    }
    return ''
}


function beginTyping() {
    llmStream()
}


async function llmStream() {
    if (info.value.llmStream) {
        let llmStream = info.value.llmStream
        ctx.ui.currentLLMStream = llmStream
        // Begin 数据中携带了本次消息的 渲染场景 scene 需要在此处初始化 渲染器
        llmStream.setBegin((data: LLMData) => {
            info.value.content = ''  // 确保内容为空字符串而不是 undefined
            if (data.type == DataType.Render) {
                // 加载 渲染器 全局
                markdown.value = sceneRenderMap.get(data.scene);
                console.log(markdown)
                if (markdown == null) {
                    console.error('sceneRenderMap loading error', data)
                    setTimeout(async () => {
                        await end()
                    }, 200)
                }
            }
        })
        llmStream.setData((data: LLMData) => {
            if (data != null) {
                // 更新 渲染数据
                if (data.content) {
                                    // 解析为 AgentItem 数据
                let agentItem = JSON.parse(data.content) as AgentItem
                
                // 检查待添加的消息是否为空字符串，空字符串不允许添加
                if (agentItem.data == '') {
                    return
                }
                
                // 检查当前的 agent 数据 在 agent 数组中最后一个数据是否相同
                if (agent.value.length > 0) {
                    let lastAgentItem = agent.value[agent.value.length - 1]
                    if (lastAgentItem.type == agentItem.type) {
                        // 如果相同则 检查数据类型 更具数据类型 更新数据
                        switch (lastAgentItem.type) {
                            case AgentItemType.Markdown:
                                lastAgentItem.data += agentItem.data
                                break
                            case AgentItemType.Thinking:
                                lastAgentItem.data += agentItem.data
                                lastAgentItem.status = agentItem.status
                                break
                            case AgentItemType.McpCall:
                                if (lastAgentItem.mcpToolId == agentItem.mcpToolId) {
                                    // 如果是同一个mcp调用过程 则 更新数据  
                                    lastAgentItem.data = agentItem.data
                                    lastAgentItem.status = agentItem.status
                                } else {
                                    // 如果不是同一个mcp调用过程 则 添加数据
                                    agent.value.push(agentItem)
                                }
                                break
                            default:
                                break
                        }
                    } else {
                        // 如果不同则 添加数据
                        agent.value.push(agentItem)
                    }
                } else {
                    agent.value.push(agentItem)
                }
                }

                emitter.emit(ScrollToBottom)
            }
        })
        llmStream.setError(async (data: LLMData) => {
            setTimeout(async () => {
                await end()
            }, 200)
        })
        llmStream.setFinish((data: LLMData) => {
            setTimeout(async () => {
                await end()
            }, 200)
        })
        llmStream.setCancel((data: LLMData) => {
            setTimeout(async () => {
                // 结束语音播报
                await end()
            }, 500)
        })
        await llmStream.listen()
    }
}

async function end() {
    await updateStopSelfMessage()
    // 关闭正在回复状态
    ctx.ui.replying = false
    setTimeout(() => {
        emitter.emit(ScrollToBottom)
    }, 100)
}

async function updateStopSelfMessage() {
    // 主动存储 agent 消息数据 服务器
    let content = JSON.stringify(agent.value)
    // let message = await getMessage(props.message.id);
    let message = await updateMessage(props.message.id, content)
    ctx.UpdateConversationLastMsg(props.message.conversationID, message)
    ctx.CurrentChat.messageList[props.index] = message
}


watch(() => info.value.content, (value) => {
    if (value !== '' && isLoading.value == true) {
        isLoading.value = false
    }
})

onBeforeMount(() => {
    info.value.ex = JSON.parse(info.value.ex)
})
/*
* @description: 如果消息是gpt消息则加载周期事件
* */
onMounted(() => {
    // 初始化默认渲染数据
    if (info.value.MultipleStream?.get(info.value.id)) {
        setTimeout(async () => {
            // 聊天默认 通过 id 获取请求
            const response = await info.value.MultipleStream.get(info.value.id)()
            info.value.llmStream = new LLMStream(response)
            // updateCursor()
            beginTyping()
        }, 1000)
    }
})
// onUpdated(updateCursor)

</script>


<style scoped>
.error-message {
    border-left: 4px solid #ff4d4f;
    padding-left: 12px;
    background-color: #fff2f0;
}

@keyframes toggle {
    30% {
        opacity: 1;
    }
}
</style>