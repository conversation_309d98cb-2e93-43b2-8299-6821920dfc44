<template>
  <div v-if="shouldRender">
    <a-collapse v-model:activeKey="activeKeys">
      <a-collapse-panel key="thinking">
        <template #header>
          <span class="panel-header">思考过程</span>
        </template>
        <div class="thinking-content" v-html="md.render(processedContent)"></div>
      </a-collapse-panel>
    </a-collapse>
  </div>
</template>

<script setup lang="ts">
import {theme} from "ant-design-vue";
import Markdown from "@/components/ai-bot-message/markdown/markdown";
import {computed, ref, watchEffect} from 'vue';

const {useToken} = theme;
const {token} = useToken();

const props = defineProps<{
  content: string
  md: Markdown
  isRender?: boolean
}>();

// 使用数组来存储激活的面板key
const activeKeys = ref<string[]>(['thinking']);

// 监听内容变化，当有新内容时自动展开
watchEffect(() => {
  if (props.content && !props.content.endsWith('[end]')) {
    if (!activeKeys.value.includes('thinking')) {
      activeKeys.value = ['thinking'];
    }
  }
});

const isEnd = computed(() => {
  return props.content.endsWith('[end]');
});

const shouldRender = computed(() => {
  if (!props.content || props.content.trim() === '') {
    return false;
  }
  if (props.isRender === false) {
    return false;
  }
  return true;
});

const processedContent = computed(() => {
  return isEnd.value ? props.content.slice(0, -5) : props.content;
});
</script>

<style scoped>
:deep(.ant-collapse) {
  margin: 8px 0;
  border-radius: 4px;
  background-color: v-bind('token.colorBgContainer');
}

:deep(.ant-collapse-item) {
  border-radius: 4px;
}

:deep(.ant-collapse-header) {
  align-items: center !important;
  padding: 8px 16px !important;
}

:deep(.ant-collapse-content-box) {
  padding: 12px 16px !important;
}

.panel-header {
  color: v-bind('token.colorTextSecondary');
  font-size: 14px;
}

.thinking-content {
  font-size: 14px;
  line-height: 1.6;
}

:deep(.ant-collapse-arrow) {
  color: v-bind('token.colorTextSecondary');
}
</style>
