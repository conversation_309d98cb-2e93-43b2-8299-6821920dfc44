<template>
    <div class="function-call-container" v-html="renderHtml"></div>
</template>

<script setup lang="ts">
import { FuncCallRender } from '@/components/common/model/render';
import { computed, onMounted } from 'vue';
import {theme} from "ant-design-vue";
const {useToken} = theme;
const {token} = useToken();

const props = defineProps<{
    functionCall: FuncCallRender
}>();

const uniqueId = `function-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

const renderHtml = computed(() => {
    return `
    <div class="function-call-wrapper">
      <div class="function-header" onclick="
        const valueElement = document.getElementById('${uniqueId}');
        const isVisible = valueElement.classList.contains('expanded');
        if (isVisible) {
          valueElement.classList.remove('expanded');
          this.querySelector('.toggle-icon').textContent = '▶';
        } else {
          valueElement.classList.add('expanded');
          this.querySelector('.toggle-icon').textContent = '▼';
        }
      ">
        <span class="toggle-icon">▶</span>
        <span class="function-name">${props.functionCall.name}</span>
      </div>
      <div id="${uniqueId}" class="function-value">
        ${props.functionCall.value.replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/\n/g, '<br/>')}
      </div>
    </div>
  `;
});

</script>

<style>
.function-call-container {
    padding: 4px;
    border-radius: 4px;
    background-color: v-bind('token.colorBgContainer');
    margin: 4px 0;
}

.function-header {
    font-weight: bold;
    color: v-bind('token.colorPrimary');
    cursor: pointer;
    display: flex;
    align-items: center;
    padding: 2px 0;
}

.toggle-icon {
    font-size: 12px;
    margin-right: 4px;
}

.function-value {
    /* margin-top: 8px; */
    padding: 8px;
    background-color: v-bind('token.colorBgContainer');
    /* border-radius: 4px; */
    /* white-space: pre-wrap; */
    word-break: break-all;
    font-family: monospace;
    
    max-height: 0;
    overflow: hidden;
    opacity: 0;
    transition: max-height 0.3s ease-in-out, opacity 0.3s ease-in-out, padding 0.3s ease-in-out;
    padding-top: 0;
    padding-bottom: 0;
}

.function-value.expanded {
    max-height: 1000px; /* 足够大的值以容纳内容 */
    opacity: 1;
    padding: 8px;
}
</style>