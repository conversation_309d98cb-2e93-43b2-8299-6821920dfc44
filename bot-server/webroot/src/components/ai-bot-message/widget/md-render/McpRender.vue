<template>
    <div class="function-call-container" v-html="renderHtml"></div>
</template>

<script setup lang="ts">
import {FuncCallRender, McpCallRender} from '@/components/common/model/render';
import { computed, onMounted } from 'vue';
import {theme} from "ant-design-vue";
const {useToken} = theme;
const {token} = useToken();

const props = defineProps<{
    functionCall: McpCallRender
}>();

const uniqueId = `function-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

const renderHtml = computed(() => {
    // 提取arguments参数
    let argumentsHtml = '';
    
    // 调试输出，便于后续排查
    console.log("MCP渲染参数:", props.functionCall);
    
    // 查找arguments的辅助函数
    const findArguments = (obj) => {
        if (!obj || typeof obj !== 'object') return null;
        
        // 直接检查args属性
        if ('arguments' in obj && obj.arguments && typeof obj.arguments === 'object') {
            console.log("找到arguments:", obj.arguments);
            return obj.arguments;
        }
        
        // 尝试在value数组中的对象中查找
        if (Array.isArray(obj.value)) {
            for (const item of obj.value) {
                if (item && typeof item === 'object' && 'arguments' in item && item.arguments) {
                    console.log("在value中找到arguments:", item.arguments);
                    return item.arguments;
                }
            }
        }
        
        return null;
    };
    
    // 尝试找到arguments
    const args = findArguments(props.functionCall);
    
    // 构建arguments的HTML
    if (args) {
        argumentsHtml = '<div class="arguments-container">';
        for (const [key, value] of Object.entries(args)) {
            // 显示的值格式化
            let displayValue = '';
            if (typeof value === 'object' && value !== null) {
                try {
                    displayValue = JSON.stringify(value);
                } catch (e) {
                    displayValue = String(value);
                }
            } else {
                displayValue = String(value);
            }
            
            // 对象值要缩短
            if (displayValue.length > 30) {
                displayValue = displayValue.substring(0, 27) + '...';
            }
            
            argumentsHtml += `<span class="argument-item"><span class="argument-key">${key}</span>:<span class="argument-value">${displayValue}</span></span>`;
        }
        argumentsHtml += '</div>';
    }

    // 处理 value 数组，将每个对象转为格式化的 JSON 字符串
    const formattedValue = Array.isArray(props.functionCall.value) 
        ? props.functionCall.value.map(obj => {
            try {
                // 对于包含text字段的对象进行特殊处理
                if (obj && typeof obj === 'object' && 'text' in obj) {
                    // 创建对象的副本，以便处理text字段
                    const processedObj = { ...obj };
                    
                    // 处理text字段，使其显示更友好
                    if (typeof processedObj.text === 'string') {
                        // 将text中的转义字符进行适当显示
                        processedObj.text = processedObj.text
                            // 先将转义的换行符替换为真实的换行标签
                            .replace(/\\n/g, '<br>')
                            // 处理转义的引号
                            .replace(/\\"/g, '&quot;')
                            // 处理其他常见转义字符
                            .replace(/\\t/g, '&nbsp;&nbsp;&nbsp;&nbsp;');
                    }
                    
                    // 为对象生成HTML表示
                    let html = '<div class="mcp-object">';
                    
                    // 先处理text字段
                    if ('text' in processedObj) {
                        html += `<div class="mcp-text-field"><strong>text</strong>: <span class="mcp-text-content">${processedObj.text}</span></div>`;
                    }
                    
                    // 处理其他字段（除了arguments，因为已经在标题旁显示）
                    for (const [key, value] of Object.entries(processedObj)) {
                        if (key !== 'text' && key !== 'arguments') {
                            const formattedValue = typeof value === 'object' 
                                ? JSON.stringify(value, null, 2).replace(/\n/g, '<br>').replace(/ /g, '&nbsp;')
                                : String(value);
                            html += `<div class="mcp-field"><strong>${key}</strong>: ${formattedValue}</div>`;
                        }
                    }
                    
                    html += '</div>';
                    return html;
                }
                
                // 对于其他类型的对象，保持原有处理方式
                const jsonStr = JSON.stringify(obj, null, 2);
                return jsonStr.replace(/\n/g, '<br>').replace(/ /g, '&nbsp;');
            } catch (e) {
                return String(obj);
            }
        }).join('<br><br>')
        : props.functionCall.value;

    return `
    <div class="function-call-wrapper">
      <div class="function-header" onclick="
        const valueElement = document.getElementById('${uniqueId}');
        const isVisible = valueElement.classList.contains('expanded');
        if (isVisible) {
          valueElement.classList.remove('expanded');
        } else {
          valueElement.classList.add('expanded');
        }
      ">
        <span class="icon iconfont moxing"></span>
        <span class="function-name">${props.functionCall.mcp}.${props.functionCall.tool}</span>
        ${argumentsHtml}
      </div>
      <div id="${uniqueId}" class="function-value">
        ${formattedValue}
      </div>
    </div>
  `;
});

</script>

<style scoped>
.function-call-container {
    padding: 4px;
    border-radius: 4px;
    background-color: v-bind('token.colorBgContainer');
    margin: 4px 0;
}

.function-header {
    font-weight: bold;
    color: v-bind('token.colorPrimary');
    cursor: pointer;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    padding: 2px 0;
}

.icon.iconfont.moxing {
    font-size: 12px;
    margin-right: 4px;
}

.function-name {
    margin-right: 8px;
}

/* arguments 样式 */
.arguments-container {
    display: flex;
    flex-wrap: wrap;
    margin-left: 8px;
    font-size: 0.85em;
    color: rgba(0, 0, 0, 0.65);
    max-width: 70%;
    overflow: hidden;
}

.argument-item {
    background-color: rgba(0, 0, 0, 0.04);
    padding: 1px 6px;
    border-radius: 10px;
    margin-right: 4px;
    margin-bottom: 2px;
    display: inline-block;
}

.argument-key {
    font-weight: 600;
    margin-right: 2px;
}

.argument-value {
    max-width: 80px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: inline-block;
    vertical-align: bottom;
}

.function-value {
    /* margin-top: 8px; */
    padding: 8px;
    background-color: v-bind('token.colorBgContainer');
    /* border-radius: 4px; */
    /* white-space: pre-wrap; */
    word-break: break-all;
    font-family: monospace;

    max-height: 0;
    overflow: hidden;
    opacity: 0;
    transition: max-height 0.3s ease-in-out, opacity 0.3s ease-in-out, padding 0.3s ease-in-out;
    padding-top: 0;
    padding-bottom: 0;
}

.function-value.expanded {
    max-height: 400px; /* 设置一个合理的最大高度 */
    opacity: 1;
    padding: 8px;
    overflow-y: auto; /* 添加垂直滚动条 */
}

/* 添加MCP对象显示样式 */
.mcp-object {
    padding: 8px;
    margin-bottom: 8px;
    border-radius: 4px;
    background-color: rgba(0, 0, 0, 0.02);
}

.mcp-text-field {
    margin-bottom: 8px;
    padding: 4px;
    border-radius: 4px;
    background-color: rgba(0, 0, 0, 0.03);
}

.mcp-text-content {
    display: block;
    margin-top: 4px;
    padding: 8px;
    background-color: white;
    border-radius: 4px;
    white-space: pre-wrap;
    max-height: 300px; /* 限制text内容的最大高度 */
    overflow-y: auto; /* 添加垂直滚动条 */
}

/* 定制滚动条样式 */
.function-value.expanded::-webkit-scrollbar,
.mcp-text-content::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

.function-value.expanded::-webkit-scrollbar-thumb,
.mcp-text-content::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
}

.function-value.expanded::-webkit-scrollbar-track,
.mcp-text-content::-webkit-scrollbar-track {
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 3px;
}

.mcp-field {
    margin-bottom: 4px;
}
</style>