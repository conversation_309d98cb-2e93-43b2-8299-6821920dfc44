<template>
  <div class="error-message-container">
    <div class="error-content">
      <div class="error-icon">
        <i class="icon">⚠️</i>
      </div>
      <div class="error-details">
        <div class="error-title">服务器繁忙</div>
        <div class="error-brief" v-if="!showDetail" @click="toggleDetail">
          点击查看详细信息
        </div>
        <div v-else class="error-detail">
          <div class="error-text">{{ errorMessage }}</div>
          <div class="collapse-btn" @click="toggleDetail">收起</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {MessageItem} from "@/components/common/model/chat";
import { computed, onBeforeMount, ref } from "vue";

const props = defineProps<{
  // 消息
  message: MessageItem,
  index: number
}>()
const info = ref({...props.message!})

const errorMessage = computed(() => {
  if (info.value.ex && info.value.ex.debugINFO) {
    return info.value.ex.debugINFO.errMsg || '未知错误'
  }
  return '未知错误'
})

const showDetail = ref(false)

const toggleDetail = () => {
  showDetail.value = !showDetail.value
}

onBeforeMount(() => {
  info.value.ex = JSON.parse(info.value.ex)
})
</script>

<style scoped>
.error-message-container {
  padding: 8px;
  border-radius: 4px;
}

.error-content {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.error-icon {
  font-size: 16px;
  color: #ff4d4f;
  line-height: 1;
}

.error-details {
  flex: 1;
}

.error-title {
  font-size: 14px;
  color: #ff4d4f;
  margin-bottom: 4px;
}

.error-brief {
  font-size: 12px;
  color: #8c8c8c;
  cursor: pointer;
}

.error-brief:hover {
  color: #1890ff;
}

.error-detail {
  margin-top: 4px;
}

.error-text {
  font-size: 12px;
  color: #666;
  line-height: 1.5;
  word-break: break-all;
  white-space: pre-wrap;
  margin-bottom: 4px;
}

.collapse-btn {
  font-size: 12px;
  color: #1890ff;
  cursor: pointer;
}

.collapse-btn:hover {
  text-decoration: underline;
}
</style>