<template>
  <div class="ai-bot-message-loading">
    <div class="skeleton-container">
      <div v-for="i in randomLines" :key="i"
           class="skeleton-line"
           :class="`line-${i}`">
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {onMounted, ref} from 'vue';
import {theme} from "ant-design-vue";

const {useToken} = theme;
const {token} = useToken();
const randomLines = ref(3);

onMounted(() => {
  // randomLines.value = Math.floor(Math.random() * 3) + 2; // 生成2-4的随机数
});
</script>

<style scoped>
.ai-bot-message-loading {
  width: 100%;
  height: 100%;
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
}

.skeleton-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.skeleton-line {
  height: 4px;
  background: v-bind('token.colorSplit');
  border-radius: 2px;
  position: relative;
  overflow: hidden;
}

.skeleton-line::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.3) 25%,
    rgba(255, 255, 255, 0.5) 50%,
    rgba(255, 255, 255, 0.3) 75%
  );
  animation: loading 1.4s ease infinite;
  background-size: 200% 100%;
}

.line-1 {
  width: 90%;
  animation: width-change-1 2s ease-in-out infinite;
}

.line-2 {
  width: 75%;
  animation: width-change-2 1.8s ease-in-out infinite;
}

.line-3 {
  width: 60%;
  animation: width-change-3 1.6s ease-in-out infinite;
}

.line-4 {
  width: 45%;
  animation: width-change-4 1.4s ease-in-out infinite;
}

@keyframes loading {
  0% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0 50%;
  }
}

@keyframes width-change-1 {
  0%, 100% {
    width: 100%;
  }
  50% {
    width: 80%;
  }
}

@keyframes width-change-2 {
  0%, 100% {
    width: 80%;
  }
  50% {
    width: 60%;
  }
}

@keyframes width-change-3 {
  0%, 100% {
    width: 60%;
  }
  50% {
    width: 40%;
  }
}

@keyframes width-change-4 {
  0%, 100% {
    width: 40%;
  }
  50% {
    width: 20%;
  }
}
</style> 