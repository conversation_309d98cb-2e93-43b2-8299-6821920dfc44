<template>
    <div class="size-full flex flex-col">
        <template v-for="(item, index) in agent" :key="index">
            <template v-if="item.type == AgentItemType.Markdown">
                <MarkdownStyle>
                    <div class="markdown-message gpt-message" v-html="getAgentMdRender(item)"> </div>
                </MarkdownStyle>
            </template>
            <template v-if="item.type == AgentItemType.Thinking">
                <AgentThinking :data="item" :md="getAgentMdRender(item)" />
            </template>
            <template v-if="item.type == AgentItemType.McpCall">
                <AgentMcpCall :data="item" />
            </template>
        </template>
    </div>
</template>

<script setup lang="ts">
import { MessageItem } from '@/components/common/model/chat';
import { sceneRenderMap } from '../../scene/agent/agent-scence';
import { AgentItem, AgentItemType } from '@/components/common/model/agent';
import AgentThinking from '@/components/ai-bot-message/widget/agent/AgentThinking.vue';
import AgentMcpCall from '@/components/ai-bot-message/widget/agent/AgentMcpCall.vue';
import { ref } from 'vue';
import MarkdownStyle from '@/components/ai-bot-message/scene/MarkdownStyle.vue';

const props = defineProps<{
    // 消息
    message: MessageItem,
    index: number,
}>()

let markdown = sceneRenderMap.get(props.message.messageType);


const agent = ref<AgentItem[]>([])

// 解析消息
if (props.message.content) {
    agent.value = JSON.parse(props.message.content) as AgentItem[]
}

function getAgentMdRender(item: AgentItem) {
    if (item.type == AgentItemType.Markdown || item.type == AgentItemType.Thinking) {
        return markdown.render(item.data)
    }
    return ''
}
</script>
