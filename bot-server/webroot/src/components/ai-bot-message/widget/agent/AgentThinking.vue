<template>
    <div v-if="shouldRender">
        <a-collapse v-model:activeKey="activeKeys" :bordered="false" :showArrow="false">
            <a-collapse-panel key="thinking" :showArrow="false">
                <template #header>
                    <span class="panel-header">
                        <ThunderboltOutlined /> 思考过程
                    </span>
                </template>
                <MarkdownStyle>
                    <div class="markdown-message gpt-message w-full" v-html="md" :class="{ 'is-loading': isLoading }">
                    </div>
                    <span v-if="isLoading && typing" class="icon-loading">
                        <a-spin :size="10" />
                    </span>
                </MarkdownStyle>
            </a-collapse-panel>
        </a-collapse>
    </div>
</template>

<script setup lang="ts">
import { theme } from 'ant-design-vue';
import { AgentItem, AgentItemStatus } from '@/components/common/model/agent';
import MarkdownStyle from '../../scene/MarkdownStyle.vue';
import { computed, inject, onMounted, Ref, ref, watch } from 'vue';
import { ThunderboltOutlined } from '@ant-design/icons-vue';

const { useToken } = theme;
const { token } = useToken();

// 提供 isTyping 状态
const isTyping = inject('isTyping') as Ref<boolean>
// 初始化 是否正在输入
const typing = ref(false)
if (isTyping) {
    typing.value = isTyping.value
}

const props = defineProps<{
    data: AgentItem
    md: string
}>();

// 使用数组来存储激活的面板key
const activeKeys = ref<string[]>(['thinking']);

// 初始化时如果状态为stop，则默认折叠
if (props.data.status === AgentItemStatus.Stop ) {
    activeKeys.value = [];
}

if (props.data.status != AgentItemStatus.Stop && !typing.value) {
    activeKeys.value = [];
}

// 根据状态判断是否显示加载动画
const isLoading = computed(() => {
    return props.data.status !== AgentItemStatus.Stop;
});

// 监听状态变化，当状态变为stop时自动折叠
watch(() => props.data.status, (newStatus, oldStatus) => {
    if (newStatus === AgentItemStatus.Stop && oldStatus !== AgentItemStatus.Stop) {
        // 当状态从非stop变为stop时，折叠面板
        activeKeys.value = [];
    }
});

// 是否应该渲染
const shouldRender = computed(() => {
    return props.md && props.md.trim() !== '';
});

onMounted(() => {
});

</script>

<style scoped>

:deep(.bot-thinking) {
    border-left: 4px solid #bfc3c7 !important;
    opacity: 0.5 !important;
    padding-left: 10px !important;
    border-radius: 4px !important;
    position: relative !important;
    overflow: hidden !important;
    transition: all 0.3s ease !important;
}

.icon-loading {
  margin-right: 4px;
  display: inline-flex;
  align-items: center;
}

:deep(.ant-collapse) {
  margin: 8px 0;
  border-radius: 4px;
  background-color: v-bind('token.colorBgContainer');
}

:deep(.ant-collapse-item) {
  border-radius: 4px;
}

:deep(.ant-collapse-header) {
  align-items: center !important;
  padding: 0 !important;
}


.panel-header {
  color: v-bind('token.colorTextSecondary') !important;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}

:deep(.ant-collapse-arrow) {
  color: v-bind('token.colorTextSecondary');
}

:deep(.ant-collapse .ant-collapse-content>.ant-collapse-content-box) {
  padding: 0 !important;
}

</style>