<template>
  <div class="agent-mcp-call-container flex justify-center">
    <div class="function-call-container">
      <div class="agent-function-call-wrapper" v-if="mcpCall">
        <div class="custom-details">
          <div class="custom-summary" @click="toggleOpen">
            <div class="agent-function-header">
              <div class="flex items-center">
                <!-- 根据status状态显示不同的图标 -->
                <span v-if="status === 'stop'" class="icon iconfont moxing"></span>
                <span v-else class="icon-loading">
                  <a-spin :size="10" />
                </span>
              </div>
              <a-typography-text strong>{{ mcpCall.mcp }}.{{ mcpCall.tool }}</a-typography-text>
              <!-- 箭头指示器 -->
              <span class="arrow-indicator" :class="{ 'is-open': isOpen }">
                <i class="icon iconfont arrow-down"></i>
              </span>
              <!-- 参数展示区域 -->
              <div v-if="args" class="arguments-container">
                <a-tag v-for="(value, key) in args" :key="key" class="argument-item" :bordered="false">
                  <span class="argument-key">{{ key }}</span>:<span class="argument-value">{{ formatValue(value)
                  }}</span>
                </a-tag>
              </div>
            </div>
          </div>
          <transition name="slide-fade">
            <div v-show="isOpen" class="details-content overflow-hidden flex flex-col">
              <a-divider orientation="left">response</a-divider>
              <div class="overflow-y-auto grow">
                <template v-if="mcpCall && Array.isArray(mcpCall.value)">
                  <template v-for="(item, index) in mcpCall.value" :key="index">
                    <div class="mcp-content">
                      <!-- 根据类型处理不同的内容 -->
                      <template v-if="item && typeof item === 'object' && 'type' in item">
                        <!-- 文本类型处理 -->
                        <template v-if="item.type === 'text' && 'text' in item">
                          <a-typography-paragraph v-html="formatTextContent(item.text)"></a-typography-paragraph>
                        </template>
                        <!-- 图片类型处理 -->
                        <template v-else-if="item.type === 'image' && 'data' in item && 'mimeType' in item">
                          <img :src="`data:${item.mimeType};base64,${item.data}`" class="response-image" />
                        </template>
                        <!-- 其他类型或未知结构 -->
                        <template v-else>
                          <a-typography-paragraph v-html="formatJson(item)"></a-typography-paragraph>
                        </template>
                      </template>
                      <!-- 不符合特定类型结构的内容 -->
                      <template v-else>
                        <a-typography-paragraph v-html="formatJson(item)"></a-typography-paragraph>
                      </template>
                    </div>
                    <a-divider v-if="mcpCall && index < mcpCall.value.length - 1" />
                  </template>
                </template>
                <template v-else-if="mcpCall">
                  <a-typography-paragraph>{{ mcpCall.value }}</a-typography-paragraph>
                </template>
              </div>
            </div>
          </transition>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { McpCallRender } from '@/components/common/model/render';
import { computed, inject, Ref, ref } from 'vue';
import { theme } from "ant-design-vue";
import { AgentItem, AgentItemStatus } from '@/components/common/model/agent';
import { SetScrollPosition } from '@/plugins/evenKey';
import emitter from '@/plugins/event';

const { useToken } = theme;
const { token } = useToken();

const props = defineProps<{
  data: AgentItem
}>();

const types = ['text', 'image'];

const mcpCall = ref<McpCallRender>();
const isOpen = ref(false);
const scrollRatio = inject('scrollRatio') as Ref<number>;
const status = computed(() => props.data.status);

mcpCall.value = JSON.parse(props.data.data) as McpCallRender;

const toggleOpen = () => {
  isOpen.value = !isOpen.value;
};

// 查找arguments的函数
const args = computed(() => {
  const obj = mcpCall.value;
  if (!obj || typeof obj !== 'object') return null;

  if ('arguments' in obj && obj.arguments && typeof obj.arguments === 'object') {
    return obj.arguments;
  }

  if (Array.isArray(obj.value)) {
    for (const item of obj.value) {
      if (item && typeof item === 'object' && 'arguments' in item && item.arguments) {
        return item.arguments;
      }
    }
  }

  return null;
});

// 格式化显示的值
const formatValue = (value: any): string => {
  let displayValue = '';
  if (typeof value === 'object' && value !== null) {
    try {
      displayValue = JSON.stringify(value);
    } catch (e) {
      displayValue = String(value);
    }
  } else {
    displayValue = String(value);
  }

  if (displayValue.length > 30) {
    displayValue = displayValue.substring(0, 27) + '...';
  }

  return displayValue;
};

// 格式化JSON对象
const formatJson = (obj: any): string => {
  try {
    const jsonStr = JSON.stringify(obj, null, 2);
    return jsonStr.replace(/\n/g, '<br>').replace(/ /g, '&nbsp;');
  } catch (e) {
    return String(obj);
  }
};

// 格式化文本内容，尝试进行JSON格式化
const formatTextContent = (text: string): string => {
  try {
    // 尝试解析为JSON
    const jsonObj = JSON.parse(text);
    // 如果是对象，格式化JSON显示
    return formatJson(jsonObj);
  } catch (e) {
    // 如果解析失败，直接返回原文本
    return text;
  }
};
</script>

<style>
/* 全局滚动条样式 */
.details-content::-webkit-scrollbar {
  width: 6px !important;
}

.details-content::-webkit-scrollbar-track {
  background: transparent !important;
}

.details-content::-webkit-scrollbar-thumb {
  background-color: v-bind('token.colorBorderSecondary') !important;
  border-radius: 10px !important;
}

.details-content::-webkit-scrollbar-thumb:hover {
  background-color: v-bind('token.colorBgLayout') !important;
}
</style>

<style scoped>
:deep(.ant-divider-horizontal) {
  margin: 0;
}

.function-call-container {
  width: 98%;
  border-radius: 4px;
  border: 1px solid v-bind('token.colorBorder');
}

.agent-function-header {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.icon.iconfont.moxing {
  font-size: 12px;
  margin-right: 4px;
}

.icon-loading {
  margin-right: 4px;
  display: inline-flex;
  align-items: center;
}

.arrow-indicator {
  margin-left: 8px;
  transition: transform 0.3s ease;
}

.arrow-indicator.is-open {
  transform: rotate(180deg);
}

.arrow-indicator .icon {
  font-size: 12px;
}

.arguments-container {
  display: flex;
  flex-wrap: wrap;
  margin-left: 8px;
  max-width: 70%;
}

.argument-item {
  margin-right: 4px;
  margin-bottom: 2px;
  border-radius: 12px;
}

.argument-key {
  font-weight: 600;
  margin-right: 2px;
}

.argument-value {
  max-width: 80px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
  vertical-align: bottom;
}

.mcp-content {
  padding: 8px;
  white-space: pre-wrap;
}

/* 自定义折叠面板样式 */
.custom-details {
  width: 100%;
  border-radius: 4px;
  background-color: transparent;
}

.custom-summary {
  cursor: pointer;

  border-radius: 4px 4px 0 0;
  background-color: v-bind('token.colorBgContainer');
  user-select: none;
}

.details-content {
  border-radius: 0 0 4px 4px;
  background-color: v-bind('token.colorBgContainer');
  padding: 8px 0;
  max-height: 25vh;
  overflow-y: auto;
}

.response-image {
  max-width: 100%;
  border-radius: 4px;
  margin: 8px 0;
}

/* 过渡动画 */
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s ease;
  max-height: 25vh;
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  opacity: 0;
  max-height: 0;
  overflow: hidden;
}
</style>
