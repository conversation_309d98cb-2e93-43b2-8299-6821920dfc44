import type {Directive, DirectiveBinding} from 'vue'
import {getContrastColor, getScreenColorAt} from '../common/util'
import html2canvas from 'html2canvas'

// 缓存 canvas 和 context
let cachedCanvas: HTMLCanvasElement | null = null;
let cachedContext: CanvasRenderingContext2D | null = null;
let cachedScreenshot: HTMLCanvasElement | null = null;

/**
 * 初始化截图
 */
async function initScreenshot(): Promise<void> {
    if (!cachedCanvas) {
        cachedCanvas = document.createElement('canvas');
        cachedCanvas.width = window.innerWidth;
        cachedCanvas.height = window.innerHeight;
        cachedContext = cachedCanvas.getContext('2d', { willReadFrequently: true });
    }

    if (!cachedScreenshot) {
        cachedScreenshot = await html2canvas(document.body, {
            backgroundColor: null,
            logging: false,
            width: window.innerWidth,
            height: window.innerHeight,
            x: window.scrollX,
            y: window.scrollY,
            scale: 1,
        });
    }
}

interface AutoIconColorElement extends HTMLElement {
    _updateTimeout?: number;
    _colorCallback?: (color: {r: number, g: number, b: number, a: number}) => void;
}

/**
 * 从 RGB/RGBA 字符串中提取颜色值
 * @param color RGB/RGBA 颜色字符串
 */
function parseColor(color: string): {r: number, g: number, b: number, a: number} | null {
    // 匹配 rgb(r, g, b) 或 rgba(r, g, b, a) 格式
    const rgbMatch = color.match(/^rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*([0-9.]+))?\)$/);
    if (rgbMatch) {
        return {
            r: parseInt(rgbMatch[1]),
            g: parseInt(rgbMatch[2]),
            b: parseInt(rgbMatch[3]),
            a: rgbMatch[4] ? parseFloat(rgbMatch[4]) : 1
        };
    }
    return null;
}

/**
 * 获取元素的背景色
 * @param el 目标元素
 */
function getElementBackgroundColor(el: HTMLElement): {r: number, g: number, b: number, a: number} | null {
    const computedStyle = window.getComputedStyle(el);
    let backgroundColor = computedStyle.backgroundColor;
    
    // 如果当前元素背景色是透明的，尝试获取父元素的背景色
    let currentEl: HTMLElement | null = el;
    while (currentEl && (backgroundColor === 'transparent' || backgroundColor === 'rgba(0, 0, 0, 0)')) {
        currentEl = currentEl.parentElement;
        if (currentEl) {
            backgroundColor = window.getComputedStyle(currentEl).backgroundColor;
        }
    }

    // 如果找到最顶层还是透明的，返回白色
    if (backgroundColor === 'transparent' || backgroundColor === 'rgba(0, 0, 0, 0)') {
        return {
            r: 255,
            g: 255,
            b: 255,
            a: 1
        };
    }

    return parseColor(backgroundColor);
}

/**
 * 获取元素中心点的颜色
 * @param el 目标元素
 */
async function getElementCenterColor(el: HTMLElement): Promise<{r: number, g: number, b: number, a: number} | null> {
    try {
        // 确保已初始化
        await initScreenshot();
        
        if (!cachedScreenshot) {
            return null;
        }

        const rect = el.getBoundingClientRect();
        // 获取元素中心点在屏幕上的坐标
        const centerX = Math.floor(rect.left + rect.width / 2);
        const centerY = Math.floor(rect.top + rect.height / 2);

        // 获取中心点的颜色
        const ctx = cachedScreenshot.getContext('2d');
        if (!ctx) return null;

        const pixel = ctx.getImageData(centerX, centerY, 1, 1).data;
        return {
            r: pixel[0],
            g: pixel[1],
            b: pixel[2],
            a: pixel[3]
        };
    } catch (error) {
        console.error('获取元素中心点颜色失败:', error);
        return null;
    }
}

/**
 * 更新元素颜色
 * @param el 目标元素
 */
async function updateIconColor(el: AutoIconColorElement) {
    try {
        const color = await getElementCenterColor(el);
        if (color) {
            // 获取对比色
            const contrastColor = getContrastColor(color.r, color.g, color.b);
            
            // 如果存在回调函数，则调用回调函数
            if (el._colorCallback) {
                // 确保透明度不会太低
                const alpha = Math.max(color.a / 255, 0.8); // 将 0-255 的透明度转换为 0-1
                el._colorCallback({...contrastColor, a: alpha});
            } else {
                // 默认行为：设置元素颜色
                // 确保透明度不会太低，这样图标始终可见
                const alpha = Math.max(color.a / 255, 0.8); // 将 0-255 的透明度转换为 0-1
                el.style.color = `rgba(${contrastColor.r}, ${contrastColor.g}, ${contrastColor.b}, ${alpha})`;
            }
        } else {
            // 如果获取不到颜色，使用默认颜色
            el.style.color = 'rgba(0, 0, 0, 0.8)';
        }
    } catch (error) {
        console.error('更新图标颜色失败:', error);
        // 发生错误时，设置一个默认的可见颜色
        el.style.color = 'rgba(0, 0, 0, 0.8)';
    }
}

/**
 * 自动图标颜色指令
 * 根据元素背景色自动调整图标颜色
 *
 * 使用方式：
 * 1. 基础用法：v-auto-icon-color
 * 2. 带回调函数：v-auto-icon-color="(color) => handleColor(color)"
 * 
 * 回调函数参数 color 的格式为: {r: number, g: number, b: number, a: number}
 * 其中 r,g,b 的范围是 0-255，a 的范围是 0-1
 */
export const vAutoIconColor: Directive = {
    async mounted(el: AutoIconColorElement, binding: DirectiveBinding) {
        // 如果指令有值，且是函数，则保存回调函数
        if (binding.value && typeof binding.value === 'function') {
            el._colorCallback = binding.value;
        }
        await updateIconColor(el);

        // 添加 MutationObserver 来监听背景色变化
        const observer = new MutationObserver(() => {
            // 当背景变化时，需要重新初始化截图
            cachedScreenshot = null;
            updateIconColor(el);
        });
        
        observer.observe(el, {
            attributes: true,
            attributeFilter: ['style', 'class']
        });

        // 保存 observer 以便在 unmounted 时清理
        (el as any)._observer = observer;

        // 监听窗口大小变化
        window.addEventListener('resize', () => {
            // 窗口大小变化时，需要重新初始化
            cachedCanvas = null;
            cachedContext = null;
            cachedScreenshot = null;
            updateIconColor(el);
        });
    },

    unmounted(el: AutoIconColorElement) {
        if (el._updateTimeout) {
            clearTimeout(el._updateTimeout);
        }
        // 清除回调函数
        el._colorCallback = undefined;
        // 清除 observer
        if ((el as any)._observer) {
            (el as any)._observer.disconnect();
        }
    }
} 