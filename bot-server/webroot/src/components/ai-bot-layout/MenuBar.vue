<template>
  <div class="menu-container flex flex-row justify-center">
    <div class="size-full flex flex-col">
      <div class="tag-menu app-drag">
        <a-tag class="app-no-drag" v-for="item in menuItems" :key="item.key"
          :class="['menu-icon-btn', selectedKeys.includes(item.key) ? 'selected' : '', isShaking && shakingKey === item.key ? 'shake-animation' : '']" 
          @click="selectMenuItem(item)"
          :bordered="false" :color="selectedKeys.includes(item.key) ? undefined : undefined">
          <component :is="item.icon" v-if="!item.isIconFont" />
          <span v-else :class="['icon', 'iconfont', item.iconClass]"></span>
        </a-tag>
      </div>

      <div class="collapsed-button-container">
        <a-tag class="menu-icon-btn sys" @click="handleThemeToggle" :bordered="false">
          <span class="icon iconfont" :class="bot.theme === 'dark' ? 'light' : 'dark'"></span>
        </a-tag>
        <a-tag class="menu-icon-btn sys" @click="openSettings" :bordered="false"
          :class="[selectedKeys.includes('SystemSetting') ? 'selected' : '', isShaking && shakingKey === 'SystemSetting' ? 'shake-animation' : '']"
          :color="selectedKeys.includes('SystemSetting') ? undefined : undefined" title="快速设置">
          <setting-outlined />
        </a-tag>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { MessageOutlined, SettingOutlined } from '@ant-design/icons-vue'
import { useAiBot } from "@/components/store/bot";
import { useGptStore } from "@/components/store/gpt";
import { theme } from 'ant-design-vue';

const { useToken } = theme;
const { token } = useToken();
let bot = useAiBot();
const gpt = useGptStore();
const selectedKeys = ref<string[]>([bot.ui]);
const isShaking = ref(false);
const shakingKey = ref<string>('');

const menuItems = [
  {
    key: 'AiBotUI',
    title: 'AI 助手',
    icon: MessageOutlined,
    isIconFont: false,
    iconClass: ''
  }
];

// 开发模式下，添加 Debug 菜单
if (import.meta.env.MODE == 'development') {

  menuItems.push( {
    key: 'AiWriterUI',
    title: 'Writer 助手',
    icon: null,
    isIconFont: true,
    iconClass: 'zhulangxiezuo-'
  })
  menuItems.push({
    key: 'AiMusicUI',
    title: 'Music 助手',
    icon: null,
    isIconFont: true,
    iconClass: 'yinle'
  })
  menuItems.push({
    key: 'AiPaintingUI',
    title: 'Painting 助手',
    icon: null,
    isIconFont: true,
    iconClass: 'huihuachuangzuo'
  })

  menuItems.push({
    key: 'TweetUI',
    title: '推文助手',
    icon: null,
    isIconFont: true,
    iconClass: 'xiezuo'
  })

  menuItems.push({
    key: 'AiBotDebugUI',
    title: 'Debug',
    icon: null,
    isIconFont: true,
    iconClass: 'debug'
  })
}

function selectMenuItem(item: any) {
  // 检查是否正在回复消息且不是当前选中的项
  if (gpt.ui.replying && !selectedKeys.value.includes(item.key)) {
    triggerShake(item.key);
    return;
  }
  
  selectedKeys.value = [item.key];
  bot.ui = item.key;
}

function openSettings() {
  // 检查是否正在回复消息且不是当前选中的项
  if (gpt.ui.replying && !selectedKeys.value.includes('SystemSetting')) {
    triggerShake('SystemSetting');
    return;
  }
  
  bot.ui = 'SystemSetting';
  selectedKeys.value = ['SystemSetting'];
}

// 触发抖动动画
function triggerShake(key: string) {
  isShaking.value = true;
  shakingKey.value = key;
  setTimeout(() => {
    isShaking.value = false;
    shakingKey.value = '';
  }, 250);
}

const handleThemeToggle = () => {
  const newTheme = bot.theme === 'light' ? 'dark' : 'light';
  bot.setTheme(newTheme);
};
</script>

<style>
.menu-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  /*   border-right: 1px solid v-bind('token.colorFill');  */
  /* 添加浅灰色右边框 */
}

.tag-menu {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.menu-icon-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: none;
  background: transparent;
  color: v-bind('token.colorTextSecondary');
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s;
  margin: 4px 0;
  padding: 0;
  font-size: 16px;
}

.menu-icon-btn:hover {
  background-color: v-bind('token.colorFillTertiary');
}

.menu-icon-btn.selected {
  color: white;
  background-color: v-bind('token.colorPrimary');
}

.collapsed-button-container {
  position: relative;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.collapsed-button-container .sys {
  margin: 4px 0;
  height: 36px;
}

/* 抖动动画 */
@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  16.67% {
    transform: translateX(-2px);
  }
  33.33% {
    transform: translateX(2px);
  }
  50% {
    transform: translateX(-2px);
  }
  66.67% {
    transform: translateX(2px);
  }
  83.33% {
    transform: translateX(-2px);
  }
}

.shake-animation {
  animation: shake 0.25s ease-in-out;
}
</style>