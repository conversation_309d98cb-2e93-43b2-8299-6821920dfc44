<template>
  <a-layout>
    <a-layout-sider v-model:collapsed="bot.collapsed" collapsible :trigger="null" width="190" theme="light"
                    :collapsedWidth="50">
      <MenuBar/>
    </a-layout-sider>

    <a-layout>
      <a-layout-header class="app-header app-drag">
        <WindowHeader/>
      </a-layout-header>
      <a-layout-content class="layout-theme">
        <div class="h-full flex flex-col">
          <component :is="bot.ui"/>
        </div>
      </a-layout-content>
    </a-layout>
  </a-layout>
</template>

<script setup lang="ts">
import {useAiBot} from "@/components/store/bot";
import WindowHeader from "@/components/desktop/WindowHeader.vue";
import {onMounted, onUnmounted, ref} from 'vue';
import MenuBar from "./MenuBar.vue";
import {useGlobalSetting} from "@/components/store/bot-global-setting";
import {theme} from 'ant-design-vue';

const {useToken} = theme;
const {token} = useToken();
const bot = useAiBot()
let globalSetting = useGlobalSetting();

// 计算侧边栏宽度为窗口宽度的五分之一
const siderWidth = ref(window.innerWidth / 5);

// 监听窗口大小变化
const handleResize = () => {
  // 使用 requestAnimationFrame 优化性能
  requestAnimationFrame(() => {
    siderWidth.value = window.innerWidth / 5;
  });
};

// 使用防抖优化窗口大小变化的处理
const debouncedResize = (() => {
  let timer: ReturnType<typeof setTimeout> | null = null;
  return () => {
    if (timer) clearTimeout(timer);
    timer = setTimeout(handleResize, 100);
  };
})();

onMounted(() => {
  globalSetting.getSetting()
  window.addEventListener('resize', debouncedResize, {passive: true});
});

onUnmounted(() => {
  window.removeEventListener('resize', debouncedResize);
});
</script>

<style>
.app-header * {
  cursor: default !important;
  user-select: none !important;
}

.app-header {
  height: 30px !important;
  padding: 0 !important;
  cursor: default !important;
  user-select: none !important;
  transition: all 0.3s ease;
  background: v-bind('token.colorBgContainer') !important;
  /* border-bottom: 1px solid v-bind('token.colorBorder'); */
}

.layout-theme {
  background: v-bind('token.colorBgContainer') !important;
}

/* 确保滚动条不占用空间 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

::-webkit-scrollbar-track {
  background: transparent;
}
</style>