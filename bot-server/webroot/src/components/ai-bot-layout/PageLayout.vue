<template>
  <div class="page-container">
    <main class="page-content">
      <slot></slot>
    </main>
  </div>
</template>

<script setup lang="ts">
import { theme } from 'ant-design-vue';

const { useToken } = theme;
const { token } = useToken();
</script>

<style scoped>
.page-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  border: 1px solid v-bind('token.colorBorder');
  border-top-left-radius: 10px;
}

.page-content {
  flex: 1;
  overflow: hidden;
}
</style>
