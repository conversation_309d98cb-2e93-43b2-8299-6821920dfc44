<template>
  <PageLayout class="aibot">
    <div class="flex size-full">
      <div class="size-full flex">
        <!-- 左侧会话列表 -->
        <!-- <ResizableConversationPanel v-if="ctx.ui.showChat" /> -->
        <!-- 右侧聊天界面 -->
        <div class="flex-1 h-full flex flex-row justify-center">
          <div class="flex h-full" style="width: 95%;">
            <SplitPanel v-if="ctx.ui.showChat" v-model:bottomHeight="bottomPanelHeight" :min-bottom="30"
              :max-bottom="50" :panel-ratio="bottomPanelHeight" class="size-full split-panel-theme">
              <!--    消息展示面板    -->
              <template #top>
                <MessagePanel />
              </template>
              <!--     消息输入面板     -->
              <template #bottom>
                <div class="size-full flex flex-col">
                  <div class="my-auto flex flex-col" style="width: 100%;height: 80%;">
                    <ChatEditorPlus @send="send" @max-editor="maxInput" @update:panelRatio="handlePanelRatio" />
                  </div>
                </div>

              </template>
            </SplitPanel>
            <div v-else class="welcome-container">
              <WelcomePanel />
            </div>
          </div>

        </div>
      </div>
    </div>
    <PluginConfigDrawer />
  </PageLayout>
</template>

<script setup lang="ts">
import { onMounted, ref, onBeforeUnmount } from "vue";
import { useGptStore } from "@/components/store/gpt";
import MessagePanel from "@/components/ai-bot-message/MessagePanel.vue";
import ChatEditorPlus from "@/components/ai-bot-editor/editor/ChatEditorPlus.vue";
import { AiBot } from "@/components/ai-bot-interface/impl/base/aibot";
import PluginConfigDrawer from "@/components/ai-bot-plugins/plugin-config/PluginConfigDrawer.vue";
import WelcomePanel from "./WelcomePanel.vue";
import { AiPlugin } from "@/components/common/model/enum";
import { useAiPluginStore } from "@/components/store/plugin";
import { IsEmpty } from "@/components/common/chatutils";
import emitter from "@/plugins/event";
import { ScrollToBottom, TriggerSendMessage } from "@/plugins/evenKey";
import SplitPanel from "@/components/ai-bot-widget/SplitPanel.vue";
import { useAiBot } from "../store/bot";
import ResizableConversationPanel from "@/components/ai-bot-conversation/ResizableConversationPanel.vue";
import PageLayout from "@/components/ai-bot-layout/PageLayout.vue";

const ctx = useGptStore()
const bot = useAiBot()
const bottomPanelHeight = ref(25)


/*
* @description: 消息编辑器发送消息
* */
async function send(message: string) {
  let pluginStore = useAiPluginStore();
  switch (pluginStore.currentPlugin.code) {
    case AiPlugin.BotPlugin:
      let bot = new AiBot()
      await bot.Chat(message)
      break
    case AiPlugin.ProgrammingPlugin:
      break
    case AiPlugin.KnowledgePlugin:
      break
  }
}

/*
* @description 编辑器最大化事件触发
* */
function maxInput() {
  if (bottomPanelHeight.value !== 100) {
    bottomPanelHeight.value = 100
    return
  }
  bottomPanelHeight.value = 25
}

/*
* @description: 处理面板比例更新
* */
function handlePanelRatio(ratio: number) {
  bottomPanelHeight.value = ratio
}

// 监听发送消息事件
function setupEventListeners() {
  emitter.on(TriggerSendMessage, (message: string) => {
    send(message);
  });
}

// 移除事件监听器
function removeEventListeners() {
  emitter.off(TriggerSendMessage);
}

onMounted(() => {
  if (!IsEmpty(ctx.CurrentChat.Current)) {
    // 设置加载 消息记录
    ctx.SetCurrentChat(ctx.CurrentChat.Current)
    setTimeout(() => {
      emitter.emit(ScrollToBottom)
    }, 300)
  }

  // 设置事件监听
  setupEventListeners();
})

onBeforeUnmount(() => {
  // 清理事件监听
  removeEventListeners();
})
</script>

<style scoped>
.aibot {}

.split-panel-theme {
  background-color: transparent;
  overflow: visible;
}

.welcome-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: visible;
}

/* 添加新的样式 */
.flex {
  overflow: visible !important;
}

.h-full {
  overflow: visible !important;
}
</style>