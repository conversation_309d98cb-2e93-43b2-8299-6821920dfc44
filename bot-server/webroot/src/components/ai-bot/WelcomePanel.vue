<template>
  <div class="size-full flex flex-col justify-center items-center">
    <div class="welcome-panel">
      <div class="welcome-content">
        <p class="welcome-text">请选择一个聊天会话开始对话，或创建新的会话。</p>
      </div>
      
      <!-- 输入框居中显示 -->
      <div class="editor-container">
        <SimpleInput @send="handleMessageSend" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {ref} from 'vue';
import SimpleInput from './SimpleInput.vue';
import {useGptStore} from "@/components/store/gpt";
import {useRouter} from 'vue-router';
import {createConversation, getConversation} from "@/components/common/manageRequest";
import emitter from "@/plugins/event";
import {TriggerSendMessage} from "@/plugins/evenKey";

const ctx = useGptStore();
const router = useRouter();

// 处理消息发送
const handleMessageSend = async (message: string) => {
  if (!message.trim()) return;
  
  // 从消息中提取标题
  const title = extractTitle(message);
  
  try {
    // 保存消息，以便稍后发送
    const messageToSend = message;
    
    // 创建新会话
    const conversationId = await createNewConversation(title);
    
    // 更新会话列表并选中新创建的会话
    await updateAndSelectConversation(conversationId);
    
    // 等待导航和会话选择完成后再触发消息发送
    setTimeout(() => {
      // 使用事件触发消息发送
      emitter.emit(TriggerSendMessage, messageToSend);
    }, 300); // 300ms延迟确保会话已经切换完成
  } catch (error) {
    console.error('创建会话失败:', error);
  }
};

// 从消息中提取标题
const extractTitle = (message: string): string => {
  const maxLength = 20;
  const content = message.trim();
  
  if (content.length <= maxLength) {
    return content;
  }
  
  // 查找前20个字符内的第一个标点符号位置
  const punctuations = ['.', '。', '!', '！', '?', '？', ',', '，', ';', '；', ':', '：'];
  let endIndex = maxLength;
  
  for (let i = 0; i < Math.min(maxLength, content.length); i++) {
    if (punctuations.includes(content[i])) {
      endIndex = i + 1;
      break;
    }
  }
  
  return content.substring(0, endIndex) + (content.length > endIndex ? '...' : '');
};

// 创建新会话
const createNewConversation = async (title: string): Promise<string> => {
  const result = await createConversation(title);
  if (result.code == 200) {
    return result.data;
  } else {
    throw new Error(result.msg || '创建会话失败');
  }
};

// 更新会话列表并选中会话
const updateAndSelectConversation = async (conversationId: string) => {
  try {
    // 获取最新的会话列表
    const conversations = await getConversation();
    
    // 更新会话列表
    ctx.setConversation(conversations);
    
    // 如果当前正在回复中，不做选择操作
    if (ctx.ui.replying) {
      return;
    }
    
    // 直接使用SetCurrentChatById选中会话，与ConversationUI.vue中的handleSelect保持一致
    ctx.SetCurrentChatById(conversationId);
  } catch (error) {
    console.error('选择会话失败:', error);
  }
};
</script>

<style scoped>
.welcome-panel {
  width: 100%;
  max-width: 800px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  user-select: none;
  gap: 3rem;
}

.welcome-content {
  text-align: center;
}

.welcome-title {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 1rem;
  background: linear-gradient(45deg, #2196f3, #4caf50);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.welcome-text {
  font-size: 1.1rem;
  color: #666;
  line-height: 1.6;
}

/* 编辑器容器样式 - 居中显示 */
.editor-container {
  width: 100%;
  max-width: 600px;
  min-height: 60px;
}
</style>
