<template>
  <div class="simple-input-container">
    <div class="input-wrapper">
      <textarea
        ref="inputRef"
        v-model="inputValue"
        class="simple-input"
        placeholder="输入您的问题，按 Enter 发送，Shift + Enter 换行..."
        @keydown="handleKeydown"
        @input="handleInput"
        rows="1"
      />
      <button
        class="send-button"
        :disabled="!inputValue.trim()"
        @click="handleSend"
      >
        <svg class="send-icon" viewBox="0 0 24 24" fill="currentColor">
          <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
        </svg>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue';

const emits = defineEmits({
  send: (message: string) => true,
});

const inputRef = ref<HTMLTextAreaElement>();
const inputValue = ref('');

// 处理键盘事件
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    // Enter 键发送消息
    event.preventDefault();
    handleSend();
  } else if (event.key === 'Enter' && event.shiftKey) {
    // Shift + Enter 换行
    // 让默认行为执行（插入换行符）
    nextTick(() => {
      adjustHeight();
    });
  }
};

// 处理输入事件
const handleInput = () => {
  adjustHeight();
};

// 自动调整高度
const adjustHeight = () => {
  if (inputRef.value) {
    inputRef.value.style.height = 'auto';
    inputRef.value.style.height = `${Math.min(inputRef.value.scrollHeight, 120)}px`;
  }
};

// 发送消息
const handleSend = () => {
  const message = inputValue.value.trim();
  if (message) {
    emits('send', message);
    inputValue.value = '';
    nextTick(() => {
      adjustHeight();
      inputRef.value?.focus();
    });
  }
};

// 聚焦输入框
const focus = () => {
  nextTick(() => {
    inputRef.value?.focus();
  });
};

// 暴露方法给父组件使用
defineExpose({
  focus,
});
</script>

<style scoped>
.simple-input-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 1rem;
}

.input-wrapper {
  position: relative;
  width: 100%;
  max-width: 800px;
  display: flex;
  align-items: flex-end;
  gap: 0.5rem;
  padding: 0.75rem;
  background: #ffffff;
  border: 1px solid #e1e5e9;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.2s ease;
}

.input-wrapper:focus-within {
  border-color: #2196f3;
  box-shadow: 0 2px 12px rgba(33, 150, 243, 0.15);
}

.simple-input {
  flex: 1;
  min-height: 24px;
  max-height: 120px;
  padding: 0;
  border: none;
  outline: none;
  resize: none;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  background: transparent;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.simple-input::placeholder {
  color: #999;
}

.simple-input::-webkit-scrollbar {
  width: 4px;
}

.simple-input::-webkit-scrollbar-track {
  background: transparent;
}

.simple-input::-webkit-scrollbar-thumb {
  background: #ddd;
  border-radius: 2px;
}

.simple-input::-webkit-scrollbar-thumb:hover {
  background: #bbb;
}

.send-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: #2196f3;
  border: none;
  border-radius: 8px;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.send-button:hover:not(:disabled) {
  background: #1976d2;
  transform: translateY(-1px);
}

.send-button:active:not(:disabled) {
  transform: translateY(0);
}

.send-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.send-icon {
  width: 16px;
  height: 16px;
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .input-wrapper {
    background: #2a2a2a;
    border-color: #404040;
  }
  
  .input-wrapper:focus-within {
    border-color: #2196f3;
    box-shadow: 0 2px 12px rgba(33, 150, 243, 0.25);
  }
  
  .simple-input {
    color: #e0e0e0;
  }
  
  .simple-input::placeholder {
    color: #888;
  }
  
  .simple-input::-webkit-scrollbar-thumb {
    background: #555;
  }
  
  .simple-input::-webkit-scrollbar-thumb:hover {
    background: #666;
  }
}
</style> 