import {Result, Tree} from "@/components/common/model/system";
import {AppChatKnowledgeFile, AppChatKnowledgeInstance, LLmMole} from "@/components/common/model/model";

import axiosForServer from "@/plugins/axiosForServer";
import {MessageItem, MessageScene, RoleType} from "@/components/common/model/chat";


/*
* @conversationID 会话 id
* @replyMsgID 回复消息的消息 id
* @platformID  当前 llm 模型平台id
* @pluginID 插件id
* @value 发送的消息
* */
export function send(chat: BaseChat) {
    return new Promise<Result<MessageItem>>(resolve => {
        axiosForServer.post<Result<MessageItem>>("/api/bot/chat/send", chat).then(({data}) => {
            resolve(data)
        })
    })
}

export function sendClear(chat: BaseChat) {
    return new Promise<Result<MessageItem>>(resolve => {
        axiosForServer.post<Result<MessageItem>>("/api/bot/chat/clear", chat).then(({data}) => {
            resolve(data)
        })
    })
}


export function deleteModel(name: string) {
    return new Promise<Result<any>>(resolve => {
        axiosForServer.post<Result<any>>("/api/chat/user/model/delete", {
            name: name
        })
            .then(({data}) => {
                resolve(data)
            })
    })
}

export function getBaseModel() {
    return new Promise<LLmMole[]>(resolve => {
        axiosForServer.get<Result<LLmMole[]>>("/api/chat/manage/modelList").then(({data}) => {
            if (data.code === 200) {
                if (data.data == null) {
                    resolve([])
                    return;
                }
                resolve(data.data)
            }
        })
    })
}


export function getFiles(pid: string) {
    return new Promise<Tree<AppChatKnowledgeFile>[]>(resolve => {
        axiosForServer.get<Result<Tree<AppChatKnowledgeFile>[]>>("/api/chat/knowledge/file/list", {
            params: {
                pid: pid
            }
        }).then(({data}) => {
            if (data.code === 200) {
                if (data.data == null) {
                    resolve([])
                    return;
                }
                resolve(data.data)
            }
        })
    })
}

export function createFiles(data: any) {
    return new Promise<Result<any>>(resolve => {
        axiosForServer.post<Result<any>>("/api/chat/knowledge/file/create", data, {})
            .then(({data}) => {
                resolve(data)
            })
    })
}

export function getKnowledge() {
    return new Promise<AppChatKnowledgeInstance[]>(resolve => {
        axiosForServer.get<Result<any[]>>("/api/chat/knowledge/list").then(({data}) => {
            if (data.code === 200) {
                if (data.data == null) {
                    resolve([])
                    return;
                }
                resolve(data.data)
            }
        })
    })
}

export async function genKnowledge(name: string, files: string[]) {
    let data = {
        name: name,
        files: files,
    }
    let response = await fetch('http://localhost:8080/api/chat/knowledge/gen', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
    });
    return response;
}


export function delKnowledge(id: string) {
    return new Promise<Result<any>>(resolve => {
        axiosForServer.post<Result<any>>("/api/chat/knowledge/del", {
            Id: id
        })
            .then(({data}) => {
                resolve(data)
            })
    })
}






