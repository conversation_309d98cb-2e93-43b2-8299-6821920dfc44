import {MessageScene} from "@/components/common/model/chat";

export type LLmHandler = (data: LLMData) => void;

export interface LLMData {
    data: any
    content: string
    type: DataType
    scene: string // 表示流数据下发的场景类型，根据场景选择不同的渲染器，或者渲染组件
    status: LLmStatus
    // 配合 LLmStatus 处理更新 message 状态
    MessageType?: MessageScene // 消息类型，用于区分消息类型，比如：用户消息、机器人消息、系统消息
    code: number
    msg: string
}

// 流消息状态
export enum LLmStatus {
    Error = "Error", // 错误
    Begin = "Begin", // 开始
    Data = "Data",   // 数据 状态
    Render = "Render", // 渲染
    Finish = "Finish", // 结束
}

// 流消息类型
// 标记每条流数据的数据作用
export enum DataType {
    Render = "Render", // 渲染 初始化渲染
    Scene = "Scene", // 场景 场景消息
}

export class LLMStream {
    private response: Response;

    /*
    * Begin 数据
    * */
    protected Begin: LLmHandler = (data: LLMData) => {
    };

    /*
    * Data 数据
    * */
    protected Data: LLmHandler = (data: LLMData) => {
    };

    /*
    * Finish 数据
    * */
    protected Finish: LLmHandler = (data: LLMData) => {
    };

    /*
    * Error 数据
    * */
    protected Error: LLmHandler = (data: LLMData) => {
    };

    /*
    * Cancel 操作 data 将传递一个 null
    * */
    protected Cancel: LLmHandler = (data: LLMData) => {
    };


    protected stop: boolean = false;

    constructor(response: Response) {
        this.response = response;
    }

    public setBegin(handler: LLmHandler) {
        this.Begin = handler
    }

    public setData(handler: LLmHandler) {
        this.Data = handler
    }

    public setError(handler: LLmHandler) {
        this.Error = handler
    }

    public setFinish(handler: LLmHandler) {
        this.Finish = handler
    }

    public setCancel(handler: LLmHandler) {
        this.Cancel = handler
    }

    public Stop() {
        this.stop = true;
    }


    public async listen() {
        const reader = this.response.body.getReader()
        const decoder = new TextDecoder('utf-8')
        try {
            while (true) {
                // 手动停止
                if (this.stop) {
                    await reader.cancel()
                    // 执行结束
                    this.Cancel(null)
                    return
                }
                const {done, value} = await reader.read()
                if (done) {
                    break
                }
                let decodedData = decoder.decode(value);
                let accumulatedData = ""
                accumulatedData += decodedData
                for (const line of accumulatedData.split('\n')) {
                    if (line) {
                        try {
                            let parsed: LLMData = JSON.parse(line)
                            // 正常流消息处理
                            switch (parsed.status) {
                                case LLmStatus.Begin:
                                    this.Begin(parsed)
                                    break
                                case LLmStatus.Data:
                                    this.Data(parsed)
                                    break
                                case LLmStatus.Error:
                                    this.Error(parsed)
                                    break
                                case LLmStatus.Finish:
                                    this.Finish(parsed)
                                    break
                            }
                        } catch (error) {
                            console.error(error)
                            continue
                        }
                    }
                }
            }
        } catch (e) {
            console.error(e)
            return
        }
        this.stop = false
    }
}