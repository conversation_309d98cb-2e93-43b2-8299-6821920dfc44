import {getGiteeFreeModelPage} from "@/components/common/manageRequest";

/**
 * 计算颜色的相对亮度
 * 使用 WCAG 2.0 的相对亮度公式
 * https://www.w3.org/TR/WCAG20/#relativeluminancedef
 */
function getLuminance(r: number, g: number, b: number): number {
    const [rs, gs, bs] = [r, g, b].map(c => {
        c = c / 255;
        return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });
    return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
}

/**
 * 判断颜色是否为深色
 * 使用多种方法综合判断
 * @param r 红色通道值 (0-255)
 * @param g 绿色通道值 (0-255)
 * @param b 蓝色通道值 (0-255)
 * @returns boolean true表示深色，false表示浅色
 */
function isDarkColor(r: number, g: number, b: number): boolean {
    // 方法1: 使用 HSP 颜色模型
    // http://alienryderflex.com/hsp.html
    const hsp = Math.sqrt(
        0.299 * (r * r) +
        0.587 * (g * g) +
        0.114 * (b * b)
    );

    // 方法2: 使用 YIQ 颜色空间
    const yiq = ((r * 299) + (g * 587) + (b * 114)) / 1000;

    // 方法3: 使用相对亮度
    const luminance = getLuminance(r, g, b);

    // 综合判断
    // HSP 阈值设为 127.5
    const isDarkHSP = hsp < 127.5;
    // YIQ 阈值设为 128
    const isDarkYIQ = yiq < 128;
    // 亮度阈值设为 0.5
    const isDarkLuminance = luminance < 0.5;

    // 如果至少两种方法判断为深色，则认为是深色
    const darkCount = Number(isDarkHSP) + Number(isDarkYIQ) + Number(isDarkLuminance);

    return darkCount >= 2;
}

/**
 * 获取屏幕指定位置的颜色信息
 * @param x 屏幕X坐标
 * @param y 屏幕Y坐标
 * @returns Promise<{r: number, g: number, b: number, a: number} | null>
 */
export async function getScreenColorAt(x: number, y: number): Promise<{
    r: number,
    g: number,
    b: number,
    a: number
} | null> {
    try {
        // 创建一个隐藏的canvas
        const canvas = document.createElement('canvas');
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
        const context = canvas.getContext('2d', {willReadFrequently: true});

        if (!context) {
            console.error('无法创建canvas context');
            return null;
        }

        // 使用html2canvas捕获整个窗口
        const html2canvas = (await import('html2canvas')).default;
        const screenshot = await html2canvas(document.body, {
            backgroundColor: null,
            logging: false,
            width: window.innerWidth,
            height: window.innerHeight,
            x: window.scrollX,
            y: window.scrollY,
            scale: 1,
        });

        // 获取指定位置的颜色
        const imageData = screenshot.getContext('2d')?.getImageData(x, y, 1, 1).data;

        if (!imageData) {
            return null;
        }

        const color = {
            r: imageData[0],
            g: imageData[1],
            b: imageData[2],
            a: imageData[3]
        };

        return color;
    } catch (error) {
        console.error('获取屏幕颜色失败:', error);
        return null;
    }
}

/**
 * 获取元素在窗口中的位置和颜色信息
 * @param element HTML元素或者元素选择器字符串
 */
export async function getElementPositionAndColor(elementOrSelector: HTMLElement | string): Promise<void> {
    try {
        let element: HTMLElement | null = null;

        // 处理选择器字符串
        if (typeof elementOrSelector === 'string') {
            element = document.querySelector(elementOrSelector);
            if (!element) {
                throw new Error(`未找到匹配的元素: ${elementOrSelector}`);
            }
        } else if (elementOrSelector instanceof HTMLElement) {
            element = elementOrSelector;
        } else {
            throw new Error('参数必须是 HTMLElement 或者有效的选择器字符串');
        }

        // 确保元素已挂载到 DOM
        if (!element.isConnected) {
            throw new Error('元素未挂载到 DOM');
        }

        // 获取元素的位置信息
        const rect = element.getBoundingClientRect();
        const position = {
            top: rect.top + window.scrollY,
            left: rect.left + window.scrollX,
            width: rect.width,
            height: rect.height
        };

        // 获取元素中心点在屏幕上的坐标
        const centerX = Math.floor(rect.left + rect.width / 2);
        const centerY = Math.floor(rect.top + rect.height / 2);

        // 获取中心点的颜色
        const color = await getScreenColorAt(centerX, centerY);

        if (color) {
            const isDark = isDarkColor(color.r, color.g, color.b);

            // 打印调试信息
            console.log('元素位置信息:', {
                position,
                windowPosition: {
                    top: rect.top,
                    left: rect.left,
                    bottom: rect.bottom,
                    right: rect.right
                },
                centerPoint: {
                    x: centerX,
                    y: centerY
                }
            });
            console.log('屏幕中心点颜色:', color);
            console.log('RGB颜色值:', `rgb(${color.r}, ${color.g}, ${color.b})`);
            console.log('颜色深浅:', isDark ? '深色' : '浅色');

            // 添加更详细的颜色分析信息
            const hsp = Math.sqrt(0.299 * (color.r * color.r) + 0.587 * (color.g * color.g) + 0.114 * (color.b * color.b));
            const yiq = ((color.r * 299) + (color.g * 587) + (color.b * 114)) / 1000;
            const luminance = getLuminance(color.r, color.g, color.b);

            console.log('颜色分析:', {
                'HSP值': hsp.toFixed(2),
                'YIQ值': yiq.toFixed(2),
                '相对亮度': luminance.toFixed(3)
            });
        }
    } catch (error) {
        console.error('获取元素位置和颜色信息失败:', error);
    }
}

/**
 * 根据输入的颜色返回一个对比色
 * 如果输入是深色，返回浅色；如果输入是浅色，返回深色
 * @param r 红色通道值 (0-255)
 * @param g 绿色通道值 (0-255)
 * @param b 蓝色通道值 (0-255)
 * @returns {{r: number, g: number, b: number}} 返回对比色的 RGB 值
 */
export function getContrastColor(r: number, g: number, b: number): { r: number, g: number, b: number } {
    const isDark = isDarkColor(r, g, b);

    if (isDark) {
        // 如果是深色，返回浅色（白色偏灰）
        return {
            r: 240,
            g: 240,
            b: 240
        };
    } else {
        // 如果是浅色，返回深色（黑色偏灰）
        return {
            r: 32,
            g: 32,
            b: 32
        };
    }
}

// 为了方便使用，添加一个接收十六进制颜色的版本
/**
 * 根据十六进制颜色值返回对比色
 * @param hexColor 十六进制颜色值（例如：'#FFFFFF' 或 'FFFFFF'）
 * @returns {string} 返回十六进制格式的对比色
 */
export function getContrastColorFromHex(hexColor: string): string {
    // 移除可能存在的 # 前缀
    const hex = hexColor.replace('#', '');

    // 将十六进制转换为 RGB
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);

    // 获取对比色
    const contrastColor = getContrastColor(r, g, b);

    // 转换回十六进制格式
    const hexR = contrastColor.r.toString(16).padStart(2, '0');
    const hexG = contrastColor.g.toString(16).padStart(2, '0');
    const hexB = contrastColor.b.toString(16).padStart(2, '0');

    return `#${hexR}${hexG}${hexB}`;
}

/**
 * 模型服务接口
 */
interface ModelService {
    id: number;
    ident: string;
    model_info: {
        id: number;
        name: string;
        namespace: string;
        mirror_namespace: string;
        mirror_url: string;
    };
}

/**
 * 操作接口
 */
interface FreeModel {
    type: string
    model: string
}


/**
 * 获取Gitee AI页面中的限时免费模型
 * @returns {Promise<Array<{name: string, ident: string}>>} 返回限时免费模型信息数组
 */
export async function getLimitedFreeAIModels(): Promise<any[]> {
    return new Promise<string[]>(async resolve => {
        try {
            // 获取页面HTML内容
            const html = await getGiteeFreeModelPage();

            if (!html) {
                console.error('获取页面内容失败');
                resolve([])
            }

            // 创建临时DOM解析HTML
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');

            // 创建结果数组
            const limitedFreeModels: any[] = [];

            // 1. 查找所有包含模型信息的卡片
            // 这里扩展选择器以捕获所有可能的模型卡片
            const allCards = doc.querySelectorAll('a[href^="/serverless-api?model="]');
            allCards.forEach(element => {
                try {
                    let type = element.parentNode.parentNode.firstChild.textContent
                    console.log(element.parentNode.parentNode.firstChild.textContent)
                    let querySelector = element.querySelector('.items-center');
                    if (querySelector.lastChild.lastChild) {
                        let content = querySelector.lastChild.lastChild.textContent;
                        if (content.match(/^限时免费$/)) {
                            let model = querySelector.lastChild.firstChild.textContent.trim()
                            limitedFreeModels.push({
                                type,
                                model
                            })
                        }
                    }

                } catch (error) {
                    console.error('解析模型卡片时出错:', error);
                }
            });
            resolve(limitedFreeModels);
        } catch (error) {
            console.error('获取限时免费模型失败:', error);
            resolve([]);
        }
    })
}
