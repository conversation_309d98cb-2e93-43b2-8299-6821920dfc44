import axiosForServer from "@/plugins/axiosForServer"
import { PaintingMessage } from "./model/painting"
import { Result } from "./model/system"

// 绘画配置相关接口定义
export interface PaintingConfigDTO {
    id?: string;
    config?: string;
    createdAt?: string;
    updatedAt?: string;
    deletedAt?: string;
}

export interface PaintingConfigQueryDTO {
    id?: string;
}

export interface PaintingConfigPO {
    id: string;
    config: string;
    createdAt: string;
    updatedAt: string;
    deletedAt: string | null;
}

/*
* 发送绘画请求
* */
export function sendPainting(chat: PaintingDTO) {
    return new Promise<Result<PaintingMessage>>(resolve => {
        axiosForServer.post<Result<PaintingMessage>>("/api/bot/chat/send", chat).then(({data}) => {
            resolve(data)
        })
    })
}

/*
* 查询绘画配置列表
* */
export function queryPaintingConfigs() {
    return new Promise<Result<PaintingConfigPO[]>>(resolve => {
        axiosForServer.get<Result<PaintingConfigPO[]>>("/api/bot/painting-configs").then(({data}) => {
            resolve(data)
        })
    })
}

/*
* 根据条件查询绘画配置
* */
export function queryPaintingConfigsByCondition(query: PaintingConfigQueryDTO) {
    return new Promise<Result<PaintingConfigPO[]>>(resolve => {
        axiosForServer.post<Result<PaintingConfigPO[]>>("/api/bot/painting-configs/query", query).then(({data}) => {
            resolve(data)
        })
    })
}

/*
* 创建绘画配置
* */
export function createPaintingConfig(config: PaintingConfigDTO) {
    return new Promise<Result<PaintingConfigPO>>(resolve => {
        axiosForServer.post<Result<PaintingConfigPO>>("/api/bot/painting-configs/create", config).then(({data}) => {
            resolve(data)
        })
    })
}

/*
* 更新绘画配置
* */
export function updatePaintingConfig(config: PaintingConfigDTO) {
    return new Promise<Result<PaintingConfigPO>>(resolve => {
        axiosForServer.post<Result<PaintingConfigPO>>("/api/bot/painting-configs/update", config).then(({data}) => {
            resolve(data)
        })
    })
}

/*
* 删除绘画配置
* */
export function deletePaintingConfig(config: PaintingConfigDTO) {
    return new Promise<Result<any>>(resolve => {
        axiosForServer.post<Result<any>>("/api/bot/painting-configs/delete", config).then(({data}) => {
            resolve(data)
        })
    })
}

/*
* 获取单个绘画配置
* */
export function getPaintingConfig(config: PaintingConfigDTO) {
    return new Promise<Result<PaintingConfigPO>>(resolve => {
        axiosForServer.post<Result<PaintingConfigPO>>("/api/bot/painting-configs/get", config).then(({data}) => {
            resolve(data)
        })
    })
}

/*
* 使用示例：
* 
* // 1. 查询所有配置
* const configs = await queryPaintingConfigs();
* 
* // 2. 创建新配置
* const newConfig: PaintingConfigDTO = {
*     config: JSON.stringify({
*         name: "我的配置",
*         promptTemplate: "一只可爱的小猫",
*         size: "1:1 (1024*1024)",
*         guidanceScale: 7.5,
*         negativePrompt: "模糊、低质量",
*         width: 1024,
*         height: 1024,
*         useCustomSize: false,
*         imageCount: 1,
*         numInferenceSteps: 20
*     })
* };
* const result = await createPaintingConfig(newConfig);
* 
* // 3. 更新配置
* const updateConfig: PaintingConfigDTO = {
*     id: "config-id",
*     config: JSON.stringify(updatedParams)
* };
* await updatePaintingConfig(updateConfig);
* 
* // 4. 删除配置
* await deletePaintingConfig({ id: "config-id" });
* 
* // 5. 获取单个配置
* const config = await getPaintingConfig({ id: "config-id" });
*/