import {ParsedMessage} from "@/components/common/model/chat";
import {DataType} from "@/components/common/model/enum";
import {McpMarkdown, MentionMarkdown} from "@/components/ai-bot-editor/editor/editor-plugins/CustomDataProcessor/augmentation";

export class MarkdownCustomConverter {

    /*
    * 提取消息中的 自定义 md 数据   
    * 提取消息中的 mcp 实例工具id
    * 提取消息中的 图片
    * 提取消息中的 视频
    * */
    static ExtractResourceData(md: string): ParsedMessage {
        const result: ParsedMessage = {
            md: md,
            images: [],
            video: [],
            mcp: []
        };
        // 寻找 :::mention ... ::: 模式的数据
        const mentionPattern = /:::mention\s+([\s\S]*?):::\n\r/g;
        // 替换所有mention匹配项
        result.text = md.replace(mentionPattern, (match, jsonContent) => {
            try {
                // 尝试解析JSON内容
                const jsonData: MentionMarkdown = JSON.parse(jsonContent.trim());

                // 如果有title属性，则替换为title的值
                if (jsonData && jsonData.dataType) {
                    switch (jsonData.dataType) {
                        case DataType.Image:
                            result.images.push(jsonData.resource)
                            return ' ' + jsonData.title + ' '
                    }
                }

                // 如果没有title属性，保留原始内容
                return match;
            } catch (error) {
                // 解析JSON出错，保留原始内容
                console.error('解析mention中的JSON数据失败:', error, jsonContent);
                return match;
            }
        });

        // 提取 mcp 实例工具id
        const mcpInstancePattern = /:::mcpinstance\s+([\s\S]*?):::\n\r/g;
        result.text = result.text.replace(mcpInstancePattern, (match, jsonContent) => {
            try {
                const jsonData: McpMarkdown = JSON.parse(jsonContent.trim());
                result.mcp.push(jsonData.id)
                return ''
            } catch (error) {
                return match;
            }
        });
        return result
    }
}