interface BaseChat {
    id?: string; // 消息id
    message?: string; // 消息内容
    images?: string[]; // 图片数据，存储的是服务器url资源
    role?: string; // 消息角色
    messageType?: string; // 消息类型
    replyMsgID?: string; // 回复的消息id
    conversationID?: string; // 会话id
    history?: any[]; // 消息列表
    stream?: boolean; // 是否流式响应
    pluginID?: string; // 平台id
    platformID?: string; // 平台id
    webSearch?: boolean; // 网络搜索
    thinking?: boolean; // 思考模式
}

interface PaintingDTO extends BaseChat {
    prompt: string;
    size?: string;
    guidanceScale?: number;
    negativePrompt?: string;
    width?: number;
    height?: number;
    imageCount?: number;
    numInferenceSteps?: number;
    promptTemplate?: string;
}
