export interface AppChatConversationItem {
    id?: string
    // 会话头像
    picture?: string
    // 会话标题
    title?: string
    // 用户id
    userID?: number
    // 最后一条会话消息的模型
    lastModel?: string
    // 最后一条会话消息
    lastMsg?: string
    // 会话创建时间
    updatedAt?: string
}

export interface Platform {
    id?: string
    type?: string
    name?: string
    icon?: string
    config?: string
    settings?: any
    configView?: string
    quickConfigView?: string
    current?: boolean
}

export interface LLmMole {

    ID?: string
    pid?: string
    // 模型展示名
    name?: string
    // 发送消息 ollama 请求所需要携带的model参数
    model?: string
    // 模型图标
    picture?: string
    size?: string
    modelDetails?: string
    isDownload?: boolean
    createTime?: string
    downloads?: boolean;
}

export interface ProgressResponse {
    status?: string
    digest?: string
    total: number
    completed?: number
}

export enum AppChatKnowledgeFileType {
    Folder = 0,
    File = 1,
}

export interface AppChatKnowledgeFile {
    ID: string
    check: boolean
    pid: string
    userId: string
    fileName: string
    filePath: string
    serverPath?: string
    createTime: string
    size?: string
    fileType: AppChatKnowledgeFileType
}


export interface AppChatKnowledgeInstance {
    ID?: string
    userId?: string
    knowledgeName?: string
    knowledgeFiles: string[]
    knowledgeDescription?: string
    knowledgeType?: string
    createTime?: string
    check: boolean
    bgColor?: string
}


//   对接 本地 ollama 模型返回数据


export interface OllamaModelResponse {
    name?: string
    model?: string
    modified_at?: string
    size?: number
    digest?: string
    details: OllamaModelDetails
}

export interface OllamaModelDetails {
    parent_model?: string
    format?: string
    family?: string
    families?: string[]
    parameter_size?: string
    quantization_level?: string
}

export interface OllamaDownload {
    model?: string
    isDownload?: boolean
    progress?: ProgressResponse
}


export interface Plugin {
    id: string
    name: string
    code: string
    icon: string
    platformID?: string
    model: string
    configView: string
    props: string
    status: boolean
    createTime: string
}


export interface ChatContext {
    conversationID: string // 聊天会话 id
    replyMsgID: string // 聊天回复消息的 id
    webSearch: boolean
    thinking: boolean
    mcp: string[]
}

export interface BotContext extends ChatContext {
    plugin: Plugin // 插件信息
    platform: Platform // 平台 id ,区别于 plugin 中的 platformID 独立使用的参数场景

}

export interface Model {
    name: string
    type: string
}

