/*
* 会话
* */


import {AppChatConversationItem} from "@/components/common/model/model";
import {LLMStream} from "@/components/common/stream/llm_stream";

export declare interface ConversationItem {
    conversationID?: string;
    conversationType?: SessionType;
    userID?: string;
    groupID?: string;
    showName?: string;
    faceURL?: string;
    latestMsg?: string;
    latestMsgSendTime?: number;
    draftText?: string;
    draftTextTime?: number;
    burnDuration?: number;
    msgDestructTime?: number;
    ex?: any;
}

/*
* 消息
* 
* */
export declare interface MessageItem {
    id?: string;
    type?: string; // 消息类型
    conversationID?: string; // 会话窗口
    messageType?: string; // 消息内容类型
    replyMsgID?: string;  // 回复消息的id
    userID?: string;  // 消息所属用户
    sendID?: string;  //发送者id
    receiveID?: string; // 消息接收者
    modelId?: string// 模型id
    role?: string; // 消息角色类型
    createTime?: string;  // 消息创建时间
    picture?: string // 消息对应头像
    content?: string // 消息内容 不同的消息类型对应不同的数据
    ex?: any; // 消息扩展信息，content 属性存储 MultipleStream 消息返回的数据


    // 一下字段 属于 客户端
    // 单一流消息 一般适用于 简单对话场景
    llmStream?: LLMStream
    // 存储多个流消息的响应 适用于处理多流消息的场景
    MultipleStream?: Map<string, any>
}


// 文本消息
export declare interface TextElem {
    content: string;
}

// MD消息
export declare interface AiBotElem {
    content: string;
    // 流式消息IO
    stream?: Response
}

// 定义消息
export declare interface CustomElem {
    content: string;
}

// MessageScene 聊天消息场景
export enum MessageScene {
    UserScene = "user",
    RenderScene = "render",
    ChatScene = "chat",
    ClearScene = "clear",
    AgentScene = "agent",
}

// 场景分类
export enum MainSceneType {
    Chat = "chat",
    Painting = "painting",
}


/*
* 编辑器数据类型
* */
export enum EditorDataType {
    // 文本
    Text = 1,
    // 代码块
    CodeBlock = 2,
    // 图片
    Image = 3,
    // 音频
    Audio = 4,
    // 视频
    Video = 5,
    // 表格
    Table = 6,

    File = 7,
}

export enum RoleType {
    User = "user",
    Assistant = "assistant"
}

export enum SessionType {
    Single = 1,
    Group = 3,
    WorkingGroup = 3,
    Notification = 4
}


export interface ConversationEntity {
    Conversation: AppChatConversationItem
    focused?: any
    active?: any
}

export interface MessageEntity {
    dataType: EditorDataType  // 编辑器数据类型
    data: any // 消息数据
}


export interface ParsedMessage {
    md?: string         // 原始内容 保留作为 ui渲染显示
    text?: string;      // 文本内容，用于发送到 LLM 需要一个相对干净文本
    images?: string[];  // 图片URL数组
    video?: string[]
    mcp?: string[] // mcp 实例工具id
}




