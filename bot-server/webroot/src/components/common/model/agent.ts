
/**
 * 消息类型
 */
export enum AgentItemType {
    Markdown = 'markdown',  // Markdown 消息
    Thinking = 'thinking',  // 思考
    McpCall = 'mcp',        // mcp调用
    Action = 'action',      // 动作调用
    Painting = 'painting',  // 绘画
}

/**
 * 消息状态
 */
export enum AgentItemStatus {
    Default = 'default',        // 默认状态
    Init = 'init',              // 初始化状态
    Start = 'start',            // 开始状态
    Running = 'running',        // 运行状态
    Pause = 'pause',            // 暂停状态
    Stop = 'stop',              // 停止状态
    Loading = 'loading',        // 加载状态
    Streaming = 'streaming',    // 流式状态
    Error = 'error',            // 错误状态
    Wait = "wait",              // 等待状态
    Finish = "finish",          // 完成状态
    Cancel = "cancel",          // 取消状态
    Timeout = "timeout",        // 超时状态
    Unknown = "unknown",        // 未知状态
    Failed = "failed",          // 失败状态
    Pending = "pending",        // 等待状态
    Success = "success",        // 成功状态
}

// AgentItem
// 定义了服务器统一返回流数据格式的标准
export interface AgentItem {
    type: AgentItemType;
    status: AgentItemStatus;
    mcpToolId: string; // mcp调用期间的随机数 同一个mcp之间的随机数保持一致
    id: string; // 消息的唯一标识符
    data: string;
}

export interface AgentAIPaintingItem extends AgentItem {
    
}


export interface TextContent {
    type: string; // Must be "text"
    // The text content of the message.
    text: string;
}
// It must have Type set to "image".
export interface ImageContent {
    type: string; // Must be "image"
    // The base64-encoded image data.
    data: string;
    // The MIME type of the image. Different providers may support different image types.
    mimeType: string;
}
