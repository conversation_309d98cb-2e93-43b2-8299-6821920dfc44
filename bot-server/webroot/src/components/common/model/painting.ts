import { LLMStream } from "../stream/llm_stream";
import { AgentItemStatus } from "./agent";

// 绘画功能消息定义
export declare interface PaintingMessage {
    id?: string;
    prompt?: string                      // 绘画提示词
    status?: AgentItemStatus             // 绘画状态 
    imageUrls?: string                   // 绘画图片URL 多个图片URL用逗号分隔
    CreatedAt?: string
    UpdatedAt?: string
    DeletedAt?: string
    // 单一流消息 一般适用于 简单对话场景
    llmStream?: LLMStream
    // 存储多个流消息的响应 适用于处理多流消息的场景
    MultipleStream?: Map<string, any>
}

export declare interface PaintingPromptConfig {
    id: string;
    config: string;
    createdAt: string;
    updatedAt: string;
    deletedAt: string;
}