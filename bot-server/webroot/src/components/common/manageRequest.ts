import {AppChatConversationItem, Platform, Plugin,} from "@/components/common/model/model";
import axiosForServer from "@/plugins/axiosForServer";
import {Result} from "@/components/common/model/system";
import {MessageItem} from "@/components/common/model/chat";

export function getPlugins() {
    return new Promise<Plugin[]>(resolve => {
        axiosForServer.get<Result<Plugin[]>>("/api/bot/plugins")
            .then(({data}) => {
                if (data.code === 200) {
                    if (data.data == null) {
                        resolve([])
                        return;
                    }
                    resolve(data.data)
                }
            })
    })
}

export function getUUID(): Promise<string> {
    return new Promise<string>(resolve => {
        axiosForServer.get<Result<string>>("/api/bot/uuid")
            .then(({data}) => {
                if (data.code === 200) {
                    resolve(data.data)
                }
            })
    })
}

export function getMessage(id: string) {
    return new Promise<MessageItem>(resolve => {
        axiosForServer.get<Result<MessageItem>>("/api/bot/conversations/message", {
            params: {
                id: id
            }
        }).then(({data}) => {
            if (data.code === 200) {
                if (data.data == null) {
                    resolve(null)
                    return;
                }
                resolve(data.data)
            }
        })
    })
}

export function updateMessage(id: string, content: string) {
    return new Promise<MessageItem>(resolve => {
        axiosForServer.post<Result<MessageItem>>("/api/bot/chat/saveAgent", {
            id: id,
            content: content
        }).then(({data}) => {
            if (data.code === 200) {
                if (data.data == null) {
                    resolve(null)
                    return;
                }
                resolve(data.data)
            }
        })
    })
}

export function getConversationMessage(id: string) {
    return new Promise<MessageItem[]>(resolve => {
        axiosForServer.get<Result<MessageItem[]>>("/api/bot/conversations/messages", {
            params: {
                id: id
            }
        }).then(({data}) => {
            if (data.code === 200) {
                if (data.data == null) {
                    resolve([])
                    return;
                }
                resolve(data.data)
            }
        })
    })
}

export function getConversation() {
    return new Promise<AppChatConversationItem[]>(resolve => {
        axiosForServer.get<Result<AppChatConversationItem[]>>('/api/bot/conversations').then(({data}) => {
            if (data.code === 200) {
                if (data.data == null) {
                    resolve([])
                    return;
                }
                resolve(data.data)
            }
        })
    })
}

export function createConversation(name: string) {
    return new Promise<Result<string>>(resolve => {
        axiosForServer.post<Result<string>>("/api/bot/conversations/create", {
            title: name
        })
            .then(({data}) => {
                resolve(data)
            })
    })
}

export function updateConversation(data: AppChatConversationItem) {
    return new Promise<AppChatConversationItem>(resolve => {
        axiosForServer.post<Result<AppChatConversationItem>>("/api/bot/conversations/update", data)
            .then(({data}) => {
                if (data.code == 200) {
                    resolve(data.data)
                } else {
                    resolve(null)
                }
            }).catch(error => {
            console.log(error)
        })
    })
}

export function delConversation(id: string) {
    return new Promise<Result<any>>(resolve => {
        axiosForServer.post<Result<any>>("/api/bot/conversations/delete", {
            Id: id
        })
            .then(({data}) => {
                resolve(data)
            })
    })
}

export function clearMessage(id: string) {
    return new Promise<Result<any>>(resolve => {
        axiosForServer.post<Result<any>>("/api/bot/conversations/messages/deletes", {
            id: id
        })
            .then(({data}) => {
                resolve(data)
            })
    })
}

export function deleteMessage(id: string) {
    return new Promise<Result<any>>(resolve => {
        axiosForServer.post<Result<any>>("/api/bot/conversations/messages/delete", {
            id: id
        })
            .then(({data}) => {
                resolve(data)
            })
    })
}

export function getPlatforms(): Promise<Platform[]> {
    return new Promise<Platform[]>(resolve => {
        axiosForServer.get<Result<Platform[]>>("/api/bot/platforms")
            .then(({data}) => {
                if (data.code === 200) {
                    if (data.data == null) {
                        resolve([])
                        return;
                    }
                    resolve(data.data)
                }
            })
    })
}

// ==================== 图片上传相关接口 ====================

// 图片上传结果接口
interface ImageUploadResult {
    url: string;     // 图片访问URL
}

// Base64图片上传请求参数接口
interface Base64UploadRequest {
    base64Data: string;  // base64编码的图片数据
    filename?: string;   // 文件名（可选）
}

/**
 * 上传base64格式的图片
 * @param base64Data - base64编码的图片数据
 * @param filename - 可选的文件名
 * @returns Promise<string> - 返回图片的访问URL
 */
export function uploadBase64Image(base64Data: string, filename?: string): Promise<string> {
    return new Promise<string>(resolve => {
        const requestData: Base64UploadRequest = {
            base64Data: base64Data,
            filename: filename
        };

        axiosForServer.post<Result<ImageUploadResult>>("/api/uploadImageBase64", requestData)
            .then(({data}) => {
                if (data.code === 200 && data.data) {
                    resolve(data.data.url);
                } else {
                    resolve("")
                }
            });
    });
}

/**
 * 上传文件（支持图片文件）
 * @param file - 要上传的文件对象
 * @returns Promise<string> - 返回图片的访问URL
 */
export function uploadFile(file: File): Promise<string> {
    const formData = new FormData();
    formData.append('file', file);

    return new Promise<string>(resolve => {
        axiosForServer.post<Result<ImageUploadResult>>("/api/uploadImage", formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        }).then(({data}) => {
            if (data.code === 200 && data.data) {
                resolve(data.data.url);
            } else {
                resolve("")
            }
        });
    });
}

// Base64图片响应接口
interface Base64ImageResponse {
    base64: string;  // base64编码的图片数据
}

/**
 * 获取图片的base64数据
 * @param url - 图片的完整URL地址
 * @returns Promise<string> - 返回图片的base64数据
 */
export function getImageBase64(url: string): Promise<string> {
    return new Promise<string>(resolve => {
        // 移除 URL 中可能存在的域名部分，只保留路径
        const path = url.split('/api/resource/')[1];
        if (!path) {
            resolve("");
            return;
        }

        axiosForServer.get<Result<Base64ImageResponse>>(`/api/base64/${path}`)
            .then(({data}) => {
                if (data.code === 200 && data.data) {
                    resolve(data.data.base64);
                } else {
                    resolve("");
                }
            })
            .catch(() => resolve(""));
    });
}


export function getImageUrl(path: string): Promise<string> {
    return new Promise<string>(resolve => {
        axiosForServer.post<Result<string>>(`/api/localFile`, {
            localPath: path
        })
            .then(({data}) => {
                if (data.code === 200 && data.data) {
                    resolve(data.data);
                } else {
                    resolve("");
                }
            })
            .catch(() => resolve(""));
    });
}

export function getGiteeFreeModelPage(): Promise<string> {
    return new Promise<string>(resolve => {
        axiosForServer.get<string>(`/api/giteeAI/freeModel`, {
            timeout: 100000,
        })
            .then(({data}) => {
                resolve(data)
            })
            .catch(() => resolve(""));
    });
}




