<template>
    <PageLayout>
        <div class="tweet-generator">
            <!-- 左侧输入区域 -->
            <div class="input-section">
                <div class="input-header">
                    <h3>创建推文</h3>
                </div>
                
                <!-- 文本输入区域 -->
                <div class="text-input-container">
                    <label for="tweet-text">推文内容</label>
                    <textarea 
                        id="tweet-text"
                        v-model="inputText"
                        placeholder="请输入您的推文内容..."
                        rows="8"
                        class="text-input"
                    ></textarea>
                </div>

                <!-- 内容资料库 -->
                <div class="content-library-container">
                    <div class="library-header">
                        <label>内容资料库</label>
                        <button @click="showAddModal = true" class="add-content-btn">
                            <span>+</span>
                            添加内容
                        </button>
                    </div>
                    
                    <div class="library-content">
                        <div class="library-tabs">
                            <button 
                                v-for="tab in libraryTabs" 
                                :key="tab.key"
                                @click="activeLibraryTab = tab.key"
                                :class="['tab-btn', { active: activeLibraryTab === tab.key }]"
                            >
                                {{ tab.label }}
                            </button>
                        </div>
                        
                        <div class="library-items">
                            <!-- 文本内容 -->
                            <div v-if="activeLibraryTab === 'texts'" class="content-items">
                                <div v-if="textContents.length === 0" class="empty-state">
                                    <div class="empty-icon">📝</div>
                                    <p>暂无文本内容</p>
                                    <p class="empty-hint">点击"添加内容"开始创建</p>
                                </div>
                                <div 
                                    v-for="item in textContents" 
                                    :key="item.id"
                                    class="content-item"
                                >
                                    <div class="content-header">
                                        <div class="content-title">{{ item.title }}</div>
                                        <button @click="removeContent('texts', item.id)" class="remove-content-btn">×</button>
                                    </div>
                                    <div class="content-preview">{{ item.preview }}</div>
                                    <div class="content-actions">
                                        <button @click="insertContent(item.content)" class="insert-btn">插入</button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 链接内容 -->
                            <div v-if="activeLibraryTab === 'links'" class="content-items">
                                <div v-if="linkContents.length === 0" class="empty-state">
                                    <div class="empty-icon">🔗</div>
                                    <p>暂无链接内容</p>
                                    <p class="empty-hint">点击"添加内容"开始创建</p>
                                </div>
                                <div 
                                    v-for="item in linkContents" 
                                    :key="item.id"
                                    class="content-item"
                                >
                                    <div class="content-header">
                                        <div class="content-title">{{ item.title }}</div>
                                        <button @click="removeContent('links', item.id)" class="remove-content-btn">×</button>
                                    </div>
                                    <div class="content-url">{{ item.url }}</div>
                                    <div class="content-desc">{{ item.description }}</div>
                                    <div class="content-actions">
                                        <button @click="insertContent(item.content)" class="insert-btn">插入</button>
                                        <a :href="item.url" target="_blank" class="visit-btn">访问</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 添加内容弹窗 -->
                <div v-if="showAddModal" class="modal-overlay" @click="showAddModal = false">
                    <div class="modal-content" @click.stop>
                        <div class="modal-header">
                            <h3>添加内容</h3>
                            <button @click="showAddModal = false" class="modal-close">×</button>
                        </div>
                        
                        <div class="modal-body">
                            <div class="content-type-selector">
                                <label>
                                    <input type="radio" v-model="newContentType" value="text" />
                                    文本内容
                                </label>
                                <label>
                                    <input type="radio" v-model="newContentType" value="link" />
                                    链接内容
                                </label>
                            </div>
                            
                            <div class="form-group">
                                <label>标题</label>
                                <input v-model="newContent.title" placeholder="请输入标题" />
                            </div>
                            
                            <div v-if="newContentType === 'text'" class="form-group">
                                <label>内容</label>
                                <textarea v-model="newContent.content" placeholder="请输入文本内容" rows="4"></textarea>
                            </div>
                            
                            <div v-if="newContentType === 'link'">
                                <div class="form-group">
                                    <label>链接地址</label>
                                    <input v-model="newContent.url" placeholder="请输入链接地址" />
                                </div>
                                <div class="form-group">
                                    <label>描述</label>
                                    <textarea v-model="newContent.description" placeholder="请输入链接描述" rows="2"></textarea>
                                </div>
                            </div>
                        </div>
                        
                        <div class="modal-footer">
                            <button @click="showAddModal = false" class="cancel-btn">取消</button>
                            <button @click="addContent" class="confirm-btn">确定</button>
                        </div>
                    </div>
                </div>

                <!-- 图片上传区域 -->
                <div class="image-upload-container">
                    <label>添加图片</label>
                    <div class="upload-area">
                        <input 
                            type="file" 
                            ref="fileInput"
                            @change="handleImageUpload"
                            accept="image/*"
                            multiple
                            style="display: none"
                        />
                        <button @click="triggerFileUpload" class="upload-btn">
                            <span class="upload-icon">📷</span>
                            选择图片
                        </button>
                        
                        <!-- 已上传图片预览 -->
                        <div v-if="uploadedImages.length > 0" class="uploaded-images">
                            <div 
                                v-for="(image, index) in uploadedImages" 
                                :key="index"
                                class="image-item"
                            >
                                <img :src="image.preview" :alt="image.name" />
                                <button @click="removeImage(index)" class="remove-btn">
                                    ×
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 生成按钮 -->
                <div class="action-buttons">
                    <button @click="generateTweet" class="generate-btn" :disabled="!canGenerate">
                        生成推文
                    </button>
                    <button @click="clearAll" class="clear-btn">
                        清空内容
                    </button>
                </div>
            </div>

            <!-- 右侧预览区域 -->
            <div class="preview-section">
                <div class="preview-header">
                    <h3>推文预览</h3>
                </div>
                
                <div class="preview-content">
                    <div v-if="!generatedMarkdown" class="empty-preview">
                        <div class="empty-icon">📝</div>
                        <p>推文预览将显示在这里</p>
                        <p class="empty-hint">请在左侧输入内容并点击"生成推文"</p>
                    </div>
                    
                    <div v-else class="markdown-preview">
                        <div class="preview-toolbar">
                            <button @click="copyToClipboard" class="copy-btn">
                                复制内容
                            </button>
                        </div>
                        <div class="markdown-content" v-html="renderedMarkdown"></div>
                    </div>
                </div>
            </div>
        </div>
    </PageLayout>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';

const inputText = ref('');
const uploadedImages = ref<Array<{file: File, preview: string, name: string}>>([]);
const generatedMarkdown = ref('');
const fileInput = ref<HTMLInputElement>();

// 内容资料库相关数据
const activeLibraryTab = ref('texts');

const libraryTabs = ref([
    { key: 'texts', label: '文本内容' },
    { key: 'links', label: '链接内容' }
]);

// 用户自定义内容
const textContents = ref<Array<{id: number, title: string, content: string, preview: string}>>([]);
const linkContents = ref<Array<{id: number, title: string, url: string, description: string, content: string}>>([]);

// 添加内容相关
const showAddModal = ref(false);
const newContentType = ref('text');
const newContent = ref({
    title: '',
    content: '',
    url: '',
    description: ''
});

let contentIdCounter = 1;

// 计算是否可以生成推文
const canGenerate = computed(() => {
    return inputText.value.trim().length > 0 || uploadedImages.value.length > 0;
});

// 计算渲染后的markdown（这里暂时简单处理，实际可以用markdown解析库）
const renderedMarkdown = computed(() => {
    if (!generatedMarkdown.value) return '';
    
    // 简单处理markdown格式
    return generatedMarkdown.value
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/\n/g, '<br>');
});

// 触发文件选择
const triggerFileUpload = () => {
    fileInput.value?.click();
};

// 处理图片上传
const handleImageUpload = (event: Event) => {
    const target = event.target as HTMLInputElement;
    const files = target.files;
    
    if (files) {
        Array.from(files).forEach(file => {
            if (file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    uploadedImages.value.push({
                        file,
                        preview: e.target?.result as string,
                        name: file.name
                    });
                };
                reader.readAsDataURL(file);
            }
        });
    }
    
    // 清空input值，允许重复选择同一文件
    if (target) target.value = '';
};

// 移除图片
const removeImage = (index: number) => {
    uploadedImages.value.splice(index, 1);
};

// 插入内容到文本框
const insertContent = (content: string) => {
    if (inputText.value) {
        inputText.value += '\n\n' + content;
    } else {
        inputText.value = content;
    }
};

// 添加内容
const addContent = () => {
    if (!newContent.value.title.trim()) {
        alert('请输入标题');
        return;
    }
    
    if (newContentType.value === 'text') {
        if (!newContent.value.content.trim()) {
            alert('请输入文本内容');
            return;
        }
        
        const preview = newContent.value.content.length > 50 
            ? newContent.value.content.substring(0, 50) + '...'
            : newContent.value.content;
            
        textContents.value.push({
            id: contentIdCounter++,
            title: newContent.value.title,
            content: newContent.value.content,
            preview: preview
        });
    } else if (newContentType.value === 'link') {
        if (!newContent.value.url.trim()) {
            alert('请输入链接地址');
            return;
        }
        
        const linkContent = `${newContent.value.title}\n链接：${newContent.value.url}${newContent.value.description ? '\n描述：' + newContent.value.description : ''}`;
        
        linkContents.value.push({
            id: contentIdCounter++,
            title: newContent.value.title,
            url: newContent.value.url,
            description: newContent.value.description || '',
            content: linkContent
        });
    }
    
    // 清空表单
    newContent.value = {
        title: '',
        content: '',
        url: '',
        description: ''
    };
    showAddModal.value = false;
};

// 删除内容
const removeContent = (type: string, id: number) => {
    if (type === 'texts') {
        const index = textContents.value.findIndex(item => item.id === id);
        if (index > -1) {
            textContents.value.splice(index, 1);
        }
    } else if (type === 'links') {
        const index = linkContents.value.findIndex(item => item.id === id);
        if (index > -1) {
            linkContents.value.splice(index, 1);
        }
    }
};

// 生成推文（暂时模拟生成）
const generateTweet = async () => {
    // 这里暂时模拟生成markdown内容
    // 实际应该调用服务器API
    
    let content = '# 🚀 AI生成推文\n\n';
    
    if (inputText.value.trim()) {
        content += `**内容：** ${inputText.value.trim()}\n\n`;
    }
    
    if (uploadedImages.value.length > 0) {
        content += `**包含图片：** ${uploadedImages.value.length} 张\n\n`;
        uploadedImages.value.forEach((img, index) => {
            content += `![图片${index + 1}](${img.preview})\n\n`;
        });
    }
    
    content += '*🤖 由AI智能生成*';
    
    generatedMarkdown.value = content;
};

// 清空所有内容
const clearAll = () => {
    inputText.value = '';
    uploadedImages.value = [];
    generatedMarkdown.value = '';
};

// 复制到剪贴板
const copyToClipboard = async () => {
    try {
        await navigator.clipboard.writeText(generatedMarkdown.value);
        alert('内容已复制到剪贴板！');
    } catch (err) {
        console.error('复制失败:', err);
        alert('复制失败，请手动选择复制');
    }
};
</script>

<style scoped>
.tweet-generator {
    display: flex;
    height: calc(100vh - 40px);
    gap: 16px;
    padding: 16px;
    background-color: #f5f5f5;
    max-width: 100%;
    overflow: hidden;
}

/* 左侧输入区域 */
.input-section {
    flex: 1;
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    overflow-y: auto;
    min-width: 0;
    max-height: 100%;
}

.input-header h3 {
    margin: 0 0 20px 0;
    color: #1a1a1a;
    font-size: 20px;
    font-weight: 600;
}

.text-input-container {
    margin-bottom: 20px;
}

.text-input-container label {
    display: block;
    margin-bottom: 8px;
    color: #333;
    font-weight: 500;
}

.text-input {
    width: 100%;
    padding: 10px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 14px;
    line-height: 1.5;
    resize: vertical;
    transition: border-color 0.2s ease;
    min-height: 120px;
    max-height: 200px;
}

.text-input:focus {
    outline: none;
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 内容资料库区域 */
.content-library-container {
    margin-bottom: 20px;
}

.library-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.library-header label {
    color: #333;
    font-weight: 500;
}

.add-content-btn {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 6px 12px;
    background: #52c41a;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.add-content-btn:hover {
    background: #73d13d;
}

.library-content {
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    overflow: hidden;
}

.library-tabs {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #e1e5e9;
}

.tab-btn {
    flex: 1;
    padding: 10px 16px;
    background: transparent;
    border: none;
    color: #666;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
    border-right: 1px solid #e1e5e9;
}

.tab-btn:last-child {
    border-right: none;
}

.tab-btn:hover {
    background: #e9ecef;
    color: #333;
}

.tab-btn.active {
    background: #1890ff;
    color: white;
}

.library-items {
    padding: 12px;
    max-height: 200px;
    overflow-y: auto;
}

/* 空状态 */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: #999;
    text-align: center;
}

.empty-icon {
    font-size: 32px;
    margin-bottom: 12px;
    opacity: 0.5;
}

.empty-hint {
    font-size: 12px;
    margin-top: 8px;
}

/* 内容项目 */
.content-items {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.content-item {
    padding: 12px;
    border: 1px solid #e1e5e9;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.content-item:hover {
    border-color: #1890ff;
    background: #f0f8ff;
}

.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.content-title {
    font-weight: 500;
    color: #333;
    font-size: 14px;
}

.remove-content-btn {
    width: 20px;
    height: 20px;
    background: #ff4d4f;
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.remove-content-btn:hover {
    background: #ff7875;
}

.content-preview {
    color: #666;
    font-size: 12px;
    line-height: 1.4;
    margin-bottom: 8px;
}

.content-url {
    color: #1890ff;
    font-size: 12px;
    word-break: break-all;
    margin-bottom: 4px;
}

.content-desc {
    color: #666;
    font-size: 12px;
    line-height: 1.4;
    margin-bottom: 8px;
}

.content-actions {
    display: flex;
    gap: 8px;
}

.insert-btn {
    padding: 4px 8px;
    background: #1890ff;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.insert-btn:hover {
    background: #40a9ff;
}

.visit-btn {
    padding: 4px 8px;
    background: #52c41a;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    text-decoration: none;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.visit-btn:hover {
    background: #73d13d;
}

/* 弹窗样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #e1e5e9;
}

.modal-header h3 {
    margin: 0;
    color: #333;
    font-size: 16px;
}

.modal-close {
    width: 24px;
    height: 24px;
    background: none;
    border: none;
    color: #666;
    font-size: 18px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    color: #333;
}

.modal-body {
    padding: 20px;
}

.content-type-selector {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.content-type-selector label {
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
    font-size: 14px;
}

.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    color: #333;
    font-size: 14px;
    font-weight: 500;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #e1e5e9;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 20px;
    border-top: 1px solid #e1e5e9;
}

.cancel-btn {
    padding: 8px 16px;
    background: #fff;
    color: #666;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.cancel-btn:hover {
    color: #333;
    border-color: #aaa;
}

.confirm-btn {
    padding: 8px 16px;
    background: #1890ff;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.confirm-btn:hover {
    background: #40a9ff;
}

/* 图片上传区域 */
.image-upload-container {
    margin-bottom: 20px;
}

.image-upload-container label {
    display: block;
    margin-bottom: 10px;
    color: #333;
    font-weight: 500;
}

.upload-area {
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    padding: 16px;
    text-align: center;
    transition: border-color 0.2s ease;
}

.upload-area:hover {
    border-color: #1890ff;
}

.upload-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    background: #1890ff;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s ease;
}

.upload-btn:hover {
    background: #40a9ff;
}

.upload-icon {
    font-size: 16px;
}

.uploaded-images {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-top: 16px;
}

.image-item {
    position: relative;
    width: 80px;
    height: 80px;
    border-radius: 8px;
    overflow: visible;
    border: 2px solid #e1e5e9;
    transition: all 0.2s ease;
    z-index: 1;
}

.image-item:hover {
    border-color: #1890ff;
}

.image-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 6px;
}

.remove-btn {
    position: absolute;
    top: 0;
    right: 0;
    width: 24px;
    height: 24px;
    background: #ff4d4f;
    color: white;
    border: 2px solid white;
    border-radius: 50%;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    transition: all 0.2s ease;
    z-index: 999;
    transform: translate(50%, -50%);
}

.remove-btn:hover {
    background: #ff7875;
    transform: translate(50%, -50%) scale(1.1);
}

/* 操作按钮 */
.action-buttons {
    display: flex;
    gap: 12px;
}

.generate-btn {
    flex: 1;
    padding: 10px 20px;
    background: #52c41a;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.generate-btn:hover:not(:disabled) {
    background: #73d13d;
}

.generate-btn:disabled {
    background: #d9d9d9;
    cursor: not-allowed;
}

.clear-btn {
    padding: 10px 20px;
    background: #fff;
    color: #666;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.clear-btn:hover {
    color: #1890ff;
    border-color: #1890ff;
}

/* 右侧预览区域 */
.preview-section {
    flex: 1;
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    display: flex;
    flex-direction: column;
    min-width: 0;
    max-height: 100%;
}

.preview-header h3 {
    margin: 0 0 20px 0;
    color: #1a1a1a;
    font-size: 20px;
    font-weight: 600;
}

.preview-content {
    flex: 1;
    overflow-y: auto;
}

.empty-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #999;
    text-align: center;
}

.empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-hint {
    font-size: 14px;
    margin-top: 8px;
}

.markdown-preview {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.preview-toolbar {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 16px;
}

.copy-btn {
    padding: 6px 12px;
    background: #f0f0f0;
    color: #666;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.copy-btn:hover {
    color: #1890ff;
    border-color: #1890ff;
}

.markdown-content {
    flex: 1;
    padding: 20px;
    background: #fafafa;
    border-radius: 8px;
    border: 1px solid #e1e5e9;
    line-height: 1.6;
    overflow-y: auto;
}

.markdown-content img {
    max-width: 100%;
    height: auto;
    border-radius: 6px;
    margin: 8px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .tweet-generator {
        flex-direction: column;
        height: auto;
    }
    
    .input-section,
    .preview-section {
        flex: none;
    }
    
    .preview-section {
        min-height: 400px;
    }
}
</style>