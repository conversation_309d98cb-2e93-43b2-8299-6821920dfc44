/* 深色模式变量 */
:root {
    --code-bg-dark: ;
    --code-text-dark: #000000;
    --code-border-dark: #333;
    --code-border-hover-dark: #666;
    --scrollbar-bg-dark: #2d2d2d;
    --scrollbar-thumb-dark: #666;
    --scrollbar-thumb-hover-dark: #888;
}

.editor .ck-content {
    /* 代码块容器样式 */

    pre {
        background-color: transparent;
        border-radius: 6px;
        padding: 16px;
        margin: 16px 0;
        overflow-x: auto;
        font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        font-size: 14px;
        line-height: 1.5;
        border: 1px solid var(--code-border-dark);
    }

    /* 代码文本样式 */

    code {
        color: var(--code-text-dark);
        background: none;
        text-shadow: none;
        font-family: inherit;
    }

    /* 代码块hover效果 */

    pre:hover {
        border-color: var(--code-border-hover-dark);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        transition: all 0.3s ease;
    }

    /* 代码块滚动条样式 */

    pre::-webkit-scrollbar {
        height: 8px;
        background-color: var(--scrollbar-bg-dark);
    }

    pre::-webkit-scrollbar-thumb {
        background-color: var(--scrollbar-thumb-dark);
        border-radius: 4px;
    }

    pre::-webkit-scrollbar-thumb:hover {
        background-color: var(--scrollbar-thumb-hover-dark);
    }

    /* 代码块样式 */

    pre {
        margin: 0 1px !important;
        padding: 2px !important;
        border-radius: 3px !important;
    }

    pre[data-language]::after {
        color: rgb(255, 255, 255) !important;
    }
}