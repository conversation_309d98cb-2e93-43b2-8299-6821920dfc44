<template>
  <a-drawer
      v-model:open="bot.pluginConfigDrawerOpen"
      destroyOnClose
      class="plugin-setting"
      root-class-name="root-class-name"
      :title="plugin.currentPlugin.name"
      placement="right"
      close-icon=""
      @after-open-change="afterOpenChange"
  >
    <div class="size-full">
      <!--   整体布局居中   -->
      <div class="size-full flex flex-col mb-auto">
        <!--   公共基础配置    -->
        <a-divider orientation="left" class="divider">平台配置</a-divider>
        <PluginBaseConfig :ctx="plugin.currentPlugin"/>
        <!--   插件独立配置    -->
        <a-divider orientation="left" class="divider">插件配置</a-divider>
        <div class="flex-grow overflow-hidden">
          <component :is="plugin.currentPlugin.configView"/>
        </div>
      </div>
    </div>
  </a-drawer>
</template>
<script setup lang="ts">
import {useAiBot} from "@/components/store/bot";
import PluginBaseConfig from "@/components/ai-bot-plugins/plugin-config/PluginBaseConfig.vue";
import {useAiPluginStore} from "@/components/store/plugin";

const bot = useAiBot()
const plugin = useAiPluginStore()

const afterOpenChange = (bool: boolean) => {
  console.log('open', bool);
};


</script>


<style>
.plugin-setting {
  position: absolute;
  -webkit-app-region: no-drag;
}

.plugin-setting .ant-drawer-body {
  padding-top: 0;
}

</style>