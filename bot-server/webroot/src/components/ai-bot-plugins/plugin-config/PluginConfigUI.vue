<template>
  <div class="fit column">
    <q-bar class="bg-transparent ellipsis">
      <RightDrawerToggleBtn/>
      {{ plugin.currentPlugin.name }}
    </q-bar>
    <PluginBaseConfig :ctx="plugin.currentPlugin"/>
  </div>
</template>

<script setup lang="ts">

import RightDrawerToggleBtn from "@/components/desktop/RightDrawerToggleBtn.vue";
import PluginBaseConfig from "@/components/ai-bot-plugins/plugin-config/PluginBaseConfig.vue";
import {useAiPluginStore} from "@/components/store/plugin";

const plugin = useAiPluginStore()

</script>


<style scoped>

</style>