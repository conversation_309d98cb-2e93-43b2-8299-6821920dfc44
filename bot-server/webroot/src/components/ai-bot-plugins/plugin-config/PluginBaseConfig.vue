<template>
  <div class="mx-auto flex flex-col" style="width: 100%;flex-wrap: nowrap">
    <div class="flex flex-row justify-between">
      <div class="select-wrapper" ref="selectWrapper" style="width: 85%">
        <a-select
            v-model:value="model"
            style="width: 100%"
            :get-popup-container="getPopupContainer"
            @select="select"
            size="small"
        >
          <a-select-option
              class="my-1"
              v-for="platform in plat.platforms"
              :label="platform.name"
              :key="platform.id"
              :value="platform.id"
          >
            <div class="flex items-center gap-2">
              <img :src="platform.icon" class="w-5 h-5 rounded-full" :alt="platform.name"/>
              <span>{{ platform.name }}</span>
            </div>
          </a-select-option>
        </a-select>
      </div>
      <a-button @click="open = !open">
        <template #icon>
          <setting-outlined/>
        </template>
      </a-button>
    </div>
    <!--  平台快速配置面板  -->
    <div class="quack-config">
      <component
          v-if="platform!=null"
          ref="quackConfig"
          :key="platform.id"
          :is="platform.quickConfigView"
          :ctx="platform"/>
    </div>
    <PlatformConfig v-model="open" :platform="platform" @save="save"/>
  </div>
</template>

<script setup lang="ts">
import {Platform, Plugin} from "@/components/common/model/model";
import {defineComponent, defineProps, onMounted, ref, watch} from 'vue'
import {getPlatforms} from "@/components/common/manageRequest";
import {usePlatform} from "@/components/store/platform";
import PlatformConfig from "@/components/ai-bot-platform/PlatformConfig.vue";
import {useAiPluginStore} from "@/components/store/plugin";
import {useAiBot} from "@/components/store/bot";
import {SettingOutlined} from "@ant-design/icons-vue";

const bot = useAiBot()

const props = defineProps<{
  // 当前插件
  ctx: Plugin
}>()

// 默认选中当前插件默认平台
const model = ref<string>(props.ctx.platformID)
const platforms = ref<Platform[]>([])
const platform = ref<Platform>()
const open = ref(false)
const plat = usePlatform()
const plugin = useAiPluginStore()
const quackConfig = ref()
const selectWrapper = ref<HTMLElement | null>(null)

const VNodes = defineComponent({
  props: {
    vnodes: {
      type: Object,
      required: true,
    },
  },
  render() {
    return this.vnodes;
  },
});

// 修改当前插件所配置的平台
function select(value) {
  // 更新当前的操作平台 用于触发 快速面板的配置刷新
  platform.value = plat.platforms.find(p => p.id === value);
  let pram = {...props.ctx}
  pram.platformID = value
  // plugin.updatePlugin(pram)
  // 修改 统一配置所有插件 使用的基座
  plugin.updateAllPlugin(platform.value)
  // 更新当前插件配置
  plugin.currentPlugin = pram
}

/*
* save 平台配置保存后触发 平台对应的快速配置面板 刷新
* */
function save() {
  platform.value = null
  let p = plat.platforms.find(p => p.id === props.ctx.platformID)
  setTimeout(() => {
    platform.value = p
  }, 200)
}

watch(() => plugin.currentPlugin.platformID, (newVal) => {
  model.value = newVal
  platform.value = plat.platforms.find(p => p.id === model.value);
}, {
  deep: true,

})

function getPopupContainer() {
  return selectWrapper.value || document.body
}

onMounted(async () => {
  platforms.value = await getPlatforms()
  // 解析平台配置
  plat.platforms = platforms.value.map(p => {
    let config = {...p}
    config.settings = JSON.parse(config.config)
    return config
  })
  platform.value = plat.platforms.find(p => p.id === props.ctx.platformID)
})

</script>

<style scoped>


</style>