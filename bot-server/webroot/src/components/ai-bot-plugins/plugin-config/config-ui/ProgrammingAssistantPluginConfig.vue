<template>

</template>

<script setup lang="ts">
import {reactive} from "vue";
import {useAiPluginStore} from "@/components/store/plugin";

const props = defineProps<{
  data: any
}>()


const plugin = useAiPluginStore()

function select(value: any, index: number) {

}

function clear() {
  plugin.ctx.programming.list.forEach((item: any) => {
    item.selected = false
    item.bgColor = ''
  })
}

</script>


<style scoped>
.programming-assistant {
  flex-grow: 1;
}
</style>