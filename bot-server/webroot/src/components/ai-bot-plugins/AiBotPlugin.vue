<template>
  <a-dropdown :trigger="['click']" v-model:open="isOpen">
      <a-button type="text" @click="isOpen = true">
        <template #icon>
          <span class="icon iconfont" :class="plugin.currentPlugin.icon"></span>
        </template>
        <span class="ml-1">{{plugin.currentPlugin.name}}</span>
      </a-button>

    <template #overlay>
      <a-menu class="custom-dropdown-menu" :selectedKeys="[plugin.currentPlugin.id]" :items="menuItems"
        @click="handleMenuClick">
      </a-menu>
    </template>
  </a-dropdown>
</template>

<script setup lang="ts">
import { computed, h, onMounted, ref } from "vue";
import { useAiPluginStore } from "@/components/store/plugin";
import { theme } from 'ant-design-vue';

const { useToken } = theme;
const { token } = useToken();

const plugin = useAiPluginStore();
const isOpen = ref(false);

// 处理菜单点击事件
const handleMenuClick = (info) => {
  const item = plugin.plugins.find(p => p.id === info.key);
  if (item) {
    setPlugin(item);
  }
};

const setPlugin = (item) => {
  plugin.currentPlugin = item;
  isOpen.value = false;
};

// 使用计算属性生成菜单项
const menuItems = computed(() =>
  plugin.plugins.map(item => ({
    key: item.id,
    label: item.name,
    title: item.name,
  }))
);


onMounted(async () => {
  await plugin.queryPlugins();
  if (plugin.plugins.length > 0) {
    plugin.currentPlugin = plugin.plugins[0];
  }
});
</script>

<style scoped></style>