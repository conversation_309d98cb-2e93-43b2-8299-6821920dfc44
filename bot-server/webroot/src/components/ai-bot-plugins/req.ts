import axiosForServer from "@/plugins/axiosForServer";
import {Platform, Plugin} from "@/components/common/model/model";

export function updatePlugin(plugin: Plugin): Promise<void> {
    return new Promise((resolve) => {
        axiosForServer.post(`/api/bot/plugins/update`, plugin)
            .then(({data}) => {
                resolve()
            })
    })
}

export function updateAllPlugin(platform: Platform): Promise<void> {
    return new Promise((resolve) => {
        axiosForServer.post(`/api/bot/plugins/updateAll`, platform)
            .then(({data}) => {
                resolve()
            })
    })
}