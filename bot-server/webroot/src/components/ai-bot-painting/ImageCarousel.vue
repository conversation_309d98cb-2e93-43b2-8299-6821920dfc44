<template>
  <div class="size-full flex flex-col">
    <AdaptiveImageCarousel  :images="images" :current-index="currentIndex" :show-thumbnails="true"
      :max-width="700" :max-height="700" :aspect-ratio="'auto'" :is-generating="isGenerating"
      @update:current-index="currentIndex = $event" @image-load="onImageLoad" />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { PaintingMessage } from '../common/model/painting';
import AdaptiveImageCarousel from './AdaptiveImageCarousel.vue';
import emitter from '@/plugins/event';
import { AiPaintingStream } from '@/plugins/evenKey';

// Props
interface Props {
  images?: string[];
  currentIndex?: number;
  isGenerating?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  images: () => [],
  currentIndex: 0,
  isGenerating: false
});

// Emits
const emit = defineEmits<{
  'update:currentIndex': [index: number];
}>()

// 使用计算属性处理内部状态和外部传入的currentIndex
const currentIndex = computed({
  get: () => props.currentIndex,
  set: (value: number) => {
    emit('update:currentIndex', value);
  }
});

// 使用传入的图片，如果没有传入则使用默认示例图片
const images = computed(() => {
  if (props.images && props.images.length > 0) {
    return props.images;
  }

  // 如果正在生成，返回空数组让轮播图显示loading状态
  if (props.isGenerating) {
    return [];
  }

  // 默认示例图片
  const baseUrl = 'https://raw.githubusercontent.com/vueComponent/ant-design-vue/main/components/carousel/demo/';
  return [
    `${baseUrl}abstract01.jpg`,
    `${baseUrl}abstract02.jpg`,
    `${baseUrl}abstract03.jpg`,
    `${baseUrl}abstract04.jpg`
  ];
});

// 图片加载完成回调
const onImageLoad = (index: number, dimensions: { width: number; height: number }) => {
  // 可以在这里处理图片加载完成的逻辑
  console.log(`Image ${index} loaded with dimensions:`, dimensions);
};



// 监听 AiPaintingStream 事件
emitter.on(AiPaintingStream, (painting: PaintingMessage) => {
  
});

</script>

<style scoped>
/* Loading 效果样式 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  border-radius: 12px;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--ant-color-border);
  border-top: 3px solid var(--ant-color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 16px;
  font-weight: 500;
  color: var(--ant-color-text);
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* 这个组件现在主要作为AdaptiveImageCarousel的包装器 */
</style>