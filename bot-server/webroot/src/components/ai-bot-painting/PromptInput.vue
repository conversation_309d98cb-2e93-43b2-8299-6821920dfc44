<template>
  <div class="input-control-area" :class="{ 'has-result': hasGeneratedImage }">
    <div class="input-container">
      <div v-if="!hasGeneratedImage" class="welcome-section">
        <div class="placeholder-icon">
          <span class="icon">🎨</span>
        </div>
        <h3>开始创作</h3>
        <p>描述你想要生成的图像，开始你的创作之旅</p>
      </div>
      
      <!-- 提示词输入区域 -->
      <div class="prompt-input-section">
        <a-textarea 
          :value="promptText"
          @change="updatePrompt"
          placeholder="描述你想要生成的图像，例如：一只可爱的小猫在花园里玩耍，阳光明媚，卡通风格..."
          :auto-size="{ minRows: hasGeneratedImage ? 2 : 3, maxRows: hasGeneratedImage ? 4 : 6 }"
          show-count
          :maxlength="1000"
          class="prompt-textarea" />
      </div>
      
      <div class="quick-actions">
        <a-button 
          type="primary" 
          size="large"
          :loading="isGenerating"
          @click="generateImage"
          class="generate-btn">
          <template #icon>
            <span v-if="!isGenerating">🎨</span>
          </template>
          {{ generateButtonText }}
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { message } from 'ant-design-vue';

// Props
interface Props {
  promptText: string;
  isGenerating: boolean;
  hasGeneratedImage: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  promptText: '',
  isGenerating: false,
  hasGeneratedImage: false
});

// Emits
const emit = defineEmits<{
  'update-prompt': [text: string];
  'generate-image': [];
}>();

// Computed
const generateButtonText = computed(() => {
  if (props.isGenerating) {
    return props.hasGeneratedImage ? '重新生成' : '生成中...';
  }
  return props.hasGeneratedImage ? '重新生成' : '生成图像';
});

// Methods
const updatePrompt = (event: Event) => {
  const target = event.target as HTMLTextAreaElement;
  emit('update-prompt', target.value);
};

const generateImage = () => {
  if (props.isGenerating) return;
  
  // 检查提示词
  if (!props.promptText.trim()) {
    message.warning('请输入图像描述');
    return;
  }
  
  emit('generate-image');
};
</script>

<style scoped>
/* 输入控制区域 */
.input-control-area {
  padding: 24px;
  background: var(--ant-color-bg-container);
  border-top: 1px solid var(--ant-color-border);
  min-height: 180px;
  display: flex;
  align-items: center;
}

.input-control-area.has-result {
  padding: 20px 24px;
  min-height: 160px;
}

.input-container {
  max-width: 1400px;
  margin: 0 auto;
  text-align: center;
  width: 100%;
}

.welcome-section {
  margin-bottom: 24px;
}

.placeholder-icon {
  margin-bottom: 24px;
}

.placeholder-icon .icon {
  font-size: 64px;
  opacity: 0.8;
}

.welcome-section h3 {
  margin: 0 0 12px 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--ant-color-text);
}

.welcome-section p {
  margin: 0 0 24px 0;
  font-size: 14px;
  line-height: 1.5;
  color: var(--ant-color-text-secondary);
}

/* 提示词输入区域样式 */
.prompt-input-section {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto 24px auto;
}

.prompt-textarea {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
  border: 1px solid var(--ant-color-border) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.prompt-textarea:hover {
  border-color: var(--ant-color-primary-hover) !important;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.1) !important;
}

.prompt-textarea:focus {
  border-color: var(--ant-color-primary) !important;
  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.15) !important;
}

.prompt-textarea .ant-input {
  font-size: 14px;
  line-height: 1.6;
  padding: 12px 16px;
}

.prompt-textarea .ant-input::placeholder {
  color: var(--ant-color-text-quaternary);
  font-style: italic;
}

.quick-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  align-items: center;
}

.generate-btn {
  height: 48px !important;
  font-size: 16px !important;
  font-weight: 500 !important;
  border-radius: 8px !important;
  padding: 0 32px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

.generate-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.3);
}

.generate-btn:active {
  transform: translateY(0);
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  background: var(--ant-color-bg-container);
  color: var(--ant-color-text);
  border: 1px solid var(--ant-color-border);
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
  height: 44px;
  border-radius: 8px;
}

.action-btn:hover {
  background: var(--ant-color-bg-text-hover);
  border-color: var(--ant-color-primary);
  color: var(--ant-color-primary);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .input-control-area {
    padding: 16px;
    min-height: 160px;
  }
  
  .input-control-area.has-result {
    padding: 16px;
    min-height: 140px;
  }
  
  .input-container {
    max-width: 100%;
  }
  
  /* 提示词输入框移动端样式 */
  .prompt-input-section {
    max-width: 100%;
    margin: 0 0 16px 0;
  }
  
  .quick-actions {
    flex-direction: column;
    gap: 8px;
  }
  
  .generate-btn {
    width: 100% !important;
    height: 44px !important;
    padding: 0 24px !important;
  }
  
  .action-btn {
    width: 100%;
    justify-content: center;
    height: 44px;
  }
}

@media (max-width: 480px) {
  .input-control-area {
    padding: 12px;
    min-height: 140px;
  }
  
  .input-control-area.has-result {
    padding: 12px;
    min-height: 120px;
  }
  
  .generate-btn {
    height: 40px !important;
    font-size: 14px !important;
    padding: 0 20px !important;
  }
  
  .action-btn {
    height: 40px;
    font-size: 14px;
  }
}
</style> 