<template>
  <div class="adaptive-carousel size-full flex flex-col">
    <!-- 主轮播区域 -->
    <div class="carousel-container relative" :style="containerStyle">
      <!-- Loading 效果 - 当正在生成时显示 -->
      <div v-if="isGenerating" class="generating-overlay">
        <div class="generating-content">
          <div class="generating-spinner"></div>
          <span class="generating-text">{{ images.length > 0 ? '正在生成更多图片...' : '正在生成图片...' }}</span>
        </div>
      </div>

      <!-- 左侧箭头 -->
      <div class="carousel-arrow carousel-arrow-left" @click="prevImage">
        <span class="arrow-icon">&#10094;</span>
      </div>

      <!-- 轮播图主区域 -->
      <div class="carousel-main">
        <div class="carousel-track" ref="carouselTrack">
          <div v-for="(image, index) in images" :key="`${image}-${index}`" class="carousel-slide"
            :class="getSlideClass(index)" :style="getSlideStyle(index)" @click="goToImage(index)">
            <img class="carousel-image" :src="image" @load="onImageLoad($event, index)"
              @error="onImageError($event, index)" />
          </div>
        </div>
      </div>

      <!-- 右侧箭头 -->
      <div class="carousel-arrow carousel-arrow-right" @click="nextImage">
        <span class="arrow-icon">&#10095;</span>
      </div>
    </div>

    <!-- 缩略图导航 -->
    <div class="thumbnail-nav" v-if="showThumbnails">
      <div v-for="(image, index) in images" :key="index" class="thumbnail-item"
        :class="{ active: currentIndex === index }" @click="goToImage(index)">
        <img :src="image" class="thumbnail-image" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, nextTick } from 'vue';
import { PaintingMessage } from '../common/model/painting';

// Props
interface Props {
  images?: string[];
  currentIndex?: number;
  showThumbnails?: boolean;
  maxWidth?: number;
  maxHeight?: number;
  aspectRatio?: 'auto' | 'square' | '4:3' | '16:9' | 'portrait';
  isGenerating?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  images: () => [],
  currentIndex: 0,
  showThumbnails: true,
  maxWidth: 500,
  maxHeight: 600,
  aspectRatio: 'auto',
  isGenerating: false
});

// Emits
const emit = defineEmits<{
  'update:currentIndex': [index: number];
  'imageLoad': [index: number, dimensions: { width: number; height: number }];
}>()

// 内部状态
const isTransitioning = ref(false);
const imageDimensions = ref<Map<number, { width: number; height: number; ratio: number }>>(new Map());
const containerDimensions = ref({ width: 400, height: 500 });

// 当前索引的响应式处理
const currentIndex = computed({
  get: () => props.currentIndex,
  set: (value: number) => {
    emit('update:currentIndex', value);
  }
});

// 图片加载完成处理
const onImageLoad = (event: Event, index: number) => {
  const img = event.target as HTMLImageElement;
  const dimensions = {
    width: img.naturalWidth,
    height: img.naturalHeight,
    ratio: img.naturalWidth / img.naturalHeight
  };

  imageDimensions.value.set(index, dimensions);
  emit('imageLoad', index, { width: dimensions.width, height: dimensions.height });

  // 如果是当前图片，更新容器尺寸
  if (index === currentIndex.value) {
    updateContainerSize();
  }
};

// 图片加载错误处理
const onImageError = (event: Event, index: number) => {
  console.warn(`Failed to load image at index ${index}`);
};

// 更新容器尺寸
const updateContainerSize = () => {
  const currentImageDimensions = imageDimensions.value.get(currentIndex.value);
  if (!currentImageDimensions) return;

  let { width, height } = currentImageDimensions;
  const { ratio } = currentImageDimensions;

  // 根据aspectRatio prop调整尺寸
  switch (props.aspectRatio) {
    case 'square':
      width = height = Math.min(width, height);
      break;
    case '4:3':
      if (ratio > 4 / 3) {
        width = height * (4 / 3);
      } else {
        height = width * (3 / 4);
      }
      break;
    case '16:9':
      if (ratio > 16 / 9) {
        width = height * (16 / 9);
      } else {
        height = width * (9 / 16);
      }
      break;
    case 'portrait':
      if (ratio > 3 / 4) {
        width = height * (3 / 4);
      } else {
        height = width * (4 / 3);
      }
      break;
    case 'auto':
    default:
      // 保持原始比例
      break;
  }

  // 限制最大尺寸
  if (width > props.maxWidth) {
    height = height * (props.maxWidth / width);
    width = props.maxWidth;
  }
  if (height > props.maxHeight) {
    width = width * (props.maxHeight / height);
    height = props.maxHeight;
  }

  // 设置最小尺寸
  const minWidth = 400;
  const minHeight = 300;
  if (width < minWidth) {
    height = height * (minWidth / width);
    width = minWidth;
  }
  if (height < minHeight) {
    width = width * (minHeight / height);
    height = minHeight;
  }

  containerDimensions.value = { width: Math.round(width), height: Math.round(height) };
};

// 监听当前索引变化，更新容器尺寸
watch(currentIndex, () => {
  nextTick(() => {
    updateContainerSize();
  });
});

// 容器样式
const containerStyle = computed(() => {
  return {
    minHeight: `${containerDimensions.value.height}px`
  };
});

// 获取滑块样式类
const getSlideClass = (index: number) => {
  return {
    'slide-active': index === currentIndex.value,
    'slide-prev': index === currentIndex.value - 1 || (currentIndex.value === 0 && index === props.images.length - 1),
    'slide-next': index === currentIndex.value + 1 || (currentIndex.value === props.images.length - 1 && index === 0),
    'slide-hidden': Math.abs(index - currentIndex.value) > 1 &&
      !(currentIndex.value === 0 && index === props.images.length - 1) &&
      !(currentIndex.value === props.images.length - 1 && index === 0),
    'transitioning': isTransitioning.value
  };
};

// 获取滑块样式
const getSlideStyle = (index: number) => {
  const totalImages = props.images.length;
  let translateX = 0;
  let scale = 0.8;
  let opacity = 0.6;
  let zIndex = 1;

  // 获取当前图片的尺寸信息用于计算偏移
  const currentImageDim = imageDimensions.value.get(currentIndex.value);
  const slideWidth = currentImageDim ? Math.min(currentImageDim.width * 0.6, containerDimensions.value.width * 0.6) : containerDimensions.value.width * 0.6;

  if (index === currentIndex.value) {
    // 当前图片居中，最高层级
    translateX = 0;
    scale = 1;
    opacity = 1;
    zIndex = 10;
  } else if (index === currentIndex.value - 1 || (currentIndex.value === 0 && index === totalImages - 1)) {
    // 左侧图片，较低层级，减少偏移量避免溢出
    translateX = -(slideWidth + 30);
    scale = 0.85;
    opacity = 0.7;
    zIndex = 2; // 降低z-index，让它在活跃图片下面
  } else if (index === currentIndex.value + 1 || (currentIndex.value === totalImages - 1 && index === 0)) {
    // 右侧图片，较低层级，减少偏移量避免溢出
    translateX = slideWidth + 30;
    scale = 0.85;
    opacity = 0.7;
    zIndex = 2; // 降低z-index，让它在活跃图片下面
  } else {
    // 隐藏的图片
    translateX = index < currentIndex.value ? -(slideWidth * 1.5) : (slideWidth * 1.5);
    scale = 0.6;
    opacity = 0;
    zIndex = 1;
  }

  // 动态设置图片容器尺寸
  const imageStyle = {
    transform: `translateX(${translateX}px) scale(${scale})`,
    opacity: opacity,
    zIndex: zIndex,
    width: `${containerDimensions.value.width}px`,
    height: `${containerDimensions.value.height}px`
  };

  return imageStyle;
};

// 轮播图控制方法
const prevImage = () => {
  if (isTransitioning.value || props.images.length === 0) return;

  isTransitioning.value = true;
  const newIndex = currentIndex.value > 0
    ? currentIndex.value - 1
    : props.images.length - 1;

  currentIndex.value = newIndex;

  setTimeout(() => {
    isTransitioning.value = false;
  }, 300);
};

const nextImage = () => {
  if (isTransitioning.value || props.images.length === 0) return;

  isTransitioning.value = true;
  const newIndex = currentIndex.value < props.images.length - 1
    ? currentIndex.value + 1
    : 0;

  currentIndex.value = newIndex;

  setTimeout(() => {
    isTransitioning.value = false;
  }, 300);
};

const goToImage = (index: number) => {
  if (index >= 0 && index < props.images.length && index !== currentIndex.value) {
    if (isTransitioning.value) return;

    isTransitioning.value = true;
    currentIndex.value = index;

    setTimeout(() => {
      isTransitioning.value = false;
    }, 300);
  }
};
</script>

<style scoped>
.adaptive-carousel {
  position: relative;
  overflow: hidden;
  /* 防止整体组件溢出 */
}

.carousel-container {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  /* 改回hidden，通过其他方式显示两侧图片 */
  perspective: 1200px;
  transition: min-height 0.5s ease;
  margin: 0 80px;
  /* 使用margin代替padding，避免影响容器大小 */
}

.carousel-main {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: visible;
  /* 让主要内容区域可以溢出显示两侧图片 */
}

.carousel-track {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  transform-style: preserve-3d;
}

.carousel-slide {
  position: absolute;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  transform-origin: center center;
  backface-visibility: hidden;
}

.carousel-slide.transitioning {
  transition-duration: 0.3s;
}

.carousel-slide.slide-active {
  z-index: 10;
  /* 活跃图片最高层级 */
}

.carousel-slide.slide-prev,
.carousel-slide.slide-next {
  z-index: 8;
  /* 两侧图片较高层级，确保圆角正常显示 */
}

.carousel-slide.slide-hidden {
  opacity: 0 !important;
  pointer-events: none;
  z-index: 1;
  /* 隐藏图片最低层级 */
}

.carousel-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
  /* 确保圆角效果 */
}

.carousel-slide:hover .carousel-image {
  border-color: rgba(0, 0, 0, 0.2);
}

.carousel-slide.slide-active .carousel-image {
  border-radius: 12px;
  border: 2px solid var(--ant-color-primary);
}

/* 确保左右两侧图片的圆角正常显示 */
.carousel-slide.slide-prev .carousel-image,
.carousel-slide.slide-next .carousel-image {
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.15);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 箭头导航样式 */
.carousel-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 20;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.carousel-arrow:hover {
  background: rgba(255, 255, 255, 1);
  transform: translateY(-50%) scale(1.1);
}

.carousel-arrow-left {
  left: -60px;
  /* 放在margin区域内，避免溢出 */
}

.carousel-arrow-right {
  right: -60px;
  /* 放在margin区域内，避免溢出 */
}

.arrow-icon {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

/* 缩略图导航样式 */
.thumbnail-nav {
  display: flex;
  gap: 8px;
  justify-content: center;
  padding: 20px 0;
  overflow-x: auto;
}

.thumbnail-item {
  width: 60px;
  height: 45px;
  border-radius: 6px;
  overflow: hidden;
  cursor: pointer;
  border: 3px solid transparent;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.thumbnail-item:hover {
  border-color: var(--ant-color-primary-hover);
  transform: scale(1.05);
}

.thumbnail-item.active {
  border-color: var(--ant-color-primary);
  transform: scale(1.1);
}

.thumbnail-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border: 1px solid rgba(0, 0, 0, 0.1);
  transition: opacity 0.3s ease;
}

.thumbnail-item:hover .thumbnail-image {
  opacity: 0.8;
}

/* 响应式样式 */
@media (max-width: 1024px) {
  .carousel-container {
    margin: 0 70px;
    /* 平板屏幕适中margin */
  }
}

@media (max-width: 768px) {
  .carousel-container {
    margin: 0 60px;
    /* 中等屏幕减少margin */
  }

  .carousel-arrow {
    width: 50px;
    height: 50px;
  }

  .carousel-arrow-left {
    left: -45px;
    /* 调整到margin区域内 */
  }

  .carousel-arrow-right {
    right: -45px;
    /* 调整到margin区域内 */
  }

  .arrow-icon {
    font-size: 20px;
  }

  .thumbnail-nav {
    gap: 6px;
    padding: 15px 0;
  }

  .thumbnail-item {
    width: 50px;
    height: 38px;
  }
}

@media (max-width: 480px) {
  .carousel-container {
    margin: 0 40px;
    /* 小屏幕进一步减少margin */
  }

  .carousel-arrow {
    width: 44px;
    height: 44px;
  }

  .carousel-arrow-left {
    left: -32px;
    /* 调整到margin区域内 */
  }

  .carousel-arrow-right {
    right: -32px;
    /* 调整到margin区域内 */
  }

  .arrow-icon {
    font-size: 18px;
  }

  .thumbnail-item {
    width: 40px;
    height: 30px;
  }
}

/* 动画优化 */
@media (prefers-reduced-motion: reduce) {
  .carousel-slide {
    transition: none;
  }
}

/* Loading 效果样式 */
.generating-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.85);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
  border-radius: 12px;
  backdrop-filter: blur(4px);
}

.generating-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.generating-spinner {
  width: 32px;
  height: 32px;
  border: 2px solid var(--ant-color-border);
  border-top: 2px solid var(--ant-color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.generating-text {
  font-size: 14px;
  font-weight: 500;
  color: var(--ant-color-text);
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>