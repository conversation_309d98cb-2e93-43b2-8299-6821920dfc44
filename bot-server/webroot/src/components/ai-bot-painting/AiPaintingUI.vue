<template>
  <PageLayout>
    <div class="painting-editor-container size-full flex flex-row">
      <!-- 左侧悬浮迷你展开按钮 -->
      <transition
          name="floating-btn"
          enter-active-class="floating-btn-enter-active"
          leave-active-class="floating-btn-leave-active"
          enter-from-class="floating-btn-enter-from"
          leave-to-class="floating-btn-leave-to">
        <div
            class="floating-expand-btn left-floating-btn"
            v-show="!isResourcePanelOpen"
            @click="toggleResourcePanel"
            :title="'展开资源面板'">
          <span class="expand-icon">📁</span>
        </div>
      </transition>

      <!-- 右侧悬浮迷你展开按钮 -->
      <transition
          name="floating-btn"
          enter-active-class="floating-btn-enter-active"
          leave-active-class="floating-btn-leave-active"
          enter-from-class="floating-btn-enter-from"
          leave-to-class="floating-btn-leave-to">
        <div
            class="floating-expand-btn right-floating-btn"
            v-show="!isConfigPanelOpen"
            @click="toggleConfigPanel"
            :title="'展开配置面板'">
          <span class="expand-icon">⚙️</span>
        </div>
      </transition>

      <!-- 绘画记录悬浮按钮 -->
      <transition
          name="floating-btn"
          enter-active-class="floating-btn-enter-active"
          leave-active-class="floating-btn-leave-active"
          enter-from-class="floating-btn-enter-from"
          leave-to-class="floating-btn-leave-to">
        <div
            class="floating-expand-btn painting-record-btn"
            v-show="!isPaintingRecordPanelOpen"
            @click="togglePaintingRecordPanel"
            :title="'绘画记录'">
          <span class="expand-icon">🎨</span>
        </div>
      </transition>

      <!-- 左侧资源管理抽屉 -->
      <a-drawer
          v-model:open="isResourcePanelOpen"
          title="资源管理"
          placement="left"
          :width="leftPanelWidth"
          :closable="true"
          :mask="true"
          :mask-closable="true"
          class="resource-panel-drawer">
        <template #title>
          <div class="drawer-title">
            <span>资源管理</span>
            <!-- 视图切换按钮 -->
            <div class="view-toggle">
              <button
                  class="view-btn"
                  :class="{ active: resourceViewMode === 'list' }"
                  @click="setResourceViewMode('list')"
                  title="列表视图">
                <span class="view-icon">☰</span>
              </button>
              <button
                  class="view-btn"
                  :class="{ active: resourceViewMode === 'grid' }"
                  @click="setResourceViewMode('grid')"
                  title="图标视图">
                <span class="view-icon">⊞</span>
              </button>
            </div>
          </div>
        </template>

        <ResourcePanel
            :file-system-data="fileSystemData"
            :recent-files="recentFiles"
            :current-path="currentPath"
            :selected-file-id="selectedFileId"
            :is-loading="isLoading"
            :is-collapsed="false"
            :panel-width="leftPanelWidth"
            :view-mode="resourceViewMode"
            :is-resizing="false"
            @set-view-mode="setResourceViewMode"
            @create-folder="showCreateDialog = true"
            @upload-files="handleFileUpload"
            @refresh-files="refreshFileTree"
            @file-selected="onFileSelected"
            @navigate-to-root="navigateToRoot"
            @navigate-to-folder="navigateToFolder"
            @select-file="selectFile"
            @open-file="openFile"
            @edit-file="editFileName"
            @delete-file="deleteFile"
            @show-context-menu="showContextMenuHandler"/>
      </a-drawer>

      <!-- 右侧配置管理抽屉 -->
      <a-drawer
          v-model:open="isConfigPanelOpen"
          placement="right"
          :width="rightPanelWidth"
          :closable="true"
          :mask="true"
          :mask-closable="true"
          class="config-panel-drawer">
        <template #title>
          <div class="drawer-title">
            <span>配置管理</span>
            <!-- 预设选择器 -->
            <div class="preset-selector">
              <a-select
                  :value="selectedPreset"
                  @change="handlePresetChange"
                  placeholder="选择预设"
                  style="width: 140px"
                  :dropdown-match-select-width="false">
                <!-- 所有预设 -->
                <a-select-option
                    v-for="preset in (allPresets || [])"
                    :key="preset.id"
                    :value="preset.id">
                  <div class="preset-option size-full flex flex-row justify-between">
                    <span class="preset-name">{{ preset.name }}</span>
                    <!-- 只对非默认配置显示删除按钮 -->
                    <a-button
                        :disabled="preset.id == 'default'"
                        type="text"
                        class="delete-btn"
                        @click.stop="deleteCustomPreset(preset.id)"
                    >
                      <DeleteOutlined/>
                    </a-button>
                  </div>
                </a-select-option>
              </a-select>
            </div>
          </div>
        </template>

        <ConfigPanel
            :ai-params="aiParams"
            :panel-width="rightPanelWidth"
            :is-resizing="false"/>

        <!-- 配置页脚 -->
        <template #footer>
          <div class="config-footer">
            <div class="footer-actions">
              <a-button
                  type="primary"
                  size="small"
                  @click="showSaveConfigDialog = true"
                  :icon="h('SaveOutlined')">
                保存配置
              </a-button>
              <a-button
                  size="small"
                  @click="resetToDefault"
                  :icon="h('ReloadOutlined')">
                重置默认
              </a-button>
            </div>
          </div>
        </template>
      </a-drawer>

      <!-- 绘画记录抽屉 -->
      <a-drawer
          v-model:open="isPaintingRecordPanelOpen"
          title="绘画记录"
          placement="right"
          :width="paintingRecordPanelWidth"
          :closable="true"
          :mask="true"
          :mask-closable="true"
          class="painting-record-panel-drawer">
        <template #title>
          <div class="drawer-title">
            <span>绘画记录</span>
            <!-- 搜索框 -->
            <div class="record-search">
              <a-input-search
                  v-model:value="paintingRecordSearch"
                  placeholder="搜索绘画记录..."
                  size="small"
                  style="width: 200px"
                  @search="searchPaintingRecords"
                  @change="onSearchChange"/>
            </div>
          </div>
        </template>

        <!-- 绘画记录列表 -->
        <div class="painting-records-container">
          <!-- 加载状态 -->
          <div v-if="isLoadingPaintingRecords" class="loading-section">
            <div class="loading-spinner">
              <div class="spinner">⏳</div>
              <div class="loading-text">加载中...</div>
            </div>
          </div>

          <!-- 记录列表 -->
          <div v-else-if="paintingRecords.length > 0" class="records-list">
            <div
                v-for="record in paintingRecords"
                :key="record.id"
                class="record-item"
                :class="{ selected: selectedRecordId === record.id }"
                @click="selectRecord(record)"
                @dblclick="openRecord(record)">
              <!-- 记录缩略图 -->
              <div class="record-thumbnail">
                <img
                    v-if="getFirstImageUrl(record.imageUrls)"
                    :src="getFirstImageUrl(record.imageUrls)"
                    :alt="record.title"
                    class="thumbnail-image"/>
                <div v-else class="no-image-placeholder">
                  <span class="placeholder-icon">🎨</span>
                </div>
              </div>

              <!-- 记录信息 -->
              <div class="record-info">
                <div class="record-title">{{ record.title || '未命名' }}</div>
                <div class="record-prompt">{{ record.prompt }}</div>
                <div class="record-meta">
                                    <span class="record-status" :class="getStatusClass(record.status)">
                                        {{ getStatusText(record.status) }}
                                    </span>
                  <span class="record-date">{{ formatDate(record.createdAt) }}</span>
                </div>
              </div>

              <!-- 操作按钮 -->
              <div class="record-actions">
                <a-button
                    type="text"
                    size="small"
                    @click.stop="openRecord(record)"
                    title="查看详情">
                  👁️
                </a-button>
                <a-button
                    type="text"
                    size="small"
                    danger
                    @click.stop="deleteRecord(record)"
                    title="删除记录">
                  🗑️
                </a-button>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-else class="empty-records">
            <div class="empty-icon">🎨</div>
            <div class="empty-text">暂无绘画记录</div>
            <div class="empty-desc">开始创作您的第一幅AI绘画吧！</div>
          </div>
        </div>

        <!-- 记录详情对话框 -->
        <a-modal
            v-model:open="showRecordDetail"
            title="绘画记录详情"
            :width="600"
            :footer="null">
          <div v-if="selectedRecord" class="record-detail">
            <div class="detail-images">
              <a-carousel v-if="getImageUrls(selectedRecord.imageUrls).length > 0">
                <div v-for="(imageUrl, index) in getImageUrls(selectedRecord.imageUrls)" :key="index">
                  <img :src="imageUrl" :alt="`图片 ${index + 1}`" class="detail-image"/>
                </div>
              </a-carousel>
            </div>

            <div class="detail-info">
              <div class="detail-item">
                <label>标题：</label>
                <span>{{ selectedRecord.title || '未命名' }}</span>
              </div>
              <div class="detail-item">
                <label>提示词：</label>
                <span>{{ selectedRecord.prompt }}</span>
              </div>
              <div class="detail-item">
                <label>状态：</label>
                <span :class="getStatusClass(selectedRecord.status)">
                                    {{ getStatusText(selectedRecord.status) }}
                                </span>
              </div>
              <div class="detail-item">
                <label>创建时间：</label>
                <span>{{ formatDate(selectedRecord.createdAt) }}</span>
              </div>
              <div class="detail-item">
                <label>更新时间：</label>
                <span>{{ formatDate(selectedRecord.updatedAt) }}</span>
              </div>
            </div>
          </div>
        </a-modal>
      </a-drawer>

      <!-- 中间主工作区 -->
      <MainWorkspace
          :has-generated-image="hasGeneratedImage"
          :generated-images="generatedImages"
          :last-prompt="lastPrompt"
          :current-image-index="currentImageIndex"
          :prompt-text="promptText"
          @download-image="downloadImage"
          @save-to-files="saveToFiles"
          @clear-result="clearResult"
          @update-current-index="currentImageIndex = $event"
          @previous-image="previousImage"
          @next-image="nextImage"
          @go-to-image="goToImage"
          @update-prompt="promptText = $event"
          @generate-image="generateImage"
          @select-template="() => {}"
          @save-images="handleSaveImages"
          @download-single-image="handleDownloadSingleImage"/>
    </div>

    <!-- 文件管理对话框 -->
    <FileModals
        v-model:show-create-dialog="showCreateDialog"
        v-model:show-rename-dialog="showRenameDialog"
        v-model:show-context-menu="showContextMenu"
        v-model:new-folder-name="newFolderName"
        v-model:new-file-name="newFileName"
        :context-menu-pos="contextMenuPos"
        :context-menu-item="contextMenuItem"
        :editing-file="editingFile"
        :is-creating="isCreating"
        :is-renaming="isRenaming"
        @create-folder="createFolder"
        @confirm-rename="confirmRename"
        @edit-file="editFileName"
        @delete-file="deleteFile"
        @refresh-files="refreshFileTree"
        @hide-context-menu="hideContextMenu"/>

    <!-- 保存配置对话框 -->
    <a-modal
        v-model:open="showSaveConfigDialog"
        title="保存配置"
        :confirm-loading="isSavingConfig"
        @ok="saveConfig"
        @cancel="() => { showSaveConfigDialog = false; newConfigName = ''; }"
        :ok-text="'保存'"
        :cancel-text="'取消'"
        :width="400">
      <div class="save-config-form">
        <div class="form-item">
          <label class="form-label">配置名称</label>
          <a-input
              v-model:value="newConfigName"
              placeholder="请输入配置名称，例如：我的高质量配置"
              :maxlength="50"
              show-count
              @keyup.enter="saveConfig"/>
        </div>
        <div class="form-item">
          <label class="form-label">当前配置预览</label>
          <div class="config-preview">
            <div class="preview-item">
              <span class="preview-label">尺寸:</span>
              <span class="preview-value">{{ aiParams.size }}</span>
            </div>
            <div class="preview-item">
              <span class="preview-label">推理步数:</span>
              <span class="preview-value">{{ aiParams.numInferenceSteps }}</span>
            </div>
            <div class="preview-item">
              <span class="preview-label">引导强度:</span>
              <span class="preview-value">{{ aiParams.guidanceScale }}</span>
            </div>
            <div class="preview-item">
              <span class="preview-label">生成数量:</span>
              <span class="preview-value">{{ aiParams.imageCount }}</span>
            </div>
          </div>
        </div>
      </div>
    </a-modal>


  </PageLayout>
</template>

<script setup lang="ts">
import {computed, h, nextTick, onMounted, reactive, ref, watch} from 'vue';
import PageLayout from '@/components/ai-bot-layout/PageLayout.vue';
import ResourcePanel from './ResourcePanel.vue';
import MainWorkspace from './MainWorkspace.vue';
import ConfigPanel from './ConfigPanel.vue';
import FileModals from './FileModals.vue';
import {message, Modal} from 'ant-design-vue';
import Axios from '@/plugins/axiosForServer';
import {useAiPaintingStore} from '../store/painting';
import { AIPainting } from '../ai-bot-interface/impl/aipainting';

// 文件系统类型定义
interface FileSystemItem {
  id: string;
  pid: string;
  name: string;
  size?: number;
  isDir: boolean;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
  childCount?: number;
}

// API 基础配置
const API_BASE = '/api/bot/files';

// 响应式数据
const fileSystemData = ref<FileSystemItem[]>([]);
const recentFiles = ref<FileSystemItem[]>([]);
const currentPath = ref<FileSystemItem[]>([]);
const currentFolderId = ref<string>('');
const selectedFileId = ref<string>('');
const isLoading = ref(false);

// 对话框状态
const showCreateDialog = ref(false);
const showRenameDialog = ref(false);
const showSaveConfigDialog = ref(false);

const newFolderName = ref('');
const newFileName = ref('');
const newConfigName = ref('');
const isCreating = ref(false);
const isRenaming = ref(false);
const isSavingConfig = ref(false);
const editingFile = ref<FileSystemItem | null>(null);

// 右键菜单
const showContextMenu = ref(false);
const contextMenuPos = reactive({x: 0, y: 0});
const contextMenuItem = ref<FileSystemItem | null>(null);

// 文件上传
const fileInput = ref<HTMLInputElement>();
const folderNameInput = ref<HTMLInputElement>();
const fileNameInput = ref<HTMLInputElement>();

// 面板状态
const leftPanelWidth = ref(400);
const rightPanelWidth = ref(380);
const isResourcePanelOpen = ref(false);
const isConfigPanelOpen = ref(false);
const resourceViewMode = ref<'list' | 'grid'>('list');

// 绘画记录面板状态
const paintingRecordPanelWidth = ref(450);
const isPaintingRecordPanelOpen = ref(false);
const paintingRecordSearch = ref('');
const isLoadingPaintingRecords = ref(false);
const paintingRecords = ref<any[]>([]);
const selectedRecordId = ref<string>('');
const selectedRecord = ref<any>(null);
const showRecordDetail = ref(false);

// 使用状态管理器
const paintingStore = useAiPaintingStore();

// 强制更新触发器
const presetUpdateTrigger = ref(0);

// 预设选择相关
const selectedPreset = computed({
  get: () => paintingStore.preset.selectedPresetId || 'default',
  set: (value: string) => paintingStore.setSelectedPreset(value)
});

// 所有预设列表
const allPresets = computed(() => {
  presetUpdateTrigger.value; // 依赖这个触发器来强制更新
  try {
    return paintingStore.getAllPresets || [];
  } catch (error) {
    console.warn('获取预设列表失败:', error);
    return [];
  }
});


// AI 绘画参数配置 - 从状态管理器获取当前参数
const aiParams = computed(() => {
  return paintingStore.getCurrentParams;
});

// 从状态管理器获取生成状态
const isGenerating = computed(() => paintingStore.ui.loading);

// 提示词输入
const promptText = ref('');

// 生成结果相关状态
const hasGeneratedImage = ref(false);
const generatedImages = ref<string[]>([]);
const currentImageIndex = ref(0);
const lastPrompt = ref('');

// 这些标记配置已移动到ConfigPanel组件中

// API 调用函数
const fileAPI = {
  // 根据父级ID查询文件列表
  async getFilesByPID(pid: string = ''): Promise<FileSystemItem[]> {
    try {
      const response = await Axios.get(`${API_BASE}/by-pid`, {
        params: {pid}
      });

      if (response.status !== 200) {
        throw new Error(`HTTP ${response.status}: ${response.data?.msg || 'Unknown error'}`);
      }

      return response.data?.data || [];
    } catch (error: any) {
      console.error('获取文件列表失败:', error);
      const errorMsg = error.response?.data?.msg || error.message || '获取文件列表失败';
      message.error(errorMsg);
      return [];
    }
  },

  // 创建文件或目录
  async createFile(data: { pid: string; name: string; isDir: boolean; size?: number }): Promise<FileSystemItem | null> {
    try {
      const response = await Axios.post(`${API_BASE}/create`, data);

      if (response.status !== 200) {
        throw new Error(`HTTP ${response.status}: ${response.data?.msg || 'Unknown error'}`);
      }

      return response.data?.data || null;
    } catch (error: any) {
      console.error('创建文件失败:', error);
      const errorMsg = error.response?.data?.msg || error.message || '创建文件失败';
      message.error(errorMsg);
      return null;
    }
  },

  // 更新文件信息
  async updateFile(data: { id: string; name?: string; pid?: string }): Promise<FileSystemItem | null> {
    try {
      const response = await Axios.post(`${API_BASE}/update`, data);

      if (response.status !== 200) {
        throw new Error(`HTTP ${response.status}: ${response.data?.msg || 'Unknown error'}`);
      }

      return response.data?.data || null;
    } catch (error: any) {
      console.error('更新文件失败:', error);
      const errorMsg = error.response?.data?.msg || error.message || '更新文件失败';
      message.error(errorMsg);
      return null;
    }
  },

  // 删除文件或目录
  async deleteFile(id: string): Promise<boolean> {
    try {
      const response = await Axios.post(`${API_BASE}/delete`, {id});

      if (response.status !== 200) {
        throw new Error(`HTTP ${response.status}: ${response.data?.msg || 'Unknown error'}`);
      }

      return true;
    } catch (error: any) {
      console.error('删除文件失败:', error);
      const errorMsg = error.response?.data?.msg || error.message || '删除文件失败';
      message.error(errorMsg);
      return false;
    }
  },

  // 移动文件
  async moveFile(fileId: string, newPid: string): Promise<FileSystemItem | null> {
    try {
      const response = await Axios.post(`${API_BASE}/move`, null, {
        params: {
          file_id: fileId,
          new_pid: newPid
        }
      });

      if (response.status !== 200) {
        throw new Error(`HTTP ${response.status}: ${response.data?.msg || 'Unknown error'}`);
      }

      return response.data?.data || null;
    } catch (error: any) {
      console.error('移动文件失败:', error);
      const errorMsg = error.response?.data?.msg || error.message || '移动文件失败';
      message.error(errorMsg);
      return null;
    }
  },

  // 获取文件树
  async getFileTree(rootPid: string = ''): Promise<FileSystemItem[]> {
    try {
      const response = await Axios.get(`${API_BASE}/tree`, {
        params: {root_pid: rootPid}
      });

      if (response.status !== 200) {
        throw new Error(`HTTP ${response.status}: ${response.data?.msg || 'Unknown error'}`);
      }

      return response.data?.data || [];
    } catch (error: any) {
      console.error('获取文件树失败:', error);
      const errorMsg = error.response?.data?.msg || error.message || '获取文件树失败';
      message.error(errorMsg);
      return [];
    }
  }
};

// 加载文件列表
const loadFiles = async (folderId: string = '') => {
  isLoading.value = true;
  try {
    const files = await fileAPI.getFilesByPID(folderId);
    fileSystemData.value = files;

    // 更新子文件夹数量
    for (const file of fileSystemData.value) {
      if (file.isDir) {
        const children = await fileAPI.getFilesByPID(file.id);
        file.childCount = children.length;
      }
    }
  } finally {
    isLoading.value = false;
  }
};

// 刷新文件树
const refreshFileTree = () => {
  loadFiles(currentFolderId.value);
};

// 导航到文件夹
const navigateToFolder = (folder: FileSystemItem, pathIndex?: number) => {
  if (pathIndex !== undefined) {
    // 点击面包屑导航
    currentPath.value = currentPath.value.slice(0, pathIndex + 1);
    currentFolderId.value = folder.id;
  } else {
    // 双击进入文件夹
    currentPath.value.push(folder);
    currentFolderId.value = folder.id;
  }
  loadFiles(currentFolderId.value);
  selectedFileId.value = '';
};

// 导航到根目录
const navigateToRoot = () => {
  currentPath.value = [];
  currentFolderId.value = '';
  loadFiles();
  selectedFileId.value = '';
};

// 选择文件
const selectFile = (file: FileSystemItem) => {
  selectedFileId.value = file.id;
};

// 打开文件
const openFile = (file: FileSystemItem) => {
  if (file.isDir) {
    navigateToFolder(file);
  } else {
    message.info(`打开文件: ${file.name}`);
    // 这里可以添加实际的文件打开逻辑
  }
};

// 创建文件夹
const createFolder = async () => {
  if (!newFolderName.value.trim()) return;

  isCreating.value = true;
  try {
    const result = await fileAPI.createFile({
      pid: currentFolderId.value,
      name: newFolderName.value.trim(),
      isDir: true
    });

    if (result) {
      message.success('文件夹创建成功');
      showCreateDialog.value = false;
      newFolderName.value = '';
      await loadFiles(currentFolderId.value);
    }
  } finally {
    isCreating.value = false;
  }
};

// 编辑文件名
const editFileName = (file: FileSystemItem) => {
  editingFile.value = file;
  newFileName.value = file.name;
  showRenameDialog.value = true;
  hideContextMenu();
};

// 确认重命名
const confirmRename = async () => {
  if (!newFileName.value.trim() || !editingFile.value) return;

  isRenaming.value = true;
  try {
    const result = await fileAPI.updateFile({
      id: editingFile.value.id,
      name: newFileName.value.trim()
    });

    if (result) {
      message.success('重命名成功');
      showRenameDialog.value = false;
      await loadFiles(currentFolderId.value);
    }
  } finally {
    isRenaming.value = false;
  }
};

// 删除文件
const deleteFile = async (file: FileSystemItem) => {
  const fileType = file.isDir ? '文件夹' : '文件';

  // 如果是文件夹，需要先检查是否有子项目
  if (file.isDir) {
    try {
      // 获取子项目列表
      const children = await fileAPI.getFilesByPID(file.id);

      if (children.length > 0) {
        // 统计子项目类型
        const folderCount = children.filter(child => child.isDir).length;
        const fileCount = children.filter(child => !child.isDir).length;

        // 构建详细的内容描述
        const contentDetails = [];
        if (folderCount > 0) {
          contentDetails.push(`${folderCount} 个文件夹`);
        }
        if (fileCount > 0) {
          contentDetails.push(`${fileCount} 个文件`);
        }
        const contentText = contentDetails.join('和');

        // 有子项目，显示警告确认对话框
        Modal.confirm({
          title: `⚠️ 删除非空文件夹`,
          content: `文件夹"${file.name}"包含 ${contentText}，共 ${children.length} 个子项目。

⚠️ 警告：删除后所有内容将永久丢失，此操作不可撤销。

确定要继续删除吗？`,
          okText: '确定删除',
          cancelText: '取消',
          okType: 'danger',
          width: 450,
          async onOk() {
            hideContextMenu();

            const success = await fileAPI.deleteFile(file.id);
            if (success) {
              message.success(`文件夹删除成功`);
              await loadFiles(currentFolderId.value);
              if (selectedFileId.value === file.id) {
                selectedFileId.value = '';
              }
            }
          },
          onCancel() {
            hideContextMenu();
          }
        });
      } else {
        // 空文件夹，显示普通确认对话框
        Modal.confirm({
          title: `删除空文件夹`,
          content: `确定要删除空文件夹"${file.name}"吗？此操作不可撤销。`,
          okText: '确定删除',
          cancelText: '取消',
          okType: 'danger',
          async onOk() {
            hideContextMenu();

            const success = await fileAPI.deleteFile(file.id);
            if (success) {
              message.success(`文件夹删除成功`);
              await loadFiles(currentFolderId.value);
              if (selectedFileId.value === file.id) {
                selectedFileId.value = '';
              }
            }
          },
          onCancel() {
            hideContextMenu();
          }
        });
      }
    } catch (error) {
      // 获取子项目失败，回退到普通确认对话框
      console.error('检查子项目失败:', error);
      Modal.confirm({
        title: `删除文件夹`,
        content: `确定要删除文件夹"${file.name}"吗？此操作不可撤销。`,
        okText: '确定删除',
        cancelText: '取消',
        okType: 'danger',
        async onOk() {
          hideContextMenu();

          const success = await fileAPI.deleteFile(file.id);
          if (success) {
            message.success(`文件夹删除成功`);
            await loadFiles(currentFolderId.value);
            if (selectedFileId.value === file.id) {
              selectedFileId.value = '';
            }
          }
        },
        onCancel() {
          hideContextMenu();
        }
      });
    }
  } else {
    // 删除文件，显示普通确认对话框
    Modal.confirm({
      title: `删除文件`,
      content: `确定要删除文件"${file.name}"吗？此操作不可撤销。`,
      okText: '确定删除',
      cancelText: '取消',
      okType: 'danger',
      async onOk() {
        hideContextMenu();

        const success = await fileAPI.deleteFile(file.id);
        if (success) {
          message.success(`文件删除成功`);
          await loadFiles(currentFolderId.value);
          if (selectedFileId.value === file.id) {
            selectedFileId.value = '';
          }
        }
      },
      onCancel() {
        hideContextMenu();
      }
    });
  }
};

// 文件上传
const handleFileUpload = () => {
  fileInput.value?.click();
};

const onFileSelected = async (files: FileList) => {
  if (!files || files.length === 0) return;

  for (const file of Array.from(files)) {
    try {
      // 这里应该有实际的文件上传逻辑
      // 现在只是模拟创建文件记录
      const result = await fileAPI.createFile({
        pid: currentFolderId.value,
        name: file.name,
        isDir: false,
        size: file.size
      });

      if (result) {
        message.success(`文件 ${file.name} 上传成功`);
      }
    } catch (error) {
      message.error(`文件 ${file.name} 上传失败`);
    }
  }

  // 刷新文件列表
  await loadFiles(currentFolderId.value);
};

// 右键菜单
const showContextMenuHandler = (event: MouseEvent, item: FileSystemItem) => {
  contextMenuItem.value = item;
  contextMenuPos.x = event.clientX;
  contextMenuPos.y = event.clientY;
  showContextMenu.value = true;

  // 点击其他地方隐藏菜单
  nextTick(() => {
    document.addEventListener('click', hideContextMenu, {once: true});
  });
};

const hideContextMenu = () => {
  showContextMenu.value = false;
  contextMenuItem.value = null;
};

// 这些工具方法已移动到ResourcePanel组件中

// 监听对话框显示，自动聚焦输入框
watch(showCreateDialog, (newVal) => {
  if (newVal) {
    nextTick(() => {
      // Ant Design Input 组件的聚焦方式
      if (folderNameInput.value) {
        try {
          (folderNameInput.value as any)?.focus?.();
        } catch (e) {
          console.warn('Focus failed:', e);
        }
      }
    });
  }
});

watch(showRenameDialog, (newVal) => {
  if (newVal) {
    nextTick(() => {
      // Ant Design Input 组件的聚焦方式
      if (fileNameInput.value) {
        try {
          (fileNameInput.value as any)?.focus?.();
          (fileNameInput.value as any)?.select?.();
        } catch (e) {
          console.warn('Focus/Select failed:', e);
        }
      }
    });
  }
});

// AI 绘画生成方法
const generateImage = async () => {
  if (isGenerating.value) return;

  // 检查提示词
  if (!promptText.value.trim()) {
    message.warning('请输入图像描述');
    return;
  }

  try {
    // 创建AIPainting实例
    let instance = new AIPainting();

    // 保存当前提示词
    lastPrompt.value = promptText.value.trim();

    // 通过实例进行请求生成，参数从状态管理内部初始化
    await instance.AiPainting(promptText.value.trim());

    // 模拟API调用延迟（实际应该由流处理完成）
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Mock生成图片数据（实际应该从流响应中获取）
    const mockImages = [
      'https://raw.githubusercontent.com/vueComponent/ant-design-vue/main/components/carousel/demo/abstract01.jpg',
      'https://raw.githubusercontent.com/vueComponent/ant-design-vue/main/components/carousel/demo/abstract02.jpg',
      'https://raw.githubusercontent.com/vueComponent/ant-design-vue/main/components/carousel/demo/abstract03.jpg',
      'https://raw.githubusercontent.com/vueComponent/ant-design-vue/main/components/carousel/demo/abstract04.jpg',
    ];

    // 根据用户设置的图片数量截取对应数量的mock图片
    const imageCount = Math.min(aiParams.value.imageCount || 2, mockImages.length);
    generatedImages.value = mockImages.slice(0, imageCount);

    // 更新状态
    hasGeneratedImage.value = true;
    currentImageIndex.value = 0;

    message.success('图像生成成功！');

  } catch (error: any) {
    console.error('生成图像失败:', error);
    message.error('生成图像失败: ' + (error.message || '未知错误'));
  }
};

// 结果操作方法
const downloadImage = () => {
  if (generatedImages.value.length === 0) {
    message.warning('没有可下载的图像');
    return;
  }

  const currentImage = generatedImages.value[currentImageIndex.value];
  if (!currentImage) {
    message.warning('当前图像不存在');
    return;
  }

  // 创建下载链接
  const link = document.createElement('a');
  link.href = currentImage;
  link.download = `generated-image-${currentImageIndex.value + 1}-${Date.now()}.png`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

  message.success(`图像 ${currentImageIndex.value + 1} 下载成功`);
};

const saveToFiles = async () => {
  if (generatedImages.value.length === 0) {
    message.warning('没有可保存的图像');
    return;
  }

  const currentImage = generatedImages.value[currentImageIndex.value];
  if (!currentImage) {
    message.warning('当前图像不存在');
    return;
  }

  try {
    // 这里可以调用API保存到文件系统
    await fileAPI.createFile({
      pid: currentFolderId.value,
      name: `generated-${currentImageIndex.value + 1}-${Date.now()}.png`,
      isDir: false,
      size: 1024 // 模拟文件大小
    });

    message.success(`图像 ${currentImageIndex.value + 1} 已保存到文件夹`);
    await loadFiles(currentFolderId.value); // 刷新文件列表
  } catch (error) {
    message.error('保存失败');
  }
};

// 保存图片数据接口
interface SaveImageData {
  folderName: string;
  saveCurrentOnly: boolean;
  saveAllImages: boolean;
  currentIndex: number;
  images: string[];
  prompt: string;
}

// 处理新的保存图片事件
const handleSaveImages = async (data: SaveImageData) => {
  try {
    // 首先创建或查找文件夹
    let targetFolderId = currentFolderId.value;

    // 创建新文件夹
    const folder = await fileAPI.createFile({
      pid: currentFolderId.value,
      name: data.folderName,
      isDir: true
    });

    if (!folder) {
      message.error('创建文件夹失败');
      return;
    }

    targetFolderId = folder.id;

    // 根据选项保存图片
    const imagesToSave = data.saveCurrentOnly
        ? [{image: data.images[data.currentIndex], index: data.currentIndex}]
        : data.images.map((image, index) => ({image, index}));

    let successCount = 0;

    for (const {image, index} of imagesToSave) {
      try {
        // 生成文件名
        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
        const promptPrefix = data.prompt ? data.prompt.slice(0, 20).replace(/[^\w\u4e00-\u9fa5]/g, '_') : 'AI画作';
        const fileName = `${promptPrefix}_${index + 1}_${timestamp}.png`;

        // 保存文件记录到数据库（这里只是创建文件记录，实际图片保存需要后端支持）
        const result = await fileAPI.createFile({
          pid: targetFolderId,
          name: fileName,
          isDir: false,
          size: 1024 // 模拟文件大小
        });

        if (result) {
          successCount++;
        }
      } catch (error) {
        console.error(`保存图片 ${index + 1} 失败:`, error);
      }
    }

    if (successCount > 0) {
      message.success(`成功保存 ${successCount} 张图片到文件夹"${data.folderName}"`);
      await loadFiles(currentFolderId.value); // 刷新文件列表
    } else {
      message.error('保存失败，请重试');
    }

  } catch (error) {
    console.error('保存图片失败:', error);
    message.error('保存失败: ' + (error instanceof Error ? error.message : '未知错误'));
  }
};

// 处理下载单张图片事件
const handleDownloadSingleImage = (index: number) => {
  if (index < 0 || index >= generatedImages.value.length) {
    message.warning('图片索引无效');
    return;
  }

  const imageUrl = generatedImages.value[index];
  if (!imageUrl) {
    message.warning('图片不存在');
    return;
  }

  // 创建下载链接
  const link = document.createElement('a');
  link.href = imageUrl;

  // 生成文件名
  const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
  const promptPrefix = lastPrompt.value ? lastPrompt.value.slice(0, 20).replace(/[^\w\u4e00-\u9fa5]/g, '_') : 'AI画作';
  link.download = `${promptPrefix}_${index + 1}_${timestamp}.png`;

  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

  message.success(`图片 ${index + 1} 下载成功`);
};

const clearResult = () => {
  hasGeneratedImage.value = false;
  generatedImages.value = [];
  currentImageIndex.value = 0;
  lastPrompt.value = '';
  message.info('已清除生成结果');
};

// 轮播图控制方法
const previousImage = () => {
  if (currentImageIndex.value > 0) {
    currentImageIndex.value--;
  }
};

const nextImage = () => {
  if (currentImageIndex.value < generatedImages.value.length - 1) {
    currentImageIndex.value++;
  }
};

const goToImage = (index: number) => {
  if (index >= 0 && index < generatedImages.value.length) {
    currentImageIndex.value = index;
  }
};

// 其他原有功能保持不变
const throttle = (func: Function, delay: number) => {
  let timeoutId: number | null = null;
  let lastExecTime = 0;
  return function (...args: any[]) {
    const currentTime = Date.now();

    if (currentTime - lastExecTime > delay) {
      func(...args);
      lastExecTime = currentTime;
    } else {
      if (timeoutId) clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        func(...args);
        lastExecTime = Date.now();
      }, delay - (currentTime - lastExecTime));
    }
  };
};

const toggleResourcePanel = () => {
  isResourcePanelOpen.value = !isResourcePanelOpen.value;
};

const toggleConfigPanel = () => {
  isConfigPanelOpen.value = !isConfigPanelOpen.value;
};

const togglePaintingRecordPanel = () => {
  isPaintingRecordPanelOpen.value = !isPaintingRecordPanelOpen.value;
  if (isPaintingRecordPanelOpen.value) {
    loadPaintingRecords();
  }
};

const setResourceViewMode = (mode: 'list' | 'grid') => {
  resourceViewMode.value = mode;
};

// 绘画记录相关方法
const loadPaintingRecords = async () => {
  isLoadingPaintingRecords.value = true;
  try {
    const response = await Axios.get('/api/bot/painting-records');
    if (response.status === 200) {
      paintingRecords.value = response.data.data || [];
    }
  } catch (error: any) {
    console.error('加载绘画记录失败:', error);
    message.error('加载绘画记录失败');
  } finally {
    isLoadingPaintingRecords.value = false;
  }
};

const searchPaintingRecords = async () => {
  if (!paintingRecordSearch.value.trim()) {
    await loadPaintingRecords();
    return;
  }

  isLoadingPaintingRecords.value = true;
  try {
    const response = await Axios.post('/api/bot/painting-records/query', {
      title: paintingRecordSearch.value.trim()
    });
    if (response.status === 200) {
      paintingRecords.value = response.data.data || [];
    }
  } catch (error: any) {
    console.error('搜索绘画记录失败:', error);
    message.error('搜索绘画记录失败');
  } finally {
    isLoadingPaintingRecords.value = false;
  }
};

const onSearchChange = (e: any) => {
  if (!e.target.value.trim()) {
    loadPaintingRecords();
  }
};

const selectRecord = (record: any) => {
  selectedRecordId.value = record.id;
};

const openRecord = (record: any) => {
  selectedRecord.value = record;
  showRecordDetail.value = true;
};

const deleteRecord = async (record: any) => {
  Modal.confirm({
    title: '删除绘画记录',
    content: `确定要删除绘画记录"${record.title || '未命名'}"吗？此操作不可撤销。`,
    okText: '确定删除',
    cancelText: '取消',
    okType: 'danger',
    async onOk() {
      try {
        const response = await Axios.post('/api/bot/painting-records/delete', {
          id: record.id
        });
        if (response.status === 200) {
          message.success('删除成功');
          await loadPaintingRecords();
          if (selectedRecordId.value === record.id) {
            selectedRecordId.value = '';
          }
        }
      } catch (error: any) {
        console.error('删除绘画记录失败:', error);
        message.error('删除失败');
      }
    }
  });
};

const getFirstImageUrl = (imageUrls: string) => {
  if (!imageUrls) return '';
  const urls = imageUrls.split(',').filter(url => url.trim());
  return urls.length > 0 ? urls[0].trim() : '';
};

const getImageUrls = (imageUrls: string) => {
  if (!imageUrls) return [];
  return imageUrls.split(',').filter(url => url.trim()).map(url => url.trim());
};

const getStatusClass = (status: string) => {
  switch (status) {
    case 'completed':
      return 'status-completed';
    case 'processing':
      return 'status-processing';
    case 'failed':
      return 'status-failed';
    default:
      return 'status-pending';
  }
};

const getStatusText = (status: string) => {
  switch (status) {
    case 'completed':
      return '已完成';
    case 'processing':
      return '处理中';
    case 'failed':
      return '失败';
    default:
      return '等待中';
  }
};

const formatDate = (dateString: string) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 处理预设选择变化
const handlePresetChange = (presetKey: string) => {
  try {
    selectedPreset.value = presetKey;
    // 状态管理器会自动同步配置，computed 会自动更新
  } catch (error) {
    console.error('切换预设配置失败:', error);
    message.error('切换预设配置失败，请重试');
  }
};

// 保存配置
const saveConfig = async () => {
  if (!newConfigName.value.trim()) {
    message.warning('请输入配置名称');
    return;
  }

  isSavingConfig.value = true;
  try {
    console.log('开始保存配置，当前参数:', aiParams.value);

    // 创建新的预设配置
    const newPreset = {
      name: newConfigName.value.trim(),
      promptTemplate: aiParams.value.promptTemplate,
      size: aiParams.value.size,
      guidanceScale: aiParams.value.guidanceScale,
      negativePrompt: aiParams.value.negativePrompt,
      width: aiParams.value.width,
      height: aiParams.value.height,
      useCustomSize: aiParams.value.useCustomSize,
      imageCount: aiParams.value.imageCount,
      numInferenceSteps: aiParams.value.numInferenceSteps
    };

    console.log('准备添加的预设配置:', newPreset);

    // 添加到状态管理器
    const newPresetId = await paintingStore.addCustomPreset(newPreset);

    console.log('配置添加成功，新ID:', newPresetId);

    // 触发预设列表更新
    presetUpdateTrigger.value++;

    // 切换到新保存的配置
    selectedPreset.value = newPresetId;

    message.success('配置保存成功！');
    showSaveConfigDialog.value = false;
    newConfigName.value = '';
  } catch (error) {
    console.error('保存配置失败:', error);
    message.error('保存配置失败: ' + (error instanceof Error ? error.message : '未知错误'));
  } finally {
    isSavingConfig.value = false;
  }
};

// 重置为默认配置
const resetToDefault = () => {
  Modal.confirm({
    title: '重置配置',
    content: '确定要重置为默认配置吗？当前的自定义设置将丢失。',
    okText: '确定重置',
    cancelText: '取消',
    okType: 'danger',
    async onOk() {
      paintingStore.resetToDefault();
      // computed 会自动更新，无需手动同步
      message.success('已重置为默认配置');
    }
  });
};

// 删除自定义预设
const deleteCustomPreset = (presetId: string) => {
  const preset = (allPresets.value || []).find(p => p.id === presetId);
  if (!preset) return;

  Modal.confirm({
    title: '删除配置',
    content: `确定要删除配置"${preset.name}"吗？此操作不可撤销。`,
    okText: '确定删除',
    cancelText: '取消',
    okType: 'danger',
    async onOk() {
      await paintingStore.removeCustomPreset(presetId);
      // 触发预设列表更新
      presetUpdateTrigger.value++;

      // 检查是否还有非默认配置
      const remainingPresets = allPresets.value || [];
      const hasNonDefaultPresets = remainingPresets.some(p => p.id !== 'default');

      // 如果没有非默认配置了，自动切换到默认配置
      if (!hasNonDefaultPresets) {
        selectedPreset.value = 'default';
        message.success('配置删除成功，已切换到默认配置');
      } else {
        message.success('配置删除成功');
      }
    }
  });
};


// 组件挂载时加载文件
onMounted(async () => {
  // 初始化状态管理器（现在是从服务器加载）
  await paintingStore.initializeStore();

  // 触发预设列表更新
  presetUpdateTrigger.value++;

  loadFiles();
});
</script>

<style scoped>
.painting-editor-container {
  height: 100%;
  background: var(--ant-color-bg-layout);
  position: relative; /* 为悬浮按钮提供定位上下文 */
}

/* 主容器样式 */
.painting-editor-container {
  background: var(--ant-color-bg-layout);
  position: relative;
  height: 100vh;
  width: 100%;
  overflow: hidden;
}


/* 工具栏 */
.toolbar-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px 0;
  border-bottom: 1px solid var(--ant-color-border-secondary);
}

.toolbar-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  background: var(--ant-color-bg-container);
  color: var(--ant-color-text);
  border: 1px solid var(--ant-color-border);
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.action-btn:hover {
  background: var(--ant-color-bg-text-hover);
  border-color: var(--ant-color-primary);
  color: var(--ant-color-primary);
}

.action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: var(--ant-color-bg-layout);
  border-color: var(--ant-color-border-secondary);
  color: var(--ant-color-text-secondary);
}

.btn-icon {
  font-size: 16px;
}

.btn-text {
  font-weight: 500;
}

.action-btn.spinning .btn-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 当前路径显示 */
.breadcrumb-section {
  margin-bottom: 12px;
  padding: 8px 0;
  border-bottom: 1px solid var(--ant-color-border-secondary);
}

.breadcrumb {
  display: flex;
  align-items: center;
  font-size: 13px;
  color: var(--ant-color-text-secondary);
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: var(--ant-color-text-secondary);
  transition: color 0.2s;
}

.breadcrumb-item:hover {
  color: var(--ant-color-primary);
}

.breadcrumb-item:not(:last-child) {
  margin-right: 4px;
}

.breadcrumb-separator {
  margin: 0 4px;
  font-size: 10px;
  color: var(--ant-color-text-secondary);
}

/* 加载状态 */
.loading-section {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px 0;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: var(--ant-color-primary);
}

.spinner {
  font-size: 36px;
  margin-bottom: 8px;
}

.loading-text {
  font-size: 14px;
  color: var(--ant-color-text-secondary);
}

/* 资源管理区域 */
.resource-section {
  margin-bottom: 24px;
  padding: 8px;
  border: 1px solid var(--ant-color-border-secondary);
  border-radius: 6px;
  background: var(--ant-color-bg-layout);
}

.section-title {
  font-size: 12px;
  font-weight: 600;
  color: var(--ant-color-text-secondary);
  margin-bottom: 8px;
  padding: 4px 8px;
  text-transform: uppercase;
  background: var(--ant-color-bg-container);
  border-radius: 4px;
  border: 1px solid var(--ant-color-border-secondary);
}

.section-title .file-count {
  font-size: 11px;
  color: var(--ant-color-text-secondary);
  margin-left: 8px;
}

.empty-folder {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
  color: var(--ant-color-text-secondary);
  font-size: 14px;
}

.empty-folder .empty-icon {
  font-size: 48px;
  margin-bottom: 12px;
}

.empty-folder .create-first-btn {
  margin-top: 16px;
  padding: 8px 16px;
  background: var(--ant-color-primary);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.empty-folder .create-first-btn:hover {
  background: var(--ant-color-primary-hover);
}

.resource-tree {
  padding: 0 4px;
}

.list-view .resource-tree {
  padding: 0;
  animation: fadeInUp 0.3s ease-out;
}

.grid-view .resource-tree {
  padding: 0;
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.tree-item {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  color: var(--ant-color-text);
  transition: background-color 0.2s;
  position: relative;
}

.tree-item:hover {
  background: var(--ant-color-bg-text-hover);
}

.tree-item.selected {
  background: var(--ant-color-primary-bg);
  color: var(--ant-color-primary);
  font-weight: 500;
}

.tree-item.sub-item {
  background: var(--ant-color-bg-layout);
}

.folder-icon, .file-icon {
  margin-right: 8px;
  font-size: 14px;
  flex-shrink: 0;
}

.folder-icon-large, .file-icon-large {
  font-size: 24px;
  margin-right: 8px;
  flex-shrink: 0;
}

.item-name {
  flex: 1;
  margin-left: 8px;
  font-weight: 500;
  color: var(--ant-color-text);
}

.item-info {
  font-size: 11px;
  color: var(--ant-color-text-secondary);
  margin-left: 8px;
}

.item-date {
  font-size: 11px;
  color: var(--ant-color-text-secondary);
  margin-left: 12px;
}

.item-actions {
  display: flex;
  gap: 4px;
  margin-left: auto;
  flex-shrink: 0;
}

.action-menu-btn {
  background: none;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  color: var(--ant-color-text-secondary);
  transition: all 0.2s;
  font-size: 16px;
  font-weight: bold;
  min-width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-menu-btn:hover {
  background: var(--ant-color-bg-text-hover);
  color: var(--ant-color-text);
}

.recent-files {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  padding: 0 4px;
}

.recent-files.list-view {
  grid-template-columns: 1fr;
  animation: fadeInUp 0.3s ease-out;
}

.recent-files.grid-view {
  grid-template-columns: 1fr 1fr;
  animation: fadeInUp 0.3s ease-out;
}

.recent-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
  border-radius: 6px;
  background: var(--ant-color-bg-layout);
  cursor: pointer;
  transition: all 0.2s;
}

.recent-item:hover {
  background: var(--ant-color-bg-text-hover);
  transform: translateY(-1px);
}

.recent-item-list {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  color: var(--ant-color-text);
  transition: background-color 0.2s;
}

.recent-item-list:hover {
  background: var(--ant-color-bg-text-hover);
}

.recent-item-list .file-icon {
  margin-right: 8px;
  font-size: 14px;
}

.recent-item-list .item-name {
  flex: 1;
  margin-left: 8px;
  font-weight: 500;
  color: var(--ant-color-text);
}

.recent-item-list .item-info {
  font-size: 11px;
  color: var(--ant-color-text-secondary);
  margin-left: 8px;
}

.recent-item .thumbnail {
  width: 40px;
  height: 30px;
  background: var(--ant-color-bg-text-active);
  border-radius: 4px;
  margin-bottom: 4px;
}

.recent-item .file-name {
  font-size: 11px;
  color: var(--ant-color-text-secondary);
  text-align: center;
}

.recent-item .file-time {
  font-size: 11px;
  color: var(--ant-color-text-secondary);
  margin-top: 4px;
}

.folder-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  margin-bottom: 16px;
}

.file-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.grid-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
  border-radius: 6px;
  background: var(--ant-color-bg-layout);
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
}

.grid-item:hover {
  background: var(--ant-color-bg-text-hover);
  transform: translateY(-1px);
}

.grid-item.is-folder {
  background: var(--ant-color-bg-layout);
}

.grid-item.selected {
  background: var(--ant-color-primary-bg);
  color: var(--ant-color-primary);
}

.grid-item .item-thumbnail {
  width: 40px;
  height: 30px;
  background: var(--ant-color-bg-text-active);
  border-radius: 4px;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--ant-color-border-secondary);
  position: relative;
  overflow: hidden;
}

.grid-item .item-thumbnail::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
  rgba(255, 255, 255, 0.1) 0%,
  transparent 50%,
  rgba(0, 0, 0, 0.1) 100%
  );
  pointer-events: none;
}

.grid-item .item-details {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.grid-item .item-name {
  font-size: 12px;
  font-weight: 600;
  color: var(--ant-color-text);
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
}

.grid-item .item-size {
  font-size: 11px;
  color: var(--ant-color-text-secondary);
  margin-top: 4px;
}

.grid-item .item-count {
  font-size: 11px;
  color: var(--ant-color-text-secondary);
  margin-top: 4px;
}

.grid-item .item-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 6px;
  opacity: 0;
  transition: opacity 0.2s ease;
  pointer-events: none;
}

.grid-item:hover .item-overlay {
  opacity: 1;
}

.grid-item .overlay-btn {
  background: none;
  border: none;
  padding: 4px;
  border-radius: 4px;
  cursor: pointer;
  color: var(--ant-color-text-secondary);
  transition: all 0.2s;
  font-size: 14px;
}

.grid-item .overlay-btn:hover {
  background: var(--ant-color-bg-text-hover);
  color: var(--ant-color-text);
}

.expanded-files {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--ant-color-border-secondary);
}

.expanded-files .section-subtitle {
  font-size: 11px;
  font-weight: 600;
  color: var(--ant-color-text-secondary);
  margin-bottom: 8px;
  padding: 4px 8px;
  text-transform: uppercase;
  background: var(--ant-color-bg-container);
  border-radius: 4px;
  border: 1px solid var(--ant-color-border-secondary);
}

.grid-file-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
  border-radius: 6px;
  background: var(--ant-color-bg-layout);
  cursor: pointer;
  transition: all 0.2s;
}

.grid-file-item:hover {
  background: var(--ant-color-bg-text-hover);
  transform: translateY(-1px);
}

.grid-file-item .file-thumbnail {
  width: 40px;
  height: 30px;
  background: var(--ant-color-bg-text-active);
  border-radius: 4px;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--ant-color-border-secondary);
  position: relative;
  overflow: hidden;
}

.grid-file-item .file-thumbnail::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
  rgba(255, 255, 255, 0.1) 0%,
  transparent 50%,
  rgba(0, 0, 0, 0.1) 100%
  );
  pointer-events: none;
}

.grid-file-item .file-name {
  font-size: 11px;
  color: var(--ant-color-text-secondary);
  text-align: center;
}

.grid-file-item .file-size {
  font-size: 11px;
  color: var(--ant-color-text-secondary);
  margin-top: 4px;
}

/* 主工作区 */
.main-workspace {
  background: var(--ant-color-bg-layout);
}

.workspace-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

/* 生成结果展示区域 */
.result-display-area {
  flex: 1;
  padding: 16px;
  background: var(--ant-color-bg-layout);
  border-bottom: 1px solid var(--ant-color-border);
  overflow: auto;
}

.result-container {
  max-width: 1000px;
  margin: 0 auto;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--ant-color-border-secondary);
}

.result-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--ant-color-text);
}

.result-actions {
  display: flex;
  gap: 8px;
}

.result-image-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: var(--ant-color-bg-container);
  border-radius: 8px;
  border: 1px solid var(--ant-color-border-secondary);
  min-height: 450px;
  max-height: 75vh;
  overflow: hidden;
}

/* 轮播图容器样式 */
.generated-images-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.image-carousel {
  flex: 1;
  background: var(--ant-color-bg-layout);
}

.carousel-slide {
  height: 100%;
  display: flex !important;
  align-items: center;
  justify-content: center;
  padding: 4px;
}

.generated-image {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;
  box-sizing: border-box;
}

.result-image {
  max-width: 95%;
  max-height: 95%;
  width: auto;
  height: auto;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.result-image:hover {
  transform: scale(1.02);
}

/* 轮播图控制器样式 */
.carousel-controls {
  padding: 12px 16px;
  background: var(--ant-color-bg-container);
  border-top: 1px solid var(--ant-color-border-secondary);
}

.carousel-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.nav-btn {
  font-size: 12px;
  height: 28px;
  padding: 0 12px;
}

.image-counter {
  font-size: 14px;
  font-weight: 500;
  color: var(--ant-color-text);
  background: var(--ant-color-bg-layout);
  padding: 6px 12px;
  border-radius: 4px;
  border: 1px solid var(--ant-color-border-secondary);
}

/* 缩略图导航样式 */
.thumbnail-nav {
  display: flex;
  gap: 8px;
  justify-content: center;
  overflow-x: auto;
  padding: 4px 0;
}

.thumbnail-item {
  width: 60px;
  height: 45px;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.thumbnail-item:hover {
  border-color: var(--ant-color-primary-hover);
  transform: scale(1.05);
}

.thumbnail-item.active {
  border-color: var(--ant-color-primary);
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

.thumbnail-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.3s ease;
}

.thumbnail-item:hover .thumbnail-image {
  opacity: 0.8;
}

.generating-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 200px;
}

.generating-animation {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  color: var(--ant-color-primary);
}

.generating-icon {
  font-size: 48px;
  animation: pulse 2s ease-in-out infinite;
}

.generating-text {
  font-size: 16px;
  font-weight: 500;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
}

/* 输入控制区域 */
.input-control-area {
  padding: 24px;
  background: var(--ant-color-bg-container);
  border-top: 1px solid var(--ant-color-border);
}

.input-control-area.has-result {
  padding: 16px 24px;
}

.input-container {
  max-width: 600px;
  margin: 0 auto;
  text-align: center;
}

.welcome-section {
  margin-bottom: 24px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .result-display-area {
    padding: 12px;
  }

  .result-container {
    max-width: 100%;
  }

  .result-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .result-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .input-control-area {
    padding: 16px;
  }

  .input-control-area.has-result {
    padding: 12px 16px;
  }

  .input-container {
    max-width: 100%;
  }
}

.placeholder-icon {
  margin-bottom: 24px;
}

.placeholder-icon .icon {
  font-size: 64px;
  opacity: 0.8;
}

.welcome-section h3 {
  margin: 0 0 12px 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--ant-color-text);
}

.welcome-section p {
  margin: 0 0 24px 0;
  font-size: 14px;
  line-height: 1.5;
  color: var(--ant-color-text-secondary);
}

/* 提示词输入区域样式 */
.prompt-input-section {
  width: 100%;
  max-width: 600px;
  margin: 0 auto 24px auto;
}

.prompt-textarea {
  border-radius: 8px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
  border: 1px solid var(--ant-color-border) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.prompt-textarea:hover {
  border-color: var(--ant-color-primary-hover) !important;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.1) !important;
}

.prompt-textarea:focus {
  border-color: var(--ant-color-primary) !important;
  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.15) !important;
}

.prompt-textarea .ant-input {
  font-size: 14px;
  line-height: 1.6;
  padding: 12px 16px;
}

.prompt-textarea .ant-input::placeholder {
  color: var(--ant-color-text-quaternary);
  font-style: italic;
}

.quick-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  align-items: center;
}

.generate-btn {
  height: 44px !important;
  font-size: 16px !important;
  font-weight: 500 !important;
  border-radius: 8px !important;
  padding: 0 24px !important;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.generate-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.3);
}

.generate-btn:active {
  transform: translateY(0);
}

.action-btn.primary {
  background: var(--ant-color-primary);
  color: white;
  border-color: var(--ant-color-primary);
}

.action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 配置管理区域 */
.config-section {
  margin-bottom: 20px;
  padding: 8px;
  border: 1px solid var(--ant-color-border-secondary);
  border-radius: 6px;
  background: var(--ant-color-bg-layout);
}

.config-section .section-title {
  font-size: 12px;
  font-weight: 600;
  color: var(--ant-color-text-secondary);
  margin-bottom: 8px;
  padding: 4px 8px;
  text-transform: uppercase;
  background: var(--ant-color-bg-container);
  border-radius: 4px;
  border: 1px solid var(--ant-color-border-secondary);
}

/* AI 参数配置样式 */
.ai-params {
  padding: 12px;
}

.param-item {
  margin-bottom: 20px;
}

.param-label {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 13px;
  font-weight: 500;
  color: var(--ant-color-text);
}

.param-tooltip {
  font-size: 12px;
  color: var(--ant-color-text-secondary);
  cursor: help;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.param-tooltip:hover {
  opacity: 1;
}

.slider-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.slider-container .ant-slider {
  flex: 1;
}

.param-input-number {
  width: 80px;
  flex-shrink: 0;
}

.param-switch {
  display: flex;
  align-items: center;
  gap: 8px;
}

.switch-label {
  font-size: 13px;
  color: var(--ant-color-text);
}


/* 自定义 Ant Design 组件样式 */
.ai-params .ant-select,
.ai-params .ant-input-number,
.ai-params .ant-input {
  border-radius: 6px;
}

.ai-params .ant-textarea {
  border-radius: 6px;
  resize: vertical;
}

.ai-params .ant-slider-track {
  background: var(--ant-color-primary);
}

.ai-params .ant-slider-handle {
  border-color: var(--ant-color-primary);
}

.ai-params .ant-slider-handle:hover {
  border-color: var(--ant-color-primary-hover);
}

.ai-params .ant-slider-handle:focus {
  border-color: var(--ant-color-primary-hover);
  box-shadow: 0 0 0 5px rgba(24, 144, 255, 0.12);
}

.ai-params .ant-btn-primary {
  height: 44px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.ai-params .ant-btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.3);
}

.ai-params .ant-btn-primary:active {
  transform: translateY(0);
}

.ai-params .ant-btn-loading {
  cursor: not-allowed;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .slider-container {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .param-input-number {
    width: 100%;
  }

  .ai-params {
    padding: 8px;
  }

  .param-item {
    margin-bottom: 16px;
  }

  /* 提示词输入框移动端样式 */
  .prompt-input-section {
    max-width: 100%;
    margin: 0 0 20px 0;
  }

  .canvas-placeholder {
    padding: 32px 16px;
  }

  .quick-actions {
    flex-direction: column;
    gap: 8px;
  }

  .generate-btn {
    width: 100% !important;
  }

  /* 轮播图移动端样式 */
  .carousel-controls {
    padding: 8px 12px;
  }

  .carousel-nav {
    flex-direction: column;
    gap: 8px;
    margin-bottom: 8px;
  }

  .image-counter {
    order: -1;
    align-self: center;
  }

  .nav-btn {
    flex: 1;
    max-width: 120px;
  }

  .thumbnail-nav {
    gap: 6px;
    padding: 2px 0;
  }

  .thumbnail-item {
    width: 50px;
    height: 38px;
  }

  .carousel-slide {
    padding: 8px;
  }

  .result-image-area {
    min-height: 250px;
  }

  .generated-image {
    padding: 12px;
  }

  .result-image {
    max-width: 90%;
    max-height: 90%;
  }
}

.layers-list {
  padding: 0 4px;
}

.layer-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  color: var(--ant-color-text);
  transition: background-color 0.2s;
}

.layer-item:hover {
  background: var(--ant-color-bg-text-hover);
}

.layer-item.active {
  background: var(--ant-color-primary-bg);
  color: var(--ant-color-primary);
}

.layer-icon {
  margin-right: 8px;
  font-size: 14px;
}

.layer-name {
  flex: 1;
}

.layer-opacity {
  font-size: 11px;
  color: var(--ant-color-text-secondary);
}

.properties {
  padding: 0 4px;
}

.property-item {
  margin-bottom: 12px;
}

.property-item label {
  display: block;
  font-size: 12px;
  color: var(--ant-color-text-secondary);
  margin-bottom: 4px;
}

.property-item input[type="range"] {
  width: 100%;
  height: 4px;
  border-radius: 2px;
  background: var(--ant-color-bg-layout);
  outline: none;
}

.property-item select {
  width: 100%;
  padding: 4px 8px;
  border: 1px solid var(--ant-color-border);
  border-radius: 4px;
  background: var(--ant-color-bg-container);
  color: var(--ant-color-text);
  font-size: 12px;
}

.color-picker {
  padding: 0 4px;
}

.color-palette {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 4px;
}

.color-item {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  cursor: pointer;
  border: 1px solid var(--ant-color-border);
  transition: transform 0.2s;
}

.color-item:hover {
  transform: scale(1.1);
}


/* 悬浮迷你展开按钮 */
.floating-expand-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: var(--ant-color-bg-container);
  border: 2px solid var(--ant-color-primary-border);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15),
  0 0 0 1px rgba(24, 144, 255, 0.1);
  z-index: 100;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0.9;
  backdrop-filter: blur(8px);
}

.left-floating-btn {
  left: 8px;
}

.right-floating-btn {
  right: 8px;
}

.floating-expand-btn:hover {
  background: var(--ant-color-primary-bg);
  border-color: var(--ant-color-primary);
  opacity: 1;
  transform: translateY(-50%) scale(1.15);
  box-shadow: 0 6px 20px rgba(24, 144, 255, 0.3),
  0 0 0 2px rgba(24, 144, 255, 0.2);
}

.floating-expand-btn:active {
  transform: translateY(-50%) scale(1.05);
  transition: all 0.15s ease;
}

.floating-expand-btn .expand-icon {
  font-size: 20px;
  color: var(--ant-color-primary);
  transition: all 0.3s ease;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.floating-expand-btn:hover .expand-icon {
  transform: scale(1.1);
  color: var(--ant-color-primary);
}

/* 添加呼吸灯效果 */
.floating-expand-btn::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 50%;
  background: linear-gradient(45deg,
  var(--ant-color-primary),
  var(--ant-color-primary-hover),
  var(--ant-color-primary));
  opacity: 0;
  z-index: -1;
  transition: opacity 0.3s ease;
  animation: breathe 3s ease-in-out infinite;
}

.floating-expand-btn:hover::before {
  opacity: 0.3;
}

@keyframes breathe {
  0%, 100% {
    opacity: 0.1;
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(1.05);
  }
}


/* 抽屉组件样式 */
.resource-panel-drawer {
  z-index: 1000;
}

.resource-panel-drawer :deep(.ant-drawer-content) {
  background: var(--ant-color-bg-container);
}

.resource-panel-drawer :deep(.ant-drawer-mask) {
  z-index: 999;
}

.config-panel-drawer {
  z-index: 1000;
}

.config-panel-drawer :deep(.ant-drawer-content) {
  background: var(--ant-color-bg-container);
}

.config-panel-drawer :deep(.ant-drawer-mask) {
  z-index: 999;
}

.drawer-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  font-size: 16px;
  font-weight: 600;
}

/* 抽屉中的视图切换按钮 */
.drawer-title .view-toggle {
  display: flex;
  gap: 2px;
  background: var(--ant-color-bg-layout);
  border-radius: 6px;
  padding: 2px;
  border: 1px solid var(--ant-color-border-secondary);
}

/* 预设选择器样式 */
.drawer-title .preset-selector {
  flex-shrink: 0;
}

.drawer-title .preset-selector .ant-select {
  font-size: 12px;
}

.drawer-title .preset-selector .ant-select-selector {
  border-radius: 4px;
  border: 1px solid var(--ant-color-border-secondary);
  background: var(--ant-color-bg-container);
}

.drawer-title .preset-selector .ant-select-focused .ant-select-selector {
  border-color: var(--ant-color-primary);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

.drawer-title .view-btn {
  background: none;
  border: none;
  padding: 6px;
  border-radius: 4px;
  cursor: pointer;
  color: var(--ant-color-text-secondary);
  transition: all 0.2s ease;
  min-width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.drawer-title .view-btn:hover {
  background: var(--ant-color-bg-text-hover);
  color: var(--ant-color-text);
  transform: scale(1.05);
}

.drawer-title .view-btn.active {
  background: var(--ant-color-bg-container);
  color: var(--ant-color-primary);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transform: scale(1);
}

.drawer-title .view-icon {
  font-size: 14px;
  font-weight: bold;
}

/* Vue Transition 动画 */
.floating-btn-enter-active {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.floating-btn-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.floating-btn-enter-from {
  opacity: 0;
  transform: translateY(-50%) translateX(-30px) scale(0.8);
}

.floating-btn-leave-to {
  opacity: 0;
  transform: translateY(-50%) translateX(-30px) scale(0.8);
}

.right-floating-btn.floating-btn-enter-from {
  opacity: 0;
  transform: translateY(-50%) translateX(30px) scale(0.8);
}

.right-floating-btn.floating-btn-leave-to {
  opacity: 0;
  transform: translateY(-50%) translateX(30px) scale(0.8);
}

/* 移动端 transition 动画调整 */
@media (max-width: 768px) {
  .floating-btn-enter-from {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px) scale(0.8);
  }

  .floating-btn-leave-to {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px) scale(0.8);
  }
}

/* 抽屉内容优化 */
.resource-panel-drawer :deep(.ant-drawer-body) {
  padding: 0;
  overflow-x: hidden;
}

.config-panel-drawer :deep(.ant-drawer-body) {
  padding: 0;
  overflow-x: hidden;
}

/* 抽屉头部样式优化 */
.resource-panel-drawer :deep(.ant-drawer-header) {
  padding: 16px 20px;
  border-bottom: 1px solid var(--ant-color-border);
}

.config-panel-drawer :deep(.ant-drawer-header) {
  padding: 16px 20px;
  border-bottom: 1px solid var(--ant-color-border);
}

/* 主工作区样式优化 */
.main-workspace {
  flex: 1;
  width: 100%;
  height: 100%;
  min-height: 500px;
  background: var(--ant-color-bg-layout);
  display: flex;
  flex-direction: column;
}

/* 响应式设计 */
@media (max-width: 1600px) {
  .resource-panel-drawer {
    max-width: 380px;
  }

  .config-panel-drawer {
    max-width: 360px;
  }

  .left-floating-btn {
    left: 16px;
  }

  .right-floating-btn {
    right: 16px;
  }
}

@media (max-width: 1200px) {
  .resource-panel-drawer {
    max-width: 350px;
  }

  .config-panel-drawer {
    max-width: 330px;
  }
}

@media (max-width: 768px) {
  .painting-editor-container {
    position: relative;
  }

  /* 移动端抽屉调整 */
  .resource-panel-drawer,
  .config-panel-drawer {
    max-width: 80vw;
  }

  /* 移动端悬浮按钮调整 */
  .left-floating-btn {
    top: 50%;
    left: 16px;
    transform: translateY(-50%);
    width: 36px;
    height: 36px;
  }

  .right-floating-btn {
    top: 50%;
    right: 16px;
    transform: translateY(-50%);
    width: 36px;
    height: 36px;
  }

  .floating-expand-btn:hover {
    transform: scale(1.15);
  }

  .floating-expand-btn:active {
    transform: scale(1.05);
  }

  .floating-expand-btn .expand-icon {
    font-size: 18px;
  }

  /* 移动端动画调整 */
  .left-floating-btn.floating-btn-enter-from {
    opacity: 0;
    transform: translateY(-50%) translateX(-20px) scale(0.8);
  }

  .left-floating-btn.floating-btn-leave-to {
    opacity: 0;
    transform: translateY(-50%) translateX(-20px) scale(0.8);
  }

  .right-floating-btn.floating-btn-enter-from {
    opacity: 0;
    transform: translateY(-50%) translateX(20px) scale(0.8);
  }

  .right-floating-btn.floating-btn-leave-to {
    opacity: 0;
    transform: translateY(-50%) translateX(20px) scale(0.8);
  }

  .main-workspace {
    min-height: 450px;
  }
}

@media (max-width: 480px) {
  .left-floating-btn,
  .right-floating-btn {
    width: 32px;
    height: 32px;
    top: 50%;
    transform: translateY(-50%);
  }

  .floating-expand-btn .expand-icon {
    font-size: 16px;
  }

  .resource-panel-drawer,
  .config-panel-drawer {
    max-width: 85vw;
  }
}

@media (max-width: 480px) {
  .floating-expand-btn {
    width: 32px;
    height: 32px;
    top: 16px;
  }

  .floating-expand-btn .expand-icon {
    font-size: 16px;
  }
}

/* Ant Design Modal 自定义样式 */

/* 右键菜单样式 */
.context-menu {
  position: fixed;
  background: var(--ant-color-bg-container);
  border: 1px solid var(--ant-color-border);
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 4px 0;
  min-width: 120px;
  z-index: 2000;
  animation: contextMenuShow 0.2s ease;
}

@keyframes contextMenuShow {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  color: var(--ant-color-text);
  font-size: 14px;
  transition: background-color 0.2s;
}

.menu-item:hover {
  background: var(--ant-color-bg-text-hover);
}

.menu-icon {
  margin-right: 8px;
  font-size: 14px;
  width: 16px;
  text-align: center;
}

.menu-divider {
  height: 1px;
  background: var(--ant-color-border);
  margin: 4px 0;
}

/* 文件操作按钮样式优化 */
.item-actions {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.tree-item:hover .item-actions {
  opacity: 1;
}

.grid-item .item-overlay {
  display: flex;
  gap: 4px;
  justify-content: center;
  align-items: center;
  pointer-events: none;
}

.grid-item:hover .item-overlay {
  pointer-events: auto;
}

/* 新的下拉菜单按钮样式 */
.overlay-menu-btn {
  pointer-events: auto;
  background: var(--ant-color-bg-container);
  border: 1px solid var(--ant-color-border);
  border-radius: 6px;
  padding: 6px 10px;
  cursor: pointer;
  color: var(--ant-color-text-secondary);
  transition: all 0.2s;
  font-size: 14px;
  font-weight: bold;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  min-width: 32px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.overlay-menu-btn:hover {
  background: var(--ant-color-primary-bg);
  border-color: var(--ant-color-primary);
  color: var(--ant-color-primary);
}

/* 下拉菜单样式优化 */
.menu-icon {
  margin-right: 8px;
  font-size: 14px;
  width: 16px;
  text-align: center;
}

/* Ant Design 下拉菜单项自定义样式 */
:deep(.ant-dropdown-menu-item) {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  font-size: 14px;
}

:deep(.ant-dropdown-menu-item:hover) {
  background: var(--ant-color-bg-text-hover);
}

:deep(.ant-dropdown-menu-item[data-menu-id="delete"]:hover) {
  background: var(--ant-color-error-bg);
  color: var(--ant-color-error);
}

.overlay-btn {
  pointer-events: auto;
  background: var(--ant-color-bg-container);
  border: 1px solid var(--ant-color-border);
  margin: 0 2px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.overlay-btn:hover {
  background: var(--ant-color-primary-bg);
  border-color: var(--ant-color-primary);
  color: var(--ant-color-primary);
}

/* 选中状态优化 */
.tree-item.selected .item-name {
  font-weight: 600;
}

.grid-item.selected .item-name {
  color: var(--ant-color-primary);
  font-weight: 600;
}

/* 加载动画优化 */
.spinner {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 文件图标优化 */
.file-icon-large {
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.item-icon-large {
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

/* 响应式样式调整 */
@media (max-width: 480px) {
  /* Ant Design Modal 在移动端的自适应样式由组件库自动处理 */
}

/* 工具栏响应式 */
@media (max-width: 768px) {
  .toolbar-actions {
    flex-wrap: wrap;
    gap: 4px;
  }

  .action-btn {
    padding: 4px 8px;
    font-size: 12px;
  }

  .btn-text {
    display: none;
  }
}

/* 文件树响应式 */
@media (max-width: 480px) {
  .file-grid {
    grid-template-columns: 1fr 1fr;
    gap: 6px;
  }

  .grid-item {
    padding: 6px;
  }

  .item-thumbnail {
    width: 32px;
    height: 24px;
  }

  .item-name {
    font-size: 11px;
  }
}

/* 配置页脚样式 */
.config-footer {
  padding: 12px 0;
  border-top: 1px solid var(--ant-color-border-secondary);
  background: var(--ant-color-bg-container);
}

.footer-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

/* 保存配置对话框样式 */
.save-config-form {
  padding: 8px 0;
}

.save-config-form .form-item {
  margin-bottom: 16px;
}

.save-config-form .form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: var(--ant-color-text);
  margin-bottom: 8px;
}

.config-preview {
  background: var(--ant-color-bg-layout);
  border: 1px solid var(--ant-color-border-secondary);
  border-radius: 6px;
  padding: 12px;
}

.preview-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
  font-size: 13px;
}

.preview-item:not(:last-child) {
  border-bottom: 1px solid var(--ant-color-border-secondary);
  padding-bottom: 8px;
  margin-bottom: 8px;
}

.preview-label {
  color: var(--ant-color-text-secondary);
  font-weight: 500;
}

.preview-value {
  color: var(--ant-color-text);
  font-weight: 600;
}


/* 预设选项样式 */
.preset-option {

  width: 100%;
}

.preset-name {
  flex: 1;
  font-size: 14px;
  color: var(--ant-color-text);
}

.delete-preset-btn {
  padding: 2px 4px;
  font-size: 12px;
  min-width: 24px;
  height: 24px;
  border-radius: 4px;
  opacity: 0.6;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--ant-color-error);
}

.delete-preset-btn:hover {
  opacity: 1;
  background: var(--ant-color-error-bg);
  color: var(--ant-color-error);
}


/* 下拉选项悬停时显示删除按钮 */
:deep(.ant-select-dropdown .ant-select-item-option:hover) .delete-preset-btn {
  opacity: 1;
  background: var(--ant-color-error-bg);
}

:deep(.ant-select-dropdown .ant-select-item-option-selected) .delete-preset-btn {
  opacity: 1;
  background: var(--ant-color-error-bg);
}

/* 抽屉内容优化 */

/* 绘画记录相关样式 */
.painting-record-btn {
  top: 60px;
  right: 8px;
}

.painting-record-panel-drawer {
  z-index: 1000;
}

.painting-record-panel-drawer :deep(.ant-drawer-content) {
  background: var(--ant-color-bg-container);
}

.painting-record-panel-drawer :deep(.ant-drawer-mask) {
  z-index: 999;
}

.painting-record-panel-drawer :deep(.ant-drawer-body) {
  padding: 0;
  overflow-x: hidden;
}

.painting-record-panel-drawer :deep(.ant-drawer-header) {
  padding: 16px 20px;
  border-bottom: 1px solid var(--ant-color-border);
}

/* 搜索框样式 */
.record-search {
  flex-shrink: 0;
}

.record-search .ant-input-search {
  font-size: 12px;
}

.record-search .ant-input-search .ant-input {
  border-radius: 4px;
  border: 1px solid var(--ant-color-border-secondary);
  background: var(--ant-color-bg-container);
}

.record-search .ant-input-search .ant-input:focus {
  border-color: var(--ant-color-primary);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

/* 绘画记录容器 */
.painting-records-container {
  height: 100%;
  overflow-y: auto;
  padding: 16px;
}

/* 记录列表 */
.records-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.record-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  background: var(--ant-color-bg-layout);
  border: 1px solid var(--ant-color-border-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.record-item:hover {
  background: var(--ant-color-bg-text-hover);
  border-color: var(--ant-color-primary-border);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.record-item.selected {
  background: var(--ant-color-primary-bg);
  border-color: var(--ant-color-primary);
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

/* 记录缩略图 */
.record-thumbnail {
  width: 60px;
  height: 60px;
  border-radius: 6px;
  overflow: hidden;
  flex-shrink: 0;
  margin-right: 12px;
  border: 1px solid var(--ant-color-border-secondary);
  background: var(--ant-color-bg-layout);
  display: flex;
  align-items: center;
  justify-content: center;
}

.thumbnail-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.2s ease;
}

.record-item:hover .thumbnail-image {
  transform: scale(1.05);
}

.no-image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--ant-color-bg-text-active);
  color: var(--ant-color-text-secondary);
}

.placeholder-icon {
  font-size: 24px;
  opacity: 0.6;
}

/* 记录信息 */
.record-info {
  flex: 1;
  min-width: 0;
  margin-right: 12px;
}

.record-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--ant-color-text);
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.record-prompt {
  font-size: 12px;
  color: var(--ant-color-text-secondary);
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.4;
}

.record-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 11px;
}

.record-status {
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.status-completed {
  background: var(--ant-color-success-bg);
  color: var(--ant-color-success);
}

.status-processing {
  background: var(--ant-color-warning-bg);
  color: var(--ant-color-warning);
}

.status-failed {
  background: var(--ant-color-error-bg);
  color: var(--ant-color-error);
}

.status-pending {
  background: var(--ant-color-info-bg);
  color: var(--ant-color-info);
}

.record-date {
  color: var(--ant-color-text-secondary);
}

/* 记录操作按钮 */
.record-actions {
  display: flex;
  gap: 4px;
  flex-shrink: 0;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.record-item:hover .record-actions {
  opacity: 1;
}

.record-actions .ant-btn {
  padding: 4px 6px;
  font-size: 12px;
  border-radius: 4px;
  min-width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 空状态 */
.empty-records {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: var(--ant-color-text-secondary);
  text-align: center;
}

.empty-records .empty-icon {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-records .empty-text {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
  color: var(--ant-color-text);
}

.empty-records .empty-desc {
  font-size: 14px;
  opacity: 0.8;
}

/* 记录详情对话框 */
.record-detail {
  padding: 16px 0;
}

.detail-images {
  margin-bottom: 20px;
}

.detail-images .ant-carousel {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid var(--ant-color-border-secondary);
}

.detail-image {
  width: 100%;
  height: 300px;
  object-fit: contain;
  background: var(--ant-color-bg-layout);
}

.detail-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  font-size: 14px;
  line-height: 1.5;
}

.detail-item label {
  font-weight: 600;
  color: var(--ant-color-text);
  min-width: 80px;
  flex-shrink: 0;
}

.detail-item span {
  color: var(--ant-color-text-secondary);
  word-break: break-word;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .painting-record-btn {
    top: 50px;
    right: 16px;
    width: 36px;
    height: 36px;
  }

  .painting-record-panel-drawer {
    max-width: 85vw;
  }

  .record-item {
    padding: 8px;
  }

  .record-thumbnail {
    width: 50px;
    height: 50px;
    margin-right: 8px;
  }

  .record-title {
    font-size: 13px;
  }

  .record-prompt {
    font-size: 11px;
  }

  .record-meta {
    font-size: 10px;
  }

  .detail-image {
    height: 200px;
  }

  .detail-item {
    font-size: 13px;
  }

  .detail-item label {
    min-width: 70px;
  }
}

@media (max-width: 480px) {
  .painting-record-btn {
    width: 32px;
    height: 32px;
    top: 40px;
    right: 12px;
  }

  .painting-record-panel-drawer {
    max-width: 90vw;
  }

  .record-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .record-thumbnail {
    width: 100%;
    height: 120px;
    margin-right: 0;
    margin-bottom: 8px;
  }

  .record-info {
    width: 100%;
    margin-right: 0;
  }

  .record-actions {
    width: 100%;
    justify-content: flex-end;
    opacity: 1;
  }
}

.delete-btn {
  color: var(--ant-color-text-secondary);
  padding: 2px 4px;
  font-size: 12px;
  min-width: 24px;
  height: 24px;
  border-radius: 4px;
  opacity: 0.7;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-btn:hover {
  color: red;
  opacity: 1;
}
</style>