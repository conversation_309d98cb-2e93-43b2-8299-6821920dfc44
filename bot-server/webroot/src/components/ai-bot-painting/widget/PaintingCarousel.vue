<template>
  <div class="painting-carousel flex flex-col justify-end">
    <!-- 轮播容器 -->
    <div class="carousel-container">
      <!-- 左侧箭头 -->
      <div class="carousel-arrow carousel-arrow-left" @click="prevSlide">
        <span class="arrow-icon">&#10094;</span>
      </div>

      <!-- 轮播内容区域 -->
      <div class="carousel-main" ref="carouselRef">
        <div class="carousel-track">
          <div 
            v-for="(item, index) in displayItems" 
            :key="`${item.id}-${index}`"
            class="carousel-item"
            :class="getSlideClass(index)"
            :style="getSlideStyle(index)"
            @click="onItemClick(item, getOriginalIndex(index))"
          >
            <!-- 卡片封面 -->
            <div class="item-cover">
              <img 
                :src="item.cover" 
                :alt="item.title"
                class="cover-image"
                @error="onImageError"
              />
            </div>
            <div class="item-title">{{ item.title }}</div>
          </div>
        </div>
      </div>

      <!-- 右侧箭头 -->
      <div class="carousel-arrow carousel-arrow-right" @click="nextSlide">
        <span class="arrow-icon">&#10095;</span>
      </div>
    </div>

    <!-- 略缩图导航 -->
    <div class="thumbnail-nav" v-if="showThumbnails">
      <div 
        v-for="(item, index) in carouselItems" 
        :key="index"
        class="thumbnail-item"
        :class="{ active: currentIndex === index }"
        @click="goToSlide(index)"
      >
        <img 
          :src="item.cover" 
          :alt="item.title"
          class="thumbnail-image"
          @error="onImageError"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, computed, watch } from 'vue'

// 定义组件props
interface CarouselItem {
  id: string | number
  title: string
  cover: string
  description?: string
  url?: string
}

interface Props {
  items?: CarouselItem[]
  autoPlay?: boolean
  interval?: number
  speed?: number
  showThumbnails?: boolean
  initialIndex?: number
  currentIndex?: number
}

const props = withDefaults(defineProps<Props>(), {
  items: () => [],
  autoPlay: false, // 默认关闭自动播放，避免与3D效果冲突
  interval: 5000,
  speed: 300,
  showThumbnails: true,
  initialIndex: 0,
  currentIndex: 0
})

// 定义事件
const emit = defineEmits<{
  itemClick: [item: CarouselItem, index: number]
  change: [index: number]
  'update:currentIndex': [index: number]
}>()

// 响应式数据
const carouselRef = ref<HTMLElement>()
const autoPlayTimer = ref<number>()
const isTransitioning = ref(false)

// 使用计算属性处理内部状态和外部传入的currentIndex
const currentIndex = computed({
  get: () => props.currentIndex,
  set: (value: number) => {
    emit('update:currentIndex', value);
    emit('change', value);
  }
});

// 监听初始索引变化
watch(() => props.initialIndex, (newVal) => {
  if (newVal !== undefined && newVal !== currentIndex.value) {
    currentIndex.value = newVal
  }
})

// 监听currentIndex变化
watch(() => props.currentIndex, (newVal, oldVal) => {
  if (newVal !== oldVal) {
    // 索引变化时的处理逻辑
  }
})

const baseUrl =
  'https://raw.githubusercontent.com/vueComponent/ant-design-vue/main/components/carousel/demo/';
const getImgUrl = (i: number) => {
  return `${baseUrl}abstract0${i + 1}.jpg`;
}
// 默认轮播数据
const defaultItems: CarouselItem[] = [
  {
    id: 1,
    title: '收神记',
    cover: getImgUrl(0),
    description: '玄幻动画作品'
  },
  {
    id: 2,
    title: '凡人修仙传',
    cover: getImgUrl(1),
    description: '修仙题材动画'
  },
  {
    id: 3,
    title: '家神家期',
    cover: getImgUrl(2),
    description: '温馨家庭动画'
  },
  {
    id: 4,
    title: '刺客伍六七',
    cover: getImgUrl(3),
    description: '搞笑动作动画'
  },
]

// 计算轮播数据
const carouselItems = computed(() => {
  return props.items.length > 0 ? props.items : defaultItems
})

// 创建显示用的项目数组（包含前后复制的项目以实现无限循环）
const displayItems = computed(() => {
  if (carouselItems.value.length === 0) return [];
  
  const totalItems = carouselItems.value.length;
  const displayCount = Math.min(5, totalItems * 2 + 1); // 显示最多5个，或者项目总数的2倍+1
  const result = [];
  
  // 创建足够的项目用于显示
  for (let i = 0; i < displayCount; i++) {
    const offset = i - Math.floor(displayCount / 2);
    let itemIndex = (currentIndex.value + offset) % totalItems;
    if (itemIndex < 0) itemIndex += totalItems;
    result.push(carouselItems.value[itemIndex]);
  }
  
  return result;
});

// 获取原始项目索引
const getOriginalIndex = (displayIndex: number) => {
  const totalItems = carouselItems.value.length;
  const displayCount = displayItems.value.length;
  const offset = displayIndex - Math.floor(displayCount / 2);
  let originalIndex = (currentIndex.value + offset) % totalItems;
  if (originalIndex < 0) originalIndex += totalItems;
  return originalIndex;
};

// 获取滑块样式类
const getSlideClass = (index: number) => {
  const centerIndex = Math.floor(displayItems.value.length / 2);
  const distance = Math.abs(index - centerIndex);
  
  return {
    'slide-center': index === centerIndex,
    'slide-left-1': index === centerIndex - 1,
    'slide-right-1': index === centerIndex + 1,
    'slide-left-2': index === centerIndex - 2,
    'slide-right-2': index === centerIndex + 2,
    'slide-hidden': distance > 2,
    'transitioning': isTransitioning.value
  };
};

// 获取滑块样式
const getSlideStyle = (index: number) => {
  const centerIndex = Math.floor(displayItems.value.length / 2);
  const distance = index - centerIndex;
  
  // 基础位置计算
  let translateX = distance * 150; // 每个位置间隔150px
  let translateZ = -Math.abs(distance) * 120; // Z轴深度
  let scale = 1 - Math.abs(distance) * 0.2; // 缩放比例
  let opacity = Math.max(0.4, 1 - Math.abs(distance) * 0.25); // 透明度
  
  // 限制最小缩放和透明度
  scale = Math.max(0.7, scale);
  
  // 隐藏距离太远的项目
  if (Math.abs(distance) > 2) {
    opacity = 0;
    scale = 0.6;
  }
  
  return {
    transform: `translateX(${translateX}px) translateZ(${translateZ}px) scale(${scale})`,
    opacity: opacity,
    zIndex: 10 - Math.abs(distance)
  };
};

// 轮播控制方法
const prevSlide = () => {
  if (isTransitioning.value) return;
  
  isTransitioning.value = true;
  const newIndex = currentIndex.value > 0 
    ? currentIndex.value - 1 
    : carouselItems.value.length - 1;
  
  currentIndex.value = newIndex;
  
  setTimeout(() => {
    isTransitioning.value = false;
  }, 400);
}

const nextSlide = () => {
  if (isTransitioning.value) return;
  
  isTransitioning.value = true;
  const newIndex = currentIndex.value < carouselItems.value.length - 1 
    ? currentIndex.value + 1 
    : 0;
  
  currentIndex.value = newIndex;
  
  setTimeout(() => {
    isTransitioning.value = false;
  }, 400);
}

const goToSlide = (index: number) => {
  if (index >= 0 && index < carouselItems.value.length && index !== currentIndex.value) {
    if (isTransitioning.value) return;
    
    isTransitioning.value = true;
    currentIndex.value = index;
    
    setTimeout(() => {
      isTransitioning.value = false;
    }, 400);
  }
}

// 自动播放相关方法
const startAutoPlay = () => {
  if (!props.autoPlay) return
  
  autoPlayTimer.value = window.setInterval(() => {
    nextSlide()
  }, props.interval)
}

const pauseAutoPlay = () => {
  if (autoPlayTimer.value) {
    clearInterval(autoPlayTimer.value)
    autoPlayTimer.value = undefined
  }
}

// 事件处理方法
const onItemClick = (item: CarouselItem, index: number) => {
  if (index !== currentIndex.value) {
    goToSlide(index);
  }
  emit('itemClick', item, index);
}

const onImageError = (e: Event) => {
  const img = e.target as HTMLImageElement
  img.src = '/api/placeholder/280/160' // 设置默认占位图
}

// 生命周期
onMounted(() => {
  if (props.autoPlay) {
    startAutoPlay()
  }
})

onUnmounted(() => {
  pauseAutoPlay()
})
</script>

<style scoped>
.painting-carousel {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
}

.carousel-container {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  display: flex;
  align-items: center;
  height: 300px;
  perspective: 1200px; /* 3D透视效果 */
}

.carousel-main {
  flex: 1;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.carousel-track {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  transform-style: preserve-3d; /* 保持3D变换 */
}

.carousel-item {
  position: absolute;
  width: 240px;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  backdrop-filter: blur(10px);
  border: 2px solid transparent;
  transform-origin: center center;
  backface-visibility: hidden;
}

.carousel-item.transitioning {
  transition-duration: 0.4s;
}

.carousel-item.slide-center {
  z-index: 10;
  border-color: var(--ant-color-primary, #1890ff);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.carousel-item.slide-left-1,
.carousel-item.slide-right-1 {
  z-index: 9;
}

.carousel-item.slide-left-2,
.carousel-item.slide-right-2 {
  z-index: 8;
}

.carousel-item.slide-hidden {
  opacity: 0 !important;
  pointer-events: none;
}

.carousel-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.2);
}

.carousel-item.slide-center:hover {
  transform: translateY(-12px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.item-cover {
  position: relative;
  width: 100%;
  height: 160px;
  overflow: hidden;
  background: #f0f0f0;
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.carousel-item:hover .cover-image {
  transform: scale(1.05);
}

.item-title {
  padding: 16px 20px;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  text-align: center;
  line-height: 1.4;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  background: var(--ant-color-bg-container, #fff);
}

/* 箭头导航样式 */
.carousel-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 20;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.carousel-arrow:hover {
  background: rgba(255, 255, 255, 1);
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.3);
  transform: translateY(-50%) scale(1.1);
}

.carousel-arrow-left {
  left: 20px;
}

.carousel-arrow-right {
  right: 20px;
}

.arrow-icon {
  font-size: 20px;
  font-weight: bold;
  color: #333;
}

/* 略缩图导航样式 */
.thumbnail-nav {
  display: flex;
  gap: 8px;
  justify-content: center;
  padding: 15px 0;
  overflow-x: auto;
  margin-top: 10px;
}

.thumbnail-item {
  width: 60px;
  height: 45px;
  border-radius: 6px;
  overflow: hidden;
  cursor: pointer;
  border: 3px solid transparent;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.thumbnail-item:hover {
  border-color: var(--ant-color-primary-hover, #40a9ff);
  transform: scale(1.05);
}

.thumbnail-item.active {
  border-color: var(--ant-color-primary, #1890ff);
  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.4);
  transform: scale(1.1);
}

.thumbnail-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.3s ease;
}

.thumbnail-item:hover .thumbnail-image {
  opacity: 0.8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .painting-carousel {
    padding: 15px;
  }
  
  .carousel-container {
    height: 240px;
  }
  
  .carousel-item {
    width: 200px;
  }
  
  .item-cover {
    height: 120px;
  }
  
  .item-title {
    padding: 12px 16px;
    font-size: 14px;
  }
  
  .carousel-arrow {
    width: 40px;
    height: 40px;
  }
  
  .carousel-arrow-left {
    left: 10px;
  }
  
  .carousel-arrow-right {
    right: 10px;
  }
  
  .arrow-icon {
    font-size: 16px;
  }
  
  .thumbnail-item {
    width: 50px;
    height: 38px;
  }
}

@media (max-width: 480px) {
  .painting-carousel {
    padding: 10px;
  }
  
  .carousel-container {
    height: 200px;
  }
  
  .carousel-item {
    width: 160px;
  }
  
  .item-cover {
    height: 100px;
  }
  
  .item-title {
    padding: 10px 12px;
    font-size: 13px;
  }
  
  .carousel-arrow {
    width: 36px;
    height: 36px;
  }
  
  .arrow-icon {
    font-size: 14px;
  }
  
  .thumbnail-item {
    width: 40px;
    height: 30px;
  }
}

/* 动画优化 */
@media (prefers-reduced-motion: reduce) {
  .carousel-item {
    transition: none;
  }
}
</style>
