<template>
    <div>

    </div>
</template>

<script setup lang="ts">
import { AgentItem, AgentItemStatus } from '@/components/common/model/agent';
import { PaintingMessage } from '@/components/common/model/painting';
import { LLMStream } from '@/components/common/stream/llm_stream';
import { useAiPaintingStore } from '@/components/store/painting';
import { onMounted, ref } from 'vue';

// Props
interface Props {
    painting?: PaintingMessage
}

const props = withDefaults(defineProps<Props>(), {});
const agent = ref<AgentItem[]>([])

const painting = ref<PaintingMessage>({ ...props.painting });
const paintingStore = useAiPaintingStore()
// 开始画画
function beginPainting() {
    // 这里可以添加绘画开始的逻辑
    console.log('开始绘画', props.painting);

}

function endPainting() {
    // 这里可以添加绘画结束的逻辑
    // 更新绘画状态
    painting.value.status = AgentItemStatus.Success;
    console.log('绘画结束', painting.value);
}

onMounted(async () => {
    // 初次加载时检查绘画状态 必然需要一个 AgentItemStatus.Start 状态
    if (props.painting && props.painting.status == AgentItemStatus.Start) {
        if (props.painting.MultipleStream?.get(props.painting.id)) {
            const response = await painting.value.MultipleStream.get(painting.value.id)()
            painting.value.llmStream = new LLMStream(response)
            beginPainting();
        }
    }
});

</script>

<style scoped></style>