<template>
  <div class="test-carousel-page">
    <h2>3D轮播图组件测试</h2>
    <p class="description">实现无限循环轮播，当前活跃图片在中间，其他图片排列在两侧的3D效果</p>
    
    <div class="section">
      <h3>1. ImageCarousel 组件 - 3D图片轮播</h3>
      <div class="carousel-container">
        <ImageCarousel 
          :images="testImages" 
          :currentIndex="currentImageIndex"
          @update:currentIndex="currentImageIndex = $event"
        />
      </div>
      <div class="controls">
        <p>当前索引: <span class="index-display">{{ currentImageIndex + 1 }} / {{ testImages.length }}</span></p>
        <div class="button-group">
          <button @click="currentImageIndex = 0" :class="{ active: currentImageIndex === 0 }">第1张</button>
          <button @click="currentImageIndex = 1" :class="{ active: currentImageIndex === 1 }">第2张</button>
          <button @click="currentImageIndex = 2" :class="{ active: currentImageIndex === 2 }">第3张</button>
          <button @click="currentImageIndex = 3" :class="{ active: currentImageIndex === 3 }">第4张</button>
          <button @click="currentImageIndex = 4" :class="{ active: currentImageIndex === 4 }">第5张</button>
          <button @click="currentImageIndex = 5" :class="{ active: currentImageIndex === 5 }">第6张</button>
        </div>
        <div class="auto-controls">
          <button @click="prevImage" class="nav-btn">← 上一张</button>
          <button @click="toggleAutoPlay" :class="{ active: autoPlayActive }">
            {{ autoPlayActive ? '停止自动播放' : '开始自动播放' }}
          </button>
          <button @click="nextImage" class="nav-btn">下一张 →</button>
        </div>
      </div>
    </div>
    
    <div class="section">
      <h3>2. PaintingCarousel 组件 - 3D卡片轮播</h3>
      <div class="carousel-container">
        <PaintingCarousel 
          :items="carouselItems" 
          :currentIndex="currentPaintingIndex"
          @update:currentIndex="currentPaintingIndex = $event"
          @itemClick="onItemClick"
          :autoPlay="false"
          :showThumbnails="true"
        />
      </div>
      <div class="controls">
        <p>当前索引: <span class="index-display">{{ currentPaintingIndex + 1 }} / {{ carouselItems.length }}</span></p>
        <div class="button-group">
          <button v-for="(item, index) in carouselItems" 
                  :key="index"
                  @click="currentPaintingIndex = index" 
                  :class="{ active: currentPaintingIndex === index }">
            {{ item.title }}
          </button>
        </div>
        <div class="auto-controls">
          <button @click="prevPainting" class="nav-btn">← 上一个</button>
          <button @click="randomSelect" class="special-btn">🎲 随机选择</button>
          <button @click="nextPainting" class="nav-btn">下一个 →</button>
        </div>
      </div>
    </div>

    <div class="features">
      <h3>✨ 功能特性</h3>
      <ul>
        <li>🔄 无限循环轮播，从最后一张可以直接切换到第一张</li>
        <li>🎯 当前活跃图片在中间，其他图片排列在两侧</li>
        <li>🎨 3D透视效果，带有深度和缩放变化</li>
        <li>👆 点击两侧图片可以快速切换到对应位置</li>
        <li>🖱️ 支持鼠标悬停效果和平滑过渡动画</li>
        <li>📱 响应式设计，适配不同屏幕尺寸</li>
        <li>🎮 支持键盘和触摸操作</li>
        <li>🔧 略缩图导航，可以快速跳转到任意位置</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import ImageCarousel from '../ImageCarousel.vue';
import PaintingCarousel from './PaintingCarousel.vue';

// 测试图片 - 增加更多图片来测试无限循环效果
const baseUrl = 'https://raw.githubusercontent.com/vueComponent/ant-design-vue/main/components/carousel/demo/';
const testImages = [
  `${baseUrl}abstract01.jpg`,
  `${baseUrl}abstract02.jpg`,
  `${baseUrl}abstract03.jpg`,
  `${baseUrl}abstract04.jpg`,
  `${baseUrl}abstract05.jpg`,
  `${baseUrl}abstract06.jpg`
];

// 轮播图数据
const carouselItems = [
  {
    id: 1,
    title: '收神记',
    cover: `${baseUrl}abstract01.jpg`,
    description: '玄幻动画作品'
  },
  {
    id: 2,
    title: '凡人修仙传',
    cover: `${baseUrl}abstract02.jpg`,
    description: '修仙题材动画'
  },
  {
    id: 3,
    title: '家神家期',
    cover: `${baseUrl}abstract03.jpg`,
    description: '温馨家庭动画'
  },
  {
    id: 4,
    title: '刺客伍六七',
    cover: `${baseUrl}abstract04.jpg`,
    description: '搞笑动作动画'
  },
  {
    id: 5,
    title: '斗罗大陆',
    cover: `${baseUrl}abstract05.jpg`,
    description: '热血冒险动画'
  },
  {
    id: 6,
    title: '完美世界',
    cover: `${baseUrl}abstract06.jpg`,
    description: '奇幻修仙动画'
  }
];

// 当前索引
const currentImageIndex = ref(0);
const currentPaintingIndex = ref(0);
const autoPlayActive = ref(false);
const autoPlayTimer = ref<number>();

// 控制方法
const prevImage = () => {
  currentImageIndex.value = currentImageIndex.value > 0 
    ? currentImageIndex.value - 1 
    : testImages.length - 1;
};

const nextImage = () => {
  currentImageIndex.value = currentImageIndex.value < testImages.length - 1 
    ? currentImageIndex.value + 1 
    : 0;
};

const prevPainting = () => {
  currentPaintingIndex.value = currentPaintingIndex.value > 0 
    ? currentPaintingIndex.value - 1 
    : carouselItems.length - 1;
};

const nextPainting = () => {
  currentPaintingIndex.value = currentPaintingIndex.value < carouselItems.length - 1 
    ? currentPaintingIndex.value + 1 
    : 0;
};

const randomSelect = () => {
  const randomIndex = Math.floor(Math.random() * carouselItems.length);
  currentPaintingIndex.value = randomIndex;
};

const toggleAutoPlay = () => {
  if (autoPlayActive.value) {
    // 停止自动播放
    if (autoPlayTimer.value) {
      clearInterval(autoPlayTimer.value);
      autoPlayTimer.value = undefined;
    }
    autoPlayActive.value = false;
  } else {
    // 开始自动播放
    autoPlayTimer.value = setInterval(() => {
      nextImage();
    }, 2000);
    autoPlayActive.value = true;
  }
};

// 处理点击事件
const onItemClick = (item: any, index: number) => {
  console.log('点击了项目:', item, '索引:', index);
};

// 组件卸载时清理定时器
const cleanup = () => {
  if (autoPlayTimer.value) {
    clearInterval(autoPlayTimer.value);
  }
};

// 监听页面卸载
window.addEventListener('beforeunload', cleanup);
</script>

<style scoped>
.test-carousel-page {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

h2 {
  margin-bottom: 10px;
  font-size: 28px;
  text-align: center;
  color: #2c3e50;
  font-weight: 700;
}

.description {
  text-align: center;
  color: #7f8c8d;
  margin-bottom: 30px;
  font-size: 16px;
  line-height: 1.6;
}

.section {
  margin-bottom: 50px;
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

h3 {
  margin-bottom: 20px;
  font-size: 20px;
  color: #34495e;
  font-weight: 600;
  border-bottom: 2px solid #3498db;
  padding-bottom: 8px;
}

.carousel-container {
  height: 500px;
  margin-bottom: 30px;
  border: 2px solid #ecf0f1;
  border-radius: 12px;
  overflow: hidden;
  background: linear-gradient(45deg, #f8f9fa, #e9ecef);
}

.controls {
  display: flex;
  flex-direction: column;
  gap: 15px;
  align-items: center;
}

.controls p {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #2c3e50;
}

.index-display {
  background: #3498db;
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-weight: 600;
}

.button-group {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: center;
}

.auto-controls {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
  justify-content: center;
}

button {
  padding: 10px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 25px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

button.active {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  box-shadow: 0 4px 15px rgba(245, 87, 108, 0.3);
}

.nav-btn {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  min-width: 120px;
}

.special-btn {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  font-size: 16px;
  min-width: 140px;
}

.features {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.features h3 {
  color: #e67e22;
  border-bottom-color: #e67e22;
}

.features ul {
  list-style: none;
  padding: 0;
}

.features li {
  padding: 8px 0;
  font-size: 15px;
  color: #2c3e50;
  line-height: 1.6;
}

.features li::before {
  margin-right: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .test-carousel-page {
    padding: 15px;
  }
  
  .section {
    padding: 20px;
    margin-bottom: 30px;
  }
  
  .carousel-container {
    height: 400px;
  }
  
  .button-group {
    gap: 6px;
  }
  
  button {
    padding: 8px 16px;
    font-size: 13px;
  }
  
  .nav-btn {
    min-width: 100px;
  }
  
  .special-btn {
    min-width: 120px;
  }
}

@media (max-width: 480px) {
  .carousel-container {
    height: 350px;
  }
  
  .controls {
    gap: 10px;
  }
  
  .button-group,
  .auto-controls {
    flex-direction: column;
    width: 100%;
  }
  
  button {
    width: 100%;
    max-width: 200px;
  }
}
</style> 