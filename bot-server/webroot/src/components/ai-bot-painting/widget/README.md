# 3D轮播图组件

## 概述

实现了两个功能强大的3D轮播图组件，满足无限循环轮播的需求，当前活跃图片位于中间，其他图片排列在两侧，形成立体的视觉效果。

## 组件列表

### 1. ImageCarousel.vue
简洁的3D图片轮播组件，专门用于图片展示。

**特性：**
- ✅ 3D透视效果，当前图片居中显示
- ✅ 无限循环轮播
- ✅ 左右箭头导航
- ✅ 略缩图快速跳转
- ✅ 响应式设计
- ✅ 平滑过渡动画

**使用方式：**
```vue
<ImageCarousel 
  :images="imageList" 
  :currentIndex="currentIndex"
  @update:currentIndex="currentIndex = $event"
/>
```

### 2. PaintingCarousel.vue
功能丰富的3D卡片轮播组件，适用于复杂内容展示。

**特性：**
- ✅ 3D卡片式布局
- ✅ 无限循环轮播
- ✅ 左右箭头导航
- ✅ 略缩图导航（可选）
- ✅ 自动播放（可选）
- ✅ 点击事件处理
- ✅ 响应式设计

**使用方式：**
```vue
<PaintingCarousel 
  :items="carouselItems" 
  :currentIndex="currentIndex"
  @update:currentIndex="currentIndex = $event"
  @itemClick="handleItemClick"
  :autoPlay="false"
  :showThumbnails="true"
/>
```

## 核心功能

### 🔄 无限循环
- 从最后一张图片可以直接切换到第一张
- 从第一张图片可以直接切换到最后一张
- 无缝循环，没有边界限制

### 🎯 3D布局
- 当前活跃图片在中间，完整显示
- 左右两侧图片按距离递减显示
- 支持透明度、缩放、深度变化
- 创造立体视觉效果

### 👆 交互功能
- 点击两侧图片快速切换到对应位置
- 左右箭头按钮导航
- 略缩图快速跳转
- 支持键盘和触摸操作

### 📱 响应式适配
- 桌面端：完整3D效果
- 平板端：适中的卡片尺寸
- 手机端：紧凑布局，保持功能完整

## 技术实现

### 3D效果实现
```css
.carousel-container {
  perspective: 1000px; /* 3D透视 */
}

.carousel-track {
  transform-style: preserve-3d; /* 保持3D变换 */
}

.carousel-slide {
  transform: translateX(120px) translateZ(-100px) scale(0.85);
  /* X轴位移 + Z轴深度 + 缩放 */
}
```

### 无限循环算法
```javascript
// 创建显示用的图片数组
const displayImages = computed(() => {
  const totalImages = images.value.length;
  const displayCount = Math.min(5, totalImages * 2 + 1);
  const result = [];
  
  for (let i = 0; i < displayCount; i++) {
    const offset = i - Math.floor(displayCount / 2);
    let imageIndex = (currentIndex.value + offset) % totalImages;
    if (imageIndex < 0) imageIndex += totalImages;
    result.push(images.value[imageIndex]);
  }
  
  return result;
});
```

## 测试页面

访问 `TestCarousel.vue` 可以看到完整的演示效果：

- 6张测试图片的无限循环
- 多种控制方式演示
- 自动播放功能
- 随机跳转功能
- 实时索引显示

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

支持现代浏览器的3D变换和CSS Grid布局。

## 性能优化

- 使用 `transform` 而非 `left/top` 进行动画
- `backface-visibility: hidden` 优化3D渲染
- `will-change` 属性提示浏览器优化
- 防抖处理防止快速点击
- 响应式图片加载

## 未来扩展

- [ ] 添加手势滑动支持
- [ ] 支持垂直轮播模式
- [ ] 添加更多过渡动画效果
- [ ] 支持视频内容轮播
- [ ] 添加懒加载功能 