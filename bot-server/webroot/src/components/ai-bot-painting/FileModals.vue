<template>
  <!-- 文件夹创建对话框 -->
  <a-modal
    v-model:open="showCreateDialog"
    title="新建文件夹"
    ok-text="确定"
    cancel-text="取消"
    :confirm-loading="isCreating"
    :ok-button-props="{ disabled: !newFolderName.trim() || isCreating }"
    @ok="createFolder"
    @cancel="closeCreateDialog">
    <a-form layout="vertical">
      <a-form-item label="文件夹名称" required>
        <a-input 
          v-model:value="newFolderName" 
          placeholder="请输入文件夹名称"
          @keyup.enter="createFolder"
          ref="folderNameInput" />
      </a-form-item>
    </a-form>
  </a-modal>
  
  <!-- 文件重命名对话框 -->
  <a-modal
    v-model:open="showRenameDialog"
    :title="`重命名${editingFile?.isDir ? '文件夹' : '文件'}`"
    ok-text="确定"
    cancel-text="取消"
    :confirm-loading="isRenaming"
    :ok-button-props="{ disabled: !newFileName.trim() || isRenaming }"
    @ok="confirmRename"
    @cancel="closeRenameDialog">
    <a-form layout="vertical">
      <a-form-item label="新名称" required>
        <a-input 
          v-model:value="newFileName" 
          @keyup.enter="confirmRename"
          ref="fileNameInput" />
      </a-form-item>
    </a-form>
  </a-modal>
  
  <!-- 右键菜单 -->
  <div 
    v-if="showContextMenu" 
    class="context-menu"
    :style="{ left: `${contextMenuPos.x}px`, top: `${contextMenuPos.y}px` }"
    @click="hideContextMenu">
    <div class="menu-item" @click="editFile(contextMenuItem)">
      <span class="menu-icon">✏️</span>
      <span>重命名</span>
    </div>
    <div class="menu-item" @click="deleteFile(contextMenuItem)">
      <span class="menu-icon">🗑️</span>
      <span>删除</span>
    </div>
    <div class="menu-divider"></div>
    <div class="menu-item" @click="refreshFiles">
      <span class="menu-icon">🔄</span>
      <span>刷新</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick, computed } from 'vue';

// 文件系统类型定义
interface FileSystemItem {
  id: string;
  pid: string;
  name: string;
  size?: number;
  isDir: boolean;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
  childCount?: number;
}

// Props
interface Props {
  showCreateDialog: boolean;
  showRenameDialog: boolean;
  showContextMenu: boolean;
  contextMenuPos: { x: number; y: number };
  contextMenuItem: FileSystemItem | null;
  editingFile: FileSystemItem | null;
  newFolderName: string;
  newFileName: string;
  isCreating: boolean;
  isRenaming: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  showCreateDialog: false,
  showRenameDialog: false,
  showContextMenu: false,
  contextMenuPos: () => ({ x: 0, y: 0 }),
  contextMenuItem: null,
  editingFile: null,
  newFolderName: '',
  newFileName: '',
  isCreating: false,
  isRenaming: false
});

// Emits
const emit = defineEmits<{
  'update:show-create-dialog': [value: boolean];
  'update:show-rename-dialog': [value: boolean];
  'update:show-context-menu': [value: boolean];
  'update:new-folder-name': [value: string];
  'update:new-file-name': [value: string];
  'create-folder': [];
  'confirm-rename': [];
  'edit-file': [file: FileSystemItem];
  'delete-file': [file: FileSystemItem];
  'refresh-files': [];
  'hide-context-menu': [];
}>();

// Refs
const folderNameInput = ref<HTMLInputElement>();
const fileNameInput = ref<HTMLInputElement>();

// Computed properties with v-model support
const showCreateDialog = computed({
  get: () => props.showCreateDialog,
  set: (value) => emit('update:show-create-dialog', value)
});

const showRenameDialog = computed({
  get: () => props.showRenameDialog,
  set: (value) => emit('update:show-rename-dialog', value)
});

const showContextMenu = computed({
  get: () => props.showContextMenu,
  set: (value) => emit('update:show-context-menu', value)
});

const newFolderName = computed({
  get: () => props.newFolderName,
  set: (value) => emit('update:new-folder-name', value)
});

const newFileName = computed({
  get: () => props.newFileName,
  set: (value) => emit('update:new-file-name', value)
});

// Methods
const createFolder = () => {
  emit('create-folder');
};

const closeCreateDialog = () => {
  showCreateDialog.value = false;
};

const confirmRename = () => {
  emit('confirm-rename');
};

const closeRenameDialog = () => {
  showRenameDialog.value = false;
};

const editFile = (file: FileSystemItem | null) => {
  if (file) {
    emit('edit-file', file);
  }
};

const deleteFile = (file: FileSystemItem | null) => {
  if (file) {
    emit('delete-file', file);
  }
};

const refreshFiles = () => {
  emit('refresh-files');
};

const hideContextMenu = () => {
  emit('hide-context-menu');
};

// 监听对话框显示，自动聚焦输入框
watch(() => props.showCreateDialog, (newVal) => {
  if (newVal) {
    nextTick(() => {
      if (folderNameInput.value) {
        try {
          (folderNameInput.value as any)?.focus?.();
        } catch (e) {
          console.warn('Focus failed:', e);
        }
      }
    });
  }
});

watch(() => props.showRenameDialog, (newVal) => {
  if (newVal) {
    nextTick(() => {
      if (fileNameInput.value) {
        try {
          (fileNameInput.value as any)?.focus?.();
          (fileNameInput.value as any)?.select?.();
        } catch (e) {
          console.warn('Focus/Select failed:', e);
        }
      }
    });
  }
});
</script>

<style scoped>
/* 右键菜单样式 */
.context-menu {
  position: fixed;
  background: var(--ant-color-bg-container);
  border: 1px solid var(--ant-color-border);
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 4px 0;
  min-width: 120px;
  z-index: 2000;
  animation: contextMenuShow 0.2s ease;
}

@keyframes contextMenuShow {
  from { 
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to { 
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  color: var(--ant-color-text);
  font-size: 14px;
  transition: background-color 0.2s;
}

.menu-item:hover {
  background: var(--ant-color-bg-text-hover);
}

.menu-icon {
  margin-right: 8px;
  font-size: 14px;
  width: 16px;
  text-align: center;
}

.menu-divider {
  height: 1px;
  background: var(--ant-color-border);
  margin: 4px 0;
}
</style> 