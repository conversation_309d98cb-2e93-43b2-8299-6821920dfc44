<template>
  <div class="resource-panel-content flex flex-col h-full">
    <!-- 工具栏 -->
    <div class="toolbar-section">
      <div class="toolbar-actions">
        <button 
          class="action-btn" 
          @click="createFolder"
          title="新建文件夹">
          <span class="btn-icon">📁+</span>
          <span class="btn-text">新建文件夹</span>
        </button>
        <button 
          class="action-btn" 
          @click="uploadFiles"
          title="上传文件">
          <span class="btn-icon">⬆️</span>
          <span class="btn-text">上传文件</span>
        </button>
        <button 
          class="action-btn" 
          @click="refreshFiles"
          :disabled="isLoading"
          title="刷新">
          <span class="btn-icon" :class="{ 'spinning': isLoading }">🔄</span>
        </button>
      </div>
      <!-- 隐藏的文件上传输入框 -->
      <input 
        ref="fileInput" 
        type="file" 
        multiple 
        style="display: none"
        @change="onFileSelected"
        accept="image/*,.psd,.ai,.sketch,.xd,.fig,.svg" />
    </div>
    
    <!-- 当前路径显示 -->
    <div class="breadcrumb-section" v-if="currentPath.length > 0">
      <div class="breadcrumb">
        <span class="breadcrumb-item" @click="navigateToRoot">🏠 根目录</span>
        <span 
          v-for="(item, index) in currentPath" 
          :key="item.id"
          class="breadcrumb-item"
          @click="navigateToFolder(item, index)">
          <span class="breadcrumb-separator">></span>
          <span>{{ item.name }}</span>
        </span>
      </div>
    </div>
    
    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-section">
      <div class="loading-spinner">
        <span class="spinner">⏳</span>
        <span class="loading-text">加载中...</span>
      </div>
    </div>
    
    <!-- 资源文件夹树 -->
    <div class="resource-section" v-else>
      <div class="section-title">
        项目文件
        <span class="file-count">({{ fileSystemData.length }} 项)</span>
      </div>
      
      <!-- 空文件夹提示 -->
      <div v-if="fileSystemData.length === 0" class="empty-folder">
        <div class="empty-icon">📂</div>
        <div class="empty-text">此文件夹为空</div>
        <button class="create-first-btn" @click="createFolder">
          创建第一个文件夹
        </button>
      </div>
      
      <!-- 列表视图 -->
      <div v-else-if="viewMode === 'list'" class="resource-tree list-view">
        <div 
          v-for="item in fileSystemData" 
          :key="item.id"
          class="tree-item"
          :class="{ 'selected': selectedFileId === item.id }"
          @click="selectFile(item)"
          @dblclick="item.isDir ? navigateToFolder(item) : openFile(item)"
          @contextmenu.prevent="showContextMenu($event, item)">
          <span class="folder-icon">{{ getFileIcon(item) }}</span>
          <span class="item-name">{{ item.name }}</span>
          <span class="item-info" v-if="item.isDir">{{ item.childCount || 0 }} 项</span>
          <span class="item-info" v-else>{{ formatFileSize(item.size || 0) }}</span>
          <span class="item-date">{{ formatModifiedTime(item.updatedAt) }}</span>
          <div class="item-actions">
            <a-dropdown trigger="click" placement="bottomRight">
              <button 
                class="action-menu-btn" 
                @click.stop
                title="更多操作">
                ⋯
              </button>
              <template #overlay>
                <a-menu>
                  <a-menu-item key="rename" @click="editFile(item)">
                    <span class="menu-icon">✏️</span>
                    重命名
                  </a-menu-item>
                  <a-menu-divider />
                  <a-menu-item key="delete" @click="deleteFile(item)">
                    <span class="menu-icon">🗑️</span>
                    删除
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
        </div>
      </div>
      
      <!-- 图标视图 -->
      <div v-else class="resource-tree grid-view">
        <div class="file-grid">
          <div 
            v-for="item in fileSystemData" 
            :key="item.id"
            class="grid-item" 
            :class="{ 
              'selected': selectedFileId === item.id,
              'is-folder': item.isDir 
            }"
            @click="selectFile(item)"
            @dblclick="item.isDir ? navigateToFolder(item) : openFile(item)"
            @contextmenu.prevent="showContextMenu($event, item)">
            <div class="item-thumbnail">
              <span class="item-icon-large">{{ getFileIcon(item) }}</span>
            </div>
            <div class="item-details">
              <span class="item-name">{{ item.name }}</span>
              <span class="item-size" v-if="!item.isDir">{{ formatFileSize(item.size || 0) }}</span>
              <span class="item-count" v-else>{{ item.childCount || 0 }} 项</span>
            </div>
            <div class="item-overlay">
              <a-dropdown trigger="click" placement="bottomRight">
                <button 
                  class="overlay-menu-btn" 
                  @click.stop
                  title="更多操作">
                  ⋯
                </button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item key="rename" @click="editFile(item)">
                      <span class="menu-icon">✏️</span>
                      重命名
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item key="delete" @click="deleteFile(item)">
                      <span class="menu-icon">🗑️</span>
                      删除
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 最近使用 -->
    <div class="resource-section" v-if="recentFiles.length > 0">
      <div class="section-title">
        最近使用
        <span class="file-count">({{ recentFiles.length }} 项)</span>
      </div>
      
      <!-- 列表视图 -->
      <div v-if="viewMode === 'list'" class="recent-files list-view">
        <div 
          class="recent-item-list" 
          v-for="item in recentFiles" 
          :key="item.id"
          @click="selectFile(item)"
          @dblclick="openFile(item)">
          <span class="file-icon">{{ getFileIcon(item) }}</span>
          <span class="item-name">{{ item.name }}</span>
          <span class="item-info">{{ formatModifiedTime(item.updatedAt) }}</span>
        </div>
      </div>
      
      <!-- 图标视图 -->
      <div v-else class="recent-files grid-view">
        <div 
          class="recent-item" 
          v-for="item in recentFiles" 
          :key="item.id"
          @click="selectFile(item)"
          @dblclick="openFile(item)">
          <div class="thumbnail">
            <span class="file-icon-large">{{ getFileIcon(item) }}</span>
          </div>
          <span class="file-name">{{ item.name }}</span>
          <span class="file-time">{{ formatModifiedTime(item.updatedAt) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue';
import { message, Modal } from 'ant-design-vue';

// 文件系统类型定义
interface FileSystemItem {
  id: string;
  pid: string;
  name: string;
  size?: number;
  isDir: boolean;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
  childCount?: number;
}

// Props
interface Props {
  fileSystemData: FileSystemItem[];
  recentFiles: FileSystemItem[];
  currentPath: FileSystemItem[];
  selectedFileId: string;
  isLoading: boolean;
  isCollapsed: boolean;
  panelWidth: number;
  viewMode: 'list' | 'grid';
  isResizing: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  fileSystemData: () => [],
  recentFiles: () => [],
  currentPath: () => [],
  selectedFileId: '',
  isLoading: false,
  isCollapsed: false,
  panelWidth: 250,
  viewMode: 'list',
  isResizing: false
});

// Emits
const emit = defineEmits<{
  'set-view-mode': [mode: 'list' | 'grid'];
  'create-folder': [];
  'upload-files': [];
  'refresh-files': [];
  'file-selected': [files: FileList];
  'navigate-to-root': [];
  'navigate-to-folder': [folder: FileSystemItem, pathIndex?: number];
  'select-file': [file: FileSystemItem];
  'open-file': [file: FileSystemItem];
  'edit-file': [file: FileSystemItem];
  'delete-file': [file: FileSystemItem];
  'show-context-menu': [event: MouseEvent, item: FileSystemItem];
}>();

// Refs
const fileInput = ref<HTMLInputElement>();

// Methods
const setViewMode = (mode: 'list' | 'grid') => {
  emit('set-view-mode', mode);
};

const createFolder = () => {
  emit('create-folder');
};

const uploadFiles = () => {
  fileInput.value?.click();
};

const refreshFiles = () => {
  emit('refresh-files');
};

const onFileSelected = (event: Event) => {
  const input = event.target as HTMLInputElement;
  const files = input.files;
  
  if (files && files.length > 0) {
    emit('file-selected', files);
  }
  
  // 清空输入框
  input.value = '';
};

const navigateToRoot = () => {
  emit('navigate-to-root');
};

const navigateToFolder = (folder: FileSystemItem, pathIndex?: number) => {
  emit('navigate-to-folder', folder, pathIndex);
};

const selectFile = (file: FileSystemItem) => {
  emit('select-file', file);
};

const openFile = (file: FileSystemItem) => {
  emit('open-file', file);
};

const editFile = (file: FileSystemItem) => {
  emit('edit-file', file);
};

const deleteFile = (file: FileSystemItem) => {
  emit('delete-file', file);
};

const showContextMenu = (event: MouseEvent, item: FileSystemItem) => {
  emit('show-context-menu', event, item);
};

// 获取文件图标
const getFileIcon = (item: FileSystemItem): string => {
  if (item.isDir) {
    return '📁';
  }
  
  const name = item.name.toLowerCase();
  const iconMap: { [key: string]: string } = {
    'psd': '🎨',
    'ai': '🎭', 
    'sketch': '💎',
    'xd': '🎪',
    'fig': '🎯',
    'png': '🖼️',
    'jpg': '🖼️',
    'jpeg': '🖼️',
    'webp': '🖼️',
    'gif': '🖼️',
    'svg': '⭐',
    'tiff': '🖼️',
    'otf': '🔤',
    'ttf': '🔤',
    'indd': '📋'
  };
  
  const ext = name.split('.').pop() || '';
  return iconMap[ext] || '📄';
};

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (!bytes) return '0 B';
  
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  
  return `${(bytes / Math.pow(1024, i)).toFixed(i === 0 ? 0 : 1)} ${sizes[i]}`;
};

// 格式化修改时间
const formatModifiedTime = (dateStr: string): string => {
  const date = new Date(dateStr);
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  
  if (days === 0) {
    return `今天 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
  } else if (days === 1) {
    return `昨天 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
  } else if (days < 7) {
    return `${days}天前`;
  } else {
    return `${date.getMonth() + 1}月${date.getDate()}日`;
  }
};
</script>

<style scoped>
/* 资源面板样式 */
.resource-panel-content {
  background: transparent;
  height: 100%;
}

/* 面板头部 */
.panel-header {
  padding: 12px 8px;
  border-bottom: 2px solid var(--ant-color-border);
  background: var(--ant-color-bg-base);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
}

.header-content {
  flex: 1;
  padding-left: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.panel-title {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--ant-color-text);
}

/* 视图切换按钮 */
.view-toggle {
  display: flex;
  gap: 2px;
  background: var(--ant-color-bg-layout);
  border-radius: 6px;
  padding: 2px;
  border: 1px solid var(--ant-color-border-secondary);
}

.view-btn {
  background: none;
  border: none;
  padding: 6px;
  border-radius: 4px;
  cursor: pointer;
  color: var(--ant-color-text-secondary);
  transition: all 0.2s ease;
  min-width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.view-btn:hover {
  background: var(--ant-color-bg-text-hover);
  color: var(--ant-color-text);
  transform: scale(1.05);
}

.view-btn.active {
  background: var(--ant-color-bg-container);
  color: var(--ant-color-primary);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transform: scale(1);
}

.view-icon {
  font-size: 14px;
  font-weight: bold;
}

/* 折叠按钮 */
.collapse-btn {
  background: none;
  border: none;
  padding: 4px;
  border-radius: 4px;
  cursor: pointer;
  color: var(--ant-color-text-secondary);
  transition: all 0.2s;
  min-width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.collapse-btn:hover {
  background: var(--ant-color-bg-text-hover);
  color: var(--ant-color-text);
}

.collapse-icon {
  font-size: 12px;
  transition: transform 0.2s;
  line-height: 1;
}

/* 面板内容 */
.panel-content {
  padding: 8px;
}

/* 工具栏 */
.toolbar-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px 0;
  border-bottom: 1px solid var(--ant-color-border-secondary);
}

.toolbar-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  background: var(--ant-color-bg-container);
  color: var(--ant-color-text);
  border: 1px solid var(--ant-color-border);
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.action-btn:hover {
  background: var(--ant-color-bg-text-hover);
  border-color: var(--ant-color-primary);
  color: var(--ant-color-primary);
}

.action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: var(--ant-color-bg-layout);
  border-color: var(--ant-color-border-secondary);
  color: var(--ant-color-text-secondary);
}

.btn-icon {
  font-size: 16px;
}

.btn-text {
  font-weight: 500;
}

.action-btn.spinning .btn-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 面包屑导航 */
.breadcrumb-section {
  margin-bottom: 12px;
  padding: 8px 0;
  border-bottom: 1px solid var(--ant-color-border-secondary);
}

.breadcrumb {
  display: flex;
  align-items: center;
  font-size: 13px;
  color: var(--ant-color-text-secondary);
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: var(--ant-color-text-secondary);
  transition: color 0.2s;
}

.breadcrumb-item:hover {
  color: var(--ant-color-primary);
}

.breadcrumb-item:not(:last-child) {
  margin-right: 4px;
}

.breadcrumb-separator {
  margin: 0 4px;
  font-size: 10px;
  color: var(--ant-color-text-secondary);
}

/* 加载状态 */
.loading-section {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px 0;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: var(--ant-color-primary);
}

.spinner {
  font-size: 36px;
  margin-bottom: 8px;
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: var(--ant-color-text-secondary);
}

/* 资源管理区域 */
.resource-section {
  margin-bottom: 24px;
  padding: 8px;
  border: 1px solid var(--ant-color-border-secondary);
  border-radius: 6px;
  background: var(--ant-color-bg-layout);
}

.section-title {
  font-size: 12px;
  font-weight: 600;
  color: var(--ant-color-text-secondary);
  margin-bottom: 8px;
  padding: 4px 8px;
  text-transform: uppercase;
  background: var(--ant-color-bg-container);
  border-radius: 4px;
  border: 1px solid var(--ant-color-border-secondary);
}

.file-count {
  font-size: 11px;
  color: var(--ant-color-text-secondary);
  margin-left: 8px;
}

.empty-folder {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
  color: var(--ant-color-text-secondary);
  font-size: 14px;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 12px;
}

.create-first-btn {
  margin-top: 16px;
  padding: 8px 16px;
  background: var(--ant-color-primary);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.create-first-btn:hover {
  background: var(--ant-color-primary-hover);
}

/* 文件树样式 */
.resource-tree {
  padding: 0 4px;
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.tree-item {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  color: var(--ant-color-text);
  transition: background-color 0.2s;
  position: relative;
}

.tree-item:hover {
  background: var(--ant-color-bg-text-hover);
}

.tree-item.selected {
  background: var(--ant-color-primary-bg);
  color: var(--ant-color-primary);
  font-weight: 500;
}

.folder-icon, .file-icon {
  margin-right: 8px;
  font-size: 14px;
  flex-shrink: 0;
}

.item-name {
  flex: 1;
  margin-left: 8px;
  font-weight: 500;
  color: var(--ant-color-text);
}

.item-info {
  font-size: 11px;
  color: var(--ant-color-text-secondary);
  margin-left: 8px;
}

.item-date {
  font-size: 11px;
  color: var(--ant-color-text-secondary);
  margin-left: 12px;
}

.item-actions {
  display: flex;
  gap: 4px;
  margin-left: auto;
  flex-shrink: 0;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.tree-item:hover .item-actions {
  opacity: 1;
}

.action-menu-btn {
  background: none;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  color: var(--ant-color-text-secondary);
  transition: all 0.2s;
  font-size: 16px;
  font-weight: bold;
  min-width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-menu-btn:hover {
  background: var(--ant-color-bg-text-hover);
  color: var(--ant-color-text);
}

/* 网格视图 */
.file-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.grid-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
  border-radius: 6px;
  background: var(--ant-color-bg-layout);
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
}

.grid-item:hover {
  background: var(--ant-color-bg-text-hover);
  transform: translateY(-1px);
}

.grid-item.selected {
  background: var(--ant-color-primary-bg);
  color: var(--ant-color-primary);
}

.item-thumbnail {
  width: 40px;
  height: 30px;
  background: var(--ant-color-bg-text-active);
  border-radius: 4px;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--ant-color-border-secondary);
  position: relative;
  overflow: hidden;
}

.item-icon-large {
  font-size: 24px;
  flex-shrink: 0;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.item-details {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.item-size, .item-count {
  font-size: 11px;
  color: var(--ant-color-text-secondary);
  margin-top: 4px;
}

.item-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 6px;
  opacity: 0;
  transition: opacity 0.2s ease;
  pointer-events: none;
}

.grid-item:hover .item-overlay {
  opacity: 1;
  pointer-events: auto;
}

.overlay-menu-btn {
  pointer-events: auto;
  background: var(--ant-color-bg-container);
  border: 1px solid var(--ant-color-border);
  border-radius: 6px;
  padding: 6px 10px;
  cursor: pointer;
  color: var(--ant-color-text-secondary);
  transition: all 0.2s;
  font-size: 14px;
  font-weight: bold;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  min-width: 32px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.overlay-menu-btn:hover {
  background: var(--ant-color-primary-bg);
  border-color: var(--ant-color-primary);
  color: var(--ant-color-primary);
}

/* 最近文件 */
.recent-files {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  padding: 0 4px;
}

.recent-files.list-view {
  grid-template-columns: 1fr;
}

.recent-item-list {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  color: var(--ant-color-text);
  transition: background-color 0.2s;
}

.recent-item-list:hover {
  background: var(--ant-color-bg-text-hover);
}

.recent-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
  border-radius: 6px;
  background: var(--ant-color-bg-layout);
  cursor: pointer;
  transition: all 0.2s;
}

.recent-item:hover {
  background: var(--ant-color-bg-text-hover);
  transform: translateY(-1px);
}

.thumbnail {
  width: 40px;
  height: 30px;
  background: var(--ant-color-bg-text-active);
  border-radius: 4px;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-icon-large {
  font-size: 20px;
}

.file-name, .file-time {
  font-size: 11px;
  color: var(--ant-color-text-secondary);
  text-align: center;
}

.file-time {
  margin-top: 4px;
}

/* 拖拽分隔器 */
.resize-handle {
  position: absolute;
  top: 0;
  width: 6px;
  height: 100%;
  cursor: col-resize;
  background: var(--ant-color-border);
  transition: all 0.2s ease;
  z-index: 10;
  border-radius: 3px;
  will-change: background-color, box-shadow;
}

.resize-handle:hover {
  background: var(--ant-color-primary);
  opacity: 0.8;
  box-shadow: 0 0 8px rgba(24, 144, 255, 0.3);
  transform: scaleX(1.2);
}

.resize-handle.resizing {
  background: var(--ant-color-primary);
  opacity: 1;
  box-shadow: 0 0 16px rgba(24, 144, 255, 0.5);
  transform: scaleX(1.5);
}

.right-handle {
  right: -3px;
}

/* 菜单图标 */
.menu-icon {
  margin-right: 8px;
  font-size: 14px;
  width: 16px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .toolbar-actions {
    flex-wrap: wrap;
    gap: 4px;
  }
  
  .action-btn {
    padding: 4px 8px;
    font-size: 12px;
  }
  
  .btn-text {
    display: none;
  }
  
  .file-grid {
    grid-template-columns: 1fr 1fr;
    gap: 6px;
  }
  
  .grid-item {
    padding: 6px;
  }
  
  .item-thumbnail {
    width: 32px;
    height: 24px;
  }
}
</style> 