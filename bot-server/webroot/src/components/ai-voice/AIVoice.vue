<template>
  <div class="ai-voice-container">
    <!-- 悬浮按钮 -->
    <div 
      class="voice-floating-button"
      :class="{ 
        'recording': isRecording,
        'processing': isProcessing,
        'error': hasError,
        'dragging': isDragging
      }"
      :style="{ top: buttonTop + 'px' }"
      @click="toggleRecording"
      @mouseenter="showTooltip = true"
      @mouseleave="showTooltip = false"
      @mousedown="startDrag"
      @touchstart="startDrag"
    >
      <div class="voice-icon">
        <svg v-if="!isRecording && !isProcessing" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 2C13.1 2 14 2.9 14 4V10C14 11.1 13.1 12 12 12C10.9 12 10 11.1 10 10V4C10 2.9 10.9 2 12 2ZM19 10V12C19 15.5 15.5 19 12 19C8.5 19 5 15.5 5 12V10H7V12C7 14.4 9.6 17 12 17C14.4 17 17 14.4 17 12V10H19ZM10 21H14V23H10V21Z"/>
        </svg>
        <svg v-else-if="isRecording" viewBox="0 0 24 24" fill="currentColor">
          <path d="M6 6H18V18H6V6Z"/>
        </svg>
        <div v-else class="loading-spinner"></div>
      </div>
      
      <!-- 录音波形动画 -->
      <div v-if="isRecording" class="wave-animation">
        <div class="wave"></div>
        <div class="wave"></div>
        <div class="wave"></div>
      </div>
    </div>

    <!-- 提示框 -->
    <div v-if="showTooltip" class="tooltip">
      <span v-if="!isRecording && !isProcessing">点击开始语音输入</span>
      <span v-else-if="isRecording">正在录音中...</span>
      <span v-else-if="isProcessing">正在识别中...</span>
    </div>

    <!-- 错误提示 -->
    <div v-if="hasError && errorMessage" class="error-message">
      {{ errorMessage }}
      <button 
        v-if="retryCount > 0 && retryCount <= maxRetries"
        class="retry-button"
        @click="retryRecording"
      >
        立即重试
      </button>
    </div>

    <!-- 识别结果预览 -->
    <div v-if="transcript && isRecording" class="transcript-preview">
      {{ transcript }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

// 定义事件
const emit = defineEmits<{
  'transcript': [text: string]
  'error': [error: string]
  'start': []
  'stop': []
}>()

// 响应式状态
const isRecording = ref(false)
const isProcessing = ref(false)
const hasError = ref(false)
const errorMessage = ref('')
const transcript = ref('')
const showTooltip = ref(false)

// 拖拽相关状态
const isDragging = ref(false)
const buttonTop = ref(window.innerHeight / 2 - 30) // 默认居中位置
const dragStartY = ref(0)
const dragStartTop = ref(0)

// 重试相关状态
const retryCount = ref(0)
const maxRetries = 3

// 语音识别实例
let recognition: any = null

// 检查浏览器支持
const checkSpeechRecognitionSupport = (): boolean => {
  return 'webkitSpeechRecognition' in window || 'SpeechRecognition' in window
}

// 检查网络状态
const checkNetworkStatus = (): boolean => {
  return navigator.onLine
}

// 获取详细的网络错误信息
const getNetworkErrorDetails = (): string => {
  if (!navigator.onLine) {
    return '设备处于离线状态，请检查网络连接'
  }
  
  const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection
  if (connection) {
    const effectiveType = connection.effectiveType
    if (effectiveType === 'slow-2g' || effectiveType === '2g') {
      return '网络连接较慢，可能影响语音识别效果'
    }
  }
  
  return '网络连接出现问题，请检查网络设置'
}

// 初始化语音识别
const initSpeechRecognition = () => {
  if (!checkSpeechRecognitionSupport()) {
    hasError.value = true
    errorMessage.value = '当前浏览器不支持语音识别功能'
    emit('error', errorMessage.value)
    return false
  }

  const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition
  recognition = new SpeechRecognition()

  // 配置识别选项
  recognition.continuous = false // 不连续识别
  recognition.interimResults = true // 显示临时结果
  recognition.lang = 'zh-CN' // 设置语言为中文
  recognition.maxAlternatives = 1

  // 识别开始
  recognition.onstart = () => {
    isRecording.value = true
    isProcessing.value = false
    hasError.value = false
    errorMessage.value = ''
    transcript.value = ''
    // 成功开始录音时的处理已移至 startRecording 函数中
    emit('start')
  }

  // 识别结果
  recognition.onresult = (event: any) => {
    let interimTranscript = ''
    let finalTranscript = ''

    for (let i = event.resultIndex; i < event.results.length; i++) {
      const result = event.results[i]
      if (result.isFinal) {
        finalTranscript += result[0].transcript
      } else {
        interimTranscript += result[0].transcript
      }
    }

    transcript.value = finalTranscript || interimTranscript
    
    if (finalTranscript) {
      // 成功识别后重置重试计数器
      retryCount.value = 0
      emit('transcript', finalTranscript.trim())
    }
  }

  // 识别结束
  recognition.onend = () => {
    isRecording.value = false
    isProcessing.value = false
    emit('stop')
  }

  // 识别错误
  recognition.onerror = (event: any) => {
    isRecording.value = false
    isProcessing.value = false
    
    switch (event.error) {
      case 'no-speech':
        hasError.value = true
        errorMessage.value = '没有检测到语音，请重试'
        retryCount.value = 0
        break
      case 'network':
        handleNetworkError()
        break
      case 'not-allowed':
        hasError.value = true
        errorMessage.value = '麦克风权限被拒绝，请在浏览器设置中允许麦克风访问'
        retryCount.value = 0
        break
      case 'service-not-allowed':
        hasError.value = true
        errorMessage.value = '语音识别服务不可用，请检查网络连接'
        retryCount.value = 0
        break
      case 'aborted':
        // 用户主动停止，不显示错误
        hasError.value = false
        retryCount.value = 0
        break
      default:
        hasError.value = true
        errorMessage.value = `语音识别出现错误: ${event.error}`
        retryCount.value = 0
    }
    
    if (hasError.value) {
      emit('error', errorMessage.value)
    }
  }

  return true
}

// 处理网络错误和重试逻辑
const handleNetworkError = () => {
  const networkDetails = getNetworkErrorDetails()
  
  retryCount.value++
  
  if (retryCount.value <= maxRetries && checkNetworkStatus()) {
    hasError.value = true
    errorMessage.value = `${networkDetails}，正在重试... (${retryCount.value}/${maxRetries})`
    emit('error', errorMessage.value)
    
    // 延迟后自动重试
    setTimeout(() => {
      if (retryCount.value <= maxRetries && checkNetworkStatus()) {
        hasError.value = false
        errorMessage.value = ''
        startRecording()
      } else if (!checkNetworkStatus()) {
        hasError.value = true
        errorMessage.value = '网络连接已断开，请检查网络后重试'
        retryCount.value = 0
        emit('error', errorMessage.value)
      }
    }, 2000)
  } else {
    hasError.value = true
    if (!checkNetworkStatus()) {
      errorMessage.value = '设备处于离线状态，请检查网络连接后重试'
    } else {
      errorMessage.value = '网络连接持续失败，请检查防火墙设置或稍后重试'
    }
    retryCount.value = 0
    emit('error', errorMessage.value)
  }
}

// 手动重试录音
const retryRecording = () => {
  if (!checkNetworkStatus()) {
    hasError.value = true
    errorMessage.value = '设备处于离线状态，请检查网络连接'
    return
  }
  
  hasError.value = false
  errorMessage.value = ''
  startRecording()
}

// 切换录音状态
const toggleRecording = () => {
  // 如果正在拖拽，不执行录音操作
  if (isDragging.value) {
    return
  }
  
  if (!recognition) {
    if (!initSpeechRecognition()) {
      return
    }
  }

  if (isRecording.value) {
    stopRecording()
  } else {
    // 用户主动开始录音时，重置重试计数器
    retryCount.value = 0
    startRecording()
  }
}

// 开始录音
const startRecording = () => {
  if (!recognition) return

  try {
    isProcessing.value = true
    recognition.start()
  } catch (error) {
    hasError.value = true
    errorMessage.value = '启动语音识别失败'
    isProcessing.value = false
    retryCount.value = 0 // 启动失败时重置重试计数
    emit('error', errorMessage.value)
  }
}

// 停止录音
const stopRecording = () => {
  if (!recognition) return

  try {
    recognition.stop()
  } catch (error) {
    console.error('停止录音失败:', error)
  }
}

// 拖拽功能
const startDrag = (e: MouseEvent | TouchEvent) => {
  // 防止拖拽时触发录音
  e.preventDefault()
  e.stopPropagation()
  
  isDragging.value = true
  showTooltip.value = false
  
  const clientY = e instanceof MouseEvent ? e.clientY : e.touches[0].clientY
  dragStartY.value = clientY
  dragStartTop.value = buttonTop.value
  
  // 添加全局事件监听器
  document.addEventListener('mousemove', handleDrag)
  document.addEventListener('mouseup', stopDrag)
  document.addEventListener('touchmove', handleDrag)
  document.addEventListener('touchend', stopDrag)
}

const handleDrag = (e: MouseEvent | TouchEvent) => {
  if (!isDragging.value) return
  
  e.preventDefault()
  const clientY = e instanceof MouseEvent ? e.clientY : e.touches[0].clientY
  const deltaY = clientY - dragStartY.value
  let newTop = dragStartTop.value + deltaY
  
  // 限制拖拽范围，不能超出屏幕边界
  const minTop = 10
  const maxTop = window.innerHeight - 70
  newTop = Math.max(minTop, Math.min(maxTop, newTop))
  
  buttonTop.value = newTop
}

const stopDrag = () => {
  if (!isDragging.value) return
  
  isDragging.value = false
  
  // 移除全局事件监听器
  document.removeEventListener('mousemove', handleDrag)
  document.removeEventListener('mouseup', stopDrag)
  document.removeEventListener('touchmove', handleDrag)
  document.removeEventListener('touchend', stopDrag)
  
  // 延迟重新启用点击事件，防止拖拽后立即触发录音
  setTimeout(() => {
    // 这里可以添加保存位置的逻辑
  }, 100)
}

// 窗口大小变化处理
const handleWindowResize = () => {
  // 确保按钮位置在有效范围内
  const minTop = 10
  const maxTop = window.innerHeight - 70
  if (buttonTop.value < minTop) {
    buttonTop.value = minTop
  } else if (buttonTop.value > maxTop) {
    buttonTop.value = maxTop
  }
}

// 组件挂载
onMounted(() => {
  initSpeechRecognition()
  window.addEventListener('resize', handleWindowResize)
})

// 组件卸载
onUnmounted(() => {
  if (recognition) {
    recognition.stop()
    recognition = null
  }
  window.removeEventListener('resize', handleWindowResize)
  // 清理拖拽事件监听器
  document.removeEventListener('mousemove', handleDrag)
  document.removeEventListener('mouseup', stopDrag)
  document.removeEventListener('touchmove', handleDrag)
  document.removeEventListener('touchend', stopDrag)
})
</script>

<style scoped lang="scss">
.ai-voice-container {
  position: fixed;
  right: 20px;
  top: 0;
  left: 0;
  bottom: 0;
  z-index: 1000;
  pointer-events: none;
}

.voice-floating-button {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  position: absolute;
  right: 20px;
  overflow: hidden;
  pointer-events: auto;
  user-select: none;

  &:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.4);
  }

  &.recording {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    animation: pulse 1.5s infinite;
  }

  &.processing {
    background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
  }

  &.error {
    background: linear-gradient(135deg, #ff6b6b 0%, #c44569 100%);
  }

  &.dragging {
    cursor: grabbing;
    transform: scale(1.05);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.5);
    transition: none;
  }

  .voice-icon {
    width: 24px;
    height: 24px;
    
    svg {
      width: 100%;
      height: 100%;
    }
  }

  .loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .wave-animation {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    pointer-events: none;

    .wave {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 100%;
      height: 100%;
      border: 2px solid rgba(255, 255, 255, 0.6);
      border-radius: 50%;
      transform: translate(-50%, -50%);
      animation: wave 2s infinite;

      &:nth-child(2) {
        animation-delay: 0.5s;
      }

      &:nth-child(3) {
        animation-delay: 1s;
      }
    }
  }
}

.tooltip {
  position: absolute;
  right: 70px;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
  white-space: nowrap;
  opacity: 0;
  animation: fadeIn 0.3s ease forwards;
  pointer-events: none;

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 100%;
    transform: translateY(-50%);
    border: 6px solid transparent;
    border-left-color: rgba(0, 0, 0, 0.8);
  }
}

.error-message {
  position: absolute;
  right: 70px;
  top: 50%;
  transform: translateY(-50%);
  background: #ff6b6b;
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
  white-space: nowrap;
  opacity: 0;
  animation: fadeIn 0.3s ease forwards;
  pointer-events: none;

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 100%;
    transform: translateY(-50%);
    border: 6px solid transparent;
    border-left-color: #ff6b6b;
  }

  .retry-button {
    display: block;
    margin-top: 8px;
    padding: 4px 8px;
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    color: white;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.3);
      border-color: rgba(255, 255, 255, 0.5);
    }

    &:active {
      transform: scale(0.95);
    }
  }
}

.transcript-preview {
  position: absolute;
  right: 70px;
  top: 50%;
  transform: translateY(-50%);
  background: white;
  color: #333;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  max-width: 300px;
  word-wrap: break-word;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  opacity: 0;
  animation: fadeIn 0.3s ease forwards;
  pointer-events: none;

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 100%;
    transform: translateY(-50%);
    border: 8px solid transparent;
    border-left-color: white;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes wave {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-50%) translateX(10px);
  }
  to {
    opacity: 1;
    transform: translateY(-50%) translateX(0);
  }
}
</style>