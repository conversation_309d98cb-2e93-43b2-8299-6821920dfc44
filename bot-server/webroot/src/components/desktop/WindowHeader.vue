<template>
      <WindowBar/>
</template>

<script setup lang="ts">
import {ref} from 'vue';
import WindowBar from "@/components/desktop/window/WindowBar.vue";
import MacBar from "@/components/desktop/apple/MacBar.vue";
import { theme } from 'ant-design-vue';
const osName = ref('MacOS');
if (navigator.userAgent.indexOf('Win') !== -1) osName.value = 'Windows';
else if (navigator.userAgent.indexOf('Mac') !== -1) osName.value = 'MacOS';
else if (navigator.userAgent.indexOf('Linux') !== -1) osName.value = 'Linux';
const { useToken } = theme;
const { token } = useToken();
</script>


<style scoped>

.desktop-header{
}

</style>