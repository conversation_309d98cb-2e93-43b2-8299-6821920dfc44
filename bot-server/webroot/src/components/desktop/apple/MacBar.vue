<template>
  <div class=" w-full h-[30px] flex">
    <div class="flex size-full">
      <component :is="currentNavComponent" v-if="currentNavComponent" os="mac" />
    </div>
  </div>
</template>

<script setup lang="ts">
import MacBtnGroup from "./MacBtnGroup.vue";
import { useGptStore } from "@/components/store/gpt";
import { useAiBot } from "@/components/store/bot";
import LocationComponent from "@/components/ai-bot-widget/LocationComponent.vue";
import { shallowRef, watch } from 'vue';
import { useRoute } from 'vue-router';
import NavComponentManager from "@/components/ai-bot-widget/nav/NavComponentManager";

const ctx = useGptStore();
const bot = useAiBot();
const route = useRoute();

// 使用 shallowRef 存储当前导航组件
const currentNavComponent = shallowRef(null);
// 是否显示位置组件
const showLocationComponent = shallowRef(false);

// 更新导航组件和位置组件显示状态
const updateNavigation = () => {
  const routeName = route.name as string;
  let uiType = bot.ui;
  // 添加兼容性，如果setting不为空，则使用setting，否则使用ui
  const setting = bot.setting;
  if (uiType === 'SystemSetting' && setting) {
    uiType = setting;
  }

  // 首先检查是否有与路由相关的导航组件
  let component = NavComponentManager.getComponentByRoute(routeName);

  // 如果没有路由相关的组件，则检查是否有与UI类型相关的导航组件
  if (!component && routeName !== 'guide' && routeName !== 'loading') {
    component = NavComponentManager.getComponentByUI(uiType);
  }

  // 更新当前导航组件
  currentNavComponent.value = component;

  // 更新位置组件显示状态
  showLocationComponent.value = NavComponentManager.shouldShowLocationComponent(routeName, uiType);
};

// 监听路由变化
watch(() => route.name, () => {
  updateNavigation();
}, { immediate: true });

// 监听UI类型变化
watch(() => bot.ui, () => {
  updateNavigation();
}, { immediate: true });

watch(() => bot.setting, () => {
  updateNavigation();
}, { immediate: true });

const toggleTheme = () => {
  bot.theme = bot.theme === 'light' ? 'dark' : 'light';
};
</script>

<style scoped>
.sys {
  -webkit-app-region: no-drag;
  color: white;
}

/* 添加可拖动区域样式 */
.drag-area {
  -webkit-app-region: drag;
  height: 100%;
}

/* 主题图标样式 */
.icon.iconfont {
  font-size: 16px;
  color: inherit;
}

.icon.iconfont.yueliang {
  font-size: 14px;
}

.icon.iconfont.taiyang {
  font-size: 16px;
}
</style>