<template>
  <div class="mac-btn-group">
    <div class="drag-area"></div>
    <button class="mac-btn close" @click.stop="desktop_close">
      <svg viewBox="0 0 12 12">
        <path d="M3.5,3.5 L8.5,8.5 M3.5,8.5 L8.5,3.5"/>
      </svg>
    </button>
    <button class="mac-btn minimize" @click.stop="desktop_minimize">
      <svg viewBox="0 0 12 12">
        <path d="M3,6 L9,6"/>
      </svg>
    </button>
    <button class="mac-btn maximize" @click.stop="desktop_full">
      <svg viewBox="0 0 12 12">
        <path d="M3.5,3.5 h5 v5 h-5 z"/>
      </svg>
    </button>
  </div>
</template>

<script setup lang="ts">
import {desktop_close, desktop_full, desktop_minimize} from "@/components/desktop/desktop";
</script>

<style scoped>
.mac-btn-group {
  display: flex;
  gap: 6px;
  padding: 0 8px;
  height: 100%;
  align-items: center;
  position: relative;
  z-index: 10;
}

.drag-area {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  -webkit-app-region: drag;
  z-index: 0;
}

.mac-btn {
  width: 13px;
  height: 13px;
  border-radius: 50%;
  border: 0.5px solid rgba(0, 0, 0, 0.1);
  padding: 0;
  position: relative;
  cursor: pointer;
  transition: all 0.1s ease-in-out;
  z-index: 1;
  -webkit-app-region: no-drag;
}

.mac-btn svg {
  width: 8px;
  height: 8px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: opacity 0.1s;
}

.mac-btn:hover svg {
  opacity: 0.5;
}

.mac-btn:active svg {
  opacity: 0.7;
}

.mac-btn svg path {
  fill: none;
  stroke: #000;
  stroke-width: 1.2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

/* 按钮颜色 */
.mac-btn.close {
  background: #ff5f57;
  border-color: #e0443e;
}
.mac-btn.minimize {
  background: #febc2e;
  border-color: #e1a116;
}
.mac-btn.maximize {
  background: #28c840;
  border-color: #27aa35;
}

/* 悬浮和点击效果 */
.mac-btn.close:hover {
  background: #ff4d4d;
}
.mac-btn.minimize:hover {
  background: #febb12;
}
.mac-btn.maximize:hover {
  background: #24be3b;
}

.mac-btn.close:active {
  background: #bf4943;
}
.mac-btn.minimize:active {
  background: #bf9112;
}
.mac-btn.maximize:active {
  background: #1f9a31;
}
</style> 