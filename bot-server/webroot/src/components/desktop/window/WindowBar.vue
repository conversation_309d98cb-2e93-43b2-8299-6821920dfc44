<template>
  <div class="size-full  flex items-center relative m-auto">    
    <!-- 中间动态组件区域 -->
    <div class="size-full flex justify-center items-center drag-area">
      <component :is="currentNavComponent" v-if="currentNavComponent"/>
    </div>
  </div>
</template>

<script setup lang="ts">
import {desktop_close, desktop_max, desktop_minimize} from "@/components/desktop/desktop";
import {useAiBot} from "@/components/store/bot";
import {useGptStore} from "@/components/store/gpt";
import LocationComponent from "@/components/ai-bot-widget/LocationComponent.vue";
import { shallowRef, watch } from 'vue';
import { useRoute } from 'vue-router';
import NavComponentManager from "@/components/ai-bot-widget/nav/NavComponentManager";
import { 
  MinusOutlined, 
  BorderOutlined, 
  CloseOutlined 
} from '@ant-design/icons-vue';

const ctx = useGptStore();
const bot = useAiBot();
const route = useRoute();

// 使用 shallowRef 存储当前导航组件
const currentNavComponent = shallowRef(null);
// 是否显示位置组件
const showLocationComponent = shallowRef(false);

// 更新导航组件和位置组件显示状态
const updateNavigation = () => {
  const routeName = route.name as string;
  let uiType = bot.ui;
  // 添加兼容性，如果setting不为空，则使用setting，否则使用ui
  const setting = bot.setting;
  if (uiType==='SystemSetting' && setting) {
    uiType = setting;
  }
  // 首先检查是否有与路由相关的导航组件
  let component = NavComponentManager.getComponentByRoute(routeName);
  
  // 如果没有路由相关的组件，则检查是否有与UI类型相关的导航组件
  if (!component && routeName !== 'guide' && routeName !== 'loading') {
    component = NavComponentManager.getComponentByUI(uiType);
  }
  
  // 更新当前导航组件
  currentNavComponent.value = component;
  
  // 更新位置组件显示状态
  showLocationComponent.value = NavComponentManager.shouldShowLocationComponent(routeName, uiType);
};

// 监听路由变化
watch(() => route.name, () => {
  updateNavigation();
}, { immediate: true });

// 监听UI类型变化
watch(() => bot.ui, () => {
  updateNavigation();
}, { immediate: true });

</script>

<style scoped>
.window-ant-btn {
  -webkit-app-region: no-drag;
  height: 100%;
  width: 35px;
  padding: 0;
  color: var(--window-btn-color, #f0f0f0);
  border: none;
  border-radius: 0;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.window-ant-btn:hover {
  background-color: rgba(255, 255, 255, 0.15);
}

.window-close-btn:hover {
  background-color: #ff4747 !important;
}

.window-close-btn:hover :deep(.anticon) {
  color: white;
}

.window-icon {
  font-size: 14px;
}

/* 添加可拖动区域样式 */
.drag-area {
  -webkit-app-region: drag;
  height: 100%;
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .window-ant-btn {
    --window-btn-color: #ffffff;
  }
}

/* 亮色主题适配 */
@media (prefers-color-scheme: light) {
  .window-ant-btn {
    --window-btn-color: #505050;
  }
}
</style>