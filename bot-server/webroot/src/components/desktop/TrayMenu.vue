<template>
  <div class="tray size-full flex flex-col justify-center">
    <a-menu mode="inline" :select="false">
      <a-menu-item @click="desktop_exit">
        退出
<!--        <template #icon>
          <icon>
            <icon-font type="exit"/>
          </icon>
        </template>-->
      </a-menu-item>
    </a-menu>
  </div>

</template>

<script setup lang="ts">


import {onMounted, ref} from "vue";
// import {ipc<PERSON>enderer} from "electron";
import {desktop_exit} from "@/components/desktop/desktop";
import {useAppStore} from "@/components/system-components/store/app";
import {useAiBot} from "@/components/store/bot";

const tray = ref()
const app = useAppStore()
const bot = useAiBot()

// ipcRenderer.on("open", () => {
  // 打开系统托盘时候获取焦点
  // tray.value.focus()
// })

// ipcRenderer.on("theme-change", (event, args) => {
  // bot.setTheme(args ? 'dark' : 'light');
  
  // 直接修改 HTML 根元素的类
  const documentElement = document.documentElement;
  // if (args) {
  //   documentElement.classList.add('dark');
  // } else {
  //   documentElement.classList.remove('dark');
  // }
//  })

// 菜单失去焦点时候隐藏

// onMounted(() => {
//   tray.value.addEventListener("blur", () => {
//     ipcRenderer.send("close-tray")
//   })
// })

</script>


<style>
.tray .ant-menu-item {
  height: 30px !important;
}
</style>