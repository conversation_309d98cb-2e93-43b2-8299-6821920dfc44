<template>
  <div id="app-container" ref="containerRef" :style="containerStyle"></div>
</template>

<script setup lang="ts">
import {computed, onMounted, onUnmounted, ref} from 'vue';
import {useGlobalSetting} from "@/components/store/bot-global-setting";

const props = defineProps<{
  movementStrength?: number; // 移动强度
  maxMovement?: number;     // 最大移动范围
}>();

const containerRef = ref<HTMLElement | null>(null);
const globalSetting = useGlobalSetting();

// 计算背景图片URL
const backgroundImage = computed(() => {
  if (!globalSetting.setting.app) {
    globalSetting.setting.app = {bg: "", blurSize: 1};
    globalSetting.updateSetting(globalSetting.setting);
  }
  return globalSetting.setting.app.bg || '/bg.jpg';
});

// 计算容器样式
const containerStyle = computed(() => ({
  backgroundImage: `url(${backgroundImage.value})`,
  backgroundSize: globalSetting.setting.app?.bg ? 'cover' : '150% 150%'  // 自定义背景使用 cover，默认背景使用 150%
}));

const movementStrength = props.movementStrength || 2;
const maxMovement = props.maxMovement || 8;

// 处理鼠标移动
const handleMouseMove = (e: MouseEvent) => {
  if (!containerRef.value) return;

  // 使用 requestAnimationFrame 优化性能
  requestAnimationFrame(() => {
    const pageX = e.pageX - (window.innerWidth / 2);
    const pageY = e.pageY - (window.innerHeight / 2);

    // 计算移动百分比，限制在 -maxMovement 到 maxMovement 之间
    const moveX = Math.max(-maxMovement, Math.min(maxMovement,
        movementStrength * (pageX) / (window.innerWidth / 2)));
    const moveY = Math.max(-maxMovement, Math.min(maxMovement,
        movementStrength * (pageY) / (window.innerHeight / 2)));

    containerRef.value.style.backgroundPosition = `calc(50% + ${moveX}%) calc(50% + ${moveY}%)`;
  });
};

// 使用防抖优化窗口大小变化的处理
const debounce = (fn: Function, delay: number) => {
  let timer: ReturnType<typeof setTimeout> | null = null;
  return function (...args: any[]) {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => fn.apply(this, args), delay);
  }
};

// 组件挂载时添加事件监听
onMounted(() => {
  // 添加节流后的鼠标移动监听
  window.addEventListener('mousemove', handleMouseMove, {passive: true});

  // 初始化背景位置
  if (containerRef.value) {
    containerRef.value.style.backgroundPosition = '50% 50%';
  }
});

// 组件卸载时移除事件监听
onUnmounted(() => {
  window.removeEventListener('mousemove', handleMouseMove);
});
</script>

<style>
#app-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
  background-size: 150% 150%;
  background-position: 50% 50%;
  transition: background-image 0.3s ease;
  /* 添加硬件加速 */
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000;
  will-change: background-position;
}
</style> 