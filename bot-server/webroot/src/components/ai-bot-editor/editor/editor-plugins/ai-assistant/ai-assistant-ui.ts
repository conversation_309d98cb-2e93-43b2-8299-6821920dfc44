import {Plugin} from '@ckeditor/ckeditor5-core';
import {
    ButtonView,
    createLabeledInputText,
    Dialog,
    DialogViewPosition,
    FocusableView,
    FocusCycler,
    InputTextView,
    LabeledFieldView,
    submitHandler,
    View,
    ViewCollection
} from '@ckeditor/ckeditor5-ui';
import {FocusTracker, KeystrokeHandler} from '@ckeditor/ckeditor5-utils';
import './theme/ai-assistant-ui.css';
/**
 * AI助手插件的UI部分
 * 负责在编辑器工具栏中添加AI助手按钮
 */
export default class AiAssistantUI extends Plugin {

    get requires() {
        return [Dialog];
    }

    /**
     * 插件名称，用于注册和获取插件
     */
    static get pluginName() {
        return 'AiAssistantUI';
    }

    /**
     * 初始化插件
     */
    init() {
        const editor = this.editor;
        const t = editor.t; // 获取翻译工具


        // 注册UI组件工厂
        editor.ui.componentFactory.add('aiAssistant', (locale) => {
            const buttonView = new ButtonView(locale);

            buttonView.set({
                label: 'AI 助手',
                tooltip: true,
                withText: true,
                class: 'ck-rounded-corners'
            });
            buttonView.render()

            // Define the button behavior on press.
            buttonView.on('execute', () => {
                const dialog = editor.plugins.get('Dialog');

                // If the button is turned on, hide the dialog.
                if (buttonView.isOn) {
                    dialog.hide();
                    buttonView.isOn = false;

                    return;
                }


                buttonView.isOn = true;

                // Otherwise, show the dialog.
                // Create a view with some simple content. It will be displayed as a dialog's body.
                const textView = new View(locale);
                const api = new LabeledFieldView(locale, createLabeledInputText);
                const key = new LabeledFieldView(locale, createLabeledInputText);
                const model = new LabeledFieldView(locale, createLabeledInputText);

                api.set({label: 'api'});
                key.set({label: 'key'});
                model.set({label: 'model'});
                api.render();
                key.render();
                model.render();

                textView.setTemplate({
                    tag: 'div',
                    attributes: {
                        style: {
                            padding: 'var(--ck-spacing-large)',
                            width: '100%',
                            maxWidth: '500px'
                        },
                        tabindex: -1
                    },
                    children: [
                        {
                            tag: "div",
                            attributes: {
                                style: {},
                                class: [
                                    "ai-from"
                                ],
                                tabindex: -1
                            },
                            children: [
                                {
                                    tag: "div",
                                    attributes: {
                                        class: [
                                            "ai-from-row"
                                        ],
                                        style: {},
                                        tabindex: -1
                                    },
                                    children: [
                                        api
                                    ]
                                },
                                {
                                    tag: "div",
                                    attributes: {
                                        class: [
                                            "ai-from-row"
                                        ],
                                        style: {},
                                        tabindex: -1
                                    },
                                    children: [
                                        key
                                    ]
                                },
                                {
                                    tag: "div",
                                    attributes: {
                                        class: [
                                            "ai-from-row"
                                        ],
                                        style: {},
                                        tabindex: -1
                                    },
                                    children: [
                                        model
                                    ]
                                }
                            ],
                        },
                    ]
                });

                // Tell the plugin to display a dialog with the title, content, and one action button.
                dialog.show({
                    isModal: true,
                    id: 'AI 设置',
                    title: 'AI 设置',
                    content: textView,
                    position: DialogViewPosition.EDITOR_CENTER ,
                    actionButtons: [
                        {
                            label: t('Yes'),
                            class: 'ck-button-action',
                            withText: true,
                            onExecute: () => dialog.hide()
                        },
                        {
                            label: t('No'),
                            withText: true,
                            onExecute: () => dialog.hide()
                        }
                    ],
                    onHide() {
                        buttonView.isOn = false;
                    },
                    onShow(dialog) {
                    }
                });
            });

            return buttonView;
        });
    }


}

/**
 * AI助手对话框视图
 */
class AiAssistantDialogView extends View {
    /**
     * 创建对话框视图类的成员属性
     */
    public apiEndpointInput!: LabeledFieldView<InputTextView>;
    public apiKeyInput!: LabeledFieldView<InputTextView>;
    public saveButtonView!: ButtonView;
    public cancelButtonView!: ButtonView;

    private _focusables: ViewCollection<FocusableView>;
    private _focusTracker!: FocusTracker;
    private _keystrokes!: KeystrokeHandler;
    private _focusCycler!: FocusCycler;

    constructor(locale) {
        super(locale);

    }


    /**
     * 渲染对话框
     */
    render(): void {
        super.render();

        // 添加提交处理
        submitHandler({
            view: this
        });

        // 添加焦点跟踪
        this._focusables.forEach(view => {
            this._focusTracker.add(view.element);
        });

        // 启用按键处理
        this._keystrokes.listenTo(this.element);

        // 设置对话框居中
        if (this.element) {
            this.element.classList.add('ck-elevation-4');
            this.element.style.position = 'fixed';
            this.element.style.top = '50%';
            this.element.style.left = '50%';
            this.element.style.transform = 'translate(-50%, -50%)';
            this.element.style.zIndex = '9999';
        }
    }

    /**
     * 聚焦第一个元素
     */
    focus(): void {
        this._focusCycler.focusFirst();
    }

    /**
     * 销毁对话框
     */
    destroy(): void {
        super.destroy();
        this._focusTracker.destroy();
        this._keystrokes.destroy();
    }
}

