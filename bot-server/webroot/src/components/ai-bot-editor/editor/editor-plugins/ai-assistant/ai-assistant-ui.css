/*
!* AI助手样式 *!
.ai-assistant-form {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border-radius: 8px; !* 增加圆角 *!
    background-color: #ffffff !important;
    box-shadow: 0 6px 30px rgba(0, 0, 0, 0.18); !* 增强阴影效果 *!
    border: 1px solid #e0e0e0;
    !* 限制最大宽度，避免在大屏幕上过宽 *!
    max-width: 500px;
    width: calc(100% - 60px); !* 考虑边距 *!
}

.ai-assistant-form-header {
    height: 50px;
    border-bottom: 1px solid #eee;
    background-color: #ffffff !important;
    display: flex; !* 添加flex布局 *!
    align-items: center; !* 垂直居中 *!
    padding: 0 20px; !* 添加左右padding *!
}

.ai-assistant-form-title {
    font-size: 22px;
    font-weight: bold;
    margin: 4px 0 0;
    color: #333;
    letter-spacing: -0.5px;
}

.ai-assistant-form-body {
    padding: 24px;
    flex-grow: 1;
    background-color: #ffffff !important;
}

.ai-assistant-form-row {
    margin-bottom: 28px; !* 增加行间距 *!
}

.ai-assistant-form-row:last-child {
    margin-bottom: 0; !* 最后一行不需要底部间距 *!
}

.ai-assistant-form-row .ck-labeled-field__label {
    font-weight: 500;
    margin-bottom: 8px;
    font-size: 14px;
    background-color: transparent;
    padding: 0 2px;
}

.ai-assistant-form-row .ck-input {
    transition: all 0.2s ease;
    border: 2px solid #e0e0e0;
    border-radius: 6px;
    padding: 12px 16px;
    min-height: 44px;
}

!* 输入框获得焦点时的样式 *!
.ai-assistant-form-row .ck-input:focus {
    border-color: #2196F3;
    box-shadow: 0 0 0 3px rgba(33,150,243,0.1);
}

!* 输入框容器的样式，确保输入框与表单边缘有足够距离 *!
.ai-assistant-form-row .ck-labeled-field {
    width: 100%;
    background-color: transparent;
}

.ai-assistant-form-footer {
    padding: 16px 24px;
    border-top: 1px solid #eee;
    background-color: #f8f8f8 !important;
}

.ai-assistant-form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    background-color: transparent;
}

!* 按钮样式 *!
.ai-assistant-save-btn,
.ai-assistant-cancel-btn {
    opacity: 1 !important;
    border-radius: 6px;
    padding: 10px 24px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.ai-assistant-save-btn {
    background: linear-gradient(135deg, #2196F3, #1976D2);
    color: white !important;
    border: none;
}

.ai-assistant-save-btn:hover {
    opacity: 0.9 !important;
    transform: translateY(-1px);
}

.ai-assistant-cancel-btn {
    background: transparent;
    border: 2px solid #e0e0e0;
    color: #666 !important;
}

.ai-assistant-cancel-btn:hover {
    border-color: #bdbdbd;
    background-color: rgba(0,0,0,0.03);
}

!* 对话框遮罩层 *!
.ck-dialog-overlay {
    animation: ck-dialog-overlay-fadein 0.2s ease;
}

@keyframes ck-dialog-overlay-fadein {
    0% { opacity: 0; }
    100% { opacity: 1; }
}

!* 覆盖可能导致透明的CKEditor类 *!
.ai-assistant-form .ck.ck-reset_all * {
    background: inherit;
}

!* 添加此规则以确保任何透明度设置被覆盖 *!
.ai-assistant-form,
.ai-assistant-form * {
    background-color: initial;
}

.ai-assistant-form {
    background-color: #ffffff !important;
}

.ai-assistant-form-header,
.ai-assistant-form-body {
    background-color: #ffffff !important;
}

.ai-assistant-form-footer {
    background-color: #f8f8f8 !important;
}

.ai-assistant-save-btn {

}

.ai-assistant-save-btn:hover {

}

.ai-assistant-cancel-btn {

}

.ai-assistant-cancel-btn:hover {

} */

.ai-from-row {
    margin-top: 20px !important;
}