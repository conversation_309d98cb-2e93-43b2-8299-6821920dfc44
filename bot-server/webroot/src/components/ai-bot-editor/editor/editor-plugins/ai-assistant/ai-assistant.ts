import {Plugin} from '@ckeditor/ckeditor5-core';
import {debounce} from 'lodash-es';
import {toWidget, viewToModelPositionOutsideModelElement, Widget} from '@ckeditor/ckeditor5-widget';
import '@ckeditor/ckeditor5-engine';
import {Text} from '@ckeditor/ckeditor5-engine';

// 添加CSS样式
import './theme/suggestion-preview.css';

import AiAssistantEditing from './ai-assistant-editing';
import AiAssistantUI from './ai-assistant-ui';

/**
 * Gitee AI API配置
 */
const GITEE_API_CONFIG = {
    endpoint: 'https://ai.gitee.com/v1/chat/completions',
    apiKey: 'IFMRT50L3XUISCC1X9SVXJOZAYRAPPVMGSACHUG2',
    package: '1910',
    model: 'Qwen2.5-72B-Instruct'
};

/**
 * 最小触发预测的文本长度
 */
const MIN_PREDICTION_TEXT_LENGTH = 5;

/**
 * AI助手主插件
 * 作为所有AI助手功能的入口点
 */
export default class AiAssistant extends Plugin {
    /**
     * 定义依赖的插件
     */
    static get requires() {
        return [AiAssistantEditing, AiAssistantUI];
    }

    /**
     * 插件名称，确保与其他地方引用的名称一致
     */
    static get pluginName() {
        return 'AiAssistant';
    }

    /**
     * 初始化插件
     */
    init() {
        console.log('AI助手已启用，提供智能预测功能');
        
        // 在这里可以添加初始化逻辑
        // 例如注册命令、设置观察者等
    }
}
