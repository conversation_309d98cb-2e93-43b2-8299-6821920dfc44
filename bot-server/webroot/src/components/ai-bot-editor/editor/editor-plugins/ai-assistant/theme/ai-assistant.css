/* AI补全建议样式 - 拼音输入效果 */
.ck-content .ai-assistant,
.ai-assistant {
    /* 更明显的样式，修复预览不显示问题 */
    background-color: rgba(240, 240, 240, 0.3) !important;
    /* 移除边框和圆角 */
    border-radius: 2px !important;
    border: none !important;
    /* 使用更醒目的灰色 */
    color: #888 !important;
    /* 内部边距 */
    padding: 0 2px !important;
    margin: 0 !important;
    /* 使用斜体，增强视觉区分度 */
    font-style: italic !important;
    /* 字体粗细稍微lighter */
    font-weight: 400 !important;
    /* 简单过渡效果 */
    transition: all 0.2s ease !important;
    /* 使用默认鼠标指针 */
    cursor: text !important;
    /* 细微的阴影效果 */
    text-shadow: 0 0 1px rgba(0,0,0,0.05) !important;
    /* 禁止选择 */
    user-select: none !important;
    /* 确保显示为内联元素 */
    display: inline !important;
    /* 确保没有文本装饰 */
    text-decoration: none !important;
    /* 字体大小与正常文本一致 */
    font-size: inherit !important;
    /* 行高与正常文本一致 */
    line-height: inherit !important;
    /* 确保透明度完全不透明 */
    opacity: 0.9 !important;
    /* 文本与周围内容自然融合 */
    vertical-align: baseline !important;
    /* 字母间距略微调整，拼音效果 */
    letter-spacing: normal !important;
    /* 增加底部边框 */
    border-bottom: 1px dotted #ccc !important;
    /* 添加动画效果 */
    animation: fadeInSuggestion 0.3s ease-in !important;
}

/* 定义淡入动画 */
@keyframes fadeInSuggestion {
    from { opacity: 0; transform: translateX(5px); }
    to { opacity: 0.9; transform: translateX(0); }
}

/* 移除之前的提示图标 */
.ck-content .ai-assistant::before,
.ai-assistant::before {
    content: '' !important;
    display: none !important;
}

/* 移除底部提示 - 改为在底部状态栏显示 */
.ck-content .ai-assistant::after,
.ai-assistant::after {
    display: none !important;
}

/* 底部提示栏样式 */
.ai-assistant-bottombar {
    position: fixed !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    background: rgba(250, 250, 250, 0.95) !important;
    border-top: 1px solid #e1e1e1 !important;
    color: #666 !important;
    font-size: 13px !important;
    padding: 6px 12px !important;
    text-align: center !important;
    z-index: 1000 !important;
    box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.05) !important;
}

/* 提示栏中的键盘快捷键样式 */
.ai-assistant-key {
    display: inline-block !important;
    background: #f0f0f0 !important;
    border: 1px solid #ddd !important;
    border-radius: 3px !important;
    padding: 0 6px !important;
    margin: 0 2px !important;
    font-family: monospace !important;
    font-size: 12px !important;
    color: #555 !important;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05) !important;
}

/* 接受建议的提示样式 */
.ck-content .ai-assistant-hint,
.ai-assistant-hint {
    position: fixed !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    background-color: rgba(250, 250, 250, 0.97) !important;
    border-top: 1px solid #e1e1e1 !important;
    color: #555 !important;
    font-size: 13px !important;
    padding: 8px 16px !important;
    text-align: center !important;
    box-shadow: 0 -1px 5px rgba(0, 0, 0, 0.08) !important;
    z-index: 1000 !important;
    pointer-events: none !important;
    opacity: 1 !important;
    font-style: normal !important;
    white-space: nowrap !important;
}

/* 建议预览样式 */
.suggestion-preview {
    /* 基础样式 */
    display: inline;
    pointer-events: all;
    user-select: none;
    -webkit-user-select: none;
    
    /* 文本样式 */
    color: #888;
    opacity: 0.75;
    font-style: italic;
    
    /* 边距和对齐 */
    margin-left: 1px;
    vertical-align: baseline;
    
    /* 交互样式 */
    cursor: pointer;
    transition: all 0.2s ease;
    
    /* 与ai-assistant.css中的相同样式合并 */
    background-color: rgba(245, 245, 245, 0.7);
    border-radius: 2px;
    padding: 0 2px;
    animation: fadeIn 0.2s ease-in;
    
    /* 确保预览文本能够正常换行 */
    max-width: 100%; /* 限制宽度，确保会触发换行 */
}

/* 确保建议预览在编辑模式下可见 */
.ck-content .suggestion-preview {
    display: inline !important;
}

/* 确保建议预览不会干扰文本流，但允许换行 */
.ck-editor__editable .suggestion-preview {
    position: relative;
    white-space: pre-wrap; /* 改为pre-wrap，保留格式但允许自动换行 */
    word-wrap: break-word; /* 确保长单词也能换行 */
    overflow-wrap: break-word; /* 现代浏览器的兼容性 */
}

/* 增加与提示相关的状态指示 - 移除下划线 */
[data-suggestion="true"] {
    /* 移除下划线 */
    border-bottom: none;
}

/* 鼠标悬停效果 */
.suggestion-preview:hover {
    color: #666;
    opacity: 0.9;
    background-color: rgba(200, 200, 200, 0.1);
}

@keyframes fadeIn {
    from { 
        opacity: 0; 
        transform: translateX(4px);
        filter: blur(2px);
    }
    to { 
        opacity: 0.9; 
        transform: translateX(0);
        filter: blur(0);
    }
}

/* AI助手基础样式 */
.ai-assistant {
    display: inline-block;
    margin: 4px 0;
    font-family: inherit;
}

/* AI助手工具栏按钮样式 */
.ck-button.ck-ai-assistant-button {
    position: relative;
}

.ck-button.ck-ai-assistant-button .ck-button__label {
    display: flex;
    align-items: center;
}

.ck-button.ck-ai-assistant-button .ck-button__label:before {
    content: "";
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: 4px;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23666"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.95-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/></svg>');
    background-size: cover;
}

/* AI助手处理中指示器 */
.ai-assistant-loading {
    display: inline-block;
    width: 16px;
    height: 16px;
    animation: ai-assistant-spinner 1.2s linear infinite;
}

@keyframes ai-assistant-spinner {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.suggestion-preview:hover:after {
    opacity: 1;
    transform: translateY(0);
} 