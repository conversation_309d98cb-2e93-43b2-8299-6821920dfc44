// placeholder/placeholdercommand.js

import {Command} from '@ckeditor/ckeditor5-core';

/**
 * AI助手命令处理程序
 */
export default class AiAssistantCommand extends Command {
    constructor(editor) {
        super(editor);
    }

    /**
     * 执行补全命令
     * @param {Object} options 命令选项
     * @param {String} options.content 预测的补全内容
     */
    execute({ content = '' } = {}) {
        const model = this.editor.model;
        const selection = model.document.selection;
        
        model.change(writer => {
            // 创建AI助手元素
            const aiAssistant = writer.createElement('aiAssistant', {
                content: content
            });
            
            // 在当前光标位置插入AI助手元素
            const position = selection.getFirstPosition();
            writer.insert(aiAssistant, position);
            
            // 将选择移至AI助手元素后
            writer.setSelection(position.getShiftedBy(1));
        });
    }

    /**
     * 检查命令是否启用
     */
    refresh() {
        const model = this.editor.model;
        const selection = model.document.selection;
        
        // 命令只在选择为折叠状态（光标）时可用
        this.isEnabled = selection.isCollapsed;
    }
}
