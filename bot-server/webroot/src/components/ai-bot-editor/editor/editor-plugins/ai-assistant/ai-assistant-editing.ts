import {Plugin} from '@ckeditor/ckeditor5-core';
import {Widget} from "@ckeditor/ckeditor5-widget";
import AiAssistantCommand from "./ai-assistant-command";
// 修改导入方式，避免找不到模块的错误
import '@ckeditor/ckeditor5-engine';
import HtmlDataProcessor from '@ckeditor/ckeditor5-engine/src/dataprocessor/htmldataprocessor';
import Document from '@ckeditor/ckeditor5-engine/src/view/document';

// 定义 AI 配置接口
interface AiAssistantConfig {
    api: string,
    key: string
    model: string;
    getHints?: (text: string) => Promise<string[]> | string[];
}

export default class AiAssistantEditing extends Plugin {

    static get requires() {
        return [Widget];
    }

    // 添加属性
    private _suggestionDebounce: ReturnType<typeof setTimeout> | null = null;
    private _currentSuggestion: string = '';
    private _showSuggestion: boolean = false;
    private _isLoadingSuggestion: boolean = false;
    private _debounceDelay: number = 150; // 固定防抖延迟，设置为较小的值
    private _lastProcessedText: string = '';
    private _suggestionPending: boolean = false;
    // 添加新属性: 标记是否为Mac系统
    private _isMac: boolean = /Mac|iPod|iPhone|iPad/.test(navigator.platform);
    // 添加请求ID和请求队列
    private _currentRequestId: number = 0;
    private _pendingRequests: Set<number> = new Set();
    private _requestTimeoutMs: number = 5000; // 请求超时时间
    // 连续补全模式
    private _continuousMode: boolean = true;
    private _maxPendingRequests: number = 3; // 最大并行请求数
    // 添加记录上次输入的字符
    private _lastInputChar: string = '';
    // 添加CapsLock状态
    private _isCapsLockOn: boolean = false;
    // 添加新的属性来跟踪输入状态
    private _isComposing: boolean = false;
    private _composingText: string = '';
    private _inputLanguage: string = '';
    private _lastInputType: string = '';
    private _compositionStartTime: number = 0;
    // 添加新属性，用于存储上一次大模型返回的预览文本
    private _lastModelSuggestion: string = '';

    private _htmlDataProcessor: HtmlDataProcessor;
    private _document: Document;

    // 默认 AI API 配置
    private _aiConfig: AiAssistantConfig = {
        api: 'https://ai.gitee.com/v1/chat/completions',
        key: '6I8WAFWMAGNJBCDMEJ8FQMCCJVQR8BDNISC5IM1C',
        model: 'GLM-4-9B-Chat'
    };

    init() {
        // 初始化插件配置
        this._initConfig();

        this._defineSchema();
        this._defineConverters();
        // 注册命令
        this.editor.commands.add('aiAssistant', new AiAssistantCommand(this.editor));

        this._htmlDataProcessor = new HtmlDataProcessor(this.editor.data.viewDocument);

        // 设置文本补全功能
        this._setupTextCompletion();
    }

    // 初始化插件配置
    _initConfig() {
        // 从编辑器配置中获取 AI 助手配置
        const editorConfig: any = this.editor.config.get('aiAssistant');

        if (editorConfig) {
            console.log('从编辑器配置中加载 AI 助手配置:', editorConfig);

            // 合并配置，保留默认值
            this._aiConfig = {
                ...this._aiConfig,
                ...editorConfig
            };

            // 如果有配置连续模式和防抖延迟
            if (editorConfig.continuousMode !== undefined) {
                this._continuousMode = Boolean(editorConfig.continuousMode);
            }

            if (editorConfig.debounceDelay !== undefined && !isNaN(editorConfig.debounceDelay)) {
                this._debounceDelay = parseInt(editorConfig.debounceDelay);
            }

            if (editorConfig.maxPendingRequests !== undefined && !isNaN(editorConfig.maxPendingRequests)) {
                this._maxPendingRequests = parseInt(editorConfig.maxPendingRequests);
            }

            console.log('合并后的 AI 配置:', this._aiConfig);
            console.log('输入模式配置:', {
                continuousMode: this._continuousMode,
                debounceDelay: this._debounceDelay,
                maxPendingRequests: this._maxPendingRequests
            });
        } else {
            console.log('使用默认 AI 配置');
        }

        // 如果没有提供 getHints 函数，创建默认实现
        if (!this._aiConfig.getHints) {
            // this._aiConfig.getHints = this._defaultGetHints.bind(this);
        }
    }

    // 默认的获取提示函数实现
    async _defaultGetHints(text: string): Promise<string[]> {
        // 尝试从 AI API 获取提示
        const hint = await this._getAiSuggestion(text);
        return hint ? [hint] : [];
    }

    // 从 AI API 获取建议
    async _getAiSuggestion(text: string): Promise<string> {
        console.log('正在获取AI建议，输入文本:', text);
        console.log('当前保存的上一次模型建议:', this._lastModelSuggestion);

        // 如果文本太短，避免无谓的 API 调用
        if (!text.trim() || text.length < 5) {
            console.log('文本过短，不调用 API');
            return '';
        }

        // 设置加载状态
        this._isLoadingSuggestion = true;

        try {
            let content = '';
            if (this._aiConfig.getHints) {
                let hints = await this._aiConfig.getHints(text);
                content = hints.join(`\n\r`)
                console.log("content:", content)
            }

            // 构建系统提示词，包含上一次模型返回的预览内容
            let systemPrompt = `
你是一个有用的智能内容补全助手,我会给你内容补全的上下文,根据给出的上下文,结合用户的当前输入内容提供文本输入的补全建议.
请直接提供补全内容,以下是不能出现的内容
    1.不要有任何前导语如"这是我的建议"，
    2.不要有任何的 html 标签，或者markdown格式的数据.
    3.补全内容不能以任何表单符号开头

上一次的补全建议是: ${this._lastModelSuggestion}
如果用户正在按照上一次的建议继续输入，请生成更有意义的后续内容。如果用户的输入方向发生变化，可以适当调整建议方向，但要确保过渡自然。
`;
            // 使用存储的上一次模型返回结果构建提示词
            if (this._lastModelSuggestion) {
                systemPrompt += `\n\n上一次的补全建议是："${this._lastModelSuggestion}"。请参考这个内容，保持语义连贯性，生成新的补全建议。新的建议应该与上一次的建议在语义和风格上保持一致。`;
            }

            console.log('系统提示词:', systemPrompt);

            /*     // 构建系统提示词，包含上一次模型返回的预览内容
                 let systemPrompt = '你是一个助手，请为用户提供文本输入的补全建议。请直接提供补全内容,以下是不能出现的内容 不要有任何前导语如"这是我的建议"，不要有任何的 html 标签，或者markdown格式的数据。请简洁直接地给出后续文本补全。';

                 // 使用存储的上一次模型返回结果构建提示词
                 if (this._lastModelSuggestion) {
                     systemPrompt += `\n\n上一次的补全建议是："${this._lastModelSuggestion}"。请参考这个内容，保持语义连贯性，生成新的补全建议。新的建议应该与上一次的建议在语义和风格上保持一致。`;

                     // 添加更多上下文信息
                     systemPrompt += `\n如果用户正在按照上一次的建议继续输入，请生成更有意义的后续内容。如果用户的输入方向发生变化，可以适当调整建议方向，但要确保过渡自然。`;
                 }

                 console.log('系统提示词:', systemPrompt);
                 console.log('上一次模型建议:', this._lastModelSuggestion);*/

            // 调用 AI API
            const response = await fetch(this._aiConfig.api, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this._aiConfig.key}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    model: this._aiConfig.model,
                    stream: false,
                    max_tokens: 50,
                    temperature: 0.7,
                    top_p: 0.7,
                    top_k: 50,
                    frequency_penalty: 1,
                    messages: [
                        {
                            role: 'system',
                            content: systemPrompt
                        },
                        {
                            role: 'user',
                            content: `请为以下文本提供后续的补全建议(直接给出补全内容，不要有任何前缀,请忽略任何 图片类型的 markdown数据)：${text}`
                        }
                    ]
                })
            });

            const data = await response.json();

            if (data.choices && data.choices.length > 0) {
                // 清理 API 返回的结果，移除不必要的前缀和格式
                let result = data.choices[0].message.content;
                result = result.replace(/^[""「」'']*/, '').trim();
                result = result.replace(/^这是我的建议：/, '');
                result = result.replace(/^建议：/, '');
                result = result.replace(/^补全内容：/, '');

                console.log('AI返回的建议:', result);
                return result;
            }

            console.log('API未返回有效建议');
            return '';
        } catch (error) {
            console.error('获取AI建议失败:', error);
            return '';
        } finally {
            this._isLoadingSuggestion = false;
        }
    }

    // 根据输入文本生成建议
    async _generateSuggestion(text: string): Promise<string> {
        try {
            // 首先尝试使用配置的 getHints 函数获取提示 todo 优化附加到提示词
            // const hints = await this._aiConfig.getHints(text);
            return await this._getAiSuggestion(text);
            // 如果没有 AI 提示，返回默认提示
            // return this._getDefaultSuggestion(text);
        } catch (error) {
            console.error('获取提示时出错:', error);
            return this._getDefaultSuggestion(text);
        }
    }

    // 设置文本补全
    _setupTextCompletion() {
        let isSetup = false;
        let isTyping = false;

        // 监听输入法事件
        this.editor.editing.view.document.on('compositionstart', (evt, data) => {
            console.log('输入法输入开始');
            this._isComposing = true;
            this._composingText = '';
            this._compositionStartTime = Date.now();

            // 尝试获取输入语言
            try {
                const inputLang = (window.navigator as any).userLanguage ||
                    window.navigator.language ||
                    document.documentElement.lang;
                this._inputLanguage = inputLang;
                console.log('当前输入语言:', this._inputLanguage);
            } catch (error) {
                console.error('获取输入语言失败:', error);
            }
        });

        this.editor.editing.view.document.on('compositionupdate', (evt, data) => {
            this._composingText = data.data;
            console.log('输入法组合更新:', this._composingText);

            // 如果有预览文本，检查是否需要临时隐藏
            if (this._showSuggestion && this._currentSuggestion) {
                // 检查组合文本是否可能匹配预览
                const potentialMatch = this._checkCompositionMatch(this._composingText);
                if (!potentialMatch) {
                    // 临时隐藏预览，但不清除状态
                    this._temporarilyHideSuggestion();
                }
            }
        });

        this.editor.editing.view.document.on('compositionend', (evt, data) => {
            console.log('输入法输入结束, 最终文本:', data.data);
            const compositionDuration = Date.now() - this._compositionStartTime;

            // 重置输入法状态
            this._isComposing = false;
            this._composingText = '';

            // 处理最终确认的文本
            this._handleCompositionEnd(data.data, compositionDuration);
        });

        // 监听输入事件
        this.editor.editing.view.document.on('beforeinput', (evt, data) => {
            this._lastInputType = data.inputType;
            console.log('输入类型:', this._lastInputType);
        });

        // 监听键盘输入事件
        this.editor.editing.view.document.on('keydown', (evt, data) => {
            // 如果正在使用输入法，不处理单字符输入
            if (this._isComposing) {
                return;
            }

            // 更新CapsLock状态
            if (data.domEvent && data.domEvent.getModifierState) {
                this._isCapsLockOn = data.domEvent.getModifierState('CapsLock');
                console.log('CapsLock状态:', this._isCapsLockOn ? '开启' : '关闭');
            }

            // 检查是否是实际的字符输入
            const isCharacterInput = !data.ctrlKey && !data.altKey && !data.metaKey
                && data.keyCode !== 9  // Tab
                && data.keyCode !== 8  // Backspace
                && data.keyCode !== 46 // Delete
                && data.keyCode !== 37 // Left arrow
                && data.keyCode !== 38 // Up arrow
                && data.keyCode !== 39 // Right arrow
                && data.keyCode !== 40; // Down arrow

            if (isCharacterInput) {
                isTyping = true;

                // 获取输入的字符 - 使用改进的方法获取准确的字符
                let char = '';
                if (data.domEvent) {
                    // 方法1: 使用keyCode或which获取ASCII码，然后转换为字符
                    const keyCode = data.domEvent.keyCode || data.domEvent.which;

                    // 检查是否为可打印字符的键码范围
                    if (keyCode >= 32 && keyCode <= 126) { // ASCII可打印字符范围
                        // 基础字符
                        let baseChar = String.fromCharCode(keyCode);

                        // 处理大小写逻辑
                        const isUpperCase = (data.shiftKey && !this._isCapsLockOn) ||
                            (!data.shiftKey && this._isCapsLockOn);

                        // 如果是字母(a-z, A-Z)
                        if ((keyCode >= 65 && keyCode <= 90) || (keyCode >= 97 && keyCode <= 122)) {
                            char = isUpperCase ? baseChar.toUpperCase() : baseChar.toLowerCase();
                        } else {
                            // 非字母字符，如果按下Shift，使用特殊映射
                            if (data.shiftKey) {
                                // 映射特殊符号 (数字键的特殊符号等)
                                const shiftMap: { [key: string]: string } = {
                                    '1': '!', '2': '@', '3': '#', '4': '$', '5': '%',
                                    '6': '^', '7': '&', '8': '*', '9': '(', '0': ')',
                                    '`': '~', '-': '_', '=': '+', '[': '{', ']': '}',
                                    '\\': '|', ';': ':', '\'': '"', ',': '<', '.': '>',
                                    '/': '?'
                                };

                                char = shiftMap[baseChar] || baseChar;
                            } else {
                                char = baseChar;
                            }
                        }
                    }
                    // 方法2: 尝试使用key属性 (现代浏览器支持)
                    else if (data.domEvent.key && data.domEvent.key.length === 1) {
                        char = data.domEvent.key;
                    }

                    console.log('输入的字符:', char, 'CapsLock:', this._isCapsLockOn, 'Shift:', data.shiftKey, 'KeyCode:', keyCode);
                }

                if (char) {
                    this._lastInputChar = char;

                    // 检查输入的字符是否与当前预览的开头匹配
                    if (this._showSuggestion && this._currentSuggestion) {
                        const suggestionFirstChar = this._currentSuggestion.charAt(0);
                        const contentMatches = char.toLowerCase() === suggestionFirstChar.toLowerCase();

                        if (contentMatches) {
                            console.log('检测到字符匹配，更新预览文本');
                            // 不阻止默认行为，让编辑器正常处理输入
                            // evt.stop();
                            // data.preventDefault = true;

                            // 在下一个事件循环中更新预览，确保编辑器已经处理了输入
                            setTimeout(() => {
                                this._updateSuggestionWhenTyping(char);
                            }, 0);
                        }
                    }
                }

                // 设置延时，在输入停止后重置状态
                setTimeout(() => {
                    isTyping = false;
                    // 输入停止后，触发一次建议请求（如果连续模式关闭）
                    if (!this._continuousMode) {
                        this._processTextCompletion();
                    }
                }, 500);
            }

            // 增加快捷键组合监听
            // Enter键 (keyCode 13) 结合修饰键
            if (data.keyCode === 13) {
                // 检查Mac上的Cmd+Enter或Windows上的Alt+Enter
                if ((this._isMac && data.metaKey) || (!this._isMac && data.altKey)) {
                    console.log('检测到快捷键组合: ' + (this._isMac ? 'Cmd+Enter' : 'Alt+Enter'));
                    evt.stop();
                    data.preventDefault = true;

                    // 触发手动生成建议
                    this._triggerManualSuggestion();
                    return false;
                }
            }

            // Tab键处理
            if (data.keyCode === 9) {
                if (this._showSuggestion && this._currentSuggestion) {
                    evt.stop();
                    data.preventDefault = true;
                    this._acceptSuggestion();
                    return false;
                }
            }

            // 清除建议（适用于Backspace和Delete键）
            if (data.keyCode === 8 || data.keyCode === 46) {
                this._clearSuggestion();
                this._lastProcessedText = '';
            }
        });

        // 监听编辑器变化
        this.editor.model.document.on('change:data', () => {
            if (!isSetup) {
                isSetup = true;
                console.log('初始化文本补全监听');
            }

            // 在连续模式下，只要有输入就触发建议
            if ((isTyping || !this._continuousMode) && !this._isComposing) {
                if (this._suggestionDebounce) {
                    clearTimeout(this._suggestionDebounce);
                }

                // 使用固定防抖延迟
                this._suggestionDebounce = setTimeout(() => {
                    this._processTextCompletion();
                }, this._debounceDelay);
            }
        });

        // 阻止点击预览文本导致的选区问题
        this.editor.editing.view.document.on('mousedown', (evt, data) => {
            // 检查点击的是否是建议元素
            if (data.target && data.target.hasClass('suggestion-preview')) {
                console.log('点击了建议预览');
                // 阻止默认行为
                evt.stop();
                // 触发接受建议
                this._acceptSuggestion();
                return false;
            }
        });
    }

    // 检查编辑器是否有焦点
    _isEditorFocused(): boolean {
        return this.editor.editing.view.document.isFocused;
    }

    // 确保编辑器重新获得焦点
    _refocusEditor() {
        this.editor.editing.view.focus();
    }

    // 处理文本补全
    async _processTextCompletion() {
        console.log('开始处理文本补全...');

        // 获取当前文本（已排除输入法组合内容）
        let text = this._getCurrentText();
        text = this._handleMdData(text)
        console.log('获取到的处理文本:', text, '当前是否在输入法输入:', this._isComposing);

        // 如果正在输入法输入，记录当前的组合文本位置
        if (this._isComposing) {
            console.log('正在输入法输入中，组合文本:', this._composingText);
        }

        // 如果文本为空或仅包含空白字符，不显示任何建议
        if (!text || text.trim() === '') {
            console.log('文本为空，不显示建议');
            // 确保清除之前的建议
            this._clearSuggestion();
            return;
        }

        // 文本太短（但确保"func"这类短关键词仍能触发建议）
        if (text.length < 3 && !text.includes('func') && !text.includes('if') && !text.includes('for')) {
            console.log('文本太短且不包含关键词，不生成建议');
            // 确保清除之前的建议
            this._clearSuggestion();
            return;
        }

        // 检查文本是否与上次处理的相同
        if (text === this._lastProcessedText && this._showSuggestion) {
            console.log('文本未发生变化，跳过处理');
            return;
        }

        // 限制最大并行请求数
        if (this._pendingRequests.size >= this._maxPendingRequests) {
            const oldestRequestId = Math.min(...Array.from(this._pendingRequests));
            console.log(`达到最大并行请求数 ${this._maxPendingRequests}，取消最旧的请求 ${oldestRequestId}`);
            this._pendingRequests.delete(oldestRequestId);
        }

        // 更新最后处理的文本
        this._lastProcessedText = text;

        // 创建新的请求ID
        const requestId = ++this._currentRequestId;
        this._pendingRequests.add(requestId);

        // 设置请求超时
        const timeoutId = setTimeout(() => {
            if (this._pendingRequests.has(requestId)) {
                console.log(`请求 ${requestId} 超时，从队列移除`);
                this._pendingRequests.delete(requestId);
            }
        }, this._requestTimeoutMs);

        try {
            // 增量提示请求开始时显示加载状态
            if (this._pendingRequests.size === 1) {
                this._isLoadingSuggestion = true;
                // 可以在这里添加UI指示器显示加载中
            }

            // 生成建议
            const suggestion = await this._generateSuggestion(text);

            // 检查这个请求是否已被取消或超时
            if (!this._pendingRequests.has(requestId)) {
                console.log(`请求 ${requestId} 已被取消或超时，不处理结果`);
                clearTimeout(timeoutId);
                return;
            }

            // 从请求队列中移除当前请求
            this._pendingRequests.delete(requestId);
            clearTimeout(timeoutId);

            // 如果没有更多待处理请求，重置加载状态
            if (this._pendingRequests.size === 0) {
                this._isLoadingSuggestion = false;
                // 可以在这里隐藏UI加载指示器
            }

            if (suggestion) {
                console.log(`请求 ${requestId} 生成的建议:`, suggestion);

                // 只有当这是最新请求或没有更新请求时才显示
                if (requestId === this._currentRequestId ||
                    (this._pendingRequests.size === 0 ||
                        Math.max(...Array.from(this._pendingRequests)) < requestId)) {
                    this._currentSuggestion = suggestion;
                    this._showSuggestion = true;

                    // 显示建议
                    this._displaySuggestion(suggestion);
                } else {
                    console.log(`请求 ${requestId} 的建议已过时，有更新的请求在处理`);
                }
            }
        } catch (error) {
            console.error(`请求 ${requestId} 生成或显示建议出错:`, error);
            // 清理请求
            if (this._pendingRequests.has(requestId)) {
                this._pendingRequests.delete(requestId);
                clearTimeout(timeoutId);

                // 如果没有更多待处理请求，重置加载状态
                if (this._pendingRequests.size === 0) {
                    this._isLoadingSuggestion = false;
                }
            }
        }
    }

    // 获取当前文本 - 完全重写文本获取方法，使用多种方式尝试获取文本
    _getCurrentText() {
        let textContent = '';
        let composingRange = null;

        try {
            // 获取编辑器的根视图元素
            const viewRoot = this.editor.editing.view.getDomRoot();
            if (viewRoot) {
                // 查找所有带有 .composition 类的元素（输入法组合区域）
                const compositionElements = viewRoot.querySelectorAll('.composition');
                if (compositionElements.length > 0) {
                    composingRange = {
                        start: 0,
                        end: 0,
                        text: ''
                    };

                    // 获取组合文本的位置和内容
                    const composingElement = compositionElements[0];
                    const range = document.createRange();
                    range.selectNode(composingElement);
                    composingRange.text = composingElement.textContent || '';

                    // 计算组合文本的开始和结束位置
                    const treeWalker = document.createTreeWalker(
                        viewRoot,
                        NodeFilter.SHOW_TEXT,
                        null
                    );

                    let currentPos = 0;
                    let node;

                    while ((node = treeWalker.nextNode())) {
                        if (node === composingElement || composingElement.contains(node)) {
                            composingRange.start = currentPos;
                            composingRange.end = currentPos + (composingRange.text.length);
                            break;
                        }
                        currentPos += (node.textContent || '').length;
                    }
                }
            }

            // 方法1：使用编辑器getData方法直接获取内容
            const data = this.editor.getData();


            if (data) {
                // 移除HTML标签获取纯文本
                textContent = data.replace(/<[^>]*>/g, '');

                // 如果存在输入法组合区域，从文本中移除
                if (composingRange && textContent.length >= composingRange.end) {
                    textContent = textContent.substring(0, composingRange.start) +
                        textContent.substring(composingRange.end);
                }

                console.log('当前输入文本:', textContent);
            }
        } catch (error) {
            console.error('获取文本失败:', error);
        }

        // 如果方法1失败，尝试方法2
        if (!textContent.trim()) {
            try {
                const editorElement = this.editor.editing.view.getDomRoot();
                if (editorElement) {
                    // 创建文本内容的副本
                    const textNodes = [];
                    const treeWalker = document.createTreeWalker(
                        editorElement,
                        NodeFilter.SHOW_TEXT,
                        null
                    );

                    let node;
                    while ((node = treeWalker.nextNode())) {
                        // 跳过输入法组合区域的文本
                        if (this._isComposing && node.parentElement?.classList.contains('composition')) {
                            continue;
                        }
                        textNodes.push(node.textContent);
                    }

                    textContent = textNodes.join('');
                    console.log('当前输入文本:', textContent);
                }
            } catch (error) {
                console.error('获取文本失败:', error);
            }
        }

        // 如果方法2也失败，尝试方法3
        if (!textContent.trim()) {
            try {
                const model = this.editor.model;
                const root = model.document.getRoot();

                if (root) {
                    const textParts = [];
                    for (const item of model.createRangeIn(root).getItems()) {
                        if (item.is('$text')) {
                            // 检查是否在输入法组合区域
                            const parent = item.parent;
                            if (parent && parent.is('element')) {
                                const viewElement = this.editor.editing.mapper.toViewElement(parent);
                                if (viewElement && !viewElement.hasClass('composition')) {
                                    textParts.push(item.data);
                                }
                            } else {
                                // 如果没有父元素或不是元素类型，直接添加文本
                                textParts.push(item.data);
                            }
                        }
                    }
                    textContent = textParts.join('');
                    console.log('当前输入文本:', textContent);
                }
            } catch (error) {
                console.error('获取文本失败:', error);
            }
        }

        // 最后的安全检查：确保不包含正在输入的组合文本
        if (this._isComposing && this._composingText) {
            const composingIndex = textContent.lastIndexOf(this._composingText);
            if (composingIndex !== -1) {
                textContent = textContent.substring(0, composingIndex) +
                    textContent.substring(composingIndex + this._composingText.length);
                console.log('移除组合文本后的内容:', textContent);
            }
        }

        return textContent;
    }

    // 显示建议 - 借鉴WriterEditor实现
    _displaySuggestion(suggestion: string) {
        // 检查建议是否有效 
        if (!suggestion || suggestion.trim() === '') {
            console.log('建议为空，不显示');
            return;
        }

        try {
            console.log('尝试显示建议:', suggestion);

            // 使用编辑器API显示建议
            this.editor.model.change(writer => {
                // 先清除已有建议，避免多个建议同时显示
                this._clearSuggestionElements(writer);

                // 保存当前光标位置 - 使用clone避免引用问题
                const selection = this.editor.model.document.selection;
                const position = selection.getFirstPosition();
                if (!position) {
                    console.error('无法获取光标位置，无法插入建议');
                    return;
                }

                // 记录原始光标位置
                const originalPosition = position.clone();

                try {
                    // 创建建议元素
                    const suggestionElement = writer.createElement('suggestionPreview');

                    // 设置属性
                    writer.setAttribute('suggestion', suggestion, suggestionElement);

                    // 添加动画样式标记
                    writer.setAttribute('animate', 'true', suggestionElement);

                    // 插入到当前位置
                    writer.insert(suggestionElement, originalPosition);
                    console.log('建议元素已插入到模型');

                    // 明确将光标重置到原始位置 - 确保位置不变
                    writer.setSelection(originalPosition);

                    // 标记建议状态
                    this._showSuggestion = true;
                    this._currentSuggestion = suggestion;
                } catch (error) {
                    console.error('创建或插入建议元素失败:', error);
                }
            });
        } catch (error) {
            console.error('显示建议时出错:', error);
        }
    }

    // 仅清除建议元素，但不重置状态标志
    _clearSuggestionElements(writer?: any) {
        try {
            const doRemove = (innerWriter: any) => {
                const root = this.editor.model.document.getRoot();
                if (!root) return;

                // 安全地收集所有建议元素
                const elementsToRemove = [];
                const items = Array.from(this.editor.model.createRangeIn(root).getItems());

                for (const item of items) {
                    if (item.is && item.is('element', 'suggestionPreview')) {
                        elementsToRemove.push(item);
                    }
                }

                // 移除建议元素
                for (const element of elementsToRemove) {
                    if (element.parent) {
                        innerWriter.remove(element);
                    }
                }

                if (elementsToRemove.length > 0) {
                    console.log(`清除了 ${elementsToRemove.length} 个建议元素`);
                }
            };

            // 如果提供了writer，直接使用，否则创建新的事务
            if (writer) {
                doRemove(writer);
            } else {
                this.editor.model.change(newWriter => {
                    doRemove(newWriter);
                });
            }
        } catch (error) {
            console.error('清除建议元素失败:', error);
        }
    }

    // 清除建议
    _clearSuggestion() {
        // 如果没有活跃建议，直接返回
        if (!this._showSuggestion && !this._currentSuggestion) {
            return;
        }

        try {
            // 收集并移除建议元素
            this.editor.model.change(writer => {
                this._clearSuggestionElements(writer);
            });

            // 重置建议状态，但保留上一次模型返回的结果
            this._showSuggestion = false;
            this._currentSuggestion = '';
            // 注意：不清除 this._lastModelSuggestion

        } catch (error) {
            console.error('清除建议元素失败:', error);
        }
    }

    // 提取默认建议逻辑到单独方法
    _getDefaultSuggestion(text: string): string {
        console.log('生成默认建议，输入文本:', text);

        // 更宽松的关键词检测 - 不仅检查最后一个词，也检查整个文本
        const containsKeyword = (keyword: string) => {
            return text.includes(keyword);
        };

        // 编程相关建议
        if (containsKeyword('func') || containsKeyword('函数')) {
            console.log('检测到关键词: func/函数');
            return '() { // 函数实现 }';
        }

        if (containsKeyword('if') || containsKeyword('如果')) {
            console.log('检测到关键词: if/如果');
            return ' (条件) { // 条件处理 }';
        }

        if (containsKeyword('for') || containsKeyword('循环')) {
            console.log('检测到关键词: for/循环');
            return ' (let i = 0; i < array.length; i++) { }';
        }

        // 获取最后一个词，用于更精确的建议
        const words = text.trim().split(/\s+/);
        const lastWord = words[words.length - 1] || '';
        console.log('最后一个词:', lastWord);

        // 中文建议
        if (/[\u4e00-\u9fa5]/.test(text)) {
            console.log('检测到中文文本');
            const suggestions = [
                '是很重要的一部分',
                '需要进一步完善',
                '的实现方法有很多种',
                '可以这样处理：',
                '是一个有趣的话题'
            ];
            return suggestions[Math.floor(Math.random() * suggestions.length)];
        }

        // 默认建议
        console.log('使用默认建议');
        const defaultSuggestions = [
            '继续写下去...',
            '可以添加更多细节',
            '是一个好的开始',
            '需要考虑各种情况'
        ];

        return defaultSuggestions[Math.floor(Math.random() * defaultSuggestions.length)];
    }

    _defineSchema() {
        const schema = this.editor.model.schema;

        // 为建议元素定义模式
        schema.register('suggestionPreview', {
            allowWhere: '$text',
            isInline: true,
            isObject: true,
            allowAttributes: ['suggestion']
        });
    }

    _defineConverters() {
        const conversion = this.editor.conversion;

        // 上升转换
        conversion.for('upcast').elementToElement({
            view: {
                name: 'span',
                classes: ['suggestion-preview']
            },
            model: (viewElement, {writer}) => {
                return writer.createElement('suggestionPreview');
            }
        });

        // 下降转换 - 数据
        conversion.for('dataDowncast').elementToElement({
            model: 'suggestionPreview',
            view: (modelElement, {writer}) => {
                const element = writer.createContainerElement('span', {
                    class: 'suggestion-preview'
                });

                return element;
            }
        });

        // 下降转换 - 编辑
        conversion.for('editingDowncast').elementToElement({
            model: 'suggestionPreview',
            view: (modelElement, {writer}) => {
                // 创建容器元素
                const container = writer.createContainerElement('span', {
                    class: 'suggestion-preview',
                    // 添加特性使元素不可选择和不会干扰光标
                    'data-suggestion': 'true',
                    'contenteditable': 'false'
                });

                // 获取建议文本
                const suggestion = modelElement.getAttribute('suggestion');

                // 检查是否应该添加动画效果
                const shouldAnimate = modelElement.getAttribute('animate') === 'true';
                if (shouldAnimate) {
                    // 添加动画类
                    writer.addClass('suggestion-animate', container);
                }

                // 添加文本
                if (suggestion && typeof suggestion === 'string') {
                    const text = writer.createText(suggestion);
                    writer.insert(writer.createPositionAt(container, 0), text);
                }

                // 设置属性防止选区问题
                writer.setCustomProperty('shouldNotSelectSuggestion', true, container);

                return container;
            }
        });
    }

    // 手动触发建议生成的方法
    async _triggerManualSuggestion() {
        console.log('手动触发建议生成...');

        // 清除任何现有建议
        this._clearSuggestion();

        // 清除所有待处理的请求
        this._clearPendingRequests();

        // 重置标志
        this._lastProcessedText = '';

        // 强制处理文本补全，忽略防抖和其他限制条件
        try {
            // 获取当前文本
            const text = this._getCurrentText();
            console.log('手动触发，获取到的文本:', text);

            // 如果文本为空，不触发
            if (!text || text.trim() === '') {
                console.log('文本为空，不生成建议');
                return;
            }

            // 创建新的请求ID
            const requestId = ++this._currentRequestId;
            this._pendingRequests.add(requestId);

            // 直接生成建议并显示
            const suggestion = await this._generateSuggestion(text);

            // 检查这个请求是否已被取消
            if (!this._pendingRequests.has(requestId)) {
                console.log(`请求 ${requestId} 已被取消，不处理结果`);
                return;
            }

            // 从请求队列中移除当前请求
            this._pendingRequests.delete(requestId);

            if (suggestion) {
                console.log('生成的建议:', suggestion);
                this._currentSuggestion = suggestion;
                this._showSuggestion = true;

                // 显示建议
                this._displaySuggestion(suggestion);
            } else {
                console.log('未能生成建议');
            }
        } catch (error) {
            console.error('手动触发建议生成出错:', error);
        }
    }

    // 清除所有待处理的请求
    _clearPendingRequests() {
        console.log(`清除 ${this._pendingRequests.size} 个待处理请求`);
        this._pendingRequests.clear();
    }

    // 接受建议
    _acceptSuggestion() {
        console.log('执行接受建议');

        // 检查是否有可接受的建议
        if (!this._showSuggestion || !this._currentSuggestion) {
            console.log('没有可接受的建议');
            return;
        }

        // 保存建议文本，确保在状态变更前获取
        const suggestionText = this._currentSuggestion;
        console.log('接受的建议内容:', suggestionText);

        try {
            // 在清除状态之前，先更新 lastModelSuggestion
            this._lastModelSuggestion = suggestionText;
            console.log('更新上一次模型建议为:', this._lastModelSuggestion);

            // 先清除显示的建议元素
            this._clearSuggestion();

            // 清除所有待处理的请求
            this._clearPendingRequests();

            // 获取当前选区位置
            const position = this.editor.model.document.selection.getFirstPosition();
            if (!position) {
                console.error('无法获取光标位置');
                return;
            }

            // 将位置克隆，确保不会被引用修改
            const insertPosition = position.clone();

            // 执行文本插入，使用单一事务
            this.editor.model.change(writer => {
                // 执行文本插入
                writer.insertText(suggestionText, insertPosition);
                console.log('成功插入建议文本');

                // 计算新位置（文本之后）
                const newPosition = insertPosition.getShiftedBy(suggestionText.length);

                // 将光标位置设置到插入文本的末尾
                writer.setSelection(newPosition);
                setTimeout(() => this.editor.editing.view.focus(), 100);
            });

            // 重置所有状态标志
            this._showSuggestion = false;
            this._currentSuggestion = '';
            this._lastProcessedText = '';

            console.log('建议接受完成');

            // 确保编辑器重新获得焦点
            setTimeout(() => this.editor.editing.view.focus(), 100);

        } catch (error) {
            console.error('接受建议执行失败:', error);
            // 即使失败也重置状态，但保留 lastModelSuggestion
            this._showSuggestion = false;
            this._currentSuggestion = '';
            this._lastProcessedText = '';
        }
    }

    // 处理用户输入与预览文本匹配的逻辑
    _updateSuggestionWhenTyping(char: string): boolean {
        // 如果正在输入法输入中，不处理
        if (this._isComposing) {
            return false;
        }

        // 如果没有显示建议或建议为空，不处理
        if (!this._showSuggestion || !this._currentSuggestion) {
            return false;
        }

        try {
            // 检查是否是单个字符输入
            const isSingleChar = char.length === 1;

            // 单字符处理逻辑
            if (isSingleChar) {
                // 获取预览文本的第一个字符
                const suggestionFirstChar = this._currentSuggestion.charAt(0);

                // 检查匹配条件（不区分大小写的内容匹配）
                const contentMatches = char.toLowerCase() === suggestionFirstChar.toLowerCase();

                if (contentMatches) {
                    console.log(`输入的字符 '${char}' 与预览文本的开头匹配 '${suggestionFirstChar}'（内容匹配）`);

                    // 从预览文本中移除匹配的字符
                    const newSuggestion = this._currentSuggestion.substring(1);

                    // 如果移除后没有内容了，完全清除建议并触发新请求
                    if (!newSuggestion) {
                        console.log('预览文本被完全消费，清除建议并触发新请求');
                        this._clearSuggestion();

                        // 延迟请求新建议
                        setTimeout(() => {
                            this._processTextCompletion();
                        }, this._debounceDelay);

                        return true;
                    }

                    // 更新当前建议
                    this._currentSuggestion = newSuggestion;

                    // 重新显示更新后的预览
                    this.editor.model.change(writer => {
                        // 清除已有建议元素
                        this._clearSuggestionElements(writer);

                        // 获取当前光标位置
                        const selection = this.editor.model.document.selection;
                        const position = selection.getFirstPosition();
                        if (!position) {
                            console.error('无法获取光标位置');
                            return;
                        }

                        // 在当前位置创建新的建议元素
                        if (newSuggestion) {
                            const suggestionElement = writer.createElement('suggestionPreview');
                            writer.setAttribute('suggestion', newSuggestion, suggestionElement);
                            writer.setAttribute('animate', 'true', suggestionElement);
                            writer.insert(suggestionElement, position);

                            // 保持光标在原位置
                            writer.setSelection(position);
                        }
                    });

                    // 如果剩余预览文本太短，触发新请求
                    if (newSuggestion.length < 5) {
                        setTimeout(() => {
                            console.log('预览文本变短，触发新请求');
                            this._processTextCompletion();
                        }, this._debounceDelay * 2); // 使用更长的延迟避免频繁请求
                    }

                    return true;
                }
            }
            // 多字符处理逻辑 - 例如中文输入法确认时
            else if (char.length > 1) {
                console.log(`处理多字符输入: '${char}'`);

                // 检查预览文本是否以这些输入字符开头（不区分大小写）
                let matchLength = 0;
                for (let i = 0; i < Math.min(char.length, this._currentSuggestion.length); i++) {
                    if (char[i].toLowerCase() === this._currentSuggestion[i].toLowerCase()) {
                        matchLength++;
                    } else {
                        break;
                    }
                }

                // 如果有匹配部分
                if (matchLength > 0) {
                    console.log(`多字符输入中有 ${matchLength} 个字符与预览匹配（不区分大小写）`);

                    // 获取匹配的部分和不匹配的部分
                    const matchedPart = char.substring(0, matchLength);
                    const unmatchedPart = char.substring(matchLength);
                    const newSuggestion = this._currentSuggestion.substring(matchLength);

                    // 如果匹配后没有剩余预览，清除预览并触发新请求
                    if (!newSuggestion) {
                        console.log('预览文本完全匹配，清除建议并触发新请求');
                        this._clearSuggestion();

                        // 延迟请求新建议
                        setTimeout(() => {
                            this._processTextCompletion();
                        }, this._debounceDelay);

                        return true;
                    }

                    // 更新编辑器内容
                    this.editor.model.change(writer => {
                        // 清除已有建议元素
                        this._clearSuggestionElements(writer);

                        // 获取当前光标位置
                        const selection = this.editor.model.document.selection;
                        const position = selection.getFirstPosition();
                        if (!position) {
                            console.error('无法获取光标位置');
                            return;
                        }

                        // 如果有新的建议内容，则显示
                        if (newSuggestion) {
                            // 更新当前建议
                            this._currentSuggestion = newSuggestion;

                            // 创建新的建议元素
                            const suggestionElement = writer.createElement('suggestionPreview');
                            writer.setAttribute('suggestion', newSuggestion, suggestionElement);
                            writer.setAttribute('animate', 'true', suggestionElement);
                            writer.insert(suggestionElement, position);

                            // 保持光标在原位置
                            writer.setSelection(position);
                        } else {
                            // 如果没有新的建议内容，重置状态
                            this._showSuggestion = false;
                            this._currentSuggestion = '';
                        }
                    });

                    return true;
                }
            }

            // 如果输入的字符与预览不匹配，清除当前预览
            console.log(`输入的字符 '${char}' 与预览文本不匹配，清除预览`);
            this._clearSuggestion();

            // 触发新的请求
            setTimeout(() => {
                console.log('输入与预览不匹配，触发新请求');
                this._processTextCompletion();
            }, this._debounceDelay);
        } catch (error) {
            console.error('处理输入与预览匹配出错:', error);
            this._clearSuggestion();
        }

        return false;
    }

    // 添加新方法：检查组合文本是否可能匹配预览
    private _checkCompositionMatch(composingText: string): boolean {
        if (!this._currentSuggestion) return false;

        // 对于中文输入，检查拼音匹配
        if (this._inputLanguage.startsWith('zh')) {
            // 将组合文本转换为拼音（需要添加拼音转换库）
            // const composingPinyin = this._convertToPinyin(composingText);
            // const suggestionPinyin = this._convertToPinyin(this._currentSuggestion.substring(0, composingText.length));
            // return composingPinyin === suggestionPinyin;

            // 临时简单匹配逻辑
            return this._currentSuggestion.startsWith(composingText);
        }

        // 对于其他语言，检查前缀匹配
        return this._currentSuggestion.toLowerCase().startsWith(composingText.toLowerCase());
    }

    // 添加新方法：临时隐藏预览
    private _temporarilyHideSuggestion() {
        this.editor.model.change(writer => {
            this._clearSuggestionElements(writer);
        });
        // 注意：不重置 _showSuggestion 和 _currentSuggestion
    }

    // 添加新方法：处理输入法输入结束
    private _handleCompositionEnd(finalText: string, duration: number) {
        console.log(`输入法输入结束，耗时: ${duration}ms, 最终文本:`, finalText);

        // 恢复之前隐藏的预览（如果有）
        if (this._showSuggestion && this._currentSuggestion) {
            // 检查最终文本是否匹配预览
            if (this._currentSuggestion.startsWith(finalText)) {
                // 更新预览文本
                const newSuggestion = this._currentSuggestion.substring(finalText.length);
                if (newSuggestion) {
                    this._currentSuggestion = newSuggestion;
                    this._displaySuggestion(newSuggestion);
                } else {
                    // 如果没有剩余预览，清除并请求新的
                    this._clearSuggestion();
                    this._processTextCompletion();
                }
            } else {
                // 如果不匹配，清除当前预览并请求新的
                this._clearSuggestion();
                // 使用较短的延迟，因为输入已经确认
                setTimeout(() => {
                    this._processTextCompletion();
                }, this._debounceDelay / 2);
            }
        } else {
            // 如果当前没有预览，直接请求新的
            setTimeout(() => {
                this._processTextCompletion();
            }, this._debounceDelay / 2);
        }
    }


    private _processHtmlData(data: string) {
        this._htmlDataProcessor.toView(data);
    }

    private _handleMdData(data: string): string {
        // 寻找 :::mention ... ::: 模式的数据
        const mentionPattern = /:::mention\s+([\s\S]*?):::/g;

        // 替换所有匹配项
        const processedData = data.replace(mentionPattern, (match, jsonContent) => {
            try {
                // 尝试解析JSON内容
                const jsonData = JSON.parse(jsonContent.trim());

                // 如果有title属性，则替换为title的值
                if (jsonData && jsonData.title) {
                    return jsonData.title;
                }

                // 如果没有title属性，保留原始内容
                return match;
            } catch (error) {
                // 解析JSON出错，保留原始内容
                console.error('解析mention中的JSON数据失败:', error, jsonContent);
                return match;
            }
        });

        return processedData;
    }

    // 清理资源
    destroy() {
        this._lastModelSuggestion = '';
        // 清除其他计时器和资源
        if (this._suggestionDebounce) {
            clearTimeout(this._suggestionDebounce);
            this._suggestionDebounce = null;
        }

        // 调用父类销毁方法
        super.destroy();
    }
}
