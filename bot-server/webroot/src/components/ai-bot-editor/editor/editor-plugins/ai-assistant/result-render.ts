/**
 * 大模型结果处理工具
 * 用于处理AI大模型返回的内容，根据内容格式进行定制化操作
 */

/**
 * 处理结果的接口
 */
export interface ProcessedResult {
  /** 原始内容 */
  originalContent: string;
  /** 处理后的内容 */
  processedContent: string;
  /** 处理状态 */
  status: 'success' | 'error' | 'warning';
  /** 附加信息 */
  meta?: Record<string, any>;
}

/**
 * 内容处理器接口
 */
export interface ContentProcessor {
  /** 检查是否可以处理该内容 */
  canProcess: (content: string) => boolean;
  /** 处理内容 */
  process: (content: string) => Promise<string> | string;
}

/**
 * 内容处理配置
 */
export interface ProcessorConfig {
  /** 处理器列表 */
  processors: ContentProcessor[];
  /** 默认处理器 */
  defaultProcessor?: ContentProcessor;
  /** 是否保留原始内容 */
  keepOriginal?: boolean;
  /** 前处理钩子 */
  preProcess?: (content: string) => string;
  /** 后处理钩子 */
  postProcess?: (content: string) => string;
}

/**
 * 提取代码块的处理器
 */
export const codeBlockProcessor: ContentProcessor = {
  canProcess: (content: string) => {
    return /```[\s\S]*?```/g.test(content);
  },
  process: (content: string) => {
    const codeBlocks = content.match(/```(?:\w+)?\s*([\s\S]*?)```/g) || [];
    return codeBlocks.map(block => {
      // 移除代码块的语言标识和反引号
      return block.replace(/```(?:\w+)?\s*([\s\S]*?)```/g, '$1').trim();
    }).join('\n\n');
  }
};

/**
 * 提取表格的处理器
 */
export const tableProcessor: ContentProcessor = {
  canProcess: (content: string) => {
    return /\|[^|]+\|[^|]+\|/.test(content) && /\|\s*[-:]+\s*\|/.test(content);
  },
  process: (content: string) => {
    // 这里只是简单地保留表格，实际应用中可能需要转换为HTML或其他格式
    return content;
  }
};

/**
 * 默认的内容处理器
 */
export const defaultProcessor: ContentProcessor = {
  canProcess: () => true,
  process: (content: string) => {
    // 默认处理器只清理一些常见问题
    return content
      .replace(/\n{3,}/g, '\n\n') // 减少过多的换行
      .trim();
  }
};

/**
 * 处理大模型返回的内容
 * @param content 大模型返回的原始内容
 * @param config 处理配置
 * @returns 包含原始内容和处理后内容的结果对象
 */
export function processModelResult(
  content: string,
  config: Partial<ProcessorConfig> = {}
): Promise<ProcessedResult> {
  return new Promise(async (resolve) => {
    // 合并默认配置
    const fullConfig: ProcessorConfig = {
      processors: config.processors || [codeBlockProcessor, tableProcessor],
      defaultProcessor: config.defaultProcessor || defaultProcessor,
      keepOriginal: config.keepOriginal !== undefined ? config.keepOriginal : true,
      preProcess: config.preProcess,
      postProcess: config.postProcess
    };

    try {
      // 前处理
      let processedContent = fullConfig.preProcess ? fullConfig.preProcess(content) : content;
      
      // 查找适用的处理器
      const processor = fullConfig.processors.find(p => p.canProcess(processedContent)) || 
                       fullConfig.defaultProcessor;
      
      // 应用处理器
      if (processor) {
        const result = await Promise.resolve(processor.process(processedContent));
        processedContent = result;
      }
      
      // 后处理
      if (fullConfig.postProcess) {
        processedContent = fullConfig.postProcess(processedContent);
      }
      
      // 返回结果
      resolve({
        originalContent: content,
        processedContent: processedContent,
        status: 'success'
      });
    } catch (error) {
      // 处理错误
      resolve({
        originalContent: content,
        processedContent: content, // 发生错误时返回原始内容
        status: 'error',
        meta: { error }
      });
    }
  });
}

/**
 * 快速处理大模型结果
 * 简化版API，使用默认处理器
 * @param content 原始内容
 * @returns 处理后的内容
 */
export function quickProcess(content: string): Promise<string> {
  return processModelResult(content)
    .then(result => result.processedContent);
}
