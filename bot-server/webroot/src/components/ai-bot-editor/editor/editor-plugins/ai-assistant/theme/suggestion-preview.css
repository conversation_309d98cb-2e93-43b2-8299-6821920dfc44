/* 建议预览样式 */
.suggestion-preview {
    opacity: 0.5;
    pointer-events: none !important;
    user-select: none !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    position: relative;
    font-style: italic !important;
    display: inline !important;
    cursor: default !important;
    text-decoration: none !important;
    border-bottom: none !important;
    filter: brightness(1.5) contrast(0.7);
}


/* 淡入动画效果 */
@keyframes fadeIn {
    from { 
        opacity: 0; 
        transform: translateX(4px);
        filter: blur(2px) brightness(1.5) contrast(0.7);
    }
    to { 
        opacity: 0.5; 
        transform: translateX(0);
        filter: blur(0) brightness(1.5) contrast(0.7);
    }
}

/* 为建议预览添加动画 */
.suggestion-preview {
    animation: fadeIn 0.2s ease-in;
} 