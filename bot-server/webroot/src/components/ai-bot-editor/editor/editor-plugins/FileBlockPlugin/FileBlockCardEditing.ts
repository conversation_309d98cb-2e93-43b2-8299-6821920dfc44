import {Plugin} from '@ckeditor/ckeditor5-core';
import {toWidget, Widget} from "@ckeditor/ckeditor5-widget";
import './theme/ref.css';

import {h, render} from "vue";

// @ts-ignore
import * as child_process from "child_process";

import FileBlockCardCommand from "./FileBlockCardCommand";
import FileCardView from "@/components/ai-bot-editor/editor/widget/file-widget/FileCardView.vue";

/*
* 当前的文件卡片是通过 本地的 base 图片实现的 图片展示在编辑器中
* 待优化为支持编辑器 文件小部件类型
* */
export default class FileBlockCardEditing extends Plugin {

    static get requires() {
        return [Widget];
    }

    init() {
        this._defineSchema();
        this._defineConverters();
        // 注册匹配的 指令到编辑器中
        this.editor.commands.add('insertFile', new FileBlockCardCommand(this.editor));
    }

    // 注册部件模式 重要
    _defineSchema() {                                                          // ADDED
        const schema = this.editor.model.schema;
        // 定义卡片块数据
        schema.register('FileCard', {
            // 定义 文件卡片为一个内联对象
            inheritAllFrom: '$inlineObject',
            // 允许在文本中
            allowWhere: '$text',
            // 允许选中
            isSelectable: true,
            // 允许内联
            isInline: true,
            // 定义使用插件时候需要传递的参数
            allowAttributes: ['name', 'path', 'type', 'size']
        });
    }

    /*
    * @description 定义视图转换 每次将任何数据加载到编辑器中时，都会发生 upcast 进程转换
    * */
    _defineConverters() {
        const conversion = this.editor.conversion;
        let upcastHelpers = conversion.for('upcast')
        let dataDowncastHelpers = conversion.for('dataDowncast')
        let downcastHelpers = conversion.for('editingDowncast')
        const editor = this.editor;  // 在这里获取编辑器实例

        // 文件卡片 部件模型视图 定义
        upcastHelpers.elementToElement({
            view: {
                name: 'div',
                classes: ['file-block-card']
            },
            // 创建模型数据
            model: (viewElement, {writer: modelWriter}) => {
                let name = viewElement.getAttribute('name');
                let path = viewElement.getAttribute('path');
                let type = viewElement.getAttribute('type');
                let size = viewElement.getAttribute('size');
                let value = {name: name, path: path, type: type, size: size}
                return modelWriter.createElement('FileCard', {
                    ...value
                });
            }
        })
        // 定义向下转化 dataDowncast 用于将型数据转换为输出 HTML数据;
        dataDowncastHelpers.elementToElement({
            model: 'FileCard',
            view: (modelItem, {writer: viewWriter}) => createFileBlockCardView(modelItem, viewWriter)
        });
        //  editingDowncast 用于将模型转换为我们在编辑器 UI 中看到的编辑视图。
        downcastHelpers.elementToElement({
            model: 'FileCard',
            view: (modelItem, {writer: viewWriter}) => {
                const widgetElement = createFileBlockCardView(modelItem, viewWriter);
                return toWidget(widgetElement, viewWriter);
            }
        })


        // 创建一个 ck 编辑器 内容元素
        function createFileBlockCardView(modelItem, viewWriter) {
            let name = modelItem.getAttribute('name')
            let path = modelItem.getAttribute('path')
            let type = modelItem.getAttribute('type')
            let size = modelItem.getAttribute('size')
            
            // 创建主容器
            const container = viewWriter.createContainerElement('div', {
                class: 'file-block-card',
                name: name,
                path: path,
                type: type,
                size: size,
                contenteditable: 'false'
            });

            // 创建内容容器
            const content = viewWriter.createRawElement('div', {
                class: 'file-block-content'
            }, function (domElement) {
                let vNode = h(FileCardView, {
                    name: name,
                    path: path,
                    type: type,
                    size: size,
                    // Vue 3 中正确的事件监听写法
                    'onUpdate:modelValue': (value: any) => {
                        console.log('value updated:', value);
                    },
                    'onClose': () => {
                        editor.model.change(writer => {
                            // 获取文件卡片的位置
                            const nodePosition = editor.model.createPositionBefore(modelItem);
                            // 删除文件卡片
                            writer.remove(modelItem);
                            // 将光标设置到文件卡片原来的位置
                            writer.setSelection(nodePosition);
                        });
                    },
                    'onUploadComplete': (result: any) => {
                        console.log('File upload complete:', result);
                    }
                })
                render(vNode, domElement);
            });

            // 组装视图
            viewWriter.insert(viewWriter.createPositionAt(container, 0), content);

            return container;
        }
    }

}
