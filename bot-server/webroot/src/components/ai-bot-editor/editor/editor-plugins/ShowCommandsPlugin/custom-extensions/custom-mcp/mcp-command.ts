import {Command} from '@ckeditor/ckeditor5-core';
import {toMap} from "@ckeditor/ckeditor5-utils";
import {MentionAttribute} from "@/components/ai-bot-editor/editor/editor-plugins/ShowCommandsPlugin/mention";
import {Range} from '@ckeditor/ckeditor5-engine'
import {Widget} from "@ckeditor/ckeditor5-widget";
import {SchemaContextDefinition} from "@ckeditor/ckeditor5-engine/src/model/schema";

const BRACKET_PAIRS = {
    '(': ')',
    '[': ']',
    '{': '}'
} as const;

export default class McpCommand extends Command {
    static get requires() {
        return [Widget];
    }

    execute(options: { mention: MentionAttribute; range?: Range }) {
        const editor = this.editor;
        const model = editor.model;
        const selection = model.document.selection;
        const range = options.range || selection.getFirstRange();

        // Don't execute command if range is in non-editable place.
        if (!model.canEditAt(range)) {
            return;
        }
        model.change(writer => {
            const currentAttributes = toMap(selection.getAttributes());
            const attributesWithMention = new Map(currentAttributes.entries());
            attributesWithMention.set('mcp', options.mention.mcp);
            const mcpElement = writer.createElement('McpMention', attributesWithMention);
            editor.model.insertObject(mcpElement, range, null, {setSelection: 'after'});
        });
    }

    refresh() {
        const model = this.editor.model;
        const selection = model.document.selection;
        this.isEnabled = model.schema.checkChild(selection.focus.parent as SchemaContextDefinition, 'SourceMention');
    }
} 