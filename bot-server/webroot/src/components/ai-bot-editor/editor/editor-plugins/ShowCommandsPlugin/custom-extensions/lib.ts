import type {MentionAttribute} from "@/components/ai-bot-editor/editor/editor-plugins/ShowCommandsPlugin/mention";
import type Editor from "@ckeditor/ckeditor5-core/src/editor/editor";
import {Range} from "@ckeditor/ckeditor5-engine"
import {
    ExtensionType
} from "@/components/ai-bot-editor/editor/editor-plugins/ShowCommandsPlugin/custom-extensions/extensions";

export function insertCustomExtensions(editor: Editor, mention: MentionAttribute, range: Range) {
    switch (mention.type) {
        case ExtensionType.Source:
            editor.execute(ExtensionType.Source, {mention, range})
            break
        case ExtensionType.Mcp:
            editor.execute(ExtensionType.Mcp, {mention, range})
            break
    }
}