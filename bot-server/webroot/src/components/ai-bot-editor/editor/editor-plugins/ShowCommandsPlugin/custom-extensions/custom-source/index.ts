import {Plugin} from '@ckeditor/ckeditor5-core';
import SourceEditing from './source-editing';
import SourceCommand from './source-command';
import {
    ExtensionType
} from "@/components/ai-bot-editor/editor/editor-plugins/ShowCommandsPlugin/custom-extensions/extensions";

export default class Source extends Plugin {
    static get requires() {
        return [SourceEditing] as const;
    }

    static get pluginName() {
        return 'source' as const;
    }

    init() {
        this.editor.commands.add(ExtensionType.Source, new SourceCommand(this.editor));
    }
} 