export type MentionExtension = {
    /*
     * 此部分开始是对 官方插件的扩展配置
     * */
    type?: ExtensionType;
    command?: CommandData;
    source?: SourceData;
    mcp?: McpData;
}

export enum ExtensionType {
    // 编辑器操作命令类型
    Command = 'editor-command',

    // 目标资源类型 一般用于应用资源
    Source = 'source-command',

    Mcp = 'mcp-command'
}

export interface CommandData {
    id: string;
    name: string;
    description?: string;
    icon?: string;
    shortcut?: string;
    execute?: () => void;
}

export interface SourceData {
    id: string;
    name: string;
    dataType?: string;
    dir?: string;
    url?: string;
    description?: string;
}

export interface McpData {
    id: string;
    name: string;
}
