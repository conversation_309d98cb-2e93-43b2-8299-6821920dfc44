import {Plugin} from '@ckeditor/ckeditor5-core';
import McpEditing from './mcp-editing';
import McpCommand from './mcp-command';
import {
    ExtensionType
} from "@/components/ai-bot-editor/editor/editor-plugins/ShowCommandsPlugin/custom-extensions/extensions";

export default class Mcp extends Plugin {
    static get requires() {
        return [McpEditing] as const;
    }   

    static get pluginName() {
        return 'mcp' as const;
    }

    init() {
        this.editor.commands.add(ExtensionType.Mcp, new McpCommand(this.editor));
    }
} 