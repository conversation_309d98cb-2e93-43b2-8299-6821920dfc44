import {Plugin} from '@ckeditor/ckeditor5-core';
import {toWidget} from "@ckeditor/ckeditor5-widget";
import SourceItem from "@/components/ai-bot-editor/editor/widget/show-commands/SourceItem.vue";
import {h, render} from "vue";
import {toMap} from "@ckeditor/ckeditor5-utils";
import {getImageUrl} from "@/components/common/manageRequest";

export default class SourceEditing extends Plugin {
    public static get pluginName() {
        return 'SourceEditing' as const;
    }

    public init(): void {
        this._defineSchema();
        this._defineConverters();
    }

    private _defineSchema() {
        const schema = this.editor.model.schema;

        schema.register('SourceMention', {
            inheritAllFrom: '$inlineObject',
            // 允许选中
            isSelectable: true,
            // 允许内联
            isInline: true,

            allowAttributes: ['source']
        });
    }

    private _defineConverters() {
        const conversion = this.editor.conversion;
        const upcast = conversion.for('upcast');
        const dataDowncast = conversion.for('dataDowncast');
        let editingDowncast = conversion.for('editingDowncast');

        upcast.elementToElement({
            view: {
                name: 'span',
                classes: ['source-mention']
            },
            model: (viewElement, {writer: modelWriter}) => {
                let id = viewElement.getAttribute('id');
                let name = viewElement.getAttribute('name');
                let dataType = viewElement.getAttribute('dataType');

                let source = {id, name, dataType}
                let model = this.editor.model;
                const selection = model.document.selection;
                const currentAttributes = toMap(selection.getAttributes());
                const attributesWithMention = new Map(currentAttributes.entries());
                attributesWithMention.set('source', source);
                console.log("upcast", viewElement)
                return modelWriter.createElement('SourceMention', attributesWithMention);
            }
        });

        editingDowncast.elementToElement({
            model: 'SourceMention',
            view: (modelItem, {writer: viewWriter}) => {
                const widgetElement = createSourceMentionView(modelItem, viewWriter);
                return toWidget(widgetElement, viewWriter);
            }
        });

        dataDowncast.elementToElement({
            model: 'SourceMention',
            view: (modelItem, {writer: viewWriter}) => createSourceMentionView(modelItem, viewWriter)
        });
    }
}

function createSourceMentionView(modelItem, viewWriter) {
    let source = modelItem.getAttribute('source');
    // todo  此处需要 在创建 SourceItem 之前初始化 资源链接属性，
    // todo 遇到大文件，服务器复制时间过长，可能导致 SourceItem 内的 url 为 未定义 ，遇到以后继续优化此处问题
    getImageUrl(source.id.substring(1)).then(url => {
        source.url = url
    })
    let element = viewWriter.createContainerElement('span', {
        class: 'source-mention',
        name: source.name,
        id: source.id,
        url: source.url,
        dataType: source.dataType,
        contenteditable: 'false'
    });
    let rawElement = viewWriter.createRawElement('span', {
    }, async (domElement) => {
        const vNode = h(SourceItem, {source: source})
        render(vNode, domElement)
    });
    element._appendChild(rawElement)
    return element;
} 