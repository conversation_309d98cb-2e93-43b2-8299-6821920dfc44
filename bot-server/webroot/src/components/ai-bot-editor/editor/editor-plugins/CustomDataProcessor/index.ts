/**
 * @module CustomDataProcessor
 */

import { Plugin } from '@ckeditor/ckeditor5-core';
import CustomDataProcessor from './customdataprocessor';

/**
 * 自定义数据处理器插件
 */
export default class CustomDataProcessorPlugin extends Plugin {
    /**
     * @inheritDoc
     */
    public static get pluginName() {
        return 'CustomDataProcessor' as const;
    }

    /**
     * @inheritDoc
     */
    public init(): void {
        const editor = this.editor;
        const processor = new CustomDataProcessor(editor.editing.view.document);

        // 设置编辑器的数据处理器
        editor.data.processor = processor;

        // 注册自定义标签处理
        processor.registerRawContentMatcher('data-custom');
        processor.registerRawContentMatcher('data-custom-format');
    }
} 