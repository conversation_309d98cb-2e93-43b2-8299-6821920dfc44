/**
 * @module CustomDataProcessor/customdataprocessor
 */

import type {ViewDocument, ViewDocumentFragment} from '@ckeditor/ckeditor5-engine';
import {HtmlDataProcessor} from '@ckeditor/ckeditor5-engine';
import {marked} from 'marked';
import TurndownService from 'turndown';
import {customMarkdownRules, htmlToMarkdownConfig, markdownParserConfig} from './markdown-config';
import {McpMarkdown, MentionMarkdown} from "@/components/ai-bot-editor/editor/editor-plugins/CustomDataProcessor/augmentation";
import {DataType} from "@/components/common/model/enum";

/**
 * 自定义数据处理器，继承自 HtmlDataProcessor
 * 用于处理 Markdown 和 HTML 之间的转换
 */
export default class CustomDataProcessor extends HtmlDataProcessor {
    private markdownParser: typeof marked;
    private htmlToMarkdown: TurndownService;

    constructor(document: ViewDocument) {
        super(document);

        // 初始化 Markdown 解析器
        this.markdownParser = marked;
        this.markdownParser.setOptions(markdownParserConfig);

        // 初始化 HTML 到 Markdown 转换器
        this.htmlToMarkdown = new TurndownService(htmlToMarkdownConfig);
        // 设置自定义转换规则
        this._setupMarkdownRules();
    }

    /**
     * 重写 toView 方法，添加 Markdown 支持
     */
    public override toView(data: string): ViewDocumentFragment {
        // 如果输入是 Markdown 格式，先转换为 HTML
        const html = this.markdownToHtml(data);

        // 调用父类的 toView 方法处理 HTML
        return super.toView(html);
    }

    /**
     * 重写 toData 方法，添加 Markdown 输出支持
     */
    public override toData(viewFragment: ViewDocumentFragment): string {
        // 先转换为 HTML
        const html = super.toData(viewFragment);
        // 将 HTML 转换为 Markdown
        return this.htmlToMarkdown.turndown(html);
    }

    /**
     * 将 Markdown 转换为 HTML
     */
    private markdownToHtml(markdown: string): string {
        // 使用 marked 解析其他 Markdown 语法
        return this.markdownParser.parse(markdown) as string;
    }

    /**
     * 设置自定义 Markdown 转换规则
     */
    private _setupMarkdownRules(): void {
        // 代码块转换规则
        if (customMarkdownRules.codeBlock.enableLanguageDetection) {
            this.htmlToMarkdown.addRule('fencedCodeBlock', {
                filter: (node: HTMLElement) => {
                    return node.nodeName === 'PRE' &&
                        node.firstChild?.nodeName === 'CODE';
                },
                replacement: (content: string, node: HTMLElement) => {
                    const code = node.firstChild as HTMLElement;
                    const language = code.getAttribute('class')?.replace('language-', '') || '';
                    return '\n```' + language + '\n' + content.trim() + '\n```\n';
                }
            });
        }

        // 修改 mention 规则
        this.htmlToMarkdown.addRule('mention', {
            filter: (node: HTMLElement, options) => {
                return node.nodeName === 'DIV' &&
                    node.classList.contains('source-data');
            },
            replacement: (content: string, node: HTMLElement) => {
                console.log("mention markdownParser => ", node)
                const dataType = node.getAttribute('data-type') || '';
                const resource = node.getAttribute('data-source') || '';
                const id = node.getAttribute('data-id') || '';
                const path = node.getAttribute('data-path') || '';
                const title = node.getAttribute('title') || '';

                // 创建资源信息对象
                const resourceInfo: MentionMarkdown = {
                        id,
                        title,
                        dataType: (dataType as DataType),
                        path,
                        resource,
                    }
                ;
                // 将对象转换为格式化的 JSON 字符串，确保格式一致
                const jsonContent = JSON.stringify(resourceInfo);
                // 使用自定义容器包装 JSON 内容，确保格式一致
                // todo '\n\r' 严格按照顺序对md数据文本分割 否则 自定义解析不到
                return `\n\r:::mention\n\r${jsonContent}\n\r:::\n\r`;
            }
        });

        // 修改 mention 规则
        this.htmlToMarkdown.addRule('mcptool', {
            filter: (node: HTMLElement, options) => {
                return node.nodeName === 'DIV' &&
                    node.classList.contains('mcp-data');
            },
            replacement: (content: string, node: HTMLElement) => {
                console.log("mention markdownParser => ", node)
                const id = node.getAttribute('data-id') || '';
                const title = node.getAttribute('title') || '';

                // 创建资源信息对象
                const resourceInfo: McpMarkdown = {
                        id,
                        title,
                    }
                ;
                // 将对象转换为格式化的 JSON 字符串，确保格式一致
                const jsonContent = JSON.stringify(resourceInfo);
                // 使用自定义容器包装 JSON 内容，确保格式一致
                // todo '\n\r' 严格按照顺序对md数据文本分割 否则 自定义解析不到
                return `\n\r:::mcpinstance\n\r${jsonContent}\n\r:::\n\r`;
            }
        });
    }
} 