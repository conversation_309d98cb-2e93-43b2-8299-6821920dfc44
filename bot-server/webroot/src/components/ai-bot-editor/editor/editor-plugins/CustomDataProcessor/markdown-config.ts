/**
 * Markdown 解析器配置
 */
export const markdownParserConfig = {
    gfm: true,              // GitHub Flavored Markdown
    breaks: true,           // 转换段落里的 '\\n' 到 <br>
    headerIds: false,       // 不生成标题ID
    mangle: false,          // 不转义内联HTML
    pedantic: false,        // 不严格遵循原始 markdown
    smartLists: true,       // 使用更智能的列表行为
    smartypants: false      // 不使用更智能的标点符号
};

/**
 * HTML 到 Markdown 转换器配置
 */
export const htmlToMarkdownConfig = {
    headingStyle: 'atx',           // 标题样式: ## Heading
    bulletListMarker: '*',         // 无序列表标记
    codeBlockStyle: 'fenced',      // 代码块样式: ```code```
    emDelimiter: '*',              // 斜体标记
    strongDelimiter: '**',         // 粗体标记
    linkStyle: 'referenced',          // 链接样式: [text](url)
    linkReferenceStyle: 'full',    // 链接引用样式
    preformattedCode: true,         // 保持代码块的格式
} as const;

/**
 * 自定义 Markdown 规则配置
 */
export const customMarkdownRules = {
    codeBlock: {
        name: 'fencedCodeBlock',
        enableLanguageDetection: true,
        defaultLanguage: ''
    },
    mention: {
        name: 'fencedMention',
        enableFormatting: true,
        alignmentMarkers: true
    },
    taskList: {
        name: 'taskListItems',
        enableCheckbox: true
    },
    table: {
        name: 'table',
        enableFormatting: true,
        alignmentMarkers: true
    },
    math: {
        name: 'math',
        inline: true,
        block: true,
        delimiter: '$$'
    }
} as const;

/**
 * Markdown 扩展功能配置
 */
export const markdownExtensions = {
    // 语法高亮支持
    highlight: {
        enabled: true,
        theme: 'github'
    },
    // LaTeX 数学公式支持
    math: {
        enabled: true,
        engine: 'katex'
    },
    // 图表支持
    charts: {
        enabled: true,
        types: ['mermaid', 'plantuml']
    },
    // 自动链接
    autolink: {
        enabled: true,
        email: true
    }
} as const; 