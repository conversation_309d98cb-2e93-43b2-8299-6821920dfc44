<template>
  <button tabindex="-1" :disabled="disabled" @click="$emit('click')">
    <div class="svg-wrapper-1">
      <div class="svg-wrapper">
        <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            width="15"
            height="15"
        >
          <path fill="none" d="M0 0h24v24H0z"></path>
          <path
              fill="currentColor"
              d="M6 5h12a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H6a1 1 0 0 1-1-1V6a1 1 0 0 1 1-1z"
          ></path>
        </svg>
      </div>
    </div>
    <span>停止</span>
  </button>

</template>

<script setup lang="ts">
import {theme} from 'ant-design-vue';

const {useToken} = theme;
const {token} = useToken();
defineEmits(['click'])
defineProps({
  disabled: {
    type: Boolean,
    default: false
  }
})
</script>

<style scoped>
/* From Uiverse.io by adamgiebl */
button {
  font-family: inherit;
  font-size: 12px;
  background: v-bind('token.colorWarningBg');
  color: v-bind('token.colorWarningText') !important;
  padding: 0.2em 0.8em;
  display: flex;
  align-items: center;
  border: none;
  border-radius: 10px;
  overflow: hidden;
  transition: all 0.2s;
  cursor: pointer;
}

button:disabled {
  background: #ccc;
  cursor: not-allowed;
  opacity: 0.6;
}

button span {
  display: block;
  margin-left: 0.3em;
}

button svg {
  display: block;
}

button:active {
  transform: scale(0.95);
}

/* 删除了所有悬停相关的动画效果 */
</style> 