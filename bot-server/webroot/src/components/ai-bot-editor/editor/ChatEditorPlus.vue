<template>
  <div class="size-full  flex flex-col overflow-auto ">
    <div class="editor-panel  size-full  flex flex-col overflow-auto mx-auto my-auto">
      <div v-if="showTool" class="flex flex-row tool-bar" @mousedown="startDrag" :class="{ 'dragging': isDragging }">
        <div class="left-tools">
          <ToolWidget>
            <AiBotPlugin />
          </ToolWidget>

          <ToolWidget>
            <ClearHistory />
          </ToolWidget>

          <!-- <ToolWidget>
            <McpTool/>
          </ToolWidget> -->

          <ToolWidget>
            <ThinkingModeToggle />
          </ToolWidget>

          <ToolWidget>
            <NetworkToggle />
          </ToolWidget>

          <ToolWidget>
            <ClearContext />
          </ToolWidget>
        </div>

        <div class="flex-auto"></div>

        <div class="right-tools">
          <!--  消息上下文携带配置按钮    -->
          <ToolWidget>
            <ContextCheck />
          </ToolWidget>
        </div>
      </div>
      <div class="w-full flex flex-col overflow-auto" style="flex-grow: 1">
        <Editor ref="editRef" v-model:characters="characters" :shame="shame" @input="input" @send="sendAction" />
      </div>
      <!-- 发送区域和工作空间索引放在同一行 -->
      <div class="bottom-bar flex flex-row-reverse justify-between">
        <div class="send-box">
          <!-- 移除了有问题的column类，直接使用flex布局 -->
          <div class="send-btn-container">
            <!-- 根据回复状态切换显示 SendBtn 或 StopBtn -->
            <template v-if="!ctx.ui.replying">
              <SendBtn @click="sendMessage" />
            </template>
            <template v-else>
              <StopBtn @click="stopReplying" />
            </template>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from "vue";
import Editor from "./Editor.vue";
import { IsEmpty } from "@/utils/systemutils";
import { useGptStore } from "@/components/store/gpt";
import ClearHistory from "@/components/ai-bot-editor/editor-tool-bar/clear-history/ClearHistory.vue";
import ToolWidget from "@/components/ai-bot-editor/editor/tool/ToolWidget.vue";
import AiBotPlugin from "@/components/ai-bot-plugins/AiBotPlugin.vue";
import CodeBlock from "@/components/ai-bot-editor/editor-tool-bar/codeBlock/CodeBlock.vue";
import ContextCheck from "@/components/ai-bot-editor/editor-tool-bar/contextCheck/ContextCheck.vue";
import SendBtn from "@/components/ai-bot-editor/editor/SendBtn.vue";
import StopBtn from "@/components/ai-bot-editor/editor/StopBtn.vue";
import NetworkToggle from "@/components/ai-bot-editor/editor-tool-bar/networkToggle/NetworkToggle.vue";
import ThinkingModeToggle from "@/components/ai-bot-editor/editor-tool-bar/thinkingModeToggle/ThinkingModeToggle.vue";
import ClearContext from "@/components/ai-bot-editor/editor-tool-bar/clearContext/ClearContext.vue";
import WorkspaceIndex from "@/components/ai-bot-editor/workspace-index/WorkspaceIndex.vue";

import { theme } from 'ant-design-vue';
import McpTool from "@/components/ai-bot-editor/editor-tool-bar/mcpTool/McpTool.vue";
import { useAiBot } from "@/components/store/bot";
import ModelCheck from "../editor-tool-bar/modelCheck/ModelCheck.vue";

const { useToken } = theme;
const { token } = useToken();

const emits = defineEmits({
  // 发送消息事件 通知外部 ChatEditor 编辑器 需要发送消息
  send: (message: string) => {
  },
  // 最大化编辑器事件 需要动态调整布高度
  MaxEditor: () => {
  },
  // 新增：拖拽调整面板比例事件
  'update:panelRatio': (ratio: number) => true,
})

// 添加 props 定义
const props = defineProps({
  panelRatio: {
    type: Number,
    default: 25
  },
  showTool: {
    type: Boolean,
    default: true
  }
})

// shape 默认 回车按键发送消息
const shape = ref()
const editRef = ref()
const characters = ref(0)
const shame = ref(false)
const ctx = useGptStore()
if (!ctx.ui.send) {
  shape.value = 'Enter'
} else {
  shape.value = 'Ctrl+Enter'
}

// 添加拖拽相关的状态
const isDragging = ref(false)
const startY = ref(0)
const currentRatio = ref(props.panelRatio) // 使用 props 的默认值初始化
const startRatio = ref(props.panelRatio) // 新增：记录开始拖拽时的比例


function stopReplying() {
  if (ctx.ui.currentLLMStream) {
    ctx.ui.currentLLMStream.Stop();
  }
  ctx.ui.replying = false;
}


/*
* description: 编辑器输入事件
* */
function input(data: string) {
  let current = ctx.CurrentChat.Current
  // 更新当前的聊天会话消息编辑草稿
  if (!IsEmpty(current)) {
    ctx.chat_editor_content[current.Conversation.id] = data
  }
}


function sendMessage() {
  let msg = editRef.value.Message()
  emits("send", msg)
  editRef.value.Clear()
  let current = ctx.CurrentChat.Current
  ctx.chat_editor_content[current.Conversation.id] = ''
}

/*
* @description 编辑器触发快捷发送消息动作
* */
function sendAction(data: string) {
  emits("send", data)
  editRef.value.Clear()
  let current = ctx.CurrentChat.Current
  ctx.chat_editor_content[current.Conversation.id] = ''
}

function insertCodeBlock(language: string) {
  editRef.value.insertCodeBlock(language)
}


watch(() => shape.value, (value) => {
  if (value === 'Ctrl+Enter') {
    ctx.ui.send = true
  } else {
    ctx.ui.send = false
  }
})

// 开始拖拽
function startDrag(e: MouseEvent) {
  // 只有当直接点击工具栏自身或拖动区域时才启动拖拽
  // e.target 是实际点击的元素，e.currentTarget 是绑定事件的元素（即工具栏）
  if (e.target !== e.currentTarget && !(e.target as HTMLElement).classList.contains('flex-auto')) {
    // 不阻止事件冒泡，让按钮点击事件正常触发
    return;
  }

  // 点击工具栏空白区域或拖拽区域，启动拖拽
  isDragging.value = true
  startY.value = e.clientY
  // 记录开始拖拽时的比例
  startRatio.value = currentRatio.value
  document.addEventListener('mousemove', onDrag)
  document.addEventListener('mouseup', stopDrag)
  // 阻止文本选择和默认行为
  e.preventDefault()
  e.stopPropagation()
}

// 拖拽中
function onDrag(e: MouseEvent) {
  if (!isDragging.value) return

  const delta = startY.value - e.clientY
  // 获取实际容器高度
  const container = document.querySelector('.split-panel')
  if (!container) return

  const containerHeight = container.getBoundingClientRect().height
  // 调整灵敏度，让移动更平滑
  const sensitivity = 1
  const deltaPercent = (delta / containerHeight) * 100 * sensitivity

  // 从开始拖拽时的比例计算新的比例
  const newRatio = Math.max(25, Math.min(50, startRatio.value + deltaPercent))
  currentRatio.value = newRatio
  // 发送面板比例更新事件
  emits('update:panelRatio', newRatio)
}

// 停止拖拽
function stopDrag() {
  isDragging.value = false
  document.removeEventListener('mousemove', onDrag)
  document.removeEventListener('mouseup', stopDrag)
}

// 监听外部传入的面板比例变化
watch(() => props.panelRatio, (newRatio) => {
  if (newRatio !== undefined && !isDragging.value) {
    currentRatio.value = newRatio
    startRatio.value = newRatio
  }
}, { immediate: true })

</script>

<style scoped>
.editor-container :not(.send-box *) {
  color: v-bind('token.colorTextBase');
}


.send-box {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  flex-shrink: 0;
  /* 防止挤压 */
  min-width: 40px;
  /* 设置最小宽度 */
  width: auto;
  /* 改为自适应宽度 */
  height: 32px;
  /* 确保与左侧元素有间距 */
}


.stop-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2px;
  border: none;
  background: transparent;
  color: #ff4d4f;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
  transform: scale(1);
}

.stop-btn svg {
  width: 14px;
  height: 14px;
}

@keyframes scaleDown {
  0% {
    transform: scale(1);
  }

  100% {
    transform: scale(0.6);
  }
}

.scale-down {
  animation: scaleDown 0.2s ease forwards;
}

.scale-enter-active,
.scale-leave-active {
  transition: all 0.3s ease;
}

.scale-enter-from,
.scale-leave-to {
  transform: scale(0);
  opacity: 0;
}

.stop-btn:hover {
  background-color: rgba(255, 77, 79, 0.1);
}

.stop-btn:active {
  background-color: rgba(255, 77, 79, 0.2);
}

.left-tools,
.right-tools {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.flex-auto {
  flex: 1 1 auto;
}

.editor-panel {
  /* 移除边框和圆角 */
}

.tool-bar {
  display: flex;
  align-items: center;
  padding: 0 2px;
  gap: 4px;
  min-height: 29px;
  user-select: none;
  /* 防止文本选择 */
  position: relative;
  cursor: default;
  /* 默认光标 */
  border: 1px solid v-bind('token.colorFill');
  /* 给工具栏添加边框 */
}

.tool-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
}

.tool-bar .flex-auto {
  cursor: ns-resize;
  /* 在空白区域显示上下拖拽光标 */
  min-width: 20px;
  /* 确保有足够的空间可点击 */
  position: relative;
}

.tool-bar.dragging {
  cursor: grabbing !important;
  background-color: rgba(0, 0, 0, 0.05);
}

.tool-bar .flex-auto:active {
  background-color: rgba(0, 0, 0, 0.05);
  cursor: grabbing !important;
}

.left-tools,
.right-tools {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 2px;
}

/* 调整工具按钮的样式使其更紧凑 */
:deep(.tool-widget) {
  margin: 0;
  padding: 1px;
}

:deep(.tool-widget button) {
  padding: 1px 2px;
  min-height: 20px;
  line-height: 1;
  font-size: 12px;
}

:deep(.tool-widget svg) {
  width: 14px;
  height: 14px;
}

:deep(.tool-widget .anticon) {
  font-size: 14px;
}

.bottom-bar {
  display: flex;
  align-items: center;
  padding: 2px 8px;
  min-height: 32px;
  max-height: 32px;
  background-color: transparent;
  box-shadow: none;
  overflow: hidden;
  /* 防止内容溢出 */
  width: 100%;
  /* 确保宽度限制在父容器内 */
  max-width: 100%;
  /* 确保不会超出父容器 */
}

.workspace-wrapper {
  flex: 1;
  min-width: 0;
  /* 关键属性：允许flex item收缩到比内容宽度更小 */
  overflow: hidden;
  /* 确保内容不会溢出 */
  /* 与发送按钮保持间距 */
  max-width: calc(100% - 50px);
  /* 为发送按钮预留空间 */
}

.workspace-index-container {
  overflow: hidden;
  /* 确保内容在容器内滚动 */
  max-width: 60%;
}

/* 调整 small 小下拉框图标不居中问题 */
.send-box .anticon.anticon-down {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.etb .icon-container {
  width: 15px !important;
  height: 15px !important;
}

.etb {
  width: 15px !important;
  height: 15px !important;
}
</style>

<style>
/* 调整 small 小下拉框图标不居中问题 */
.send-box .anticon.anticon-down {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.etb .icon-container {
  width: 15px !important;
  height: 15px !important;
}

.etb {
  width: 15px !important;
  height: 15px !important;
}


:deep(.source-mention.ck-widget.ck-widget_selected) {
  outline: 1px solid var(--ck-color-focus-border) !important;
}

:deep(.ck .ck-widget:hover) {
  outline-width: 1px !important;
}
</style>