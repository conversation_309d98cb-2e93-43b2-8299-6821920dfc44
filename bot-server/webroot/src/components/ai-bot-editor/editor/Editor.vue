<template>
  <div class="editor-area size-full">
    <div class="editor-container size-full">
      <div ref="editorArea" id="editor" @click="Focus" spellcheck="false">
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">

import { onMounted, onUnmounted, ref, watch } from "vue";
// import { ipc<PERSON>enderer } from 'electron';

// CKEditorInspector 编辑器开发的检查工具
// @ts-ignore
import CKEditorInspector from "@ckeditor/ckeditor5-inspector";
import { keyCodes } from '@ckeditor/ckeditor5-utils/src/keyboard';
import { useGptStore } from "@/components/store/gpt";
import { EditorDataType, MessageEntity } from "@/components/common/model/chat";
import { createEditor } from "@/components/ai-bot-editor/editor/ckeditor";
import { useAiPluginStore } from "@/components/store/plugin";
import { useAiBot } from "@/components/store/bot";
import { Node } from "@ckeditor/ckeditor5-engine";
import { theme } from "ant-design-vue";
import { DecoupledEditor } from "@ckeditor/ckeditor5-editor-decoupled";


const emits = defineEmits({
  send: function (md: string) {

  },
  input: function (data: string) {

  }
})
defineExpose({
  insert,
  Message,
  Clear,
  insertCodeBlock
})
const props = defineProps({
  shame: {
    type: Boolean,
    default: false
  },
})


const { useToken } = theme;
const { token } = useToken();

const editorArea = ref()
// 编辑器 当前的符数量双向绑定
const characters = defineModel('characters', { default: 0 })
const ctx = useGptStore()
const plugin = useAiPluginStore()
// 当前编辑器是否处于回复消息状态
const isRef = ref(false)
const bot = useAiBot()
const textColor = ref('black')

// 编辑器对象
let editor: DecoupledEditor = null


// 编辑器的配置 (相对固定的比一部分配置)
const editorConfig = {
  wordCount: {
    onUpdate: stats => {
      characters.value = stats.characters
    }
  },
  placeholder: '输入消息...',
}

function getColor(value: { r: number; g: number; b: number; a: number }) {
  textColor.value = `rgba(${value.r}, ${value.g}, ${value.b}, ${value.a})`
}

function insert(data: MessageEntity) {
  switch (data.dataType) {
    // 插入文本消息
    case EditorDataType.Text:
      editor.model.change(writer => {
        const insertPosition = editor.model.document.selection.getFirstPosition();
        editor.model.insertContent(writer.createText(data.data), insertPosition);
      });
      break
    // 插入视频消息
    case EditorDataType.Video:
      break
    default:
  }
  editor.editing.view.focus();
}


/*
* @description: 清空编辑器内容
* 一用于关闭当前会话,发送完一次消息以后
* */
function Clear() {
  editor.setData('')
  editor.editing.view.focus()
}

/*
* 解析编辑器中的内容元素返回到上层
* */
function Message(): string {
  return editor.getData()
}


function Focus(event: MouseEvent) {
  editor.editing.view.focus();
}


/*
* @description 把图片复制到编辑器中
* */
function copyImage(file: Blob) {
  // 读取图片数据
  let reader = new FileReader()
  reader.addEventListener("load", async () => {
    editor.execute('addImage', {
      src: reader.result as string,
    });
  })
  reader.readAsDataURL(file)
}

/*
* @description 把视频复制到编辑器中
* */
function copyVideo(file: Blob) {

}

function insertCodeBlock(language: string) {
  console.log('insertCodeBlock', language)
  editor.execute('codeBlock', { language: language, forceValue: true })
}

/*
 * @description: 检查当前选中件的模型已经安装到本地,若本地未安装,则经用编辑器并且禁用发送按钮
 * */
watch(() => plugin.currentPlugin, (value) => {

})


onMounted(async () => {
  let elementById = document.querySelector("#editor") as HTMLElement;
  // 初始化编辑器
  editor = await createEditor(elementById)
  console.log(editor)
  EditorEvents(editor)
})

function EditorEvents(editor: DecoupledEditor) {
  // 监听实现删除整个 小部件
  editor.editing.view.document.on('keydown', (event, data) => {
    if (data.keyCode === keyCodes.delete || data.keyCode === keyCodes.backspace) {
      const selection = editor.model.document.selection;
      let nodeBefore: Node = (selection.focus.nodeBefore) as unknown as Node
      if (nodeBefore == null) {
        return;
      }
      console.log(nodeBefore)
      if (nodeBefore! &&
        nodeBefore.is('element', 'FileCard') ||
        nodeBefore.is('element', 'SourceMention') ||
        // 删除 @ / # 等资源小部件
        nodeBefore.hasAttribute('mention')
      ) {
        editor.model.change(writer => {
          if (nodeBefore) {
            writer.remove(nodeBefore )
          }
        })
        data.preventDefault()
        event.stop()
      }
    }
  })

  // 监听实现快捷键组合发送消息
  editor.editing.view.document.on('keydown', (event, data) => {
    if (checkSend(editor)) {
      // 判断 ctrl+enter 组合发送消息
      if (keyCodes.enter === data.keyCode && data.ctrlKey && !ctx.ui.replying) {
        // 检查当前的消息模式
        if (ctx.ui.send) {
          // 执行发送消息触发事件
          let msg = Message()
          emits('send', msg)
          data.preventDefault()
          event.stop()
        } else {
          editor.model.change(writer => {
            editor.model.insertContent(writer.createElement('softBreak'), editor.model.document.selection);
          })
        }
      }

      // 没有 按住 ctrl 使用 enter 发送消息
      if (keyCodes.enter === data.keyCode && !data.ctrlKey && !ctx.ui.replying) {

        if (!ctx.ui.send) {
          // 执行发送消息触发事件
          let msg = Message()
          emits('send', msg)
          data.preventDefault()
          event.stop()
        }
      }
    }
  })

  // 保存当前编辑器的内容
  editor.model.document.on('change:data', () => {  // Listen to all changes in model.

  });

  /*  let watcher = new TextWatcher(editor.model, function (text) {
      console.log(text)
    })*/

  // 监听复制事件
  editor.editing.view.document.on('copy', (evt, data) => {
    const selection = editor.model.document.selection;
    const selectedElement = selection.getSelectedElement();

    if (selectedElement) {
      if (selectedElement.is('element', 'FileCard')) {
        // 获取文件小部件的完整数据
        const path = selectedElement.getAttribute('path');
        const name = selectedElement.getAttribute('name');
        // 只保留必要的文件信息
        data.dataTransfer.setData('text/plain', path);
        data.preventDefault();
        evt.stop();
      } else if (selectedElement.is('element', 'SourceMention')) {
        // 获取 Source 小部件的完整数据
        const mention = selectedElement.getAttribute('mention') as any;
        // 复制源引用信息
        if (mention?.source?.name) {
          data.dataTransfer.setData('text/plain', '#' + mention.source.name);
          data.preventDefault();
          evt.stop();
        }
      }
    }
  });
}

/*
* 检查 当前编辑区域内 是否可以发送消息
* */
function checkSend(editor: DecoupledEditor): boolean {
  // 在特殊组件中不触发 send 动作
  const selection = editor.model.document.selection;
  const element = selection.getSelectedElement();
  if (element) {
    // console.log('Selected element:', element.name);
  } else {
    const position = selection.getFirstPosition();
    let parent = position.parent;
    while (parent && !editor.model.schema.isBlock(parent)) {
      parent = parent.parent;
    }
    if (parent) {
      // console.log('Current block:', parent.name);
      // 检查当前 编辑位置是否在代码块中 在代码块中我们阻止 消息发送动作
      if (parent.is('element', 'codeBlock')) {
        return false; // Don't trigger send action'
      }
    }
  }
  return true;
}

</script>


<style scoped>
.editor-area {
  flex-grow: 1;
  overflow: auto;
  display: flex;
}

#editor {
  padding: 0;
  width: auto;
  height: auto;
  min-height: 100%;
  border: none;
  outline: none;
  box-shadow: none;
  overflow: hidden;
}


:deep(.chat-editor p) {
  margin: 0 !important;
  outline: none !important;
}

:global(.chat-editor .ck-placeholder) {
  margin: 0 !important;
  outline: none !important;
}

:global(#editor > *:first-child) {
  margin: 0 !important;
  outline: none !important;
}

.chat-editor .ck.ck-content.ck-editor__editable {
  border: none !important;
  box-shadow: none !important;
}

/* 对编辑器中 p 进行强制换行显示*/
.chat-editor .ck.ck-content.ck-editor__editable p {
  padding: 0 !important;
  margin: 0 !important;
  word-break: break-all;
}

.chat-editor .ck .ck-widget {
  /*去除编辑器小部件的选中和取消选中的 轮廓变化过度*/
  transition: none;
}

.chat-editor .ck .ck-widget:hover {
  outline: none !important;
}


/* 删除 CKEditor 的商标 */
.ck.ck-powered-by {
  display: none !important;
}


/* 占位符文本颜色 */
.editor .ck-editor__editable.ck-placeholder::before {
  color: rgba(0, 0, 0, 0.6);
}


/* 其他样式 */
.ck.ck-sticky-panel__content {
  border-style: none !important;
}






/* 全局资源@ / 下拉框样式适配 */
:global(.ck .ck-on *) {
  color: v-bind('token.colorTextBase') !important;
}

:global(.ck.ck-list__item > .ck-button:not(.ck-list-item-button)) {
  padding: 2px 5PX !important;
}

:global(.ck-list) {
  background-color: transparent !important;
  padding: 0 !important;
  max-height: 200px !important;
  overflow-y: auto !important;
}

</style>
