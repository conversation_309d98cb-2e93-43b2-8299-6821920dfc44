<template>
  <button tabindex="-1" :disabled="disabled" @click="$emit('click')">
    <div class="svg-wrapper-1">
      <div class="svg-wrapper">
        <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 22 22"
            width="15"
            height="15"
        >
          <path fill="none" d="M0 0h24v24H0z"></path>
          <path
              fill="currentColor"
              d="M1.946 9.315c-.522-.174-.527-.455.01-.634l19.087-6.362c.529-.176.832.12.684.638l-5.454 19.086c-.15.529-.455.547-.679.045L12 14l6-8-8 6-8.054-2.685z"
          ></path>
        </svg>
      </div>
    </div>
    <span>发送</span>
  </button>

</template>

<script setup lang="ts">
import {theme} from 'ant-design-vue';

const {useToken} = theme;
const {token} = useToken();
defineEmits(['click'])
defineProps({
  disabled: {
    type: Boolean,
    default: false
  }
})
</script>

<style scoped>
/* From Uiverse.io by adamgiebl */
button {
  font-family: inherit;
  font-size: 12px;
  background: v-bind('token.colorInfoBg');
  color: v-bind('token.colorInfoText') !important;
  padding: 0.2em 0.8em;
  display: flex;
  align-items: center;
  border: none;
  border-radius: 10px;
  overflow: hidden;
  transition: all 0.2s;
  cursor: pointer;
}

button:disabled {
  background: #ccc;
  cursor: not-allowed;
  opacity: 0.6;
}

button:disabled:hover .svg-wrapper {
  animation: none;
}

button:disabled:hover svg {
  transform: none;
}

button:disabled:hover span {
  transform: none;
}

button span {
  display: block;
  margin-left: 0.3em;
  transition: all 0.3s ease-in-out;
}

button svg {
  display: block;
  transform-origin: center center;
  transition: transform 0.3s ease-in-out;
}

button:hover .svg-wrapper {
  animation: fly-1 0.6s ease-in-out infinite alternate;
}

button:hover svg {
  transform: translateX(1.2em) rotate(45deg) scale(1.1);
}

button:hover span {
  transform: translateX(5em);
}

button:active {
  transform: scale(0.95);
}

@keyframes fly-1 {
  from {
    transform: translateY(0.1em);
  }

  to {
    transform: translateY(-0.1em);
  }
}

</style>