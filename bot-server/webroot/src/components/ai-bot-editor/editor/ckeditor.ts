// ckeditor.ts

import { Clipboard } from '@ckeditor/ckeditor5-clipboard';
import { Bold, Code, Italic, Strikethrough, Underline } from '@ckeditor/ckeditor5-basic-styles';

import { Essentials } from '@ckeditor/ckeditor5-essentials';
import { Paragraph } from '@ckeditor/ckeditor5-paragraph';
import { WordCount } from '@ckeditor/ckeditor5-word-count';

import { Markdown, PasteFromMarkdownExperimental } from "@ckeditor/ckeditor5-markdown-gfm";
import { CodeBlock } from '@ckeditor/ckeditor5-code-block';
import CustomDataProcessorPlugin from "@/components/ai-bot-editor/editor/editor-plugins/CustomDataProcessor";
import { h, render } from 'vue';
import MentionItem from './widget/mention/MentionItem.vue';
import { Mention } from "@/components/ai-bot-editor/editor/editor-plugins/ShowCommandsPlugin";
import Source
    from "@/components/ai-bot-editor/editor/editor-plugins/ShowCommandsPlugin/custom-extensions/custom-source";
import { MentionFeedItem } from './editor-plugins/ShowCommandsPlugin/mentionconfig';
import AiAssistant from "@/components/ai-bot-editor/editor/editor-plugins/ai-assistant/ai-assistant";
import { DecoupledEditor } from "@ckeditor/ckeditor5-editor-decoupled";
import { Image, ImageCaption, ImageResize, ImageStyle } from "@ckeditor/ckeditor5-image";
import { Heading } from "@ckeditor/ckeditor5-heading";
import { List } from "@ckeditor/ckeditor5-list";
import { Link } from "@ckeditor/ckeditor5-link";
import { Table, TableToolbar } from "@ckeditor/ckeditor5-table";
import { Alignment } from "@ckeditor/ckeditor5-alignment";
import { Font } from "@ckeditor/ckeditor5-font";
import { Indent } from "@ckeditor/ckeditor5-indent";
import { TextTransformation } from "@ckeditor/ckeditor5-typing";
import { Base64UploadAdapter } from "@ckeditor/ckeditor5-upload";
import { PageBreak } from "@ckeditor/ckeditor5-page-break";
import { Dialog } from "@ckeditor/ckeditor5-ui";
import McpItem from "@/components/ai-bot-editor/editor/widget/mention/McpItem.vue";
import {
    ExtensionType
} from "@/components/ai-bot-editor/editor/editor-plugins/ShowCommandsPlugin/custom-extensions/extensions";
import { getMcpList } from '@/components/ai-bot-mcp/mcpReq';
import Mcp from '@/components/ai-bot-editor/editor/editor-plugins/ShowCommandsPlugin/custom-extensions/custom-mcp';

class Editor extends DecoupledEditor {
}

Editor.builtinPlugins = [
    Essentials, Paragraph,
    Bold, Italic, Underline, Strikethrough,
    Heading,
    Image, ImageCaption, ImageResize, ImageStyle,
    List,
    Link,
    Table, TableToolbar,
    Alignment,
    Font,
    Indent,
    TextTransformation,
    WordCount,
    Base64UploadAdapter,
    PageBreak,
    Dialog,
];

Editor.defaultConfig = {};


export async function createEditor(element: HTMLElement) {
    // 从IndexedDB加载所有@前缀的数据
    const mentionFeedItems = await loadMentionItemsFromDB();
    const mcpFeedItems = await loadMcpItemsFromDB();

    return await Editor.create(element, {
        placeholder: ' 发送消息...',
        plugins: [
            CustomDataProcessorPlugin,
            Mention, Source, Mcp, AiAssistant
        ],
        extraPlugins: [Essentials, Paragraph, Markdown, PasteFromMarkdownExperimental, Clipboard, WordCount, Code, CodeBlock],
        mention:
        {
            feeds: [
                {
                    marker: '@',
                    feed: mentionFeedItems, // 使用从IndexedDB加载的数据
                    itemRenderer: item => {
                        const div = document.createElement('div');

                        // 使用 h 函数直接渲染组件
                        const vnode = h(MentionItem, {
                            item: {
                                id: item.id || '',
                                text: item.text || item.id || ''
                            }
                        });

                        // 将虚拟节点渲染到 div 中
                        render(vnode, div);

                        return div;
                    }
                },
                {
                    marker: '/',
                    feed: mcpFeedItems,
                    itemRenderer: item => {
                        const div = document.createElement('div');

                        // 使用 h 函数直接渲染组件
                        const vnode = h(McpItem, {
                            item: {
                                id: item.id || '',
                                text: item.text || item.id || ''
                            }
                        });

                        // 将虚拟节点渲染到 div 中
                        render(vnode, div);

                        return div;
                    }
                }
            ]
        }
        ,
        language: 'cn',
        codeBlock: {
            languages: [
                { language: 'plaintext', label: 'Plain text' }, // The default language.
                { language: 'c', label: 'C' },
                { language: 'cpp', label: 'C++' },
                { language: 'go', label: 'GO' },
                { language: 'java', label: 'Java' },
                { language: 'python', label: 'Python' },
                { language: 'php', label: 'PHP' },
                { language: 'sh', label: 'Shell' },
                { language: 'html', label: 'HTML' },
                { language: 'css', label: 'CSS' },
                { language: 'javascript', label: 'JavaScript' },
                { language: 'typescript', label: 'TypeScript' },
                { language: 'sql', label: 'SQL' },
                { language: 'json', label: 'Json' },
                { language: 'xml', label: 'XML' }
            ]
        },
    })
}

async function loadMcpItemsFromDB(): Promise<MentionFeedItem[]> {
    try {

        // 获取 MCP 列表
        const result = await getMcpList();
        const mcpItems: MentionFeedItem[] = result.map(mcp => ({
            id: `/${mcp.name}`,
            text: `/${mcp.name}`,
            type: ExtensionType.Mcp,
            mcp: {
                id: mcp.id,
                name: mcp.name,
            }
        }));
        return mcpItems;
    } catch (error) {
        console.error('CKEditor MCP: 从服务器加载 MCP 数据失败:', error);
        return [];
    }
}

// 从IndexedDB加载所有@前缀的数据项
async function loadMentionItemsFromDB(): Promise<MentionFeedItem[]> {
    try {
        // 初始化默认数据（以防IndexedDB加载失败）
        let mentionItems: MentionFeedItem[] = [];

        // 打开数据库
        const db = await initIndexedDB();
        const transaction = db.transaction(['mentionItems'], 'readonly');
        const store = transaction.objectStore('mentionItems');

        // 获取所有键
        const allKeysRequest = store.getAllKeys();

        return new Promise((resolve) => {
            allKeysRequest.onsuccess = async () => {
                const keys = allKeysRequest.result as string[];
                // console.log('CKEditor Mention: 从IndexedDB获取到的所有键:', keys);

                // 过滤出以@开头的键
                const mentionKeys = keys.filter(key => typeof key === 'string' && key.startsWith('@'));

                if (mentionKeys.length === 0) {
                    // console.log('CKEditor Mention: 未找到以@开头的键');
                    resolve(mentionItems);
                    return;
                }

                // 为每个键加载数据
                for (const key of mentionKeys) {
                    const getRequest = store.get(key);

                    await new Promise<void>(resolveKey => {
                        getRequest.onsuccess = () => {
                            const data = getRequest.result;
                            if (Array.isArray(data)) {
                                // console.log(`CKEditor Mention: 为键 ${key} 加载了 ${data.length} 个项目`);
                                mentionItems = [...mentionItems, ...data];
                            }
                            resolveKey();
                        };

                        getRequest.onerror = () => {
                            console.error(`CKEditor Mention: 无法加载键 ${key} 的数据`);
                            resolveKey();
                        };
                    });
                }

                // console.log(`CKEditor Mention: 总共加载了 ${mentionItems.length} 个提及项目`);
                resolve(mentionItems);
            };

            allKeysRequest.onerror = () => {
                console.error('CKEditor Mention: 获取IndexedDB键失败');
                resolve(mentionItems);
            };

            transaction.oncomplete = () => {
                db.close();
            };
        });
    } catch (error) {
        console.error('CKEditor Mention: 从IndexedDB加载数据失败:', error);
        return [];
    }
}

// 初始化IndexedDB
async function initIndexedDB() {
    return new Promise<IDBDatabase>((resolve, reject) => {
        const request = indexedDB.open('AiBotEditorDB', 1);

        request.onerror = (event) => {
            console.error('CKEditor Mention: IndexedDB打开失败:', event);
            reject(new Error('无法打开IndexedDB'));
        };

        request.onsuccess = (event) => {
            const db = (event.target as IDBOpenDBRequest).result;
            resolve(db);
        };

        request.onupgradeneeded = (event) => {
            const db = (event.target as IDBOpenDBRequest).result;
            if (!db.objectStoreNames.contains('mentionItems')) {
                db.createObjectStore('mentionItems');
            }
        };
    });
}

/*
 * @description 编辑器At 列表渲染列表项
 */
function customItemRenderer(item) {
    let insert = null
    insert = document.createElement("div")
    insert.appendChild(document.createTextNode(item.id))
    return insert;
}


