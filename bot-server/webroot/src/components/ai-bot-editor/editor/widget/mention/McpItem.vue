<template>
  <a-tag>
    <span class="mention-prefix icon iconfont moxing"></span>
    <span class="mention-text">{{item.text.replace(/^\//, '') }}</span>
  </a-tag>
</template>
<script setup lang="ts">

interface McpItem {
  id: string;
  text: string;
}

const props = defineProps<{
  item: McpItem;
  active?: boolean;
}>();

const emit = defineEmits<{
  (e: 'select', item: McpItem): void;
}>();

const selectItem = () => {
  emit('select', props.item);
};
</script>


<style scoped>
.mention-item {
  padding: 4px 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  border-radius: 2px;
  margin: 1px 0;
  transition: all 0.2s ease;
  height: 22px;
  line-height: 22px;
}


.mention-prefix {
  font-weight: 600;
  margin-right: 2px;
  font-size: 12px;
}

.mention-text {
  font-size: 12px;
  color: #333;
}

.is-active {
  /* 移除浅色背景 */
  background-color: transparent;
}
</style>