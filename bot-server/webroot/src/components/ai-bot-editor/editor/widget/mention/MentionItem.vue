<template>
   <a-tag
      :title="item.text.replace(/^@/, '')"
      class="mention-item"
      :class="{ 'is-active': active }"
      @click="selectItem"
    >
      <div class="flex items-center text-ellipsis mention-text-wrapper">
        <span class="mention-prefix">@</span>
        <span class="mention-text">{{ item.text.replace(/^@/, '') }}</span>
      </div>
    </a-tag>
</template>
<script setup lang="ts">
import { theme } from "ant-design-vue";

const { useToken } = theme;
const { token } = useToken();
interface MentionItem {
  id: string;
  text: string;
}

const props = defineProps<{
  item: MentionItem;
  active?: boolean;
}>();

const emit = defineEmits<{
  (e: 'select', item: MentionItem): void;
}>();

const selectItem = () => {
  emit('select', props.item);
};
</script>
<style scoped>
.mention-item {
  padding: 0 5px !important;
  margin: 0 !important;
  height: 22px;
  line-height: 22px;
}

.mention-text-wrapper {
  min-width: 0;
  flex: 1;
  width: 100%;
  overflow: hidden;
  display: flex;
  align-items: center;
}

.mention-prefix {
  font-weight: 600;
  font-size: 12px;
  flex-shrink: 0;
  margin-right: 4px;
}

.mention-text {
  font-size: 12px;
  text-overflow: ellipsis !important;
  overflow: hidden !important;
  white-space: nowrap !important;
  display: block;
  max-width: 100%;
}


</style>