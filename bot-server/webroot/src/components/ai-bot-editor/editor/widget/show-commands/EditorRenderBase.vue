<template>
 <slot></slot>
</template>

<script setup lang="ts">
</script>

<style scoped>
:global(.source-mention.ck-widget) {
  outline: none;
  border-radius: 5px !important;
}

:global(.source-mention.ck-widget:hover) {
  outline: none;
}


:global(.source-mention.ck-widget.ck-widget_selected), :global(.source-mention.ck-widget.ck-widget_selected:hover) {
  outline: none;
}


:global(.mcp-mention.ck-widget) {
  outline: none;
  border-radius: 5px !important;
}

:global(.mcp-mention.ck-widget:hover) {
  outline: none;
}


:global(.mcp-mention.ck-widget.ck-widget_selected), :global(.mcp-mention.ck-widget.ck-widget_selected:hover) {
  outline: none;
}
</style>
