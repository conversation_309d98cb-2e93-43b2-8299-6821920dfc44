<template>
  <EditorRenderBase>
    <div
      class="source-wrapper source-data"
      :title="formattedName"
      :data-path="source.name"
      :data-id="source.id"
      :data-source="source.url"
      :data-type="source.dataType"
      :data-dir="source.name"
  >
    <a-tag
      class="source-tag"
      ref="sourceItemEl"
      :bordered="false"

    >
      <span class="source-prefix">@</span>
      <!-- <img v-if="fileIcon" :src="fileIcon" class="source-icon" alt="file icon"/> --> 
      <span class="source-name">
        {{ formattedName }}
      </span>
    </a-tag>
  </div>
  </EditorRenderBase>

</template>

<script setup lang="ts">
import {computed, onMounted, ref, watch} from 'vue';
import {
  SourceData
} from "@/components/ai-bot-editor/editor/editor-plugins/ShowCommandsPlugin/custom-extensions/extensions";
// import {ipcRenderer} from 'electron';
import {theme } from 'ant-design-vue';

import EditorRenderBase from '@/components/ai-bot-editor/editor/widget/show-commands/EditorRenderBase.vue';

// 导入预制图标
// import defaultFileIcon from '@/assets/file-icon/file.png';
// import docxIcon from '@/assets/file-icon/docx1.png';
// import pdfIcon from '@/assets/file-icon/pdfwenjian.png';
// import rarIcon from '@/assets/file-icon/rar.png';
// import xlsxIcon from '@/assets/file-icon/xlsx.png';
// import zipIcon from '@/assets/file-icon/zip.png';

const {useToken} = theme;
const {token} = useToken();
const props = defineProps<{
  source: SourceData
}>();

// 文件图标状态
const fileIcon = ref<string>('');

// 根据文件扩展名获取对应图标
const getFileIconByExtension = (filePath: string): string => {
  // if (!filePath) return defaultFileIcon;

  // 获取文件扩展名
  const extension = filePath.split('.').pop()?.toLowerCase() || '';

  // 根据扩展名返回对应图标
  switch (extension) {
    case 'doc':
    case 'docx':
      // return docxIcon;
    case 'pdf':
      // return pdfIcon;
    case 'rar':
      // return rarIcon;
    case 'xls':
    case 'xlsx':
      // return xlsxIcon;
    case 'zip':
      // return zipIcon;
    default:
      // return defaultFileIcon;
      return '';
  }
};

// 通过Electron获取文件图标
const getElectronFileIcon = async (filePath: string): Promise<string | null> => {
  // try {
  //   if (filePath) {
  //     const result = await ipcRenderer.invoke('get-file-icon', filePath);
  //     if (result.success) {
  //       return result.data;
  //     }
  //   }
  //   return null;
  // } catch (error) {
  //   console.error('加载文件图标失败:', error);
  //   return null;
  // }
  return null;
};

// 加载文件图标 - 先尝试使用Electron获取，失败则使用扩展名判断
const loadFileIcon = async () => {
  let filePath = props.source.id
  console.log(filePath)
  filePath = filePath.substring(0)
  console.log(filePath)
  if (filePath) {
    try {
      // 先尝试通过Electron获取图标
      const electronIcon = await getElectronFileIcon(filePath);
      if (electronIcon) {
        fileIcon.value = electronIcon;
      } else {
        // 如果Electron获取失败，使用扩展名判断
        fileIcon.value = getFileIconByExtension(filePath);
      }
    } catch (error) {
      // 出错时使用扩展名判断
      fileIcon.value = getFileIconByExtension(filePath);
    }
  }
};

const url = ref<string>('');

// 添加URL路径提取方法
const extractUrlPath = (url: string): string => {
  try {
    if (!url) return '';
    // 如果是文件路径格式，直接返回
    if (url.includes('\\') || url.startsWith('/')) {
      return url;
    }
    // 创建URL对象
    const urlObj = new URL(url);
    // 获取端口号后的路径部分（pathname + search + hash）
    return decodeURIComponent(urlObj.pathname + urlObj.search + urlObj.hash);
  } catch (error) {
    console.error('URL解析错误:', error);
    return url; // 如果解析失败，返回原始URL
  }
};

// 组件挂载时加载图标
onMounted(() => {
  loadFileIcon();
});

// 优化后的路径格式化函数 - 保留头尾，中间省略
const formattedName = computed(() => {
  const name = props.source.name;

  // 获取最后一个分隔符的位置
  const lastSlashIndex = Math.max(
      name.lastIndexOf('/'),
      name.lastIndexOf('\\')
  );

  // 如果找到分隔符，返回最后一部分（文件名），否则返回完整名称
  return lastSlashIndex >= 0 ? name.substring(lastSlashIndex + 1) : name;
});

const showTooltip = ref(false);
const sourceItemEl = ref<HTMLElement | null>(null);
let tooltipTimer: number | null = null;
let hideTimer: number | null = null;

const clearTimers = () => {
  if (tooltipTimer) {
    clearTimeout(tooltipTimer);
    tooltipTimer = null;
  }
  if (hideTimer) {
    clearTimeout(hideTimer);
    hideTimer = null;
  }
};

const handleSourceEnter = () => {
  clearTimers();
  tooltipTimer = window.setTimeout(() => {
    showTooltip.value = true;
  }, 500);
};

const handleSourceLeave = () => {
  clearTimers();
  hideTimer = window.setTimeout(() => {
    showTooltip.value = false;
  }, 200);
};

const handleTooltipEnter = () => {
  clearTimers();
  showTooltip.value = true;
};

const handleTooltipLeave = () => {
  clearTimers();
  showTooltip.value = false;
};

const sourcePath = () => {
  const path = props.source.id;
  return path.substring(1); // 从索引1开始截取字符串，去掉第一个字符
}

const sourceUrl = computed(() => {
  return props.source.url;
});

// 监控URL变化时更新图标
watch(sourceUrl, (newUrl) => {
  console.log('URL 发生变化:', newUrl);
  loadFileIcon();
});
</script>

<style scoped>
.source-wrapper {
  display: inline-block;
}

.source-tag {
  display: flex !important;
  align-items: center !important;
  height: 21px !important;
  padding: 0 2px !important;
  border-radius: 5px !important;
  user-select: none !important;
}

.source-tag:hover {
  cursor: default;
}

.source-prefix {
  font-size: 14px;
  margin-right: 2px;
  opacity: 0.6;
}

.source-name {
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-decoration: none;
}

.source-tag:hover .source-name {
  text-decoration: underline;
  text-underline-offset: 2px;
}

.source-icon {
  width: 16px;
  height: 16px;
  margin-right: 2px;
  object-fit: contain;
}

.source-tooltip-content {
  font-size: 13px;
  padding: 4px 0;
  min-width: 150px;
  word-break: break-all;
  cursor: text;
}


</style>

