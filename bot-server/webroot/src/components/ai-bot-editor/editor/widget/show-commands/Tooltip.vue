<template>
  <div v-if="modelValue" 
       class="tooltip" 
       ref="tooltipEl"
       @mouseenter="$emit('update:modelValue', true)"
       @mouseleave="$emit('update:modelValue', false)">
    <slot></slot>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue';

const props = defineProps<{
  modelValue: boolean;
  targetEl: HTMLElement | null;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void;
}>();

const tooltipEl = ref<HTMLElement | null>(null);

const updatePosition = () => {
  if (!tooltipEl.value || !props.targetEl) return;
  
  const sourceRect = props.targetEl.getBoundingClientRect();
  const tooltipRect = tooltipEl.value.getBoundingClientRect();
  const viewportWidth = window.innerWidth;
  const viewportHeight = window.innerHeight;

  // 计算各个方向的可用空间
  const spaceTop = sourceRect.top;
  const spaceBottom = viewportHeight - (sourceRect.top + sourceRect.height);

  let left = 0;
  let top = 0;
  let arrowPosition = '';

  // 优先显示在下方
  if (spaceBottom >= tooltipRect.height + 8 || spaceBottom > spaceTop) {
    top = sourceRect.top + sourceRect.height + 8;
    arrowPosition = 'top';
  } else {
    // 否则显示在上方
    top = sourceRect.top - tooltipRect.height - 8;
    arrowPosition = 'bottom';
  }

  // 水平居中对齐
  left = sourceRect.left + (sourceRect.width - tooltipRect.width) / 2;

  // 确保不超出视口左右边界
  if (left < 8) {
    left = 8;
  } else if (left + tooltipRect.width > viewportWidth - 8) {
    left = viewportWidth - tooltipRect.width - 8;
  }

  tooltipEl.value.style.left = `${left}px`;
  tooltipEl.value.style.top = `${top}px`;
  tooltipEl.value.dataset.position = arrowPosition;
};

watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    requestAnimationFrame(updatePosition);
  }
});

onMounted(() => {
  window.addEventListener('scroll', updatePosition, true);
  window.addEventListener('resize', updatePosition);
});

onUnmounted(() => {
  window.removeEventListener('scroll', updatePosition, true);
  window.removeEventListener('resize', updatePosition);
});
</script>

<style scoped>
.tooltip {
  position: fixed;
  background: var(--color-bg);
  border-radius: 6px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  padding: 8px;
  z-index: 99999;
  width: max-content;
  max-width: 300px;
  animation: tooltip-fade 0.2s ease;
  border: 1px solid var(--border-color);
  backdrop-filter: none;
  -webkit-backdrop-filter: none;
  background-color: #fff;
}

@keyframes tooltip-fade {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

</style> 