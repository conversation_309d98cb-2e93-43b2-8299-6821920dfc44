<script setup lang="ts">


import {
  CommandData
} from "@/components/ai-bot-editor/editor/editor-plugins/ShowCommandsPlugin/custom-extensions/extensions";

defineProps<{
  command?: CommandData
}>();
</script>

<template>
  <div class="command-item" @click="command?.execute">
    <div class="command-content">
      <div class="command-name">{{ command?.name }}</div>
      <div class="command-desc" v-if="command?.description">{{ command.description }}</div>
    </div>
    <div class="command-shortcut" v-if="command?.shortcut">{{ command.shortcut }}</div>
  </div>
</template>

<style scoped>
.command-item {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 4px;
  gap: 8px;
  user-select: none;
  min-height: 28px;
}

.command-content {
  flex: 1;
  min-width: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.command-name {
  font-size: 13px;
  color: var(--text-primary, #333);
  white-space: nowrap;
}

.command-desc {
  font-size: 12px;
  color: var(--text-secondary, #666);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.command-shortcut {
  font-size: 11px;
  color: var(--text-secondary, #666);
  padding: 1px 4px;
  background-color: var(--shortcut-bg, rgba(0, 0, 0, 0.05));
  border-radius: 3px;
  white-space: nowrap;
}

</style>