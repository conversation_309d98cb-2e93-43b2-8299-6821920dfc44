<template>
  <EditorRenderBase>
    <div class="mcp-wrapper mcp-data" :title="mcp.name" :data-id="mcp.id">
      <a-tag class="mcp-tag" :bordered="false">
        <span class="mcp-prefix icon iconfont moxing"></span>
        <span class="mcp-name">
          {{ mcp.name }}
        </span>
      </a-tag>
    </div>
  </EditorRenderBase>
</template>

<script setup lang="ts">
import {onMounted} from 'vue';
import {
  McpData
} from "@/components/ai-bot-editor/editor/editor-plugins/ShowCommandsPlugin/custom-extensions/extensions";
import EditorRenderBase from '@/components/ai-bot-editor/editor/widget/show-commands/EditorRenderBase.vue';


const props = defineProps<{
  mcp: McpData
}>();


// 组件挂载时加载图标
onMounted(() => {

});


</script>

<style scoped>
.mcp-wrapper {
  display: inline-block;
  margin-left: 2px;
}
.mcp-tag {
  margin: 0 !important;
  margin-left: 2px !important;
  margin-right: 2px !important;
}
</style>
