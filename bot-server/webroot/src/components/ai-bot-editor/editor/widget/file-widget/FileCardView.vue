<template>
  <div class="edit-file" @dblclick="handleDoubleClick">
    <div class="close-button" @click.stop="handleClose">
      <svg viewBox="0 0 24 24" width="14" height="14">
        <path fill="currentColor" d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
      </svg>
    </div>
    <div class="card-content">
      <div style="max-width: 120px">
        <div style="overflow-x: hidden">
          {{ name }}
        </div>
        <div>
          {{ getSize }}
        </div>
      </div>
      <div class="file-icon-wrapper">
        <div class="file-icon" :style="{
          backgroundImage: `url(${icon})`,
        }"></div>
      </div>
    </div>
    <div class="completed-mark" v-if="currentStatus === 'completed'">
      <svg viewBox="0 0 24 24" width="16" height="16">
        <path fill="#ffffff" d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
      </svg>
    </div>
    <div class="progress-wrapper" v-if="!isUploaded && currentStatus !== 'completed'">
      <div class="status-info">
        <div class="status-text" :class="currentStatus">
          {{ statusText }}
          <span v-if="currentStatus === 'uploading'" class="chunk-info">({{ currentChunk }}/{{ totalChunks }})</span>
        </div>
        <div class="progress-percent" v-if="showProgress">{{ progress }}%</div>
      </div>
      <div class="progress-container" v-if="showProgress">
        <div class="progress-bar" :class="currentStatus" :style="{ width: `${progress}%` }"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAiBot } from "@/components/store/bot";
import {computed, onMounted, ref, defineEmits, defineProps} from "vue";
// import { shell, ipcRenderer } from 'electron';

const props = defineProps({
  path: String,
  name: String,
  size: Number,
  type: String,
  status: {
    type: String,
    default: 'preparing',
  },
  isUploaded: {
    type: Boolean,
    default: false
  },
  onClose: {
    type: Function,
    default: () => {}
  }
})

const emit = defineEmits<{
  (e: 'close'): void
  (e: 'upload-complete', result: { success: boolean, file: { name: string, path: string, size: number } }): void
}>()

const currentStatus = ref(props.status)
const progress = ref(0)
const showProgress = computed(() => currentStatus.value !== 'completed')
const bot = useAiBot()

const CHUNK_SIZE = 1024 * 1024 // 1MB 每片
const totalChunks = ref(0)
const currentChunk = ref(0)

const statusText = computed(() => {
  switch (currentStatus.value) {
    case 'preparing':
      return '准备上传'
    case 'uploading':
      return '上传中'
    case 'completed':
      return '已完成'
    default:
      return '准备上传'
  }
})

const simulateUpload = async () => {
  if (!props.size) return

  totalChunks.value = Math.ceil(props.size / CHUNK_SIZE)
  currentChunk.value = 0
  
  currentStatus.value = 'preparing'
  progress.value = 0
  await new Promise(resolve => setTimeout(resolve, 1000))

  currentStatus.value = 'uploading'
  
  for (let i = 0; i < totalChunks.value; i++) {
    currentChunk.value = i + 1
    
    await new Promise(resolve => setTimeout(resolve, 500))
    
    progress.value = Math.floor((currentChunk.value / totalChunks.value) * 100)
    
    console.log(`正在上传分片 ${currentChunk.value}/${totalChunks.value}, 进度: ${progress.value}%`)
  }

  await new Promise(resolve => setTimeout(resolve, 500))
  currentStatus.value = 'completed'
  
  // 触发上传完成事件
  emit('upload-complete', {
    success: true,
    file: {
      name: props.name || '',
      path: props.path || '',
      size: props.size || 0
    }
  })
}

const handleClose = (event: Event) => {
  event.preventDefault()
  emit('close')
}

const getSize = computed(() => {
  if (size) {
    return `${size.toFixed(2)} ${units[i]}`
  }
  return ''
})

const icon = ref('')
const color = ref('black')
const units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
let size = props.size
let i = 0;
while (size >= 1024 && i < units.length - 1) {
  size /= 1024;
  i++;
}

const getSystemIcon = async () => {
  if (props.path) {
    try {
      // 通过 IPC 获取系统图标
      // const iconDataUrl = await ipcRenderer.invoke('get-file-icon', props.path)
      // if (iconDataUrl) {
      //   icon.value = iconDataUrl
      // } else {
        setDefaultIcon()
      // }
    } catch (error) {
      console.error('获取系统图标失败:', error)
      setDefaultIcon()
    }
  } else {
    setDefaultIcon()
  }
}

const setDefaultIcon = () => {
  if (props.name) {
    let suffix = props.name.substr(props.name.lastIndexOf('.') + 1)
    switch (suffix) {
      case "pdf":
        icon.value = '/file-icon/pdfwenjian.png'
        break;
      case "xlsx":
        icon.value = '/file-icon/xlsx.png'
        break;
      case "docx":
        icon.value = '/file-icon/docx1.png'
        break;
      case "zip":
        icon.value = '/file-icon/zip.png'
        break;
      case "rar":
        icon.value = '/file-icon/rar.png'
      default:
        icon.value = '/file-icon/file.png'
        break;
    }
  }
}

function handleDoubleClick() {
  if (props.path) {
    // shell.showItemInFolder(props.path);
  }
}

onMounted(() => {
  getSystemIcon()

  if (props.isUploaded) {
    currentStatus.value = 'completed'
    progress.value = 100
  } else {
    simulateUpload()
  }
  
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      mutation.removedNodes.forEach((node) => {
        if (node.contains && node.contains(document.querySelector('.progress-wrapper'))) {
          if (currentStatus.value !== 'completed') {
            console.log('上传被中断 (节点被移除):', {
              文件路径: props.path,
              文件名: props.name,
              上传进度: `${progress.value}%`,
              当前分片: `${currentChunk.value}/${totalChunks.value}`
            })
          }
          observer.disconnect()
        }
      })
    })
  })

  observer.observe(document.body, {
    childList: true,
    subtree: true
  })
})
</script>

<style scoped>
.edit-file {
  width: 200px;
  height: 60px;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1;
  border-radius: 8px;
  border: 1px solid v-bind('bot.theme === "dark" ? "rgba(255, 255, 255, 0.1)" : "rgba(0, 0, 0, 0.1)"');
  box-shadow: v-bind('bot.theme === "dark" ? "0 2px 8px rgba(0, 0, 0, 0.2)" : "0 2px 8px rgba(0, 0, 0, 0.05)"');
  transition: all 0.3s ease;
  cursor: pointer;
}

.card-content {
  flex: 1;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px;
  color: v-bind('bot.theme === "dark" ? "#e0e0e0" : "#333"');
}

.file-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  z-index: 2;
}

.file-icon {
  width: 32px;
  height: 32px;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  image-rendering: -webkit-optimize-contrast;  /* 提高图标清晰度 */
  transform: translateZ(0);  /* 启用硬件加速 */
}

.edit-file:hover {
  background-color: v-bind('bot.theme === "dark" ? "rgba(255, 255, 255, 0.05)" : "rgba(37, 32, 32, 0.05)"');
  border-color: v-bind('bot.theme === "dark" ? "rgba(255, 255, 255, 0.2)" : "rgba(0, 0, 0, 0.15)"');
  box-shadow: v-bind('bot.theme === "dark" ? "0 4px 12px rgba(0, 0, 0, 0.3)" : "0 4px 12px rgba(0, 0, 0, 0.08)"');
}

.progress-wrapper {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: v-bind('bot.theme === "dark" ? "rgba(30, 30, 30, 0.95)" : "rgba(255, 255, 255, 0.95)"');
  padding: 4px 8px;
  border-radius: 0 0 8px 8px;
  border-top: 1px solid v-bind('bot.theme === "dark" ? "rgba(255, 255, 255, 0.1)" : "rgba(0, 0, 0, 0.05)"');
  box-shadow: v-bind('bot.theme === "dark" ? "0 -1px 3px rgba(0, 0, 0, 0.2)" : "0 -1px 3px rgba(0, 0, 0, 0.05)"');
  backdrop-filter: blur(4px);
}

.status-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2px;
}

.status-text {
  font-size: 12px;
  color: v-bind('bot.theme === "dark" ? "#aaa" : "#666"');
}

.progress-percent {
  font-size: 12px;
  color: v-bind('bot.theme === "dark" ? "#aaa" : "#666"');
  font-family: monospace;
}

.status-text.uploading {
  color: v-bind('bot.theme === "dark" ? "#81c784" : "#4CAF50"');
}

.status-text.completed {
  color: v-bind('bot.theme === "dark" ? "#64b5f6" : "#2196F3"');
}

.progress-container {
  width: 100%;
  height: 3px;
  background-color: v-bind('bot.theme === "dark" ? "#333" : "#f0f0f0"');
  border-radius: 1.5px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  transition: width 0.1s linear;
}

.progress-bar.preparing {
  background: linear-gradient(90deg, #FFC107, #FFE082);
}

.progress-bar.uploading {
  background: linear-gradient(90deg, #4CAF50, #8BC34A);
}

.progress-bar.completed {
  background: linear-gradient(90deg, #2196F3, #64B5F6);
}

.completed-mark {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 16px;
  height: 16px;
  background-color: #52c41a;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 99999;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.completed-mark svg {
  width: 12px;
  height: 12px;
}

.close-button {
  position: absolute;
  bottom: 4px;
  right: 4px;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ff4d4f;
  border-radius: 50%;
  cursor: pointer;
  opacity: 0;
  transition: all 0.2s ease;
  color: #ffffff;
  z-index: 99998;
}

.chunk-info {
  font-size: 11px;
  opacity: 0.8;
  margin-left: 4px;
}

.edit-file:hover .close-button {
  opacity: 1;
}

.close-button:hover {
  background-color: #ff7875;
  transform: scale(1.1);
}

.close-button:active {
  transform: scale(0.95);
  background-color: #f5222d;
}

.close-button svg {
  width: 12px;
  height: 12px;
}

:deep(.ck-widget) {
  outline: none !important;
}

:deep(.ck-widget.ck-widget_selected) {
  outline: none !important;
}
</style>