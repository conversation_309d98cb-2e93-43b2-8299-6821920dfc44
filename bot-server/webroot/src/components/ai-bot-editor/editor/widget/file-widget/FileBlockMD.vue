<template>
  <div class="edit-file" :path="path" @dblclick="handleDoubleClick" :data-file-uid="props.uid">
    <div class="card-content">
      <div class="file-info">
        <div class="file-name" :title="name">
          {{ name }}
        </div>
      </div>
      <div class="file-icon-wrapper">
        <div ref="icon" class="file-icon"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {useAiBot} from "@/components/store/bot";
import {defineProps, onMounted, ref} from "vue";
// import {shell} from 'electron';

// 生成唯一ID的函数
function generateUID() {
  return 'file_' + Date.now().toString(36) + Math.random().toString(36).substr(2);
}

const props = defineProps({
  uid: String,
  path: String,
  name: String,
})

const bot = useAiBot()
const icon = ref<HTMLElement | null>(null)

function handleDoubleClick() {
  if (props.path) {
    // shell.showItemInFolder(props.path);
  }
}

onMounted(() => {
  // 不再需要在这里获取图标
  console.log('File block mounted with uid:', props.uid);
})
</script>

<style scoped>
.edit-file {
  width: 200px;
  height: 60px;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1;
  border-radius: 6px;
  background-color: #ffffff;
  border: 1px solid rgba(0, 0, 0, 0.08);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
  transition: all 0.2s ease;
  cursor: pointer;
  margin: 4px 0;
  overflow: hidden;
}

.card-content {
  flex: 1;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 12px;
  color: #000000;
  background-color: #ffffff;
}

.file-info {
  flex: 1;
  min-width: 0;
  padding-right: 12px;
  background-color: transparent;
}

.file-name {
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
  font-weight: 450;
  color: #000000;
  line-height: 1.4;
  letter-spacing: 0.2px;
  background-color: transparent;
}

.edit-file:hover {
  background-color: #ffffff;
  border-color: rgba(0, 0, 0, 0.12);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
}

[theme="dark"] .edit-file {
  background-color: #1a1a1a;
  border-color: rgba(255, 255, 255, 0.08);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

[theme="dark"] .card-content {
  color: #e0e0e0;
  background-color: #1a1a1a;
}

[theme="dark"] .file-name {
  color: #e0e0e0;
}

[theme="dark"] .edit-file:hover {
  background-color: #1a1a1a;
  border-color: rgba(255, 255, 255, 0.15);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.file-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  z-index: 2;
  flex-shrink: 0;
}

.file-icon {
  width: 28px;
  height: 28px;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  image-rendering: -webkit-optimize-contrast;
  transform: translateZ(0);
  transition: all 0.2s ease;
}

.icon-loading {
  opacity: 0.6;
}

:deep(.ck-widget) {
  outline: none !important;
}

:deep(.ck-widget.ck-widget_selected) {
  outline: none !important;
}
</style> 