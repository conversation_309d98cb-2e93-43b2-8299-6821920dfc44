<template>
  <div class="workspace-index">
    <div class="workspace-content">
      <!-- 有工作空间路径时显示标签 -->
      <div v-if="workspacePaths.length > 0" class="path-container">
        <!-- 左侧添加按钮 -->
        <div class="left-btn-area">
          <a-tooltip title="添加路径">
            <a-button
                tabindex="-1"
                type="link"
                size="small"
                @click="selectFolder"
                class="add-btn"
            >
              <template #icon>
                <PlusOutlined/>
              </template>
            </a-button>
          </a-tooltip>
        </div>

        <!-- 标签滚动区域 -->
        <div class="path-tags-scroll" ref="scrollContainer" @wheel="handleWheel" @scroll="checkScrollButtons">
          <div class="path-tags" ref="tagsContainer">
            <FilePathTag
                v-for="(path, index) in workspacePaths"
                :key="index"
                :active="activePathIndex === index"
                closable
                @close="removePath(index)"
                @click="handlePathClick(index, path)"
                :folderPath="path"
                :autoLoad="index === workspacePaths.length - 1"
                @filesLoaded="handleFilesLoaded"
            >
              <template #icon-content>
                <FolderOutlined style="margin-right: 2px"/>
              </template>
              <a-tooltip :title="path">
                <span class="path-text">{{ formatPath(path) }}</span>
              </a-tooltip>
            </FilePathTag>
          </div>
        </div>
      </div>

      <!-- 空状态显示提示语和添加按钮 -->
      <div v-else class="empty-workspace">
        <a-tooltip title="添加路径">
          <a-button
              type="link"
              size="small"
              @click="selectFolder"
              class="add-btn"
          >
            <template #icon>
              <PlusOutlined/>
            </template>
          </a-button>
        </a-tooltip>
        <span class="empty-text">添加工作空间</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {nextTick, onMounted, onUnmounted, ref, watch} from 'vue';
import {FolderOutlined, PlusOutlined} from '@ant-design/icons-vue';
// import {ipcRenderer} from 'electron';
import FilePathTag from './FilePathTag.vue';
import {theme} from 'ant-design-vue';

const {useToken} = theme;
const {token} = useToken();

// 工作空间路径列表
const workspacePaths = ref<string[]>([]);
// 活动路径索引
const activePathIndex = ref(-1);
// 标签容器引用
const tagsContainer = ref(null);
// 滚动容器引用
const scrollContainer = ref(null);
// 文件树可见性
const isTreeVisible = ref(false);
// 是否显示特定路径的文件树
const isShowingSpecificPath = ref(false);
// 是否显示左右滚动按钮
const showLeftScrollButton = ref(false);
const showRightScrollButton = ref(false);
// 标签容器当前滚动位置
const currentScrollPosition = ref(0);
// 标签容器总宽度
const totalWidth = ref(0);
// 标签容器可见宽度
const visibleWidth = ref(0);
// 每个文件夹中找到的文件
const folderFiles = ref<{ [key: string]: string[] }>({});

// 初始化IndexedDB
const initIndexedDB = async () => {
  return new Promise<IDBDatabase>((resolve, reject) => {
    const request = indexedDB.open('AiBotEditorDB', 1);

    request.onerror = (event) => {
      console.error('IndexedDB打开失败:', event);
      reject(new Error('无法打开IndexedDB'));
    };

    request.onsuccess = (event) => {
      const db = (event.target as IDBOpenDBRequest).result;
      resolve(db);
    };

    request.onupgradeneeded = (event) => {
      const db = (event.target as IDBOpenDBRequest).result;
      // 创建存储文件路径的对象仓库
      if (!db.objectStoreNames.contains('mentionItems')) {
        db.createObjectStore('mentionItems');
      }
    };
  });
};

// 从IndexedDB读取所有工作空间路径
const loadWorkspacePathsFromDB = async () => {
  try {
    const db = await initIndexedDB();
    const transaction = db.transaction(['mentionItems'], 'readonly');
    const store = transaction.objectStore('mentionItems');

    // 获取所有键
    const getAllKeysRequest = store.getAllKeys();

    getAllKeysRequest.onsuccess = () => {
      const keys = getAllKeysRequest.result as string[];
      console.log('从IndexedDB获取到的所有键:', keys);

      // 过滤出以@开头的键，这些是文件夹路径
      const folderPaths = keys
          .filter(key => typeof key === 'string' && key.startsWith('@'))
          .map(key => key.substring(1)); // 去除@前缀

      console.log('处理后的文件夹路径:', folderPaths);

      // 设置工作空间路径
      if (folderPaths.length > 0) {
        workspacePaths.value = folderPaths;
      }
    };

    getAllKeysRequest.onerror = (event) => {
      console.error('获取IndexedDB键失败:', event);
    };

    transaction.oncomplete = () => {
      db.close();
    };
  } catch (error) {
    console.error('从IndexedDB加载工作空间路径失败:', error);
  }
};

// 检查滚动状态
const checkScrollButtons = () => {
  if (!scrollContainer.value) return;

  // 获取当前滚动位置
  currentScrollPosition.value = scrollContainer.value.scrollLeft;
  // 总内容宽度
  totalWidth.value = scrollContainer.value.scrollWidth;
  // 可见宽度
  visibleWidth.value = scrollContainer.value.clientWidth;

  // 如果滚动位置大于0，显示左滚动按钮
  showLeftScrollButton.value = currentScrollPosition.value > 0;
  // 如果有未显示的内容，显示右滚动按钮
  showRightScrollButton.value = totalWidth.value > (currentScrollPosition.value + visibleWidth.value);
};

// 处理鼠标滚轮事件，实现横向滚动
const handleWheel = (event: WheelEvent) => {
  if (!scrollContainer.value) return;

  // 阻止默认滚动行为
  event.preventDefault();

  // 获取滚动方向和距离
  const delta = event.deltaY || event.deltaX;

  // 计算新的滚动位置
  const scrollAmount = 50; // 每次滚动的像素值
  const direction = delta > 0 ? 1 : -1; // 确定滚动方向
  const newPosition = Math.max(
      0,
      Math.min(
          scrollContainer.value.scrollLeft + (direction * scrollAmount),
          scrollContainer.value.scrollWidth - scrollContainer.value.clientWidth
      )
  );

  // 平滑滚动到新位置
  scrollContainer.value.scrollTo({
    left: newPosition,
    behavior: 'smooth'
  });
};

// 处理文件加载完成事件
const handleFilesLoaded = (files: string[], path?: string) => {
  const folderPath = path || workspacePaths.value[workspacePaths.value.length - 1];
  if (folderPath) {
    folderFiles.value[folderPath] = files;
    console.log(`加载了 ${files.length} 个文件，来自 ${folderPath}`);
  }
};

// 监听标签容器的滚动事件
watch(workspacePaths, () => {
  nextTick(() => {
    checkScrollButtons();
  });
}, {deep: true});

// 格式化路径，长路径中间用省略号表示
const formatPath = (path: string) => {
  // 如果路径以@开头，去掉@
  let processedPath = path;
  if (processedPath.startsWith('@')) {
    processedPath = processedPath.substring(1);
  }

  // 路径分隔符 \ 或 /
  const isWindows = processedPath.includes('\\');
  const separator = isWindows ? '\\' : '/';

  // 将路径分割为部分
  const parts = processedPath.split(separator).filter(p => p);

  // 如果路径部分少于4个，直接返回完整路径
  if (parts.length < 4) {
    return processedPath;
  }

  // 保留开头的1个部分和结尾的2个部分
  const start = parts[0];
  const end = parts.slice(-2).join(separator);

  // 构建带省略的新路径
  const formattedPath = isWindows
      ? `${start}${separator}...${separator}${end}`
      : `/${start}/.../${end}`;

  return formattedPath;
};

// 切换文件树显示/隐藏
const toggleFileTree = () => {
  isTreeVisible.value = !isTreeVisible.value;
};

// 处理工作空间路径点击事件
const handlePathClick = (index: number, path: string) => {
  // 如果点击了当前活动路径，则切换显示所有路径/单个路径
  if (activePathIndex.value === index) {
    isShowingSpecificPath.value = !isShowingSpecificPath.value;

    // 如果切换为不显示特定路径，则重置活动路径
    if (!isShowingSpecificPath.value) {
      activePathIndex.value = -1;
    }
  } else {
    // 设置新的活动路径
    activePathIndex.value = index;
    isShowingSpecificPath.value = true;
  }

  // 如果文件树未显示，则显示文件树
  if (!isTreeVisible.value) {
    isTreeVisible.value = true;
  }
};

// 处理文件选择事件
const handleFileSelected = (filePath: string) => {
  console.log('Selected file:', filePath);
};

// 添加新路径
const addPath = (path: string) => {
  if (path && !workspacePaths.value.includes(path)) {
    workspacePaths.value.push(path);

    // 添加新标签后，滚动到最右侧，显示最新添加的标签
    nextTick(() => {
      if (scrollContainer.value) {
        scrollContainer.value.scrollLeft = scrollContainer.value.scrollWidth;
        checkScrollButtons();
      }
    });
  }
};

// 移除路径
const removePath = (index: number) => {
  // 如果移除的是活动路径，重置状态
  if (index === activePathIndex.value) {
    activePathIndex.value = -1;
    isShowingSpecificPath.value = false;
  } else if (index < activePathIndex.value) {
    // 如果移除的路径在活动路径前面，活动路径索引需要减1
    activePathIndex.value--;
  }

  // 获取要删除的路径
  const pathToRemove = workspacePaths.value[index];

  // 从路径列表中移除
  workspacePaths.value.splice(index, 1);

  // 从文件缓存中也移除该路径的文件列表
  if (pathToRemove && folderFiles.value[pathToRemove]) {
    delete folderFiles.value[pathToRemove];
  }

  // 如果移除后没有路径了，隐藏文件树
  if (workspacePaths.value.length === 0) {
    isTreeVisible.value = false;
  }

  // 更新滚动按钮状态
  nextTick(checkScrollButtons);
};

// 直接选择文件夹并添加到工作区
const selectFolder = async () => {
  try {
    // 调用Electron的文件夹选择对话框
    // const result = await ipcRenderer.invoke('open-file-dialog');
    const result = {
      canceled: false,
      filePaths: []
    };

    if (!result.canceled && result.filePaths.length > 0) {
      // 用户选择了文件夹，直接添加到工作区
      addPath(result.filePaths[0]);
    }
  } catch (error) {
    console.error('打开文件夹选择对话框失败:', error);
  }
};

// 初始化和监听
onMounted(async () => {
  // 从IndexedDB加载工作空间路径
  await loadWorkspacePathsFromDB();

  // 初始化后检查滚动状态
  nextTick(() => {
    checkScrollButtons();
  });
});

// 确保在组件卸载时移除事件监听器
onUnmounted(() => {
  // 不需要手动移除事件监听，因为我们使用了v-on指令
});
</script>

<style scoped>
.workspace-index {
  display: flex;
  flex-direction: column;
  width: 100%;
  background-color: transparent;
  padding: 0;
  margin: 0;
  max-width: 100%;
  flex-shrink: 1; /* 允许组件收缩 */
}

.workspace-content {
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;
  max-width: 100%;
}

.path-container {
  display: flex;
  align-items: center;
  position: relative;
  min-height: 24px;
  width: 100%;
  max-width: 100%;
}

.left-btn-area {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-right: 4px;
  margin-right: 4px;
  position: relative;
  z-index: 2;
  flex-shrink: 0; /* 按钮区域不收缩 */
}

.path-tags-scroll {
  flex: 1;
  overflow-x: auto; /* 确保水平滚动可用 */
  overflow-y: hidden; /* 禁止垂直滚动 */
  position: relative;
  max-width: calc(100% - 32px);
  min-width: 0; /* 允许在flex布局中缩小到内容宽度以下 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  -webkit-overflow-scrolling: touch;
}

/* 隐藏滚动容器的滚动条 */
.path-tags-scroll::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
}

.path-tags {
  display: flex;
  flex-wrap: nowrap;
  gap: 4px;
  padding: 0 4px 2px 0;
  width: max-content; /* 确保容器宽度适应所有子元素 */
  min-height: 22px;
  -webkit-overflow-scrolling: touch;
  flex-shrink: 0; /* 防止内容被压缩 */
}

.path-tags::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
}

.add-btn, .tree-toggle-btn {
  padding: 0;
  height: 20px;
  width: 20px;
  font-size: 12px;
  border: none;
  box-shadow: none;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  z-index: 2;
  border-radius: 4px;
}


.path-text {
  display: inline-block;
  max-width: 120px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  pointer-events: none; /* 防止文本被单独选中 */
}

.empty-workspace {
  display: flex;
  align-items: center;
  color: v-bind('token.colorTextQuaternary');
  font-size: 10px;
  width: 100%;
  padding: 0 8px;
}

.empty-text {
  font-style: italic;
  margin-right: 4px;
}

.file-tree-container {
  border-top: 1px solid #f0f0f0;
  margin-top: 4px;
  background-color: white;
  max-height: 300px;
  overflow: auto;
  position: relative;
  transition: max-height 0.3s ease;
}
</style> 