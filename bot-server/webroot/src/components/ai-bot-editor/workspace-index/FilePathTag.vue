<template>
  <div class="file-path-tag" :style="progressStyle" @click="$emit('click', $event)">
    <slot name="icon">
      <div class="tag-icon">
        <slot name="icon-content"></slot>
      </div>
    </slot>

    <div class="tag-content">
      <slot></slot>
    </div>

    <div v-if="closable" class="close-icon" @click.stop="$emit('close', $event)">
      <span class="close-x">×</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import {computed, onMounted, onUnmounted, ref, watch} from 'vue';
// import {ipcRenderer} from 'electron';

// 导入MentionFeedObjectItem类型和ExtensionType枚举
import {ExtensionType} from '../editor/editor-plugins/ShowCommandsPlugin/custom-extensions/extensions';
import {theme} from 'ant-design-vue';
import {DataType} from "@/components/common/model/enum";

const {useToken} = theme;
const {token} = useToken();
const props = defineProps({
  active: {
    type: Boolean,
    default: false
  },
  closable: {
    type: Boolean,
    default: true
  },
  folderPath: {
    type: String,
    default: ''
  },
  autoLoad: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['click', 'close', 'filesLoaded']);

// 内部状态 - 所有加载状态都由组件内部维护
const internalProgress = ref(0);
const isLoading = ref(false);
const loadedFiles = ref<string[]>([]);
const hasScanned = ref(false);

// 进度样式计算
const progressStyle = computed(() => {
  // 只使用内部维护的进度状态
  if (isLoading.value && internalProgress.value < 100) {
    return {
      backgroundImage: `linear-gradient(to right, #bae0ff ${internalProgress.value}%, #e6f4ff ${internalProgress.value}%)`,
      position: 'relative' as const,
      overflow: 'hidden' as const
    };
  }

  return {};
});

// 添加IndexedDB相关函数
const initIndexedDB = async () => {
  return new Promise<IDBDatabase>((resolve, reject) => {
    const request = indexedDB.open('AiBotEditorDB', 1);

    request.onerror = (event) => {
      console.error('IndexedDB打开失败:', event);
      reject(new Error('无法打开IndexedDB'));
    };

    request.onsuccess = (event) => {
      const db = (event.target as IDBOpenDBRequest).result;
      resolve(db);
    };

    request.onupgradeneeded = (event) => {
      const db = (event.target as IDBOpenDBRequest).result;
      // 创建存储文件路径的对象仓库
      if (!db.objectStoreNames.contains('mentionItems')) {
        db.createObjectStore('mentionItems');
      }
    };
  });
};

const saveMentionItemsToDB = async (items: any[], key: string) => {
  try {
    const db = await initIndexedDB();
    const transaction = db.transaction(['mentionItems'], 'readwrite');
    const store = transaction.objectStore('mentionItems');
    const request = store.put(items, key);

    transaction.oncomplete = () => {
      db.close();
    };
  } catch (error) {
    // 保留错误日志以便调试
    console.error('保存到IndexedDB失败:', error);
  }
};


// 简化文件类型映射配置
const fileTypeConfig = {
  // 图片文件
  image: {
    extensions: ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg', '.tiff', '.ico', '.raw'],
    type: DataType.Image
  },
  // 视频文件
  video: {
    extensions: ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv', '.webm', '.m4v'],
    type: DataType.Video
  },
  // 文档文件 (包含各种文档格式)
  document: {
    extensions: [
      '.txt', '.md', '.markdown', '.rtf',
      '.doc', '.docx', '.odt', '.pdf',
      '.ppt', '.pptx', '.odp',
      '.epub', '.mobi'
    ],
    type: DataType.Document
  },
  // Excel文件
  excel: {
    extensions: ['.xls', '.xlsx', '.csv'],
    type: DataType.Excel
  }
};

// 获取文件类型的函数
const getFileType = (filePath: string): DataType => {
  const ext = filePath.substring(filePath.lastIndexOf('.')).toLowerCase();

  for (const [_, config] of Object.entries(fileTypeConfig)) {
    if (config.extensions.includes(ext)) {
      return config.type;
    }
  }

  return DataType.Other;
};

// 修改扫描文件夹函数
const scanFolder = async (folderPath: string) => {
  if (!folderPath) return;

  try {
    // const files = await ipcRenderer.invoke('scan-readable-files', folderPath);
    const files = [];
    loadedFiles.value = files;

    const mentionItems = files.map((file) => {
      const fileName = file.split('/').pop() || file.split('\\').pop() || file;
      const id = `@${file}`;
      const dataType = getFileType(file);

      return {
        id: id,
        text: file,
        displayText: fileName,
        searchText: file,
        type: ExtensionType.Source,
        dataType: dataType,
        source: {
          id: id,
          name: file,
          dir: file,
          dataType: dataType
        }
      };
    });

    // 按简化后的类型统计文件
    const typeStats = mentionItems.reduce((acc, item) => {
      acc[item.dataType] = (acc[item.dataType] || 0) + 1;
      return acc;
    }, {} as Record<DataType, number>);

    await saveMentionItemsToDB(mentionItems, `@${folderPath}`);
    emit('filesLoaded', files, folderPath);
    hasScanned.value = true;

    return files;
  } catch (error) {
    console.error('扫描文件失败:', error);
    return [];
  }
};

// 监听路径变化
watch(() => props.folderPath, (newPath, oldPath) => {
  if (newPath && newPath !== oldPath && !hasScanned.value) {
    scanFolder(newPath);
  }
});

// 初始化时自动加载文件
onMounted(() => {
  if (props.folderPath) {
    scanFolder(props.folderPath);
  }
});

// 清理工作
onUnmounted(() => {
  // 移除不必要的日志
});

// 暴露方法给父组件
defineExpose({
  scanFolder,
  getLoadedFiles: () => loadedFiles.value
});
</script>

<style scoped>
.file-path-tag {
  display: inline-flex;
  align-items: center;
  margin: 0;
  font-size: 10px;
  line-height: 1.2;
  height: 20px;
  padding: 0 4px;
  border: none;
  flex-shrink: 0;
  cursor: pointer;
  user-select: none;
  white-space: nowrap;
  background-color: v-bind('token.colorPrimaryBg');
  color: v-bind('token.colorPrimaryText');
  border-radius: 2px;
  transition: background-color 0.2s, color 0.2s;
}

.tag-icon {
  display: flex;
  align-items: center;
  margin-right: 4px;
}

.tag-content {
  display: inline-block;
  max-width: 120px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  pointer-events: none;
}

.close-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 4px;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.close-icon:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.close-x {
  font-size: 12px;
  line-height: 1;
  display: block;
}

/* 添加进度动画效果 */
@keyframes pulse {
  0% {
    opacity: 0.7;
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 0.7;
  }
}

.file-path-tag[style*="background-image"]:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: pulse 1.5s infinite;
  pointer-events: none;
}
</style>