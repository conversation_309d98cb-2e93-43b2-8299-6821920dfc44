<template>
  <div class="flex flex-row items-center justify-center">
    <a-button  
      @click="toggleThinkingMode"
      :type="thinkingMode ? 'primary' : 'text'"
      :style="{ cursor: 'pointer' }"
    >
      <template #icon>
        <span class="icon iconfont yishendusikao"></span>
      </template>
    </a-button>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useGptStore } from '@/components/store/gpt';
import { usePlatform } from '@/components/store/platform';
import { useAiPluginStore } from '@/components/store/plugin';
import { message } from 'ant-design-vue';

const ctx = useGptStore();
const platformStore = usePlatform();
const pluginStore = useAiPluginStore();

// 思考模式状态，从store中获取
const thinkingMode = computed(() => ctx.ui.thinkingMode || false);

// 检查模型是否为思考模型
function isThinkingModel(modelName: string): boolean {
  // 定义支持思考模式的模型列表（字符串匹配）
  const thinkingModels = [
    'deepseek-r1',
    'o1-preview',
    'o1-mini'
  ];
  
  // 定义支持思考模式的模型正则表达式
  const thinkingModelPatterns = [
    /^deepseek-r1.*$/i,                    // 匹配所有 deepseek-r1 开头的模型
    /^o1-(preview|mini).*$/i,              // 匹配 o1-preview 或 o1-mini 开头的模型
    /.*-r1(-.*)?$/i,                       // 匹配任何包含 -r1 的模型
    /^claude.*thinking.*$/i,               // 匹配 claude 开头且包含 thinking 的模型
    /^gpt.*thinking.*$/i,                  // 匹配 gpt 开头且包含 thinking 的模型
    /.*reasoning.*$/i,                     // 匹配包含 reasoning 的模型
    /.*思考.*$/i,                          // 匹配包含中文"思考"的模型
    /.*推理.*$/i                           // 匹配包含中文"推理"的模型
  ];
  
  const modelNameLower = modelName.toLowerCase();
  
  // 先检查字符串匹配
  const stringMatch = thinkingModels.some(thinkingModel => 
    modelNameLower.includes(thinkingModel.toLowerCase())
  );
  
  // 再检查正则表达式匹配
  const regexMatch = thinkingModelPatterns.some(pattern => 
    pattern.test(modelName)
  );
  
  return stringMatch || regexMatch;
}

// 检查当前平台是否支持思考模型
function checkThinkingModelSupport(): { supported: boolean, currentPlatform?: any, currentModel?: string } {
  try {
    // 获取当前选中的插件
    const currentPlugin = pluginStore.currentPlugin;
    if (!currentPlugin || !currentPlugin.platformID) {
      return { supported: false };
    }
    
    // 获取当前平台信息
    const currentPlatform = platformStore.platforms.find((p: any) => p.id === currentPlugin.platformID);
    if (!currentPlatform) {
      return { supported: false };
    }
    
    // 获取当前选中的模型
    const currentModel = currentPlatform.settings?.currentModel;
    if (!currentModel) {
      return { supported: false, currentPlatform };
    }
    
    // 检查当前模型是否为思考模型
    const isCurrentModelThinking = isThinkingModel(currentModel);
    
    return {
      supported: isCurrentModelThinking,
      currentPlatform,
      currentModel
    };
  } catch (error) {
    console.error('检查思考模型支持时出错:', error);
    return { supported: false };
  }
}

// 切换思考模式状态
function toggleThinkingMode() {
  // 如果要开启思考模式，先检查当前平台是否支持
  if (!thinkingMode.value) {
    const supportInfo = checkThinkingModelSupport();
    
    if (!supportInfo.supported) {
      let errorMessage = '当前平台没有思考能力，不能打开思考模式';
      
      if (supportInfo.currentPlatform && supportInfo.currentModel) {
        errorMessage = `当前模型 "${supportInfo.currentModel}" 不支持思考模式，请切换到支持思考的模型（如: deepseek-r1 系列）`;
      } else if (supportInfo.currentPlatform) {
        errorMessage = `当前平台 "${supportInfo.currentPlatform.name}" 未配置模型，请先选择支持思考的模型`;
      } else {
        errorMessage = '请先选择支持思考模式的平台和模型';
      }
      
      message.warning({
        content: errorMessage,
        duration: 4,
        key: 'thinking-mode-error' // 使用固定的key避免重复消息
      });
      return;
    }
  }
  
  // 如果检查通过或者是关闭思考模式，则执行切换
  ctx.ui.thinkingMode = !ctx.ui.thinkingMode;
}
</script>

<style scoped>
.thinking-mode-tag {
  transition: all 0.3s;
  user-select: none;
}
</style>