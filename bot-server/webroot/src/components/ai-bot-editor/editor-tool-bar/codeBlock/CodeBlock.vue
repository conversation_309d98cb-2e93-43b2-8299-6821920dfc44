<template>
  <a-dropdown trigger="click">
    <AiIconButton :size="25">
      <span class="icon iconfont code"></span>
    </AiIconButton>
    <template #overlay>
      <a-menu @click="handleMenuClick">
        <a-menu-item key="plaintext">纯文本</a-menu-item>
        <a-menu-item key="go">Go</a-menu-item>
        <a-menu-item key="javascript">JavaScript</a-menu-item>
        <a-menu-item key="python">Python</a-menu-item>
        <a-menu-item key="java">Java</a-menu-item>
        <a-menu-item key="rust">Rust</a-menu-item>
        <a-menu-item key="php">PHP</a-menu-item>
        <a-menu-item key="html">HTML</a-menu-item>
        <a-menu-item key="css">CSS</a-menu-item>
        <a-menu-item key="cpp">C++</a-menu-item>
        <a-menu-item key="sh">Shell</a-menu-item>
        <a-menu-item key="json">JSON</a-menu-item>
        <a-menu-item key="sql">SQL</a-menu-item>
      </a-menu>
    </template>
  </a-dropdown>
</template>

<script setup lang="ts">
import { onMounted } from "vue";
import { useGptStore } from "@/components/store/gpt";
import { useAiBot } from "@/components/store/bot";
import AiIconButton from "@/components/ai-bot-widget/AiIconButton.vue";

const emits = defineEmits({
  insertCodeBlock: (lang) => {
    return true;
  }
})

const ctx = useGptStore()
const bot = useAiBot()

// 处理菜单点击事件
const handleMenuClick = (info) => {
  insertCodeBlock(info.key);
}

function insertCodeBlock(lang: string) {
  emits('insertCodeBlock', lang)
}

onMounted(() => {
})
</script>

<style scoped>
/* 图标基础样式 */
.icon.iconfont {
  font-size: 16px;
  color: inherit;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
}

/* 自定义下拉菜单样式 */
:deep(.ant-dropdown-menu) {
  border-radius: 8px;
  max-height: 300px;
  overflow-y: auto;
  padding: 4px;
  /* 隐藏滚动条但保持滚动功能 - 提高优先级 */
  scrollbar-width: none !important; /* Firefox */
  -ms-overflow-style: none !important; /* IE and Edge */
}

/* 隐藏Webkit浏览器的滚动条 - 增强选择器 */
:deep(.ant-dropdown-menu::-webkit-scrollbar),
:deep(.ant-dropdown-menu *::-webkit-scrollbar) {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
  background: transparent !important;
}

/* 确保滚动容器样式正确 */
:deep(.ant-dropdown) {
  overflow: visible !important;
}

:deep(.ant-dropdown-menu-item),
:deep(.ant-menu-item) {
  padding: 4px 12px !important;
  border-radius: 6px !important;
  margin: 2px !important;
  height: 32px !important;
  line-height: 22px !important;
}

/* 移除下拉菜单箭头 */
:deep(.ant-dropdown-arrow),
:deep(.ant-dropdown-arrow::before) {
  display: none !important;
  content: none !important;
}
</style>