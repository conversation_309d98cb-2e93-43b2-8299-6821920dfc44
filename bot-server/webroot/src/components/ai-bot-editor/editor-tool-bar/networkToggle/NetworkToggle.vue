<template>
  <a-button
    @click="toggleNetwork"
    :type="networkEnabled ? 'primary' : 'text'"
    :style="{ cursor: 'pointer' }"
  >
    <template #icon>
      <span class="icon iconfont hulianwang"></span>
    </template>
  </a-button>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useGptStore } from '@/components/store/gpt';

const ctx = useGptStore();

// 网络搜索状态
const networkEnabled = computed(() => ctx.ui.network);

// 切换网络搜索状态
function toggleNetwork() {
  ctx.ui.network = !ctx.ui.network;
}
</script>

<style scoped>
</style> 