<template>
  <a-button type="text" @click="clear">
    <template #icon>
      <span class="icon iconfont qingkong"></span>
    </template>
  </a-button>
</template>

<script setup lang="ts">
import { useGptStore } from "@/components/store/gpt";
import { clearMessage } from "@/components/common/manageRequest";
import AiIconButton from "@/components/ai-bot-widget/AiIconButton.vue";

const ctx = useGptStore()

async function clear() {
  if (ctx.CurrentChat.messageList.length === 0) return
  // 获取当前对话
  const id = ctx.CurrentChat.Current.Conversation.id;
  const result = await clearMessage(id);
  if (result.code) {
    ctx.CurrentChat.messageList = []
  }
}
</script>

<style scoped>
/* 清空按钮特殊样式 */
.icon.iconfont.qingkong {
  font-size: 16px;
  transition: color 0.3s;
}

:deep(.ai-icon-button:active) {
  color: #ff4d4f !important;
  background: rgba(255, 77, 79, 0.16) !important;
}

:deep(.ai-icon-button:hover) .icon.iconfont.qingkong {
  animation: iconBounce 0.6s ease-in-out;
}

@keyframes iconBounce {
  0% { transform: scale(1); }
  25% { transform: scale(1.1); }
  50% { transform: scale(0.9); }
  75% { transform: scale(1.1); }
  100% { transform: scale(1); }
}
</style>