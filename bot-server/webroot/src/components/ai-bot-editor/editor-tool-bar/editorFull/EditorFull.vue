<template>
  <AiIconButton @click="bot.isMax = !bot.isMax">
    <span class="icon iconfont" :class="bot.isMax ? 'zuixiaohua' : 'zuidahua'"></span>
  </AiIconButton>
</template>

<script setup lang="ts">
import { useAiBot } from "@/components/store/bot";
import AiIconButton from "@/components/ai-bot-widget/AiIconButton.vue";

const bot = useAiBot()
</script>

<style scoped>
.icon.iconfont {
  font-size: 16px;
  color: inherit;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  transition: all 0.3s ease;
}

/* 动画效果 */
.icon.iconfont:hover {
  transform: scale(1.1);
}

.icon.iconfont:active {
  transform: scale(0.95);
}
</style>