<template>
  <a-tag
      class="clear-context-tag"
      @click="clearContext"
      :style="{ cursor: 'pointer' }"
      :bordered="false"
  >
    <span class="icon iconfont qingkong"></span>
    清空上下文
  </a-tag>
</template>

<script setup lang="ts">
import {useGptStore} from '@/components/store/gpt';
import {AiBot} from "@/components/ai-bot-interface/impl/base/aibot";
import {theme} from 'ant-design-vue';

const {token} = theme.useToken();

const ctx = useGptStore();

// 清空上下文的处理函数
function clearContext() {
  // 预留清空上下文的请求逻辑
  console.log('清空上下文请求发送');
  // TODO: 实现清空上下文的实际逻辑
  let bot = new AiBot()
  bot.ClearContext()
}
</script>

<style scoped>
.clear-context-tag {
  font-size: 12px;
  margin: 0;
  cursor: pointer;
  transition: all 0.3s;
  user-select: none;
  display: flex;
  align-items: center;
  gap: 4px;
}

.clear-context-tag:hover {
  color: v-bind('token.colorWarning');
}

.clear-context-tag .icon {
  font-size: 14px;
  margin-right: 2px;
}
</style> 