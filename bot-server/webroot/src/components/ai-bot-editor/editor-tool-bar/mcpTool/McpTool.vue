<template>
  <a-dropdown
      v-model:open="dropdownVisible"
      :trigger="['click']"
      :getPopupContainer="triggerNode => triggerNode.parentNode"
  >
    <a-button
        size="small"
        @click="dropdownVisible = !dropdownVisible"
        type="text"
    >
      <template #icon>
       <span class="icon iconfont moxing"></span>
      </template>
      <span class="mcp-tool-text">MCP 工具</span>
    </a-button>

    <template #overlay>
      <a-menu class="mcp-tools-menu">
        <!-- 搜索过滤框 -->
        <div class="search-wrapper">
          <a-input
            v-model:value="searchText"
            placeholder="搜索工具..."
            allowClear
            size="small"
            @click.stop
          />
        </div>
        <template v-if="loading">
          <a-menu-item key="loading">
            <a-spin size="small"/>
            加载中...
          </a-menu-item>
        </template>
        <template v-else-if="filteredMcpTools.length > 0">
          <a-menu-item
              v-for="tool in filteredMcpTools"
              :key="tool.name"
          >
          <template #icon>
            <span class="icon iconfont moxing"></span>
          </template>
            <div class="mcp-tool-item">
              <span class="mcp-tool-name">{{ tool.name }}</span>
              <a-switch
                  class="mcp-tool-switch"
                  size="small"
                  :checked="!tool.disabled"
                  @change="toggleToolStatus(tool)"
                  @click.stop
              />
            </div>
          </a-menu-item>
        </template>
        <template v-else-if="mcpStore.getMcpServers.length > 0 && filteredMcpTools.length === 0">
          <a-menu-item key="no-results" disabled>无匹配工具</a-menu-item>
        </template>
        <template v-else>
          <a-menu-item key="none" disabled>暂无可用工具</a-menu-item>
        </template>
      </a-menu>
    </template>
  </a-dropdown>
</template>

<script setup lang="ts">
import {onMounted, ref, computed} from "vue";
import {useAiMcpStore} from "@/components/store/mcp";
import {McpInfo} from "@/components/ai-bot-mcp/model";
import {ToolOutlined} from '@ant-design/icons-vue';

const emits = defineEmits({
  useMcpTool: (toolName) => {
    return true;
  }
})

const mcpStore = useAiMcpStore();
const loading = ref(false);
const dropdownVisible = ref(false);
const searchText = ref('');

// 过滤MCP工具列表
const filteredMcpTools = computed(() => {
  if (!searchText.value) {
    return mcpStore.getMcpServers;
  }
  return mcpStore.getMcpServers.filter(tool => 
    tool.name.toLowerCase().includes(searchText.value.toLowerCase())
  );
});

// 处理工具点击事件
const handleToolClick = (toolName: string) => {
  if (toolName !== 'loading' && toolName !== 'none') {
    useMcpTool(toolName);
    // 使用工具后关闭下拉菜单
    dropdownVisible.value = false;
  }
}

// 切换工具启用/禁用状态
const toggleToolStatus = async (tool: McpInfo) => {
  try {
    // 更新本地状态
    tool.disabled = tool.disabled == 0 ? 1 : 0;
    await mcpStore.updateMcpServer(tool);
  } catch (error) {
    console.error('切换工具状态失败:', error);
  }
}

// 使用MCP工具
function useMcpTool(toolName: string) {
  emits('useMcpTool', toolName);
}

// 从当前可用的MCP服务器中收集所有工具
async function collectMcpTools() {
  loading.value = true;
  try {
    await mcpStore.fetchMcpServers();
  } catch (error) {
    console.error('获取MCP工具出错:', error);
  } finally {
    loading.value = false;
  }
}

onMounted(() => {
  collectMcpTools();
})
</script>

<style scoped>
.mcp-tool-button {
  display: flex;
  align-items: center;
  min-width: 100px;
}

.mcp-tool-text {
  margin-left: 6px;
}

/* 搜索框样式 */
.search-wrapper {
  padding: 8px;
  position: sticky;
  top: 0;
  z-index: 1;
  background-color: inherit;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  margin-bottom: 4px;
}

/* MCP工具列表项样式 */
.mcp-tool-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.mcp-tool-name {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.mcp-tool-switch {
  margin-left: 8px;
}

/* 自定义下拉菜单样式 */
:deep(.mcp-tools-menu.ant-dropdown-menu) {
  min-width: 180px;
  border-radius: 8px;
  max-height: 300px;
  overflow-y: auto;
  padding: 4px;
  /* 隐藏滚动条但保持滚动功能 - 提高优先级 */
  scrollbar-width: none !important; /* Firefox */
  -ms-overflow-style: none !important; /* IE and Edge */
}

/* 隐藏Webkit浏览器的滚动条 - 增强选择器 */
:deep(.mcp-tools-menu.ant-dropdown-menu::-webkit-scrollbar),
:deep(.mcp-tools-menu.ant-dropdown-menu *::-webkit-scrollbar) {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
  background: transparent !important;
}

/* 确保滚动容器样式正确 */
:deep(.ant-dropdown) {
  overflow: visible !important;
}

:deep(.ant-dropdown-menu-item),
:deep(.ant-menu-item) {
  padding: 6px 12px !important;
  border-radius: 6px !important;
  margin: 2px !important;
  height: auto !important;
  min-height: 32px !important;
  line-height: 22px !important;
}

/* 移除下拉菜单箭头 */
:deep(.ant-dropdown-arrow),
:deep(.ant-dropdown-arrow::before) {
  display: none !important;
  content: none !important;
}
</style>