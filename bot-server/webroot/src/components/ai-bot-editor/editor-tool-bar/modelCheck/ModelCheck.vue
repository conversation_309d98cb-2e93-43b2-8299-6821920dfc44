<template>
  <div class="model-switch-container flex flex-col justify-center">
    <a-select v-model:value="selectedMode" @change="handleModeChange" :bordered="false" class="mode-select">
      <a-select-option class="model-value my-1" value="chat">问答模式</a-select-option>
      <a-select-option class="model-value my-1" value="agent">代理模式</a-select-option>
    </a-select>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';
import { theme } from 'ant-design-vue';
import { useGptStore } from '@/components/store/gpt';

const useToken = theme.useToken;
const { token } = useToken();

const selectedMode = ref('chat');
const ctx = useGptStore()

// 当模式切换时触发事件
const handleModeChange = (value) => {
  console.log(`切换到${value}模式`);
  // 在这里可以添加实际的模式切换逻辑
  // 例如：emit一个事件通知父组件模式已更改
};
</script>

<style scoped>
.mode-select :deep(.ant-select-selection-item) {
  font-size: 10px;
  font-weight: 500;
}
.model-value {
  font-size: 10px !important;
  font-weight: 500 !important;
}
</style>