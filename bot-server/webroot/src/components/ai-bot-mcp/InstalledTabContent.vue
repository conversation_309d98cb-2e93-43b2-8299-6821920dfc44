<template>
  <div class="installed-tab-content">
    <McpServerList :servers="mcpServers" @select-plugin="(pluginId) => $emit('select-plugin', pluginId)" />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import McpServerList from './McpServerList.vue';
import { useAiMcpStore } from '../store/mcp';
import { theme } from 'ant-design-vue';

// 使用Ant Design主题token
const { useToken } = theme;
const { token } = useToken();

// 使用MCP Store
const mcpStore = useAiMcpStore();
const mcpServers = computed(() => mcpStore.getMcpServers);

defineEmits(['select-plugin']);
</script>

<style scoped>
.installed-tab-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: v-bind('token.colorBgContainer');
  border-radius: v-bind('token.borderRadius');
}
</style> 