<template>
  <div class="plugin-detail-wrapper">
    <div v-if="selectedPlugin" class="plugin-detail">
      <div class="plugin-detail-header">
        <h2 class="plugin-detail-title">{{ getSelectedPluginName() }}</h2>
        <div class="plugin-detail-version">版本: {{ getSelectedPluginVersion() }}</div>
        <div class="plugin-detail-author">作者: {{ getSelectedPluginAuthor() }}</div>
      </div>

      <div class="plugin-detail-content">
        <h3>插件介绍</h3>
        <p>这是一个用于演示的插件介绍内容。此插件提供了多种功能，可以帮助您更高效地进行开发工作。</p>

        <h3>功能列表</h3>
        <ul>
          <li>功能1: 提供智能代码补全</li>
          <li>功能2: 自动检查代码质量</li>
          <li>功能3: 集成第三方服务</li>
          <li>功能4: 优化开发工作流</li>
        </ul>

        <h3>使用说明</h3>
        <p>安装插件后，您可以通过以下方式使用:</p>
        <ol>
          <li>在编辑器中右键点击，选择对应功能</li>
          <li>使用快捷键触发特定操作</li>
          <li>通过命令面板搜索并执行功能</li>
        </ol>
      </div>
    </div>
    <div v-else class="plugin-detail-empty">
      <p>请选择左侧的插件查看详细信息</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useAiMcpStore } from '../store/mcp';

const props = defineProps<{
  selectedPlugin: string;
  demoPlugins: Record<string, any>;
}>();

// 使用MCP Store
const mcpStore = useAiMcpStore();
const mcpServers = computed(() => mcpStore.getMcpServers);

// 获取选中插件的名称
const getSelectedPluginName = () => {
  const id = props.selectedPlugin;
  if (!id) return '';

  if (id === 'baidu-comate') return props.demoPlugins.baiduComate.name;
  if (id === 'eslint-restart') return props.demoPlugins.eslint.name;
  if (id === 'jetbrains-ai') return props.demoPlugins.jetbrainsAI.name;
  if (id === 'mcp-server') return props.demoPlugins.mcpServer.name;

  if (mcpServers.value && mcpServers.value[id]) return mcpServers.value[id].name || id;

  return '';
};

// 获取选中插件的版本
const getSelectedPluginVersion = () => {
  const id = props.selectedPlugin;
  if (!id) return '';

  if (id === 'baidu-comate') return props.demoPlugins.baiduComate.version;
  if (id === 'eslint-restart') return props.demoPlugins.eslint.version;
  if (id === 'jetbrains-ai') return props.demoPlugins.jetbrainsAI.version;
  if (id === 'mcp-server') return props.demoPlugins.mcpServer.version;

  // 可以从服务器配置中获取版本信息，如果有的话
  return '1.0.0';
};

// 获取选中插件的作者
const getSelectedPluginAuthor = () => {
  const id = props.selectedPlugin;
  if (!id) return '';

  if (id === 'baidu-comate') return props.demoPlugins.baiduComate.author;
  if (id === 'eslint-restart') return props.demoPlugins.eslint.author;
  if (id === 'jetbrains-ai') return props.demoPlugins.jetbrainsAI.author;
  if (id === 'mcp-server') return props.demoPlugins.mcpServer.author;

  // 可以从服务器配置中获取作者信息，如果有的话
  return '用户自定义';
};
</script>

<style scoped>
.plugin-detail-wrapper {
  height: 100%;
  width: 100%;
  padding: 20px;
  overflow-y: auto;
}

.plugin-detail {
  height: 100%;
}

.plugin-detail-header {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e0e0e0;
}

.plugin-detail-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 5px;
}

.plugin-detail-version, .plugin-detail-author {
  font-size: 13px;
  color: #666;
  margin-bottom: 5px;
}

.plugin-detail-content {
  font-size: 14px;
  line-height: 1.6;
}

.plugin-detail-content h3 {
  font-size: 16px;
  font-weight: 500;
  margin: 15px 0 10px;
}

.plugin-detail-content p {
  margin-bottom: 10px;
}

.plugin-detail-content ul, .plugin-detail-content ol {
  padding-left: 20px;
  margin-bottom: 10px;
}

.plugin-detail-content li {
  margin-bottom: 5px;
}

.plugin-detail-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #999;
  font-size: 14px;
}
</style> 