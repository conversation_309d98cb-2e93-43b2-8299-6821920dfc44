<template>
    <div class="json-editor-container size-full">
        <div ref="editorRef" class="json-editor"></div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed, defineExpose, defineProps, defineEmits } from 'vue';
import { EditorState, Extension } from '@codemirror/state';
import { EditorView, keymap, lineNumbers, highlightActiveLineGutter } from '@codemirror/view';
import { defaultKeymap, indentWithTab } from '@codemirror/commands';
import { json } from '@codemirror/lang-json';
import {
    defaultHighlightStyle, syntaxHighlighting, indentOnInput,
    bracketMatching, foldGutter, foldKeymap
} from "@codemirror/language"
import { oneDark } from '@codemirror/theme-one-dark'
import {basicSetup} from "codemirror"
import { theme } from 'ant-design-vue';
const { useToken } = theme;
const { token } = useToken();




const props = defineProps({
    modelValue: {
        type: String,
        default: '{}'
    },
    readOnly: {
        type: Boolean,
        default: false
    }
});

const emit = defineEmits(['update:modelValue']);

const editorRef = ref<HTMLElement | null>(null);
let editorView: EditorView | null = null;


const jsonLinter = json().language.data.of({
    async validate(text) {
        try {
            JSON.parse(text)
            return []
        } catch (e) {
            return [{
                from: e.pos,
                to: e.pos + 1,
                severity: "error",
                message: e.message
            }]
        }
    }
})


// 创建编辑器实例
const createEditor = () => {
    if (!editorRef.value) return;

    const startState = EditorState.create({
        doc: props.modelValue,
        extensions: getExtensions()
    });

    editorView = new EditorView({
        state: startState,
        parent: editorRef.value
    });
};

// 获取编辑器扩展
const getExtensions = (): Extension[] => {
    const extensions: Extension[] = [
        basicSetup,
        oneDark,
        lineNumbers(),
        highlightActiveLineGutter(),
        syntaxHighlighting(defaultHighlightStyle),
        keymap.of([...defaultKeymap, indentWithTab]),
        json(),
        EditorView.theme({
            "&": { fontSize: "14px" },
            ".cm-content": { fontFamily: "Fira Code, monospace" },
            ".cm-line": { padding: "0 8px" }
        }),
        EditorView.updateListener.of(update => {
            if (update.docChanged) {
                const value = update.state.doc.toString();
                emit('update:modelValue', value);
            }
        })
    ];

    if (props.readOnly) {
        extensions.push(EditorState.readOnly.of(true));
    }

    return extensions;
};

// 格式化 JSON
const formatJson = (jsonStr: string): string => {
    try {
        // 尝试解析并格式化 JSON
        return JSON.stringify(JSON.parse(jsonStr), null, 2);
    } catch (e) {
        // 如果解析失败，返回原始字符串
        console.warn('JSON 解析失败:', e);
        return jsonStr;
    }
};

// 更新编辑器内容
const updateContent = (content: string) => {
    if (!editorView) return;

    const formatted = formatJson(content);
    const currentContent = editorView.state.doc.toString();

    if (formatted !== currentContent) {
        editorView.dispatch({
            changes: { from: 0, to: currentContent.length, insert: formatted }
        });
    }
};

// 监听 modelValue 变化
watch(() => props.modelValue, (newValue) => {
    updateContent(newValue);
});

// 组件挂载时创建编辑器
onMounted(() => {
    createEditor();
});

// 暴露方法给父组件
defineExpose({
    formatJson,
    updateContent
});
</script>

<style scoped>
.json-editor-container {
    overflow: hidden;
}


:global(.cm-gutters) {
    background-color: transparent !important;
}
:global(.ͼ1f) {
    background-color: transparent !important;
}


:global(.cm-editor) {
    background-color: transparent !important;
}
:global(.cm-activeLineGutter) {
    background-color: v-bind('token.colorPrimaryBg') !important;
    color: v-bind('token.colorPrimaryText') !important;
}
:global(.cm-activeLine) {
    background-color: v-bind('token.colorPrimaryBg') !important;
}
:global(.cm-line::selection) {
    background-color: v-bind('token.colorPrimaryBg') !important;
}
:global(.cm-content ::selection) {
    background-color: v-bind('token.colorPrimaryBg') !important;
    color: v-bind('token.colorPrimaryText') !important;
}
:global(.json-editor ::selection) {
    background-color: v-bind('token.colorPrimaryBg') !important;
    color: v-bind('token.colorPrimaryText') !important;
}
:global(.cm-selectionBackground) {
    background-color: v-bind('token.colorPrimaryBg') !important;
}
</style>