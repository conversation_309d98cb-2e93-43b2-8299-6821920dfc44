<template>
  <div class="mcp-ui">
    <!-- 使用 Ant Design Vue 的 Tabs 组件，并将内容放入标签页中 -->
    <InstalledTabContent @select-plugin="handleSelectPlugin" />
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue';
import MarketplaceTabContent from './MarketplaceTabContent.vue';
import InstalledTabContent from './InstalledTabContent.vue';
import PluginDetail from './PluginDetail.vue';
import { theme } from 'ant-design-vue';

const { useToken } = theme;
const { token } = useToken();
// 标签页状态
const activeTab = ref('installed');
const selectedPlugin = ref('');

// 处理插件选择
const handleSelectPlugin = (pluginId: string) => {
  selectedPlugin.value = pluginId;
};

// 插件数据 (用于演示)
const demoPlugins = reactive({
  baiduComate: {
    name: 'Baidu Comate',
    version: '3.1.1',
    author: 'Baidu',
    enabled: false,
  },
  eslint: {
    name: 'ESLint Restart Service Action',
    version: '0.0.1',
    author: '<PERSON>',
    enabled: false,
  },
  jetbrainsAI: {
    name: 'JetBrains AI Assistant',
    version: '243.23654.270.16',
    author: 'JetBrains',
    enabled: true,
    tags: ['freemium', 'no-license'],
  },
  mcpServer: {
    name: 'MCP Server',
    version: '1.0.16',
    author: 'JetBrains',
    enabled: true,
  },
});

// 获取已安装插件数量
const getInstalledCount = () => {
  // 这里可以根据实际数据计算插件数量，暂时使用固定值
  return Object.keys(demoPlugins).length;
};
</script>

<style scoped>
.mcp-ui {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: v-bind('token.colorBgContainer');
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

:global(.mcp-ui .ant-tabs-nav) {
  margin: 0;
}

/* 标签页完全填充容器 */
.mcp-tabs {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* 调整ant-tabs的默认样式，使内容区域占满剩余空间 */
:deep(.ant-tabs-content) {
  flex: 1;
  height: 100%;
}

:deep(.ant-tabs-tabpane) {
  height: 100%;
  padding: 0;
}

.split-layout {
  display: flex;
  height: 100%;
}

.plugin-list-panel {
  width: 50%;
  overflow: hidden;
  border-right: 1px solid v-bind('token.colorBorder');
}

.plugin-detail-panel {
  width: 50%;
  overflow: hidden;
}
</style>