<template>
  <div class="mcp-server-list">
    <!-- 搜索框 -->
    <div class="search-container">
      <a-input v-model:value="searchText" placeholder="输入 / 以查看选项" :bordered="false" class="search-input" />
    </div>

    <div class="servers-container">
      <a-spin :spinning="loading">
        <a-collapse v-model:activeKey="activeKeys" :bordered="false">
          <a-collapse-panel v-for="server in mcpStore.getMcpServers" :key="server.id" class="server-collapse-panel"
            :showArrow="false">
            <template #header>
              <div class="server-item">
                <div class="server-name">{{ server.name }}</div>
                <div class="server-controls" @click.stop>
                  <a-switch :checked="!server.disabled" @change="toggleToolStatus(server)" />
                  <div class="status-indicator" :class="{ 'is-active': getServerStatus(server) }"></div>
                </div>
              </div>
            </template>

            <!-- 面板内容 -->
            <div class="server-panel flex flex-col">
              <!-- 使用Segmented组件切换内容 -->
              <div class="tabs-container">
                <a-segmented :value="getPanelValue(server.id)"
                  @update:value="value => handlePanelChange(server.id, value)" :options="[
                    { value: '工具', label: `工具 (${mcpTools[server.id]?.length || 0})` },
                    { value: '资源', label: '资源 (0)' }
                  ]" style="width: 200px" size="small" block />
              </div>

              <div class="flex flex-col justify-between mt-2" style="flex-grow: 1">

                <div class="panel-content">
                  <!-- 工具内容 -->
                  <div v-if="selectedPanel[server.id] === '工具'" class="panel-section tools-section">
                    <div class="tools-container">
                      <div v-if="(mcpTools[server.id] || []).length === 0" class="empty-tools">
                        无可用工具
                      </div>
                      <div v-for="tool in mcpTools[server.id] || []" :key="tool.name" class="tool-item flex flex-col">
                        <div class="tool-header">
                          <div class="tool-icon">
                            <span class="icon iconfont moxing"></span>
                          </div>
                          <div class="tool-name">{{ tool.name }}</div>
                        </div>
                        <div class="tool-description">{{ tool.description || '' }}</div>
                      </div>
                    </div>

                    <!-- 基础设置模式 -->
                    <div v-if="serverEditModes[server.id] === 'base'" class="base-settings">
                      <div class="timeout-section">
                        <div class="timeout-label">Request Timeout</div>
                        <div class="timeout-selector">
                          <a-select v-model:value="server.timeout" style="width: 100%"
                            @change="(value) => handleTimeoutChange(server, value)">
                            <a-select-option :value="30">30 seconds</a-select-option>
                            <a-select-option :value="60">60 seconds</a-select-option>
                            <a-select-option :value="120">120 seconds</a-select-option>
                          </a-select>
                        </div>
                      </div>
                    </div>

                    <!-- JSON编辑模式 -->
                    <div v-else class="json-editor-view">
                      <JsonEditor v-model="server.interfaceParams" height="300px" :read-only="false"
                        @update:modelValue="(value) => handleServerJsonChange(server, value)" />
                    </div>
                  </div>

                  <!-- 资源内容 -->
                  <div v-if="selectedPanel[server.id] === '资源'" class="panel-section resources-section">
                    <div class="empty-resources">
                      无可用资源
                    </div>
                  </div>
                </div>

                <!-- 操作按钮 - 上下排列 -->
                <div class="panel-actions vertical">
                  <a-button type="primary" block @click="handleRestartServer(server.id)">
                    <template #icon><i class="fas fa-sync"></i></template>
                    Restart Server
                  </a-button>
                  <a-button danger block @click="handleDeleteServer(server.id)">
                    <template #icon><i class="fas fa-trash"></i></template>
                    Delete Server
                  </a-button>
                </div>
              </div>

            </div>
          </a-collapse-panel>
        </a-collapse>
      </a-spin>
    </div>

    <!-- 底部固定的添加按钮 -->
    <div class="add-server-button-container">
      <a-button class="add-server-button" type="primary" @click="openAddServerModal">
        <i class="fas fa-plus"></i> 添加自定义MCP服务器
      </a-button>
    </div>

    <!-- 添加服务器对话框 -->
    <a-modal v-model:open="isAddModalVisible" title="添加MCP服务" @ok="handleAddServer" @cancel="isAddModalVisible = false"
      :okText="'确定'" :cancelText="'取消'" :width="500"
      :bodyStyle="{ padding: '12px', maxHeight: `${modalHeight}px`, minHeight: `${modalHeight}px`, overflowY: 'auto' }"
      :centered="true">

      <div class="flex flex-row mb-2">
        <a-radio-group v-model:value="interfaceParamsEditMode" button-style="solid">
          <a-radio-button value="form">表单模式</a-radio-button>
          <a-radio-button value="json">JSON模式</a-radio-button>
        </a-radio-group>
      </div>

      <!-- JSON编辑模式 -->
      <div v-if="interfaceParamsEditMode === 'json'" class="json-edit-container size-full">
        <JsonEditor v-model="newEditValue" :read-only="false" />
      </div>

      <!-- 表单编辑模式 -->
      <a-form v-else :model="newServer" layout="vertical" :labelCol="{ style: { padding: '0 0 4px' } }">
        <a-form-item label="MCP名称" name="name" :rules="[{ required: true, message: '请输入MCP名称' }]">
          <a-input v-model:value="newServer.name" placeholder="请输入唯一标识符" />
        </a-form-item>

        <a-form-item label="命令" name="command" :rules="[{ required: true, message: '请输入命令' }]">
          <a-input v-model:value="newServer.command" placeholder="请输入命令" />
        </a-form-item>

        <a-form-item label="类型" name="type" :rules="[{ required: true, message: '请选择类型' }]">
          <a-select v-model:value="newServer.type">
            <a-select-option value="stdio">标准模式 (stdio)</a-select-option>
            <a-select-option value="sse">流模式 (sse)</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="参数" name="args">
          <a-input v-model:value="argsInput" placeholder="输入参数，多个参数用空格分隔" @change="updateArgs" />
        </a-form-item>

        <a-form-item label="环境变量" name="env">
          <div v-for="(value, key) in newServer.env" :key="key" class="env-item">
            <div class="env-pair">
              <a-input v-model:value="newServer.tempEnvKey" placeholder="KEY" class="env-key"
                @change="updateEnvKey(key)" />
              <a-input v-model:value="newServer.env[key]" placeholder="VALUE" class="env-value" />
            </div>
            <a-button type="text" danger @click="removeEnvVar(key)">
              <template #icon><i class="fas fa-times"></i></template>
            </a-button>
          </div>
          <a-button type="dashed" block @click="addEnvVar">
            <template #icon><i class="fas fa-plus"></i></template>
            添加环境变量
          </a-button>
        </a-form-item>

        <a-form-item label="请求超时(秒)" name="timeout">
          <a-select v-model:value="newServer.timeout">
            <a-select-option :value="30">30秒</a-select-option>
            <a-select-option :value="60">60秒</a-select-option>
            <a-select-option :value="120">120秒</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="状态" name="disabled">
          <a-switch :checked="getServerStatus(newServer)" :checkedChildren="'启用'" :unCheckedChildren="'禁用'"
            @change="toggleNewServerStatus" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { computed, onBeforeMount, onMounted, onUnmounted, reactive, ref, watch } from 'vue';
import { McpInfo } from './model';
import { message, theme } from 'ant-design-vue';
import { useAiMcpStore } from '../store/mcp';
import { getMcpTools } from './mcpReq';
import JsonEditor from './JsonEditor.vue';
const { useToken } = theme;
const { token } = useToken();
// 使用Pinia store
const mcpStore = useAiMcpStore();

// 搜索框状态
const searchText = ref('');

// 组件内部的状态
const activeKeys = ref<string[]>([]);
const isAddModalVisible = ref(false);
const mcpTools = reactive<Record<string, any[]>>({});

// 为每个服务器实例保存当前选中的面板
const selectedPanel = reactive<Record<string, string>>({});

// 为每个服务器实例保存当前选中的编辑模式
const newEditValue = ref('');

// 将McpInfo对象转换为JSON字符串用于编辑
const mcpInfoToJsonString = (mcpInfo: McpInfo): string => {
  try {
    // 创建一个干净的对象副本，排除临时字段
    const cleanMcpInfo = {
      id: mcpInfo.id,
      name: mcpInfo.name,
      command: mcpInfo.command,
      type: mcpInfo.type || 'stdio',
      args: mcpInfo.args || [],
      env: mcpInfo.env || {},
      disabled: mcpInfo.disabled,
      timeout: mcpInfo.timeout || 30,
      autoApprove: mcpInfo.autoApprove || [],
      interfaceParams: mcpInfo.interfaceParams
    };
    
    return JSON.stringify(cleanMcpInfo, null, 2);
  } catch (error) {
    console.error('转换McpInfo到JSON字符串失败:', error);
    return '{}';
  }
};

// 将JSON字符串转换回McpInfo对象
const jsonStringToMcpInfo = (jsonStr: string): McpInfo => {
  try {
    const parsedObj = JSON.parse(jsonStr);
    
    // 确保对象具有所有必需的字段
    const mcpInfo: McpInfo = {
      id: parsedObj.id || '',
      name: parsedObj.name || '',
      command: parsedObj.command || '',
      type: parsedObj.type || 'stdio',
      args: Array.isArray(parsedObj.args) ? parsedObj.args : [],
      env: typeof parsedObj.env === 'object' ? parsedObj.env : {},
      disabled: typeof parsedObj.disabled === 'number' ? parsedObj.disabled : 0,
      timeout: typeof parsedObj.timeout === 'number' ? parsedObj.timeout : 30,
      autoApprove: Array.isArray(parsedObj.autoApprove) ? parsedObj.autoApprove : [],
      interfaceParams: parsedObj.interfaceParams || '{}'
    };
    
    return mcpInfo;
  } catch (error) {
    console.error('转换JSON字符串到McpInfo失败:', error);
    // 返回一个默认的McpInfo对象
    return {
      id: '',
      name: '',
      command: '',
      type: 'stdio',
      args: [],
      env: {},
      disabled: 0,
      timeout: 30,
      autoApprove: [],
      interfaceParams: '{}'
    };
  }
};

// 新服务器配置
const newServer = reactive<McpInfo>({
  id: '',
  type: 'stdio',
  name: '',
  command: '',
  args: [] as string[],
  env: {} as Record<string, string>,
  disabled: 0, // 0-启用, 1-禁用
  autoApprove: [] as string[],
  timeout: 30,
  tempEnvKey: '' as any, // 临时存储环境变量的键
});

// 监听newServer变化，实时转为JSON字符串
watch(
  () => newServer,
  (val) => {
    newEditValue.value = mcpInfoToJsonString(val);
  },
  { deep: true, immediate: true }
);

// 监听JSON编辑器值变化，转换回newServer对象
watch(
  () => newEditValue.value,
  (val) => {
    if (interfaceParamsEditMode.value === 'json') {
      try {
        const mcpInfo = jsonStringToMcpInfo(val);
        // 只更新主要字段，避免覆盖临时字段
        newServer.id = mcpInfo.id;
        newServer.name = mcpInfo.name;
        newServer.command = mcpInfo.command;
        newServer.args = mcpInfo.args;
        newServer.env = mcpInfo.env;
        newServer.disabled = mcpInfo.disabled;
        newServer.timeout = mcpInfo.timeout;
        newServer.autoApprove = mcpInfo.autoApprove || [];
        newServer.interfaceParams = mcpInfo.interfaceParams;
      } catch (error) {
        console.error('JSON字符串转换失败:', error);
      }
    }
  }
);

// 用于参数输入的临时变量
const argsInput = ref('');

// 对话框高度计算
const modalHeight = ref(0);

// 计算对话框高度为窗口高度的2/3
const calculateModalHeight = () => {
  modalHeight.value = Math.floor(window.innerHeight * 2 / 3);
};

// 窗口调整大小时重新计算高度
const handleResize = () => {
  calculateModalHeight();
};

onBeforeMount(() => {
  calculateModalHeight();
  window.addEventListener('resize', handleResize);
});

// 获取服务器状态工具函数
const getServerStatus = (server: McpInfo): boolean => {
  return server.disabled === 0;
};

// 切换工具启用/禁用状态
const toggleToolStatus = async (tool: McpInfo) => {
  try {
    // 更新本地状态
    tool.disabled = tool.disabled == 0 ? 1 : 0;
    await mcpStore.updateMcpServer(tool);
  } catch (error) {
    console.error('切换工具状态失败:', error);
  }
}

const loading = computed(() => mcpStore.isLoading);

// 更新参数数组
const updateArgs = () => {
  if (argsInput.value.trim()) {
    // 按空格分割输入文本，过滤掉空字符串
    newServer.args = argsInput.value.trim().split(/\s+/).filter(arg => arg !== '');
  } else {
    newServer.args = [];
  }
};

// 接口参数编辑模式
const interfaceParamsEditMode = ref<'json' | 'form'>('form');

// 接口参数表单数据
interface ParamItem {
  key: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  value: any;
}

const interfaceParamsForm = reactive<ParamItem[]>([]);

// 处理 JSON 变化
const handleJsonChange = (value: string) => {
  try {
    // 尝试解析 JSON 并更新表单
    const parsed = JSON.parse(value);
    updateFormFromJson(parsed);
  } catch (e) {
    // JSON 解析失败，不更新表单
    console.warn('JSON 解析失败:', e);
  }
};

// 从 JSON 更新表单
const updateFormFromJson = (jsonObj: any) => {
  interfaceParamsForm.length = 0; // 清空现有表单

  Object.entries(jsonObj).forEach(([key, value]) => {
    let type: ParamItem['type'] = 'string';

    if (typeof value === 'number') type = 'number';
    else if (typeof value === 'boolean') type = 'boolean';
    else if (Array.isArray(value)) type = 'array';
    else if (typeof value === 'object' && value !== null) type = 'object';

    interfaceParamsForm.push({
      key,
      type,
      value: type === 'object' || type === 'array' ? JSON.stringify(value) : value
    });
  });
};

// 更新接口参数 JSON
const updateInterfaceParams = () => {
  const params: Record<string, any> = {};

  interfaceParamsForm.forEach(item => {
    if (!item.key.trim()) return; // 忽略空键名

    try {
      if (item.type === 'number') {
        params[item.key] = Number(item.value);
      } else if (item.type === 'boolean') {
        params[item.key] = Boolean(item.value);
      } else if (item.type === 'object' || item.type === 'array') {
        params[item.key] = JSON.parse(item.value || '{}');
      } else {
        params[item.key] = item.value;
      }
    } catch (e) {
      console.warn(`参数 ${item.key} 解析失败:`, e);
      params[item.key] = item.value;
    }
  });

  newServer.interfaceParams = JSON.stringify(params, null, 2);
};

// 添加参数
const addParam = () => {
  interfaceParamsForm.push({
    key: '',
    type: 'string',
    value: ''
  });
};

// 移除参数
const removeParam = (index: number) => {
  interfaceParamsForm.splice(index, 1);
  updateInterfaceParams();
};

// 更新参数类型
const updateParamType = (index: number) => {
  const item = interfaceParamsForm[index];

  // 根据类型重置值
  if (item.type === 'string') item.value = '';
  else if (item.type === 'number') item.value = 0;
  else if (item.type === 'boolean') item.value = false;
  else if (item.type === 'object') item.value = '{}';
  else if (item.type === 'array') item.value = '[]';

  updateInterfaceParams();
};

// 获取参数值组件
const getParamValueComponent = (type: ParamItem['type']) => {
  if (type === 'boolean') return 'a-switch';
  return 'a-input';
};

// 获取参数值占位符
const getParamValuePlaceholder = (type: ParamItem['type']) => {
  if (type === 'string') return '输入字符串';
  if (type === 'number') return '输入数字';
  if (type === 'object') return '输入JSON对象';
  if (type === 'array') return '输入JSON数组';
  return '';
};

// 打开添加服务器对话框
const openAddServerModal = () => {
  // 重置表单
  Object.assign(newServer, {
    id: '',
    name: '',
    command: '',
    type: 'stdio',
    args: [],
    env: {},
    disabled: 0,
    autoApprove: [],
    timeout: 30,
    tempEnvKey: '',
    interfaceParams: '{}',
  });
  // 重置 args 输入框
  argsInput.value = '';
  // 重置接口参数表单
  interfaceParamsForm.length = 0;
  // 默认使用表单模式
  interfaceParamsEditMode.value = 'form';
  isAddModalVisible.value = true;
};

// 添加环境变量
const addEnvVar = () => {
  const key = `env_${Object.keys(newServer.env).length}`;
  newServer.env[key] = '';
  newServer.tempEnvKey = key;
};

// 更新环境变量的键
const updateEnvKey = (oldKey: string) => {
  if (newServer.tempEnvKey && newServer.tempEnvKey !== oldKey) {
    const value = newServer.env[oldKey];
    delete newServer.env[oldKey];
    newServer.env[newServer.tempEnvKey] = value;
  }
};

// 删除环境变量
const removeEnvVar = (key: string) => {
  delete newServer.env[key];
};

// 切换新服务器状态
const toggleNewServerStatus = (checked: boolean) => {
  newServer.disabled = checked ? 0 : 1;
};

// 处理添加服务器
const handleAddServer = async () => {
  if (newServer.name && newServer.command) {
    // 创建MCP数据对象
    const mcpData: McpInfo = {
      id: '', // 添加时ID由后端生成
      name: newServer.name,
      command: newServer.command,
      type: newServer.type || 'stdio',
      args: newServer.args,
      env: newServer.env,
      disabled: newServer.disabled,
      timeout: newServer.timeout,
      interfaceParams: newServer.interfaceParams
    };

    try {
      await mcpStore.addMcpServer(mcpData);
      isAddModalVisible.value = false;
    } catch (error) {
      console.error('添加MCP服务器出错:', error);
    }
  }
};

// 处理删除服务器
const handleDeleteServer = async (serverId: string) => {
  try {
    await mcpStore.deleteMcpServer(serverId);
  } catch (error) {
    console.error('删除MCP服务器出错:', error);
  }
};

// 处理重启服务器
const handleRestartServer = async (serverId: string) => {
  message.info(`重启MCP服务器[${serverId}]功能暂未实现`);
};

const getPanelValue = (serverId: string) => selectedPanel[serverId] || '工具';
const handlePanelChange = (serverId: string, value: string) => {

  selectedPanel[serverId] = value;
  if (value === '工具') {
    loadMcpTools(serverId);
  }
};

// 加载MCP工具列表
const loadMcpTools = async (serverId: string) => {
  try {
    console.log('加载工具列表的服务器ID:', serverId);
    mcpTools[serverId] = await getMcpTools(serverId)
  } catch (error) {
    console.error('加载MCP工具列表出错:', error);
  }
};


// 监听折叠面板打开，确保选中面板已初始化
const watchActiveKeys = (newKeys: string[]) => {
  newKeys.forEach(key => {
    if (!selectedPanel[key]) {
      selectedPanel[key] = '工具';
    }
    // 当展开面板时加载工具列表
    if (selectedPanel[key] === '工具') {
      loadMcpTools(key);

      // 初始化服务器编辑模式
      if (!serverEditModes[key]) {
        serverEditModes[key] = 'base';
      }

      // 初始化服务器表单数据
      const server = mcpStore.getMcpServers.find(s => s.id === key);
      if (server && server.interfaceParams && !serverParamsForm[key]) {
        try {
          const parsed = JSON.parse(server.interfaceParams);
          updateServerFormFromJson(server, parsed);
        } catch (e) {
          console.warn(`服务器 ${key} 的 JSON 解析失败:`, e);
        }
      }
    }
  });
};

// 监听activeKeys变化
watch(activeKeys, watchActiveKeys);

// 切换服务器状态
const toggleServerStatus = async (serverId: string) => {
  try {
    await mcpStore.toggleServerStatus(serverId);
  } catch (error) {
    console.error('切换MCP服务器状态出错:', error);
  }
};

// 处理超时时间变更
const handleTimeoutChange = async (mcp: McpInfo, timeout: number) => {
  try {
    mcp.timeout = timeout
    // 调用store方法更新服务器
    await mcpStore.updateMcpServer(mcp);
  } catch (error) {
    console.error('更新MCP服务器超时时间出错:', error);
    message.error('更新MCP服务器超时时间失败');
  }
};

// 现有服务器的编辑模式
const serverEditModes = reactive<Record<string, 'base' | 'json'>>({});

// 现有服务器的表单数据
const serverParamsForm = reactive<Record<string, ParamItem[]>>({});

// 处理服务器 JSON 变化
const handleServerJsonChange = async (server: McpInfo, value: string) => {
  try {
    // 尝试解析 JSON 并更新表单
    const parsed = JSON.parse(value);
    updateServerFormFromJson(server, parsed);

    // 更新服务器
    server.interfaceParams = value;
    await mcpStore.updateMcpServer(server);
  } catch (e) {
    console.warn('服务器 JSON 解析失败:', e);
    message.error('JSON 格式错误');
  }
};

// 从 JSON 更新服务器表单
const updateServerFormFromJson = (server: McpInfo, jsonObj: any) => {
  if (!serverParamsForm[server.id]) {
    serverParamsForm[server.id] = [];
  } else {
    serverParamsForm[server.id].length = 0; // 清空现有表单
  }

  Object.entries(jsonObj).forEach(([key, value]) => {
    let type: ParamItem['type'] = 'string';

    if (typeof value === 'number') type = 'number';
    else if (typeof value === 'boolean') type = 'boolean';
    else if (Array.isArray(value)) type = 'array';
    else if (typeof value === 'object' && value !== null) type = 'object';

    serverParamsForm[server.id].push({
      key,
      type,
      value: type === 'object' || type === 'array' ? JSON.stringify(value) : value
    });
  });
};

// 更新服务器参数 JSON
const updateServerParams = async (server: McpInfo) => {
  const params: Record<string, any> = {};

  if (!serverParamsForm[server.id]) return;

  serverParamsForm[server.id].forEach(item => {
    if (!item.key.trim()) return; // 忽略空键名

    try {
      if (item.type === 'number') {
        params[item.key] = Number(item.value);
      } else if (item.type === 'boolean') {
        params[item.key] = Boolean(item.value);
      } else if (item.type === 'object' || item.type === 'array') {
        params[item.key] = JSON.parse(item.value || '{}');
      } else {
        params[item.key] = item.value;
      }
    } catch (e) {
      console.warn(`参数 ${item.key} 解析失败:`, e);
      params[item.key] = item.value;
    }
  });

  server.interfaceParams = JSON.stringify(params, null, 2);
  try {
    await mcpStore.updateMcpServer(server);
  } catch (error) {
    console.error('更新服务器参数出错:', error);
    message.error('更新服务器参数失败');
  }
};

// 添加服务器参数
const addServerParam = (server: McpInfo) => {
  if (!serverParamsForm[server.id]) {
    serverParamsForm[server.id] = [];
  }

  serverParamsForm[server.id].push({
    key: '',
    type: 'string',
    value: ''
  });
};

// 移除服务器参数
const removeServerParam = async (server: McpInfo, index: number) => {
  serverParamsForm[server.id].splice(index, 1);
  await updateServerParams(server);
};

// 更新服务器参数类型
const updateServerParamType = async (server: McpInfo, index: number) => {
  const items = serverParamsForm[server.id];
  if (!items || !items[index]) return;

  const item = items[index];

  // 根据类型重置值
  if (item.type === 'string') item.value = '';
  else if (item.type === 'number') item.value = 0;
  else if (item.type === 'boolean') item.value = false;
  else if (item.type === 'object') item.value = '{}';
  else if (item.type === 'array') item.value = '[]';

  await updateServerParams(server);
};

// 组件挂载时获取服务器列表
onMounted(() => {
  console.log('MCP服务器列表组件已挂载，开始获取数据');
  mcpStore.fetchMcpServers();
});

// 组件卸载时移除事件监听
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});

// 监听编辑模式变化
watch(interfaceParamsEditMode, (newMode, oldMode) => {
  if (newMode === 'form' && oldMode === 'json') {
    // 从JSON模式切换到表单模式，需要校验JSON数据
    try {
      // 检查JSON格式是否有效
      const mcpInfo = jsonStringToMcpInfo(newEditValue.value);
      
      // 验证必要字段是否存在，而不是检查值是否为空
      const requiredFields = ['name', 'command'];
      const missingFields = requiredFields.filter(field => !(field in mcpInfo));
      
      if (missingFields.length > 0) {
        // 存在缺失的必要字段
        message.error(`JSON数据缺少必要字段: ${missingFields.join(', ')}`);
        interfaceParamsEditMode.value = 'json'; // 阻止切换到表单模式
        return;
      }
      
      // 验证字段类型
      if ('args' in mcpInfo && !Array.isArray(mcpInfo.args)) {
        message.error('args字段必须是数组类型');
        interfaceParamsEditMode.value = 'json';
        return;
      }
      
      if ('env' in mcpInfo && typeof mcpInfo.env !== 'object') {
        message.error('env字段必须是对象类型');
        interfaceParamsEditMode.value = 'json';
        return;
      }
      
      // 更新newServer
      Object.keys(mcpInfo).forEach(key => {
        if (key in newServer) {
          newServer[key] = mcpInfo[key];
        }
      });
      
      // 更新argsInput
      if (mcpInfo.args && Array.isArray(mcpInfo.args)) {
        argsInput.value = mcpInfo.args.join(' ');
      }
      
    } catch (error) {
      console.error('JSON转换失败:', error);
      message.error('JSON格式错误，无法切换到表单模式');
      interfaceParamsEditMode.value = 'json'; // 阻止切换到表单模式
    }
  } else if (newMode === 'json' && oldMode === 'form') {
    // 从表单模式切换到JSON模式，更新JSON字符串
    newEditValue.value = mcpInfoToJsonString(newServer);
  }
});
</script>

<style scoped>
.mcp-server-list {
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
}

/* 搜索框样式 */
.search-container {
  display: flex;
  padding: 0;
  border-bottom: 1px solid v-bind('token.colorBorderSecondary');
}

.search-input {
  width: 100%;
  padding: 10px 16px;
  border: none;
  border-radius: 0;
  font-size: 13px;
  outline: none;
}

.search-input:focus {
  background-color: v-bind('token.colorBgTextHover');
}

.servers-container {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
  margin-bottom: 56px;
  /* 为底部按钮留出空间 */
}

/* 底部固定的添加按钮样式 */
.add-server-button-container {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 10px;
  z-index: 10;
}

.add-server-button {
  width: 100%;
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: background-color 0.3s;
}


.server-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.2s ease;
  width: 100%;
}

.server-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.server-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}


.tool-item {
  display: flex;
  font-size: 12px;
  border-radius: 5px;
}

.tool-icon-inline {
  width: 20px;
  margin-right: 8px;
}

.tool-name {
  flex: 1;
  font-weight: 500;
}

.auto-approve {
  font-size: 11px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.timeout-section {
  margin-bottom: 12px;
}

.timeout-label {
  font-size: 12px;
  margin-bottom: 8px;
}

.empty-tools,
.empty-resources {
  text-align: center;
  font-size: 12px;
  padding: 12px;
  border-radius: 4px;
}

.panel-actions {
  display: flex;
  gap: 8px;
  margin-top: 12px;
}

.panel-actions.vertical {
  flex-direction: column;
}

.action-btn {
  margin-bottom: 8px;
  height: 32px;
}

.footer-actions {
  padding: 12px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.footer-btn,
.footer-link {
  text-align: left;
}

.server-panel {}

/* 添加服务器对话框样式 */
.env-item {
  display: flex;
  margin-bottom: 8px;
  align-items: center;
}

.env-pair {
  display: flex;
  flex: 1;
  gap: 8px;
}

.env-key {
  width: 40%;
}

.env-value {
  width: 60%;
}

/* 表单项间距调整 */
:deep(.ant-form-item) {
  margin-bottom: 12px;
}

:deep(.ant-form-item-label > label) {
  font-size: 13px;
}


:deep(.ant-modal-content) {
  display: flex;
  flex-direction: column;
  max-height: 66.7vh;
}

:deep(.ant-modal-body) {
  flex: 1;
  overflow-y: auto;
}

/* 自定义工具列表样式 */
.tools-container {
  margin-bottom: 8px;
  border-bottom: none;
}

.tool-item {
  padding: 8px 10px;
  margin-bottom: 8px;
  border-radius: v-bind('token.borderRadius');
  background-color: v-bind('token.colorBgContainer');
  border: 1px solid v-bind('token.colorBorderSecondary');
}

.tool-header {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.tool-icon {
  font-size: 16px;
  margin-right: 8px;
  color: v-bind('token.colorPrimary');
  display: flex;
  align-items: center;
  justify-content: center;
  height: 20px;
  line-height: 1;
  transform: translateY(2px);
}

.tool-name {
  font-size: 13px;
  font-weight: 500;
  flex: 1;
  line-height: 20px;
  display: flex;
  align-items: center;
  color: v-bind('token.colorText');
}

.tool-description {
  font-size: 12px;
  color: v-bind('token.colorTextSecondary');
  margin-left: 24px;
  line-height: 1.3;
}

.auto-approve-toggle {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 12px;
}

.checkbox {
  width: 14px;
  height: 14px;
  border: 1px solid v-bind('token.colorBorder');
  border-radius: v-bind('token.borderRadiusSM');
  margin-right: 4px;
  position: relative;
  display: inline-block;
}

.checkbox.checked {
  background-color: v-bind('token.colorPrimary');
  border-color: v-bind('token.colorPrimary');
}

.checkbox.checked::after {
  content: '';
  position: absolute;
  display: block;
  border: 2px solid #fff;
  border-top: 0;
  border-left: 0;
  width: 6px;
  height: 9px;
  left: 4px;
  top: 1px;
  transform: rotate(45deg);
}

.label {
  color: v-bind('token.colorTextSecondary');
}

.empty-tools {
  text-align: center;
  padding: 16px;
  color: v-bind('token.colorTextDisabled');
  font-size: 12px;
}

.timeout-section {
  margin-top: 8px;
  border-top: none;
}

.interface-params-section {
  margin-top: 12px;
  padding-top: 8px;
  border-top: 1px solid v-bind('token.colorBorderSecondary');
}

.interface-params-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.interface-params-label {
  font-size: 12px;
  margin-bottom: 8px;
}

.edit-mode-switch {
  text-align: right;
}

.param-item {
  margin-bottom: 8px;
}

.param-input-group {
  display: flex;
  gap: 8px;
  align-items: center;
}

.param-key {
  width: 30%;
}

.param-type {
  width: 25%;
}

.param-value {
  flex: 1;
}

.mt-2 {
  margin-top: 8px;
}

.editor-container,
.form-editor-container {
  border: 1px solid v-bind('token.colorBorderSecondary');
  border-radius: 4px;
  padding: 8px;
}

.form-editor-container {
  max-height: 300px;
  overflow-y: auto;
}

.interface-params-editor {
  margin-bottom: 10px;
}

.form-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.form-section-title {
  font-size: 13px;
  font-weight: 500;
}

.tool-section-header {
  display: flex;
  justify-content: center;
  margin-bottom: 12px;
  margin-top: 10px;
}

.section-tabs {
  text-align: center;
}

.base-settings {
  margin-top: 10px;
}

.json-editor-view {
  margin-top: 10px;
  border: 1px solid v-bind('token.colorBorderSecondary');
  border-radius: 4px;
  padding: 8px;
}
</style>