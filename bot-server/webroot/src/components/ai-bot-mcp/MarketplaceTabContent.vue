<template>
  <div class="marketplace-tab-content">
    <McpMarketplaceList @select-plugin="(pluginId) => $emit('select-plugin', pluginId)" />
  </div>
</template>

<script setup lang="ts">
import McpMarketplaceList from './McpMarketplaceList.vue';

defineEmits(['select-plugin']);
</script>

<style scoped>
.marketplace-tab-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style> 