import {Result} from '../common/model/system'
import Axios from '@/plugins/axiosForServer'
import {McpInfo, McpTool} from './model'
import {message} from 'ant-design-vue';

// API基础URL
const API_BASE_URL = '/api/bot/mcp'

/**
 * 获取MCP列表
 * @returns {Promise<McpInfo[]>} MCP列表结果
 */
export function getMcpList(): Promise<McpInfo[]> {
    return new Promise<McpInfo[]>((resolve, reject) => {
        Axios.get<Result<McpInfo[]>>(`${API_BASE_URL}/list`)
            .then(({data}) => {
                if (data.code === 200) {
                    // 如果服务器返回null，则表示空列表
                    if (data.data === null) {
                        resolve([]);
                    } else {
                        resolve(data.data);
                    }
                } else {
                    console.error('获取MCP列表失败:', data);
                    reject(new Error(data.msg || '获取MCP列表请求失败'));
                }
            })
            .catch(error => {
                console.error('获取MCP列表异常:', error);
                reject(error);
            });
    });
}

/**
 * 获取MCP详情
 * @param {string} id MCP ID
 * @returns {Promise<McpInfo>} MCP详情结果
 */
export function getMcpInfo(id: string): Promise<McpInfo> {
    return new Promise((resolve, reject) => {
        Axios.get<Result<McpInfo>>(`${API_BASE_URL}/info`, {
            params: {id}
        })
            .then(({data}) => {
                if (data.code === 200) {
                    // 如果服务器返回null，则返回空对象
                    if (data.data === null) {
                        resolve({} as McpInfo);
                    } else {
                        resolve(data.data);
                    }
                } else {
                    console.error(`获取MCP[${id}]详情失败:`, data);
                    reject(new Error(data.msg || `获取MCP[${id}]详情请求失败`));
                }
            })
            .catch(error => {
                console.error(`获取MCP[${id}]详情异常:`, error);
                reject(error);
            });
    });
}

/**
 * 获取MCP工具列表
 * @param {string} id MCP ID
 * @returns {Promise<McpTool[]>} MCP工具列表结果
 */
export function getMcpTools(id: string): Promise<McpTool[]> {
    return new Promise((resolve, reject) => {
        Axios.get<Result<McpTool[]>>(`${API_BASE_URL}/tools`, {
            params: {id}
        })
            .then(({data}) => {
                if (data.code === 200) {
                    // 如果服务器返回null，则返回空数组
                    if (data.data === null) {
                        resolve([]);
                    } else {
                        resolve(data.data);
                    }
                } else {
                    console.error(`获取MCP[${id}]工具列表失败:`, data);
                    reject(new Error(data.msg || `获取MCP[${id}]工具列表请求失败`));
                }
            })
            .catch(error => {
                console.error(`获取MCP[${id}]工具列表异常:`, error);
                reject(error);
            });
    });
}

/**
 * 添加MCP
 * @param {McpInfo} mcpInfo MCP信息
 * @returns {Promise<any>} 添加结果
 */
export function addMcp(mcpInfo: McpInfo): Promise<any> {
    return new Promise((resolve, reject) => {
        Axios.post<Result<any>>(`${API_BASE_URL}/add`, mcpInfo)
            .then(({data}) => {
                if (data.code === 200) {
                    resolve(data.data);
                } else {
                    console.error('添加MCP失败:', data);
                    reject(new Error(data.msg || '添加MCP请求失败'));
                }
            })
            .catch(error => {
                console.error('添加MCP异常:', error);
                reject(error);
            });
    });
}

/**
 * 编辑MCP
 * @param {McpInfo} mcpInfo MCP信息
 * @returns {Promise<any>} 编辑结果
 */
export function editMcp(mcpInfo: McpInfo): Promise<any> {
    return new Promise((resolve, reject) => {
        Axios.post<Result<any>>(`${API_BASE_URL}/edit`, mcpInfo)
            .then(({data}) => {
                if (data.code === 200) {
                    resolve(data.data);
                } else {
                    console.error(`编辑MCP[${mcpInfo.id}]失败:`, data);
                    message.error(data.msg || `更新MCP服务器失败`);
                    reject(new Error(data.msg || `编辑MCP[${mcpInfo.id}]请求失败`));
                }
            })
            .catch(error => {
                console.error(`编辑MCP[${mcpInfo.id}]异常:`, error);
                message.error(`更新MCP服务器失败`);
                reject(error);
            });
    });
}

/**
 * 删除MCP
 * @param {string} id MCP ID
 * @returns {Promise<any>} 删除结果
 */
export function deleteMcp(id: string): Promise<any> {
    return new Promise((resolve, reject) => {
        Axios.post<Result<any>>(`${API_BASE_URL}/delete`, {id})
            .then(({data}) => {
                if (data.code === 200) {
                    resolve(data.data);
                } else {
                    console.error(`删除MCP[${id}]失败:`, data);
                    reject(new Error(data.msg || `删除MCP[${id}]请求失败`));
                }
            })
            .catch(error => {
                console.error(`删除MCP[${id}]异常:`, error);
                reject(error);
            });
    });
}
