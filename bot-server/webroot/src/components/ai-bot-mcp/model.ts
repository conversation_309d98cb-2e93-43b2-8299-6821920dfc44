// MCP工具接口定义
export interface McpTool {
    name: string        // 工具名称
    description: string // 工具描述
    inputSchema: ToolInputSchema // 输入参数模式
    rawInputSchema?: any // 原始输入参数模式
}

// 工具输入参数模式接口
export interface ToolInputSchema {
    type: string
    properties?: Record<string, {
        type: string
        description?: string
        enum?: string[]
        [key: string]: any
    }>
    required?: string[]

    [key: string]: any
}

// MCP信息接口定义
export interface McpInfo {
    id: string              // MCP ID
    name: string            // MCP名称
    command: string         // 执行命令
    args: string[]          // 命令参数
    env: Record<string, string> // 环境变量
    disabled: number        // 是否禁用 (0-启用, 1-禁用)
    timeout: number         // 超时时间(秒)
    tools?: McpTool[]       // 工具列表
    autoApprove?: string[]  // 自动批准的工具列表
    [key: string]: any      // 允许额外的属性，解决编辑器临时字段问题
}
