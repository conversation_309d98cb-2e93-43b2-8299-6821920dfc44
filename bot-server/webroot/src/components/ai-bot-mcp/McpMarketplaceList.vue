<template>
  <div class="mcp-marketplace-list">
    <!-- 搜索框 -->
    <div class="search-container">
      <a-input 
        v-model:value="searchText" 
        placeholder="输入 / 以查看选项" 
        :bordered="false"
        class="search-input" 
      />
    </div>
    
    <div class="list-header">
      <h2 class="list-title">插件市场</h2>
    </div>

    <div class="plugins-container">
      <a-spin :spinning="loading">
        <a-list :dataSource="filteredPlugins" size="small">
          <template #renderItem="{ item }">
            <a-list-item>
              <a-list-item-meta :description="item.description">
                <template #avatar>
                  <a-avatar style="display: flex; align-items: center; justify-content: center;">
                    <template #icon>
                      <span class="icon iconfont moxing"></span>
                    </template>
                  </a-avatar>
                </template>
                <template #title>
                  <div class="plugin-title">
                    <span>{{ item.name }}</span>
                    <div class="plugin-tags">
                      <span v-if="item.tags && item.tags.includes('freemium')" class="tag freemium">Freemium</span>
                    </div>
                  </div>
                </template>
              </a-list-item-meta>
              <template #actions>
                <a-button type="primary" @click="handleInstallPlugin(item.id)">安装</a-button>
              </template>
              <template #extra>
                <div class="plugin-info">
                  <div class="plugin-version">{{ item.version }}</div>
                  <div class="plugin-author">{{ item.author }}</div>
                </div>
              </template>
            </a-list-item>
          </template>
        </a-list>
      </a-spin>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { message, theme } from 'ant-design-vue';
import { useAiMcpStore } from '../store/mcp';

// 使用Ant Design主题token
const { useToken } = theme;
const { token } = useToken();

// 使用Pinia store
const mcpStore = useAiMcpStore();

// 搜索框状态
const searchText = ref('');
const loading = ref(false);

// 插件数据 (用于演示)
const marketplacePlugins = ref([
  {
    id: 'baidu-comate',
    name: 'Baidu Comate',
    version: '3.1.1',
    author: 'Baidu',
    description: 'AI coding assistant for developers',
    tags: ['popular', 'freemium']
  },
  {
    id: 'eslint-restart',
    name: 'ESLint Restart Service Action',
    version: '0.0.1',
    author: 'Gareth Jones',
    description: 'Restart ESLint service with a click',
    tags: []
  },
  {
    id: 'jetbrains-ai',
    name: 'JetBrains AI Assistant',
    version: '243.23654.270.16',
    author: 'JetBrains',
    description: 'AI-powered coding assistant',
    tags: ['freemium', 'popular']
  },
  {
    id: 'postcss',
    name: 'PostCSS',
    version: '243.23654.119',
    author: 'JetBrains',
    description: 'A tool for transforming CSS with JavaScript plugins',
    tags: []
  },
  {
    id: 'sass',
    name: 'Sass',
    version: '243.23654.119',
    author: 'JetBrains',
    description: 'Sass support for the IDE',
    tags: []
  },
  {
    id: 'python-community',
    name: 'Python Community Edition',
    version: '243.24978.46',
    author: 'JetBrains',
    description: 'Python language support',
    tags: ['popular']
  }
]);

// 处理插件安装
const handleInstallPlugin = (pluginId: string) => {
  loading.value = true;
  setTimeout(() => {
    loading.value = false;
    message.success(`已成功安装插件: ${pluginId}`);
  }, 1000);
};

// 根据搜索条件过滤插件
const filteredPlugins = computed(() => {
  if (!searchText.value) return marketplacePlugins.value;
  
  const lowercaseSearch = searchText.value.toLowerCase();
  return marketplacePlugins.value.filter(plugin => 
    plugin.name.toLowerCase().includes(lowercaseSearch) || 
    plugin.description.toLowerCase().includes(lowercaseSearch) ||
    plugin.author.toLowerCase().includes(lowercaseSearch)
  );
});

// 组件挂载时获取插件列表
onMounted(() => {
  // 可以在这里从API获取插件列表
  console.log('插件市场组件已挂载，准备获取数据');
});
</script>

<style scoped>
.mcp-marketplace-list {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* 搜索框样式 */
.search-container {
  display: flex;
  padding: 0;
  border-bottom: 1px solid v-bind('token.colorBorderSecondary');
}

.search-input {
  width: 100%;
  padding: 10px 16px;
  border: none;
  border-radius: 0;
  font-size: 13px;
  outline: none;
  transition: all 0.3s;
  color: v-bind('token.colorText');
}

.search-input:focus {
  background-color: v-bind('token.colorBgTextHover');
}

.list-header {
  padding: 10px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid v-bind('token.colorBorderSecondary');
}

.list-title {
  font-size: 14px;
  font-weight: 600;
  margin: 0;
  color: v-bind('token.colorTextHeading');
}

.plugins-container {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.plugin-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.plugin-tags {
  display: flex;
}

.tag {
  font-size: 11px;
  padding: 2px 5px;
  border-radius: v-bind('token.borderRadiusSM');
  margin-left: 5px;
}

.tag.freemium {
  background-color: v-bind('token.colorPrimaryBg');
  color: v-bind('token.colorPrimary');
}

.plugin-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  font-size: 12px;
  color: v-bind('token.colorTextSecondary');
}

.plugin-version, .plugin-author {
  line-height: 1.5;
}

:deep(.ant-list-item-meta-title) {
  margin-bottom: 0;
}

:deep(.ant-list-item-meta-description) {
  font-size: 12px;
}
</style> 