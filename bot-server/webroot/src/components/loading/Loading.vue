<template>
  <div class="loading-container">
    <div class="loading-content">
        <!-- <img src="./app.png" alt="loading" class="loading-svg"/> -->
    </div>
  </div>
</template>

<script setup lang="ts">
// 加载页面组件
</script>

<style scoped>
.loading-container {
  width: 100%;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: transparent;
}

.loading-content {
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-svg {
  width: 80px;
  height: 80px;
  backdrop-filter: blur(50px) saturate(180%);
}
</style> 