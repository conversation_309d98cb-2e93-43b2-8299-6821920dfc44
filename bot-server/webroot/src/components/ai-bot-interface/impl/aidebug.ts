import {AiBot} from "@/components/ai-bot-interface/impl/base/aibot";
import MarkdownWriter from "@/components/ai-bot-message/markdown/markdown_writer";
import {BotContext} from "@/components/common/model/model";
import {MessageItem, MessageScene} from "@/components/common/model/chat";

export class AiDebug extends AiBot {

    constructor() {
        super();
    }

    public async Chat(message: string): Promise<void> {
        
    }

    public async ChatReplyStream(ctx: BotContext, source: string, message: MessageItem): Promise<void> {

    }
}