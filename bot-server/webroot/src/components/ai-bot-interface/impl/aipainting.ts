import {sendPainting} from "@/components/common/paintingRequest";
import {AiBot} from "./base/aibot";
import {useAiPaintingStore} from "@/components/store/painting";
import pina from "@/pinia";
import {BotContext} from "@/components/common/model/model";
import {PaintingMessage} from "@/components/common/model/painting";
import {VITE_APP_SERVER} from "@/env";
import {MainSceneType} from "@/components/common/model/chat";
import emitter from "@/plugins/event";
import { AiPaintingStream } from "@/plugins/evenKey";



export class AIPainting extends AiBot {

    public aiPaintingStore = useAiPaintingStore(pina);

    constructor() {
        super();
    }

    // 绘画方法 - 从状态管理内部初始化参数
    async AiPainting(message: string): Promise<void> {
        // 验证提示词
        if (!message || message.trim().length === 0) {
            console.error("绘画消息不能为空");
            return;
        }

        // 从状态管理获取所有参数
        const currentParams = this.aiPaintingStore.getCurrentParams;
        const params: PaintingDTO = {
            prompt: message.trim(),
            size: currentParams.size,
            guidanceScale: currentParams.guidanceScale,
            negativePrompt: currentParams.negativePrompt,
            width: currentParams.width,
            height: currentParams.height,
            imageCount: currentParams.imageCount,
            numInferenceSteps: currentParams.numInferenceSteps,
            promptTemplate: currentParams.promptTemplate
        };

        try {
            // 设置加载状态
            this.aiPaintingStore.setLoading(true);
            // 获取绘画请求上下文
            const context = this.Context(MainSceneType.Painting);
            // 发送绘画请求
            const response = await sendPainting({
                prompt: params.prompt,
                platformID: context.platform.id,
            });

            if (response.code === 200) {
                // 开始正式进入绘流请求，传入完整参数
                await this.PaintingStream(context, params.prompt, response.data, params);
            } else {
                console.error("绘画请求失败:", response);
                this.aiPaintingStore.setLoading(false);
            }
        } catch (error) {
            console.error("绘画请求异常:", error);
            this.aiPaintingStore.setLoading(false);
        }
    }

    // 绘画流处理方法 - 从状态管理获取参数
    public async PaintingStream(ctx: BotContext, source: string, message: PaintingMessage, params: PaintingDTO) {
        // 请求流数据参数 - 使用传入的参数
        const data = {
            prompt: source,                              // 绘画提示词
            platformID: ctx.platform.id,                 // 平台ID
            size: params.size,
            guidanceScale: params.guidanceScale,
            negativePrompt: params.negativePrompt,
            width: params.width,
            height: params.height,
            imageCount: params.imageCount,
            numInferenceSteps: params.numInferenceSteps
        };

        // 先将消息添加到列表中，显示加载状态
        message.MultipleStream = new Map()
        message.MultipleStream.set(message.id, async () => this.Stream(`${VITE_APP_SERVER}/api/bot/painting/stream`, data))

        // 绘画场景和 聊天场景不同，绘画场景只有一个绘画信息界面 需要通过事件去触发 通知 调用生成的图片
        emitter.emit(AiPaintingStream, message);
    }
}