import {IAiBot} from "@/components/ai-bot-interface/Iaibot";
import {MainSceneType, MessageItem, MessageScene, ParsedMessage, RoleType} from "@/components/common/model/chat";
import {BotContext, ChatContext} from "@/components/common/model/model";
import {getUUID} from "@/components/common/manageRequest";
import pinia from "@/pinia";
import {useGptStore} from "@/components/store/gpt";
import {send, sendClear} from "@/components/common/chatRequest";
import {VITE_APP_SERVER} from "@/env";
import emitter from "@/plugins/event";
import {ScrollToBottom} from "@/plugins/evenKey";
import {useAiPluginStore} from "@/components/store/plugin";
import Markdown from "@/components/ai-bot-message/markdown/markdown";
import {MarkdownCustomConverter} from "@/components/common/markdown/custom-converter";
import {useAiMcpStore} from "@/components/store/mcp";
import {usePlatform} from "@/components/store/platform";

export class AiBot implements IAiBot {

    public md: Markdown
    public aiStore=useGptStore(pinia)

    constructor() {
        this.md = new Markdown()
    }

    /*
    * 消息发送成功 把消息刷新到消息显示界面
    * */
    public RefreshMessage(message: MessageItem) {
        this.aiStore.CurrentChat.messageList.push(message)
        this.MessageScrollToBottom()
    }

    async Chat(message: string) {
        let ctx = this.Context()
        if (this.isMessageEmpty(message)) {
            console.warn("收到空消息。");
            return;
        }
        console.log(message)
        send({
            pluginID: ctx.plugin.id,
            conversationID: ctx.conversationID,
            message: message,
            replyMsgID: ctx.replyMsgID,
            role: RoleType.User,
            thinking: ctx.thinking,
            webSearch: ctx.webSearch,
            messageType: MessageScene.UserScene,
        }).then(async (resp) => {
            if (resp.code === 200) {
                // 提前 处理展示回复状态
                this.aiStore.ui.replying = true;
                await this.ChatReplyStream(ctx, message, resp.data)
            }
        })
    }

    isMessageEmpty(message: string): boolean {
        return message.trim() === '';
    }

    /*
    * @{ctx} 发送消息的上下文信息
    * @{source} 发送消息的原文 格式为 md，在获取流消息时候需要对源消息解析，分离图片，视频等资源信息
    * @{message} 消息发送到服务器以后返回的数据，通常用来做前端数据渲染
    * */
    public async ChatReplyStream(ctx: BotContext, source: string, message: MessageItem) {
        this.RefreshMessage(message)
        let dataPre = await this.RequestDataPre(ctx, source, message);
        let botMsg: MessageItem = await this.CreateBotMsg(message)
        // 请求流数据参数
        let data = {
            id: botMsg.id,
            thinking: ctx.thinking,
            webSearch: ctx.webSearch,
            ...dataPre
        }
        // 先将消息添加到列表中，显示加载状态
        botMsg.MultipleStream = new Map()
        botMsg.MultipleStream.set(botMsg.id, async () => this.Stream(`${VITE_APP_SERVER}/api/bot/chat/stream`, data))
        this.aiStore.CurrentChat.messageList.push(botMsg)
    }


    /*
    * ClearContext 清空上下文消息
    * */
    public async ClearContext() {
        if (this.aiStore.CurrentChat.messageList.length == 0) {
            return
        }
        let ctx = this.Context()
        sendClear({
            conversationID: ctx.conversationID,
            role: RoleType.User,
            messageType: MessageScene.ClearScene,
        }).then(async (resp) => {
            if (resp.code === 200) {
                console.log(resp.data)
                this.RefreshMessage(resp.data)
            }
        })
    }


    public GetHistoryContext() {
        let messages = [];

        // 从最后一条消息开始检查，直到找到一条清空消息为止
        for (let i = this.aiStore.CurrentChat.messageList.length - 2; i >= 0; i--) {
            let item = this.aiStore.CurrentChat.messageList[i];

            // 如果找到清空消息，则停止循环
            if (item.messageType == MessageScene.ClearScene) {
                break;
            }

            // 将消息添加到结果数组（使用unshift保持原始顺序）
            messages.unshift({...item}); // 使用解构创建副本而不是引用
        }

        return messages;
    }

    /*
    * CreateBotMsg
    * 请求服务器返回的 bot消息
    * uuid 属性来自服务器创建
    * @returns {MessageItem} 一个bot消息
    * */
    public async CreateBotMsg(message: MessageItem): Promise<MessageItem> {
        let uuid = await getUUID()
        return {
            id: uuid,
            content: '',
            conversationID: message.conversationID,
            receiveID: message.receiveID,
            role: RoleType.Assistant,
            // 客户端创建 bot 消息的时候必须选择 rander 进入公用渲染器
            messageType: MessageScene.RenderScene,
            //重写 方法需要默认添加这个初始化操作，否则gpt 消息加载阶段可能出问题
            ex: "{}"
        }
    }

    public MessageScrollToBottom() {
        emitter.emit(ScrollToBottom)
        setTimeout(() => {
            emitter.emit(ScrollToBottom)
        }, 300)
    }

    public async Stream(url: string, data: any): Promise<Response> {
        // 使用fetch API进行流式请求，而不是axios
        // fetch API默认没有超时限制，适合长时间运行的SSE流式响应
        // 注意：此处不需要设置AbortSignal或timeout，这样可以保持连接不会因超时而中断
        // 当客户端需要取消请求时，可以通过其他方式手动中断
        return await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                // 可以考虑添加keep-alive头部以确保连接保持开启
                'Connection': 'keep-alive',
            },
            body: JSON.stringify(data),
            // 注意：不设置signal，确保请求不会自动终止
        });
    }

    /*
    * Context
    * scene 获取制定场景的配置
    * 上下文请求主要包含 平台，插件，当前会话等信息，不包含业务参数相关信息
    * */
    public Context(scene: string = ""): BotContext {
        // 获取当前连天选择模型
        let pluginStore = useAiPluginStore(pinia)
        // 获取当前选中插件
        let plugin = pluginStore.currentPlugin
        let plat = usePlatform(pinia)
        let chatContext = null;
        switch (scene) {
            case MainSceneType.Chat:
                // 默认获取聊天上下文信息
                chatContext = this.ChatContext()
                break
            case MainSceneType.Painting:
                break
            default:
                // 默认获取聊天上下文信息
                chatContext = this.ChatContext()
        }
        return {...chatContext, plugin, platform: plat.currentPlatform}
    }

    public ChatContext(): ChatContext {
        let conversationID = ""
        if (!this.aiStore.CurrentChat.Current) {
            console.error("conversationId is null");
        }
        // 获取当前会话信息
        if (!this.aiStore.CurrentChat.Current.Conversation) {
            console.error("CurrentChat.Current.Conversation is null")
        }
        conversationID = this.aiStore.CurrentChat.Current.Conversation.id
        // 计算当前用户发送的消息对于回复gpt的那条消息
        let replyMsgID = ""
        if (this.aiStore.CurrentChat.messageList.length > 0) {
            let data = this.aiStore.CurrentChat.messageList[this.aiStore.CurrentChat.messageList.length - 1]
            // 如果最后一次的消息依就是用户的,gpt那边没有,应该认为这一条用户消息为一个新消息
            if (data.role !== 'user') {
                replyMsgID = data.id
            }
        }
        // 检查当前的 mcp 工具是否有启用
        let mcpStore = useAiMcpStore(pinia)
        let mcp = []
        for (let i = 0; i < mcpStore.getMcpServers.length; i++) {
            let mcpServer = mcpStore.mcpServers[i];
            if (mcpServer.disabled == 0) {
                mcp.push(mcpServer.id)
            }
        }
        return {
            // 会话id
            conversationID: conversationID,
            mcp,
            replyMsgID,
            // 网络搜索开关
            webSearch: this.aiStore.ui.network_enabled,
            thinking: this.aiStore.ui.thinkingMode,
        }
    }

    /**
     * 解析 Markdown 内容，提取媒体资源但保持原始 Markdown 格式
     * @param content Markdown格式的内容
     * @returns {ParsedMessage} 返回解析后的消息结构，text 保持原始 Markdown 格式
     */
    public async ParseMessage(content: string): Promise<ParsedMessage> {
        const result: ParsedMessage = {
            md: content,
            text: content,
            images: [],
            video: []
        };

        // 提取图片链接
        const imageRegex = /!\[([^\]]*)\]\(([^)]+)\)/g;
        let match;

        // 查找所有图片标记
        while ((match = imageRegex.exec(content)) !== null) {
            const [_, __, imageUrl] = match;
            // 添加图片URL到数组（避免重复）
            if (!result.images.includes(imageUrl)) {
                result.images.push(imageUrl);
            }
        }

        return result;
    }

    /*
    * 请求数据预处理方法，调用此方法获取基础的请求参数
    * 此方法获取的参数不是做中的完全请求参数，自定义的参数需要通过返回的数据自行添加，或者自己重新实现请求参数的封装
    * @param ctx {BotContext} ai-bot 请求上下文
    * @param message {MessageItem} 客户端成功发送的消息
    * @returns {any}
    * */
    public async RequestDataPre(ctx: BotContext, source: string, message: MessageItem): Promise<any> {
        let history = []
        // 检查是否开启上下文聊天
        if (this.aiStore.ui.autoHistory) history.push(...this.GetHistoryContext())
        let obj = {...message}
        // let parsedMessage = await this.ParseMessage(source);
        let parsedMessage = MarkdownCustomConverter.ExtractResourceData(source);
        console.log(parsedMessage)
        // 原始消息 传递到 content 以便数据展示
        obj.content = parsedMessage.md
        history.push(obj)
        return new Promise<any>(resolve => {
            resolve({
                conversationID: message.conversationID,
                replyMsgID: message.id,
                pluginID: ctx.plugin.id,
                message: parsedMessage.text,
                images: parsedMessage.images,
                history,
                mcp: parsedMessage.mcp
            })
        })
    }

    public AiPainting(message: string): void {
        console.warn("AiPainting method not implemented in base class. Please override in subclass.");
        return;
    }
}

