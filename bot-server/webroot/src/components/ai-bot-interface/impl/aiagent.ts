import { MessageItem, MessageScene, RoleType } from "@/components/common/model/chat";
import { AiBot } from "./base/aibot";
import { getUUID } from "@/components/common/manageRequest";

export class AiAgent extends AiBot {
    constructor() {
        super()
    }

    public async CreateBotMsg(message: MessageItem): Promise<MessageItem> {
        let uuid = await getUUID()
        return {
            id: uuid,
            content: '',
            conversationID: message.conversationID,
            receiveID: message.receiveID,
            role: RoleType.Assistant,
            // 客户端创建 bot 消息的时候必须选择 rander 进入公用渲染器
            messageType: MessageScene.AgentScene,
            //重写 方法需要默认添加这个初始化操作，否则gpt 消息加载阶段可能出问题
            ex: "{}"
        }
    }
}

