<template>
    <PageLayout>
        <div class="debug-container">
            <!-- 内容区域 -->
            <div class="debug-content">
                <component :is="currentComponent" v-if="currentComponent" />
            </div>
        </div>
    </PageLayout>
</template>

<script setup lang="ts">
import { ref, computed, markRaw, defineAsyncComponent, onMounted, watch } from 'vue';
import PageLayout from '../ai-bot-layout/PageLayout.vue';
import DebugNavComponent from '../ai-bot-widget/nav/DebugNavComponent.vue';
import { useAiDebugStore, type DebugItem } from '../store/debug';

// 使用debug状态管理器
const debugStore = useAiDebugStore();

// 懒加载组件映射
const componentMap = {
    'prompt_mcp': markRaw(defineAsyncComponent(() => 
        import('./components/McpPromptDebugComponent.vue')
    ))
};

// 当前选中的调试项
const currentDebugItem = computed<DebugItem | undefined>(() => 
    debugStore.debugItems.find(item => item.key === debugStore.currentDebugKey)
);

// 当前要展示的组件
const currentComponent = computed(() => {
    const key = debugStore.currentDebugKey;
    return componentMap[key as keyof typeof componentMap] || null;
});
</script>

<style scoped>
.debug-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.debug-content {
    flex: 1;
    padding: 20px;
    overflow: auto;
}

.debug-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #999;
}
</style>