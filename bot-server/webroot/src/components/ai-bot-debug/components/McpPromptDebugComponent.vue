<template>
  <div class="mcp-prompt-debug-component size-full flex flex-col">
    <div class="debug-content size-full flex flex-col">
      <SplitPanel v-model:bottomHeight="bottomPanelHeight" :min-bottom="25">
        <!--    消息展示面板    -->
        <template #top>
          <div class="size-full flex flex-col overflow-auto" ref="scroller">
            <div ref="typing" class="markdown-message typing w-full render" 
                v-if="agent.length > 0">
                <template v-for="(item, index) in agent" :key="index">
                   <template v-if="item.type == AgentItemType.MD">
                        <MarkdownStyle>
                            <div class="markdown-message typing w-full"  v-html="getAgentMdRender(index)">
                            </div>
                        </MarkdownStyle>
                    </template>
                    <template v-if="item.type == AgentItemType.McpCall">
                        <AgentMcpCall :data="item" />
                    </template>
                </template>
            </div>
          </div>
        </template>
        <!--     消息输入面板     -->
        <template #bottom>
          <div class="size-full flex flex-col">
            <div class="my-auto flex flex-col" style="width: 100%;height: 80%;">
              <ChatEditorPlus @send="send" />
            </div>
          </div>

        </template>
      </SplitPanel>
    </div>
  </div>
</template>

<script setup lang="ts">
import Markdown from '@/components/ai-bot-message/markdown/markdown';
import { AgentItem, AgentItemType } from '@/components/common/model/agent';
import { MessageScene } from '@/components/common/model/chat';
import { LLMData } from '@/components/common/stream/llm_stream';
import { VITE_APP_SERVER } from '@/env';
import { ref, reactive } from 'vue';
import MarkdownStyle from '@/components/ai-bot-message/scene/MarkdownStyle.vue';



const typingBox = ref<HTMLElement>()
const typing = ref<HTMLElement>()

const emit = defineEmits(['begin-typing'])

const agent = ref<AgentItem[]>([])
const agentRefs = ref<HTMLElement[]>([])
const markdown = ref<Markdown>()


function getAgentMdRender(index: number) {
  if (agent.value.length < index) {
    return ''
  }
  let item = agent.value[index]
  if (item.type == AgentItemType.MD) {
    return markdown.value.render(item.data)
  }
  return ''
}


// 表单数据
const selectedTool = ref<string>('');
const promptContent = ref<string>('');
const isLoading = ref<boolean>(false);
const bottomPanelHeight = ref(25)
const scroller = ref()

// 测试结果
interface TestResult {
  success: boolean;
  duration: number;
  output: string;
}
const testResult = ref<TestResult | null>(null);

const content = ref<string>('')

/*
* @description: 消息编辑器发送消息
* */
async function send(message: string) {
  let data = {
    query: message,
  }
  let response = await fetch(`${VITE_APP_SERVER}/api/test/prompt-mcp`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      // 可以考虑添加keep-alive头部以确保连接保持开启
      'Connection': 'keep-alive',
    },
    body: JSON.stringify(data),
    // 注意：不设置signal，确保请求不会自动终止
  });
  content.value = ''
  const reader = response.body.getReader()
  const decoder = new TextDecoder('utf-8')
  try {
    while (true) {
      const { done, value } = await reader.read()
      if (done) {
        break
      }
      let decodedData = decoder.decode(value);
      let accumulatedData = ""
      accumulatedData += decodedData
      for (const line of accumulatedData.split('\n')) {
        if (line) {
          try {
            let parsed: LLMData = JSON.parse(line)
            if (parsed != null) {
              // 更新 渲染数据
              if (parsed.content) {
                // 解析为 AgentItem 数据
                let agentItem = JSON.parse(parsed.content) as AgentItem
                // 检查当前的 agent 数据 在 agent 数组中最后一个数据是否相同
                if (agent.value.length > 0) {
                  let lastAgentItem = agent.value[agent.value.length - 1]
                  if (lastAgentItem.type == agentItem.type) {
                    // 如果相同则 检查数据类型 更具数据类型 更新数据
                    switch (lastAgentItem.type) {
                      case AgentItemType.MD:
                        lastAgentItem.data += agentItem.data
                        break
                      case AgentItemType.McpCall:
                        if (lastAgentItem.mcpToolId == agentItem.mcpToolId) {
                          // 如果是同一个mcp调用过程 则 更新数据  
                          lastAgentItem.data = agentItem.data
                        } else {
                          // 如果不是同一个mcp调用过程 则 添加数据
                          agent.value.push(agentItem)
                        }
                        break
                      default:
                        break
                    }
                  } else {
                    // 如果不同则 添加数据
                    agent.value.push(agentItem)
                  }
                } else {
                  agent.value.push(agentItem)
                }
              }
              scrollToBottom()
            }
          } catch (error) {
            console.error(error)
            continue
          }
        }
      }
    }
  } catch (e) {
    console.error(e)
    return
  }
}


function scrollToBottom() {
  scroller.value.scrollTo({
    top: scroller.value.scrollHeight,
    behavior: 'smooth'
  })
}
</script>

<style scoped>
.mcp-prompt-debug-component {
  width: 100%;
}

.debug-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.prompt-card,
.result-card {
  width: 100%;
}

.loading-container,
.empty-result {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #999;
}

.result-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.result-output {
  margin-top: 20px;
}

.result-output pre {
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  white-space: pre-wrap;
  word-break: break-all;
}
</style>