<template>
  <a-layout>
    <a-layout-header></a-layout-header>
    <a-layout-content class="size-full flex flex-col justify-center">
      <a-card
          class="m-auto"
          style="width: 80vw;height: 70vh"
      >
        <div class="size-full flex flex-col">
          <div>
            <a-steps :current="current" :items="items"></a-steps>
          </div>
        </div>

        <div class="steps-content">
          {{ steps[current].content }}
        </div>
        <div class="steps-action">
          <a-button v-if="current < steps.length - 1" type="primary" @click="next">Next</a-button>
          <a-button
              v-if="current == steps.length - 1"
              type="primary"
              @click="message.success('Processing complete!')"
          >
            Done
          </a-button>
          <a-button v-if="current > 0" style="margin-left: 8px" @click="prev">Previous</a-button>
        </div>
      </a-card>
    </a-layout-content>
  </a-layout>
</template>

<script lang="ts" setup>
import {ref} from 'vue';
import {message} from 'ant-design-vue';

const current = ref<number>(0);
const next = () => {
  current.value++;
};
const prev = () => {
  current.value--;
};
const steps = [
  {
    title: 'First',
    content: 'First-content',
  },
  {
    title: 'Second',
    content: 'Second-content',
  },
  {
    title: 'Last',
    content: 'Last-content',
  },
];
const items = steps.map(item => ({key: item.title, title: item.title}));
</script>
<style scoped>
.steps-content {
  margin-top: 16px;
  border: 1px dashed #e9e9e9;
  border-radius: 6px;
  background-color: #fafafa;
  min-height: 200px;
  text-align: center;
  padding-top: 80px;
}

.steps-action {
  margin-top: 24px;
}

[data-theme='dark'] .steps-content {
  background-color: #2f2f2f;
  border: 1px dashed #404040;
}
</style>

