import {defineStore} from "pinia";

export interface ResumeTemplate {
    id: string;
    name: string;
    personalInfo: any;
    isDefault?: boolean;
}

export const useInformation = defineStore("information", {
    state: () => {
        return {
            resumeTemplates: [] as ResumeTemplate[],
            currentTemplateId: ''
        }
    },
    actions: {
        // 添加新简历模板
        addResumeTemplate(template: ResumeTemplate) {
            this.resumeTemplates.push(template);
            // 如果是第一个模板，设置为当前模板
            if (this.resumeTemplates.length === 1) {
                this.currentTemplateId = template.id;
            }
        },
        // 更新简历模板
        updateResumeTemplate(id: string, data: any) {
            const index = this.resumeTemplates.findIndex(t => t.id === id);
            if (index !== -1) {
                this.resumeTemplates[index] = { ...this.resumeTemplates[index], ...data };
            }
        },
        // 删除简历模板
        deleteResumeTemplate(id: string) {
            const index = this.resumeTemplates.findIndex(t => t.id === id);
            if (index !== -1) {
                this.resumeTemplates.splice(index, 1);
                // 如果删除的是当前模板，则选择另一个模板
                if (this.currentTemplateId === id && this.resumeTemplates.length > 0) {
                    this.currentTemplateId = this.resumeTemplates[0].id;
                }
            }
        },
        // 设置当前模板
        setCurrentTemplate(id: string) {
            this.currentTemplateId = id;
        },
        // 初始化默认模板（如果没有模板）
        initDefaultTemplate() {
            if (this.resumeTemplates.length === 0) {
                this.addResumeTemplate({
                    id: `template-${Date.now()}`,
                    name: '默认简历',
                    personalInfo: {},
                    isDefault: true
                });
            }
        }
    },
    getters: {
        currentTemplate: (state) => {
            return state.resumeTemplates.find(t => t.id === state.currentTemplateId) || null;
        },
        hasTemplates: (state) => {
            return state.resumeTemplates.length > 0;
        }
    },
    persist: true
})