import {defineStore} from "pinia";
import {addMcp, deleteMcp, editMcp, getMcpList} from "../ai-bot-mcp/mcpReq";
import {McpInfo} from "../ai-bot-mcp/model";
import {message} from 'ant-design-vue';

export const useAiMcpStore = defineStore('ai-mcp', {
    state: () => {
        return {
            mcpServers: [] as McpInfo[],
            loading: false
        }
    },
    persist: true,
    getters: {
        getMcpServers() {
            return this.mcpServers
        },
        isLoading: (state) => state.loading
    },
    actions: {
        /**
         * 获取MCP服务器列表
         */
        async fetchMcpServers() {
            this.loading = true;
            try {
                const result = await getMcpList();
                console.log('获取MCP列表结果:', result);
                this.mcpServers = result
            } catch (error) {
                console.error('获取MCP服务器列表出错:', error);
                message.error('获取MCP服务器列表出错');
                throw error;
            } finally {
                this.loading = false;
            }
        },

        /**
         * 添加MCP服务器
         * @param mcpData MCP数据
         */
        async addMcpServer(mcpData: McpInfo) {
            try {
                await addMcp(mcpData);
            } catch (error) {
                console.error('添加MCP服务器出错:', error);
                throw error;
            } finally {
                await this.fetchMcpServers();
            }
        },

        /**
         * 编辑MCP服务器
         * @param mcpData MCP数据
         */
        async updateMcpServer(mcpData: McpInfo) {
            try {
                await editMcp(mcpData);
            } catch (error) {
                throw error;
            } finally {
                await this.fetchMcpServers();
            }
        },

        /**
         * 删除MCP服务器
         * @param serverId MCP服务器ID
         */
        async deleteMcpServer(serverId: string) {
            try {
                const result = await deleteMcp(serverId);
                console.log(`删除MCP[${serverId}]结果:`, result);
                await this.fetchMcpServers();
            } catch (error) {
                console.error('删除MCP服务器出错:', error);
                message.error('删除MCP服务器出错');
                throw error;
            }
        },

        /**
         * 切换服务器状态
         * @param serverId MCP服务器ID
         */
        async toggleServerStatus(serverId: string | number) {
            // 确保ID是字符串类型
            const id = String(serverId);

            if (this.mcpServers[id]) {
                const server = this.mcpServers[id];
                const isDisabled = server.disabled === 0 ? 1 : 0;

                try {
                    // 构建MCP数据对象，保持原有的其他字段
                    const mcpData: McpInfo = {
                        ...server,
                        disabled: isDisabled
                    };

                    const result = await editMcp(mcpData);
                    console.log(`更新MCP[${id}]状态结果:`, result);

                    if (result.code === 200) {
                        // 更新成功，更新本地缓存
                        this.mcpServers[id].disabled = isDisabled;
                    } else {
                        message.error(result.msg || '切换MCP服务器状态失败');
                    }
                    // 重新加载列表
                    await this.fetchMcpServers();

                    return result;
                } catch (error) {
                    console.error('切换MCP服务器状态出错:', error);
                    message.error('切换MCP服务器状态出错');
                    throw error;
                }
            }
        }
    }
})