import {defineStore} from "pinia";

export const useMessage = defineStore("msg", {
    state: () => {
        return {
            // 存储渲染后的HTML缓存
            renderedCache: {} as Record<string, string>
        }
    },
    actions: {
        // 获取缓存的HTML
        getCachedHtml(messageId: string): string | null {
            return this.renderedCache[messageId] || null
        },
        // 设置缓存的HTML
        setCachedHtml(messageId: string, html: string) {
            this.renderedCache[messageId] = html
        }
    },
    persist: true
})