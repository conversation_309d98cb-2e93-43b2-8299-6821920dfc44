import {defineStore} from "pinia";
import {Platform, Plugin} from "@/components/common/model/model";
import {updateAllPlugin, updatePlugin} from "@/components/ai-bot-plugins/req";
import {getPlugins} from "@/components/common/manageRequest";

// 定义菜单项接口
export interface MenuItem {
    key: string;
    label: string;
    children?: MenuItem[];
}

// 定义调试页面类型接口
export interface DebugItem {
    key: string;
    label: string;
    component?: string;
}

export const useAiDebugStore = defineStore('ai-debug', {
    state: () => {
        return {
           // 当前选中的调试项
           currentDebugKey: 'prompt_mcp',
           
           // 导航菜单配置
           menuItems: [
               {
                   key: 'prompt',
                   label: '提示词调试',
                   children: [
                       { key: 'prompt_mcp', label: 'MCP提示词调试' }
                   ]
               }
           ] as MenuItem[],
           
           // 调试内容项
           debugItems: [
               { key: 'prompt_mcp', label: 'MCP提示词调试', component: 'McpPromptDebugComponent' }
           ] as DebugItem[]
        }
    },
    persist: true,
    actions: {
       // 设置当前选中的调试项
       setCurrentDebugKey(key: string) {
           this.currentDebugKey = key;
       },
       
       // 获取当前选中的调试项
       getCurrentDebugItem() {
           return this.debugItems.find(item => item.key === this.currentDebugKey);
       },
       
       // 获取当前分组的子项
       getSubMenuItems(parentKey: string) {
           const parent = this.menuItems.find(item => item.key === parentKey);
           return parent?.children || [];
       }
    }
})