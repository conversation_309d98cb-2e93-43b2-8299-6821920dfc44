import {defineStore} from "pinia";
import {ConversationEntity, MessageItem} from "@/components/common/model/chat";
import {
    AppChatConversationItem,
    AppChatKnowledgeFile,
    AppChatKnowledgeInstance,
    LLmMole,
    OllamaDownload,
    OllamaModelResponse
} from "@/components/common/model/model";
import {IsEmpty} from "@/components/common/chatutils";
import {Tree} from "@/components/common/model/system";
import {LLMStream} from "@/components/common/stream/llm_stream";
import {getConversation, getConversationMessage, updateConversation} from "@/components/common/manageRequest";
import emitter from "@/plugins/event";
import {ScrollToBottom} from "@/plugins/evenKey";

export interface UIState {
    // ... existing code ...
    network: boolean
    thinkingMode: boolean // 添加思考模式状态
    // ... existing code ...
}

export const useGptStore = defineStore('gpt', {
    state: () => {
        return {
            test: '',
            defaultAvatar: './user-avatar.svg',
            gptDefaultAvatar: './app.svg',
            CurrentChat: {
                selectedKey: [] as string[],
                // 存储用户主动发起的会话记录 在第一次发送消息成功时候会把它移动到 conversationList 列表中
                create: [] as ConversationEntity[],
                // 当前会话在线情况
                status: 0,
                // 当前会话列表
                conversationList: [] as ConversationEntity[],
                // 当前聊天会话消息列表
                messageList: [] as MessageItem[],
                view: null as Map<string, boolean>,
                // 当前选中会话 需要持久化存储 JSON.parse(localStorage.getItem("current") as string) as sys.ConversationEntity||
                Current: {} as ConversationEntity,
                index: -1,
                // 当前预览的消息
                previewData: {
                    // 当前聊天会话 消息预览状态 整个聊天 确保指挥出现一个预览框
                    previewStatus: false,
                    // 预览消息列表,存储的消息 是当前聊天消息列表中的 图片和视频消息
                    previewMessages: [],
                    index: 0,
                    // message: JSON.parse(localStorage.getItem("preview-message")) as MessageItem | {} as MessageItem
                    message: {} as MessageItem
                },
                // 预览消息窗口需要加载的数据 同  previewData
                previewWinData: {},
                // 存储每个对话的滚动位置
                scrollPositions: {} as Record<string, number>
            },
            // 存储会话消息
            message: {},
            // 缓存 聊天编辑信息用于草稿状态
            chat_editor_content: {},
            ui: {
                // 会话搜索关键字
                search: '',
                // gpt响应的消息是否自动折叠
                autoFold: false,
                // 发送消息是否携带历史
                autoHistory: false,

                // gpt 是否正在回复消息 , 目前对于回复中的消息不能切换会话
                replying: false,

                // 是否禁用发送消息按钮
                sendDisable: false,

                // 编辑器发送消息方式切换 (false 案件 enter 发送) (true 按键 ctrl+enter 发送)
                send: false,

                stop: false,

                tts: false,

                enableTTS: false,

                currentLLMStream: null as LLMStream,

                // 聊天面板展示隐藏属性
                showChat: false,
                network_enabled: false,
                // 历史数据区间范围 marks 的区间来自于 当前历史消息内
                marks: [0, 0],
                defaultMarks: 0,
                // 所有模型列表
                modelList: [] as OllamaModelResponse[],
                // 用于前端 通过消息记录中的 modeid 快速查询到 模型信息显示消息名称等信息
                modelInfo: {},
                network: true,
                thinkingMode: false, // 初始化思考模式状态为false
                chatMode: '', // 初始化聊天模式为空
            },
        }
    },
    persist: {
        // 取消 消息记录的持久化
        omit: ['CurrentChat.messageList']
    },
    getters: {
        sortConversation() {
            return this.CurrentChat.conversationList
        },
    },
    actions: {
        // 保存对话的滚动位置
        saveScrollPosition(conversationId: string, position: number) {
            if (conversationId) {
                this.CurrentChat.scrollPositions[conversationId] = position;
                this.cleanupScrollPositions();
            }
        },

        // 获取对话的滚动位置
        getScrollPosition(conversationId: string): number | undefined {
            if (conversationId) {
                return this.CurrentChat.scrollPositions[conversationId];
            }
            return undefined;
        },

        // 清理过多的滚动位置记录
        cleanupScrollPositions() {
            const MAX_STORED_POSITIONS = 50; // 最大存储数量
            const ids = Object.keys(this.CurrentChat.scrollPositions);
            
            if (ids.length > MAX_STORED_POSITIONS) {
                // 获取当前会话列表中所有会话的ID
                const activeIds = new Set(
                    this.CurrentChat.conversationList.map(conv => conv.Conversation.id)
                );
                
                // 首先移除不在当前会话列表中的记录
                const idsToRemove = ids.filter(id => !activeIds.has(id));
                
                for (const id of idsToRemove) {
                    delete this.CurrentChat.scrollPositions[id];
                }
                
                // 如果仍然超过限制，移除最早的记录（不包括当前会话）
                const remainingIds = Object.keys(this.CurrentChat.scrollPositions);
                if (remainingIds.length > MAX_STORED_POSITIONS) {
                    const currentId = this.CurrentChat.Current?.Conversation?.id;
                    const idsToRemoveCount = remainingIds.length - MAX_STORED_POSITIONS;
                    
                    const idsToRemove = remainingIds
                        .filter(id => id !== currentId)
                        .slice(0, idsToRemoveCount);
                    
                    for (const id of idsToRemove) {
                        delete this.CurrentChat.scrollPositions[id];
                    }
                }
            }
        },

        setConversation(list: AppChatConversationItem[]) {
            let arr = []
            for (const element of list) {
                arr.push({
                    Conversation: element,
                    focused: false,
                    active: false
                })
            }
            this.CurrentChat.conversationList = arr
            if (arr.length == 0) {
                this.CurrentChat.Current = {}
                this.ui.showChat = false
                this.CurrentChat.index = -1
                return
            }
            if (!IsEmpty(this.CurrentChat.Current)) {
                let index = this.CurrentChat.conversationList.findIndex(item => {
                    return item.Conversation.id == this.CurrentChat.Current.Conversation.id
                })
                if (index != -1) {
                    this.CurrentChat.Current = this.CurrentChat.conversationList[index]
                    this.ui.showChat = true
                    this.CurrentChat.Current.focused = true
                    this.CurrentChat.Current.active = true
                }
                return;
            }
        },

        SetCurrentChatById(id: string) {
            let newIndex = -1
            // 查询当前即将选择的会话 index
            newIndex = this.CurrentChat.conversationList.findIndex(item => {
                return item.Conversation.id == id
            })
            let item = null
            item = this.CurrentChat.conversationList[newIndex]
            this.SetCurrentChat(item)
        },

        /*
        * @description: 设置当前会话
        * */
        SetCurrentChat(conversation: ConversationEntity) {
            this.CurrentChat.Current = conversation
            this.ui.showChat = true
            // 加载当前选中会话的消息记录
            this.GetConversationMessageList(conversation.Conversation.id)
        },

        /*
        * @description 获取聊天会话消息列表
        * */
        async GetConversationList() {
            getConversation().then(data => {
                this.setConversation(data)
            })
        },

        /*
        * @description 加载指定会话消息的历史记录到当前聊天记录
        * @param {string} id 会话id
        * */
        async GetConversationMessageList(id: string) {
            this.CurrentChat.messageList = await getConversationMessage(id)
            
            // 只有在没有记录滚动位置时才自动滚动到底部
            const hasScrollPosition = this.getScrollPosition(id) !== undefined;
            // if (!hasScrollPosition) {
            //     setTimeout(() => {
            //         emitter.emit(ScrollToBottom)
            //     }, 300)
            // }
        },

        UpdateConversationLastMsg(conversationID: string, message: MessageItem) {
            for (const conversationEntity of this.CurrentChat.conversationList) {
                if (conversationEntity.Conversation.id == conversationID) {
                    // 限制 lastMsg 最多只存储十个字符
                    conversationEntity.Conversation.lastMsg = message.content.length > 10 ?
                        message.content.substring(0, 10) + '...' :
                        message.content
                    conversationEntity.Conversation.picture = message.picture
                    let data = {...conversationEntity.Conversation}
                    data.updatedAt = ''
                    updateConversation(data).then(r => {
                        conversationEntity.Conversation.updatedAt = r.updatedAt
                    })
                }
            }
        },

        clear() {
            this.CurrentChat = {
                create: [] as ConversationEntity[],
                status: 0,
                conversationList: [] as ConversationEntity[],
                messageList: [] as MessageItem[],
                Current: {} as ConversationEntity,
                index: -1,
                scrollPositions: {} // 清空滚动位置记录
            }
            this.view = []
            this.newView = []
            this.ui = {
                search: '',
                autoFold: false,
                autoHistory: false,
                replying: false,
                send: false,
                stop: false,
                showChat: false,
                marks: [0, 0],
                defaultMarks: 0,
                baseModelList: [] as LLmMole[],
                currentModel: null as OllamaModelResponse,
                modelList: [] as OllamaModelResponse[],
                modelInfo: {},
                downloadModelList: [] as OllamaDownload[],
                knowledge: {
                    nva: [],
                    root: [] as Tree<AppChatKnowledgeFile>[],
                    files: [] as Tree<AppChatKnowledgeFile>[],
                    instance: [] as AppChatKnowledgeInstance[],
                },
                network: true,
                thinkingMode: false,
            }
        },

        /*
        * @description 从消息列表中删除指定消息
        * @param {string} messageId 要删除的消息ID
        */
        removeMessage(messageId: string) {
            const index = this.CurrentChat.messageList.findIndex(msg => msg.id === messageId)
            if (index !== -1) {
                this.CurrentChat.messageList.splice(index, 1)
            }
        },
        

    }
})
