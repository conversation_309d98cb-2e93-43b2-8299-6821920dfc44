import {defineStore} from "pinia";
import {LLMStream} from "../common/stream/llm_stream";
import {PaintingMessage, PaintingPromptConfig} from "../common/model/painting";
import {
    createPaintingConfig,
    deletePaintingConfig,
    getPaintingConfig,
    type PaintingConfigDTO,
    type PaintingConfigPO,
    queryPaintingConfigs,
    updatePaintingConfig
} from "../common/paintingRequest";

// AI参数接口定义
export interface AiParams {
    promptTemplate: string;
    size: string;
    guidanceScale: number;
    negativePrompt: string;
    width: number;
    height: number;
    useCustomSize: boolean;
    imageCount: number;
    numInferenceSteps: number;
}

// 预设配置接口
interface PresetConfig {
    id: string;
    name: string;
    promptTemplate: string;
    size: string;
    guidanceScale: number;
    negativePrompt: string;
    width: number;
    height: number;
    useCustomSize: boolean;
    imageCount: number;
    numInferenceSteps: number;
}

// 预设配置列表
export const presetConfigsList: PresetConfig[] = [
    {
        id: 'default',
        name: '默认配置',
        promptTemplate: '一只可爱的小猫在花园里玩耍，阳光明媚，卡通风格',
        size: '1:1 (1024*1024)',
        guidanceScale: 7.5,
        negativePrompt: '模糊、低质量、变形、扭曲',
        width: 1024,
        height: 1024,
        useCustomSize: false,
        imageCount: 1,
        numInferenceSteps: 20
    }
];

export const useAiPaintingStore = defineStore('ai-painting', {
    state: () => {
        return {
            current:{} as PaintingMessage, // 当前绘画消息
            ui: {
                currentLLMStream: null as LLMStream, // 当前绘画流
                loading: false // 是否正在加载绘画
            },
            // 预设配置相关状态
            preset: {
                selectedPresetId: '' as string, // 当前选中的预设ID，初始为空
                presetConfigs: [] as PresetConfig[], // 预设配置列表，初始为空
                currentConfig: {
                    id: '',
                    config: '',
                    createdAt: '',
                    updatedAt: '',
                    deletedAt: ''
                } as PaintingPromptConfig // 当前配置，初始为空
            },
            // 服务器配置相关状态
            server: {
                configs: [] as PaintingConfigPO[], // 服务器配置列表
                loading: false, // 是否正在加载服务器配置
                error: null as string | null // 错误信息
            }
        }
    },
    persist: true,
    getters: {
        // 获取当前选中的预设配置
        getCurrentPreset: (state) => {
            return state.preset.presetConfigs.find(preset => preset.id === state.preset.selectedPresetId);
        },

        // 获取所有预设配置
        getAllPresets: (state) => {
            return state.preset.presetConfigs;
        },

        // 获取当前AI参数（从配置JSON字符串解析）
        getCurrentParams: (state): AiParams => {
            try {
                if (!state.preset.currentConfig.config) {
                    // 如果没有配置，返回默认配置
                    return presetConfigsList[0];
                }
                return JSON.parse(state.preset.currentConfig.config);
            } catch (error) {
                console.error('解析当前配置失败:', error);
                // 返回默认配置
                return presetConfigsList[0];
            }
        },

        // 获取当前配置对象
        getCurrentConfig: (state): PaintingPromptConfig => {
            return state.preset.currentConfig;
        },

        // 获取服务器配置列表
        getServerConfigs: (state): PaintingConfigPO[] => {
            return state.server.configs;
        },

        // 获取服务器配置加载状态
        getServerLoading: (state): boolean => {
            return state.server.loading;
        },

        // 获取服务器错误信息
        getServerError: (state): string | null => {
            return state.server.error;
        }
    },
    actions: {
        // 设置选中的预设
        setSelectedPreset(presetId: string) {
            this.preset.selectedPresetId = presetId;
            // 同步更新当前配置
            this.syncPresetToConfig(presetId);
        },

        // 同步预设配置到当前配置
        syncPresetToConfig(presetId: string) {
            const preset = this.preset.presetConfigs.find(p => p.id === presetId);
            if (preset) {
                // 将预设配置转换为JSON字符串存储
                const configJson = JSON.stringify(preset);
                this.preset.currentConfig = {
                    id: presetId,
                    config: configJson,
                    createdAt: this.preset.currentConfig.createdAt,
                    updatedAt: new Date().toISOString(),
                    deletedAt: ''
                };
            }
        },

        // 更新单个参数
        async updateParam(key: keyof AiParams, value: any): Promise<boolean> {
            try {
                const currentParams = JSON.parse(this.preset.currentConfig.config);
                currentParams[key] = value;
                this.preset.currentConfig.config = JSON.stringify(currentParams);
                this.preset.currentConfig.updatedAt = new Date().toISOString();

                // 所有配置更改都同步到服务器
                const serverConfigId = await this.saveConfigToServer(currentParams, this.preset.currentConfig.id);
                if (!serverConfigId) {
                    console.error('同步参数到服务器失败');
                    return false;
                }

                // 如果是从默认配置创建的，更新当前配置ID
                if (this.preset.currentConfig.id === 'default' && serverConfigId) {
                    this.preset.currentConfig.id = serverConfigId;
                    this.preset.selectedPresetId = serverConfigId;
                }

                console.log(`参数 ${key} 更新成功，值: ${value}, 配置ID: ${this.preset.currentConfig.id}`);
                return true;
            } catch (error) {
                console.error('更新参数失败:', error);
                return false;
            }
        },

        // 更新多个参数
        async updateParams(params: Partial<AiParams>): Promise<boolean> {
            try {
                const currentParams = JSON.parse(this.preset.currentConfig.config);
                const updatedParams = {...currentParams, ...params};
                this.preset.currentConfig.config = JSON.stringify(updatedParams);
                this.preset.currentConfig.updatedAt = new Date().toISOString();

                // 所有配置更改都同步到服务器
                const serverConfigId = await this.saveConfigToServer(updatedParams, this.preset.currentConfig.id);
                if (!serverConfigId) {
                    console.error('同步参数到服务器失败');
                    return false;
                }

                // 如果是从默认配置创建的，更新当前配置ID
                if (this.preset.currentConfig.id === 'default' && serverConfigId) {
                    this.preset.currentConfig.id = serverConfigId;
                    this.preset.selectedPresetId = serverConfigId;
                }

                console.log('多个参数更新成功:', Object.keys(params), '配置ID:', this.preset.currentConfig.id);
                return true;
            } catch (error) {
                console.error('更新多个参数失败:', error);
                return false;
            }
        },

        // 重置为默认配置
        resetToDefault() {
            this.setSelectedPreset('default');
        },

        // 获取预设配置的完整参数
        getPresetParams(presetId: string): AiParams | null {
            const preset = this.preset.presetConfigs.find(p => p.id === presetId);
            if (preset) {
                const {id, name, createdAt, updatedAt, ...params} = preset;
                return params;
            }
            return null;
        },

        // 添加自定义预设配置
        async addCustomPreset(preset: Omit<PresetConfig, 'id' | 'createdAt' | 'updatedAt'>): Promise<string | null> {
            try {
                // 确保presetConfigs是数组
                if (!Array.isArray(this.preset.presetConfigs)) {
                    console.warn('presetConfigs不是数组，重新初始化');
                    this.preset.presetConfigs = [...presetConfigsList];
                }

                // 保存到服务器 并同步配置列表
                const serverConfigId = await this.saveConfigToServer(preset);
                if (!serverConfigId) {
                    console.error('保存到服务器失败');
                    return null;
                }
                return serverConfigId;
            } catch (error) {
                console.error('创建自定义预设配置失败:', error);
                return null;
            }
        },

        // 删除自定义预设配置
        async removeCustomPreset(presetId: string): Promise<boolean> {
            try {
                const index = this.preset.presetConfigs.findIndex(p => p.id === presetId);
                if (index !== -1) {
                    const preset = this.preset.presetConfigs[index];

                    // 如果不是默认配置，尝试从服务器删除
                    if (preset.id !== 'default') {
                        // 尝试从服务器删除（如果存在对应的服务器配置）
                        const serverDeleteResult = await this.deleteConfigFromServer(presetId);
                        if (!serverDeleteResult) {
                            console.warn('服务器删除配置失败，但继续删除本地配置:', presetId);
                        }
                    }

                    // 从本地配置列表中删除
                    this.preset.presetConfigs.splice(index, 1);

                    // 如果删除的是当前选中的预设，切换到默认预设
                    if (this.preset.selectedPresetId === presetId) {
                        this.setSelectedPreset('default');
                    }

                    console.log('自定义预设配置删除成功:', presetId);
                    return true;
                }
                return false;
            } catch (error) {
                console.error('删除自定义预设配置失败:', error);
                return false;
            }
        },

        // 更新预设配置
        async updatePreset(presetId: string, updates: Partial<PresetConfig>): Promise<boolean> {
            try {
                const preset = this.preset.presetConfigs.find(p => p.id === presetId);
                if (preset) {
                    // 如果不是默认配置且包含AI参数更新，同步到服务器
                    if (preset.id !== 'default') {
                        const {name, ...aiParams} = updates;
                        if (Object.keys(aiParams).length > 0) {
                            // 提取完整的AI参数
                            const fullAiParams = {
                                promptTemplate: updates.promptTemplate || preset.promptTemplate,
                                size: updates.size || preset.size,
                                guidanceScale: updates.guidanceScale || preset.guidanceScale,
                                negativePrompt: updates.negativePrompt || preset.negativePrompt,
                                width: updates.width || preset.width,
                                height: updates.height || preset.height,
                                useCustomSize: updates.useCustomSize !== undefined ? updates.useCustomSize : preset.useCustomSize,
                                imageCount: updates.imageCount || preset.imageCount,
                                numInferenceSteps: updates.numInferenceSteps || preset.numInferenceSteps
                            };

                            // 保存到服务器
                            const serverConfigId = await this.saveConfigToServer(fullAiParams, presetId);
                            if (!serverConfigId) {
                                console.error('更新服务器配置失败');
                                return false;
                            }
                        }
                    }

                    // 更新本地配置
                    Object.assign(preset, updates, {updatedAt: new Date().toISOString()});

                    // 如果更新的是当前选中的预设，同步更新当前配置
                    if (this.preset.selectedPresetId === presetId) {
                        this.syncPresetToConfig(presetId);
                    }

                    console.log('预设配置更新成功:', presetId);
                    return true;
                }
                return false;
            } catch (error) {
                console.error('更新预设配置失败:', error);
                return false;
            }
        },


        // 服务器配置相关方法

        // 从服务器加载配置列表
        async loadConfigsFromServer() {
            this.server.loading = true;
            this.server.error = null;

            try {
                const result = await queryPaintingConfigs();
                if (result.code === 200 && result.data) {
                    this.server.configs = result.data;
                    console.log("begin to parse server configs",this.preset.presetConfigs);
                    // 将服务器配置转换为本地预设配置格式
                    this.preset.presetConfigs = result.data.map((serverConfig, index) => {
                        try {
                            const config = JSON.parse(serverConfig.config);
                            console.log(config)
                            return {
                                id: serverConfig.id,
                                ...config,
                                createdAt: serverConfig.createdAt,
                                updatedAt: serverConfig.updatedAt
                            } as PresetConfig;
                        } catch (error) {
                            console.error('解析服务器配置失败:', error);
                            return null;
                        }
                    }).filter(Boolean) as PresetConfig[];

                    console.log('从服务器加载配置成功:', this.preset.presetConfigs);
                } else {
                    this.server.error = result.msg || '加载配置失败';
                    console.error('加载配置失败:', result);
                }
            } catch (error) {
                this.server.error = '网络请求失败';
                console.error('加载配置网络错误:', error);
            } finally {
                this.server.loading = false;
            }
        },

        // 保存配置到服务器
        async saveConfigToServer(config: AiParams, configId?: string): Promise<string | null> {
            try {
                const configJson = JSON.stringify(config);
                const configDTO: PaintingConfigDTO = {
                    config: configJson
                };

                let result;
                if (configId && configId !== 'default') {
                    // 更新现有配置（排除默认配置）
                    configDTO.id = configId;
                    result = await updatePaintingConfig(configDTO);
                } else {
                    // 创建新配置（包括默认配置的更改）
                    result = await createPaintingConfig(configDTO);
                }

                if (result.code === 200 && result.data) {
                    console.log('保存配置到服务器成功:', result.data);
                    // 重新加载配置列表
                    await this.loadConfigsFromServer();
                    return result.data.id;
                } else {
                    this.server.error = result.msg || '保存配置失败';
                    console.error('保存配置失败:', result);
                    return null;
                }
            } catch (error) {
                this.server.error = '网络请求失败';
                console.error('保存配置网络错误:', error);
                return null;
            }
        },

        // 从服务器删除配置
        async deleteConfigFromServer(configId: string): Promise<boolean> {
            try {
                const configDTO: PaintingConfigDTO = {
                    id: configId
                };

                const result = await deletePaintingConfig(configDTO);

                if (result.code === 200) {
                    console.log('删除配置成功:', configId);
                    // 重新加载配置列表
                    await this.loadConfigsFromServer();
                    return true;
                } else {
                    this.server.error = result.msg || '删除配置失败';
                    console.error('删除配置失败:', result);
                    return false;
                }
            } catch (error) {
                this.server.error = '网络请求失败';
                console.error('删除配置网络错误:', error);
                return false;
            }
        },

        // 从服务器获取单个配置
        async getConfigFromServer(configId: string): Promise<AiParams | null> {
            try {
                const configDTO: PaintingConfigDTO = {
                    id: configId
                };

                const result = await getPaintingConfig(configDTO);

                if (result.code === 200 && result.data) {
                    try {
                        const config = JSON.parse(result.data.config);
                        console.log('获取配置成功:', config);
                        return config;
                    } catch (error) {
                        console.error('解析配置JSON失败:', error);
                        return null;
                    }
                } else {
                    this.server.error = result.msg || '获取配置失败';
                    console.error('获取配置失败:', result);
                    return null;
                }
            } catch (error) {
                this.server.error = '网络请求失败';
                console.error('获取配置网络错误:', error);
                return null;
            }
        },

        // 将当前配置保存到服务器
        async saveCurrentConfigToServer(): Promise<string | null> {
            const currentParams = this.getCurrentParams;
            return await this.saveConfigToServer(currentParams, this.preset.currentConfig.id);
        },

        // 从服务器配置加载到当前配置
        async loadConfigFromServer(configId: string): Promise<boolean> {
            const config = await this.getConfigFromServer(configId);
            if (config) {
                this.preset.currentConfig = {
                    id: configId,
                    config: JSON.stringify(config),
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString(),
                    deletedAt: ''
                };
                return true;
            }
            return false;
        },

        // 设置绘画加载状态
        setLoading(loading: boolean) {
            this.ui.loading = loading;
        },

        // 清除服务器错误
        clearServerError() {
            this.server.error = null;
        },

        // 初始化状态管理器
        async initializeStore() {
            try {
                console.log('开始初始化状态管理器...');

                // 从服务器加载配置列表
                await this.loadConfigsFromServer();

                // 如果服务器没有配置，创建默认配置
                if (this.server.configs.length === 0) {
                    console.log('服务器没有配置，创建默认配置...');
                    const defaultConfig = presetConfigsList[0];
                    const serverConfigId = await this.saveConfigToServer(defaultConfig);
                    if (serverConfigId) {
                        console.log('默认配置创建成功:', serverConfigId);
                    }
                }

                // 设置当前配置为第一个服务器配置或默认配置
                if (this.preset.presetConfigs.length > 0) {
                    const firstPreset = this.preset.presetConfigs[0];
                    this.preset.currentConfig = {
                        id: firstPreset.id,
                        config: JSON.stringify({
                            promptTemplate: firstPreset.promptTemplate,
                            size: firstPreset.size,
                            guidanceScale: firstPreset.guidanceScale,
                            negativePrompt: firstPreset.negativePrompt,
                            width: firstPreset.width,
                            height: firstPreset.height,
                            useCustomSize: firstPreset.useCustomSize,
                            imageCount: firstPreset.imageCount,
                            numInferenceSteps: firstPreset.numInferenceSteps
                        }),
                        createdAt: firstPreset.createdAt || new Date().toISOString(),
                        updatedAt: firstPreset.updatedAt || new Date().toISOString(),
                        deletedAt: ''
                    };
                    this.preset.selectedPresetId = firstPreset.id;
                } else {
                    // 使用默认配置
                    this.preset.currentConfig = {
                        id: 'default',
                        config: JSON.stringify(presetConfigsList[0]),
                        createdAt: new Date().toISOString(),
                        updatedAt: new Date().toISOString(),
                        deletedAt: ''
                    };
                    this.preset.selectedPresetId = 'default';
                }

                console.log('状态管理器初始化完成:', {
                    selectedPresetId: this.preset.selectedPresetId,
                    serverConfigsCount: this.server.configs.length,
                    currentConfig: this.preset.currentConfig
                });
            } catch (error) {
                console.error('状态管理器初始化失败:', error);
                // 初始化失败时使用默认配置
                this.preset.currentConfig = {
                    id: 'default',
                    config: JSON.stringify(presetConfigsList[0]),
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString(),
                    deletedAt: ''
                };
                this.preset.selectedPresetId = 'default';
            }
        }
    }
});