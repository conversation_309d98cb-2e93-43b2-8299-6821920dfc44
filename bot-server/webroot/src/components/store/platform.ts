import {defineStore} from "pinia";
import {Platform} from "@/components/common/model/model";
import {createPlatform, deletePlatform, updatePlatform} from "@/components/ai-bot-platform/req";
import {getPlatforms} from "@/components/common/manageRequest";


export const usePlatform = defineStore('platform', {
    state: () => {
        return {
            currentPlatform: {} as Platform, // 当前选中的平台
            platforms: [] as Platform[]
        }
    },
    persist: true,
    actions: {
        updatePlatform(platform: Platform) {
            updatePlatform(platform)
                .then(() => {
                    this.queryPlatforms()
                })
        },
        createPlatform(platform: Platform) {
            createPlatform(platform)
                .then(() => {
                    this.queryPlatforms()
                })
        },
        deletePlatform(platform: Platform) {
            deletePlatform(platform).then(() => {
                this.queryPlatforms()
            })
        },
        queryPlatforms() {
            getPlatforms().then((list) => {
                this.platforms = list.map(p => {
                    let config = {...p}
                    config.settings = JSON.parse(config.config)
                    return config
                })
            })
        }
    }
})