import {defineStore} from "pinia";
import {getSetting, updateSetting} from "@/components/ai-bot-setting/req";
import {Setting, TtsSettings} from "@/components/ai-bot-setting/model";

export const useGlobalSetting = defineStore('ai-bot-global-setting', {
    state: () => ({
        id: "",
        setting: {} as Setting
    }),
    persist: true,
    actions: {
        getSetting() {
            getSetting()
                .then((data: any) => {
                    this.id = data.id
                    const setting = JSON.parse(data.value)
                    if (!setting.tts) {
                        setting.tts = new TtsSettings()
                    } else {
                        setting.tts = {
                            currentTTS: setting.tts.currentTTS || '',
                            list: setting.tts.list || [],
                            enabled: setting.tts.enabled ?? false,
                            model: setting.tts.model || 'edge',
                            volume: setting.tts.volume ?? 50,
                            rate: setting.tts.rate ?? 1
                        }
                    }
                    this.setting = setting
                })
        },
        updateSetting(data: any) {
            updateSetting(this.id, data)
                .then((res: any) => {
                    this.getSetting()
                })
        },
    }
});