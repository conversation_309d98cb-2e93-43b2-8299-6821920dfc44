import {defineStore} from "pinia";

export const useAiBot = defineStore('ai-bot', {
    state: () => {
        return {
            // 消息面板折叠
            collapsed: true,
            // 主题模式
            theme: "light",
            // 插件配置抽屉
            pluginConfigDrawerOpen: false,
            // 编辑器最大化状态
            isMax: false,
            // 主显示界面
            ui: 'AiBotUI',
            // 设置界面
            setting: 'AiBotUI',
            // 菜单选中项
            selectedKeys: ['AiBotUI'],
        }
    },
    persist: true,
    actions: {
        setTheme(theme: string) {
            this.theme = theme
        },
        // 设置菜单选中项
        setSelectedKeys(keys: string[]) {
            this.selectedKeys = keys
        }
    },
    getters: {
        getUI() {
            return this.ui
        }
    }
})