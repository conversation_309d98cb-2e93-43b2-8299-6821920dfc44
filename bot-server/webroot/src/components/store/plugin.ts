import {defineStore} from "pinia";
import {Platform, Plugin} from "@/components/common/model/model";
import {updateAllPlugin, updatePlugin} from "@/components/ai-bot-plugins/req";
import {getPlugins} from "@/components/common/manageRequest";

export const useAiPluginStore = defineStore('ai-plugin', {
    state: () => {
        return {
            filter: '',
            plugins: [] as Plugin[],
            currentPlugin: {} as Plugin,
            checked: [] as string[],
        }
    },
    persist: true,
    actions: {
        updatePlugin(plugin: Plugin) {
            updatePlugin(plugin)
                .then(() => {
                    this.queryPlugins()
                })
        },

        /*
        *  把所有的 插件都更新为同一个 平台
        * */
        updateAllPlugin(platform: Platform) {
            updateAllPlugin(platform)
                .then(() => {
                    this.queryPlugins()
                })
        },

        async queryPlugins() {
            this.plugins = await getPlugins()
        },
        // 点击某一项时
    }
})