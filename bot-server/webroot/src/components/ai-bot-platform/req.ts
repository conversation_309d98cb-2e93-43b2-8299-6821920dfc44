import axiosForServer from "@/plugins/axiosForServer";
import {Result} from "@/components/common/model/system";
import {Model, Platform} from "@/components/common/model/model";


export function OllamaModels(platformID: string, modelType: string=""): Promise<Model[]> {
    return new Promise<Model[]>(resolve => {
        axiosForServer.get<Result<Model[]>>('/api/bot/ollama/models', {
            params: {
                ID: platformID,
                model: modelType
            }
        }).then(({data}) => {
            if (data.code == 200) {
                resolve(data.data)
                return
            }
            resolve([])
        })
    })
}

export function DeepSeekModels(platformID: string, modelType: string=""): Promise<Model[]> {
    return new Promise<Model[]>(resolve => {
        axiosForServer.get<Result<Model[]>>('/api/bot/deepSeek/models', {
            params: {
                ID: platformID,
                model: modelType
            }
        }).then(({data}) => {
            if (data.code == 200) {
                resolve(data.data)
                return
            }
            resolve([])
        })
    })
}

export function GiteeAiModels(platformID: string, modelType: string=""): Promise<Model[]> {
    return new Promise<Model[]>(resolve => {
        axiosForServer.get<Result<Model[]>>('/api/bot/giteeAI/models', {
            params: {
                ID: platformID,
                model: modelType
            }
        }).then(({data}) => {
            if (data.code == 200) {
                resolve(data.data)
                return
            }
            resolve([])
        })
    })
}

export function VolcengineModels(platformId: string, modelType: string=""): Promise<any> {
    return new Promise<any>(resolve => {
        axiosForServer.get<Result<any>>(`/api/bot/volcengine/models`, {
            params: {
                ID: platformId,
                model: modelType
            }
        })
            .then(({data}) => {
                if (data.code === 200) {
                    resolve(data.data)
                }
            })
    })
}

export function updatePlatform(platform: Platform): Promise<void> {
    return new Promise(resolve => {
        axiosForServer.post<Result<any>>('/api/bot/platforms/update', {
            ...platform
        }).then(({data}) => {
            resolve()
        })
    })
}

export function createPlatform(platform: Platform): Promise<void> {
    return new Promise<void>(resolve => {
        axiosForServer.post<Result<any>>('/api/bot/platforms/create', {
            ...platform
        }).then(({data}) => {
            resolve()
        })
    })
}

export function deletePlatform(platform: Platform): Promise<void> {
    return new Promise<void>(resolve => {
        axiosForServer.post<Result<any>>('/api/bot/platforms/delete', {
            ...platform
        }).then(({data}) => {
            resolve()
        })
    })
}