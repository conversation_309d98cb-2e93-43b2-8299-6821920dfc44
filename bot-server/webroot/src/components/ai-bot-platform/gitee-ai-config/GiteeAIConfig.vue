<template>
  <div class="w-full flex flex-row">
    <a-form class="size-full" :model="setting">
      <a-form-item
          label="服务地址"
          name="api"
          :rules="[
          { required: true, message: '服务地址不能为空' },
          { validator: validateApiRule }
        ]"
      >
        <a-input
            v-model:value="setting.api"
            placeholder="例如: https://gitee.com"
        />
      </a-form-item>

      <a-form-item
          label="API 密钥"
          name="key"
          :rules="[{ required: true, message: 'API 密钥不能为空' }]"
      >
        <a-input-password
            v-model:value="setting.key"
            placeholder="请输入 API 密钥"
        />
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import {Platform} from "@/components/common/model/model";
import {ref} from 'vue'
import {useAiBot} from "@/components/store/bot";
import '@/components/ai-bot-platform/config-style.css';

const props = defineProps<{
  ctx: Platform
}>()

const bot = useAiBot()
const setting = ref(props.ctx.settings)

// 验证URL格式
function isValidUrl(url: string): boolean {
  try {
    const urlObj = new URL(url)
    return urlObj.protocol === 'http:' || urlObj.protocol === 'https:'
  } catch {
    return false
  }
}

// API地址验证规则
const validateApiRule = async (_rule: any, value: string) => {
  if (value && !isValidUrl(value)) {
    return Promise.reject('请输入有效的http/https地址');
  }
  return Promise.resolve();
};

// 验证API地址
function validateApi() {
  const api = setting.value.api?.trim()

  if (!api) {
    return false
  }

  if (!isValidUrl(api)) {
    return false
  }

  return true
}

// 验证 Key
function validateKey() {
  const key = setting.value.key?.trim()

  if (!key) {
    return false
  }

  return true
}

// 清空配置数据
function clear() {
  setting.value.api = ''
  setting.value.key = ''
}

// 导出验证状态方法
function isValid(): boolean {
  return validateApi() && validateKey()
}

function Setting(): any {
  if (!validateApi() || !validateKey()) {
    return null
  }
  return setting.value
}

defineExpose({
  Setting,
  isValid,
  clear
})
</script>

<style scoped>
</style> 