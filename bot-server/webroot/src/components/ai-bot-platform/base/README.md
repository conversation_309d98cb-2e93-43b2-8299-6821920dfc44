# BaseModelSelector 基础模型选择器组件

## 概述

`BaseModelSelector` 是一个通用的模型选择器组件，提取了各个AI平台快捷配置组件的公共功能。通过统一的接口和可配置的参数，实现了代码复用和统一的用户体验。

## 功能特性

- ✅ **统一的UI界面**：所有平台使用相同的选择器样式
- ✅ **自动数据获取**：支持异步获取模型列表
- ✅ **智能默认选择**：自动选择配置中的模型或第一个可用模型
- ✅ **搜索过滤**：支持模型名称搜索
- ✅ **数据转换**：灵活的数据转换机制适配不同API格式
- ✅ **自定义显示**：支持插槽自定义模型选项显示
- ✅ **状态管理**：自动更新平台配置
- ✅ **错误处理**：完善的加载和错误状态处理

## 基本用法

```vue
<template>
  <BaseModelSelector
    :ctx="platformConfig"
    :models-fetcher="apiFunction"
    :data-transformer="transformData"
    placeholder="请选择模型"
    @model-change="onModelChange"
  />
</template>

<script setup lang="ts">
import BaseModelSelector from "@/components/ai-bot-platform/base/BaseModelSelector.vue";
import { ApiFunction } from "@/components/ai-bot-platform/req";

const props = defineProps<{
  ctx: Platform
}>();

function transformData(rawData: any[]) {
  return rawData.map(item => ({
    name: item.name,
    type: item.type,
    description: item.description
  }));
}

function onModelChange(modelName: string) {
  console.log("模型已切换到:", modelName);
}
</script>
```

## Props 参数

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| `ctx` | `Platform` | ✅ | - | 平台配置对象 |
| `modelsFetcher` | `(platformId: string) => Promise<BaseModelInfo[]>` | ✅ | - | 获取模型列表的API函数 |
| `dataTransformer` | `(rawData: any[]) => BaseModelInfo[]` | ❌ | `ModelDataTransformers.standard` | 数据转换函数 |
| `modelFilter` | `(model: BaseModelInfo) => boolean` | ❌ | `ModelFilters.all` | 模型过滤函数 |
| `placeholder` | `string` | ❌ | `"请选择模型"` | 选择器占位符 |
| `disabled` | `boolean` | ❌ | `false` | 是否禁用选择器 |
| `autoFetch` | `boolean` | ❌ | `true` | 是否在挂载时自动获取数据 |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `model-change` | `modelName: string` | 模型选择改变时触发 |
| `models-loaded` | `models: BaseModelInfo[]` | 模型列表加载完成时触发 |
| `load-error` | `error: any` | 加载失败时触发 |

## Slots 插槽

### model-option

自定义模型选项的显示内容。

```vue
<BaseModelSelector :ctx="ctx" :models-fetcher="fetchModels">
  <template #model-option="{ model }">
    <div class="custom-model-item">
      <span class="model-name">{{ model.name }}</span>
      <span class="model-desc">{{ model.description }}</span>
    </div>
  </template>
</BaseModelSelector>
```

## 数据结构

### BaseModelInfo

```typescript
interface BaseModelInfo {
  name: string;           // 模型名称（必填）
  type?: string;          // 模型类型
  description?: string;   // 模型描述
  [key: string]: any;     // 其他扩展字段
}
```

## 模型过滤

组件支持通过 `modelFilter` 属性来过滤要显示的模型。这个功能非常有用，可以根据不同的业务需求来控制显示哪些模型。

### 内置过滤器

```typescript
import { ModelFilters } from "@/components/ai-bot-platform/base/types";

// 显示所有模型（默认）
ModelFilters.all

// 只显示已启用的模型
ModelFilters.enabledOnly

// 按类型过滤
ModelFilters.byType('chat')

// 按多个类型过滤
ModelFilters.byTypes(['chat', 'embedding'])

// 按名称包含关键词过滤
ModelFilters.byNameContains('gpt')

// 按名称正则表达式过滤
ModelFilters.byNamePattern(/^gpt-[0-9]/i)

// 排除特定名称的模型
ModelFilters.excludeNames(['deprecated-model', 'test-model'])

// 组合过滤器 - AND逻辑
ModelFilters.and(
  ModelFilters.enabledOnly,
  ModelFilters.byType('chat')
)

// 组合过滤器 - OR逻辑
ModelFilters.or(
  ModelFilters.byType('chat'),
  ModelFilters.byType('completion')
)

// 反转过滤器结果
ModelFilters.not(ModelFilters.byType('embedding'))
```

### 自定义过滤器

```typescript
// 自定义过滤器函数
const customFilter = (model: BaseModelInfo) => {
  // 只显示版本号大于1.0的模型
  return model.version && parseFloat(model.version) > 1.0;
};

// 使用自定义过滤器
<BaseModelSelector
  :ctx="ctx"
  :models-fetcher="fetchModels"
  :model-filter="customFilter"
/>
```

### 动态过滤

```vue
<template>
  <div>
    <!-- 过滤控制 -->
    <a-radio-group v-model:value="filterType" @change="updateFilter">
      <a-radio-button value="all">全部</a-radio-button>
      <a-radio-button value="chat">对话模型</a-radio-button>
      <a-radio-button value="embedding">嵌入模型</a-radio-button>
      <a-radio-button value="enabled">仅启用</a-radio-button>
    </a-radio-group>
    
    <!-- 模型选择器 -->
    <BaseModelSelector
      :ctx="ctx"
      :models-fetcher="fetchModels"
      :model-filter="currentFilter"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { ModelFilters } from "@/components/ai-bot-platform/base/types";

const filterType = ref('all');

const currentFilter = computed(() => {
  switch (filterType.value) {
    case 'chat':
      return ModelFilters.byType('chat');
    case 'embedding':
      return ModelFilters.byType('embedding');
    case 'enabled':
      return ModelFilters.enabledOnly;
    default:
      return ModelFilters.all;
  }
});
</script>
```

## 实际应用示例

### 1. DeepSeek 平台（基础用法）

```vue
<template>
  <BaseModelSelector
    :ctx="ctx"
    :models-fetcher="DeepSeekModels"
    :data-transformer="ModelDataTransformers.standard"
    placeholder="请选择DeepSeek模型"
    @model-change="onModelChange"
  />
</template>

<script setup lang="ts">
import { DeepSeekModels } from "@/components/ai-bot-platform/req";
import BaseModelSelector from "@/components/ai-bot-platform/base/BaseModelSelector.vue";
import { ModelDataTransformers } from "@/components/ai-bot-platform/base/types";
</script>
```

### 2. DeepSeek 平台（只显示对话模型）

```vue
<template>
  <BaseModelSelector
    :ctx="ctx"
    :models-fetcher="DeepSeekModels"
    :data-transformer="ModelDataTransformers.standard"
    :model-filter="ModelFilters.byType('chat')"
    placeholder="请选择DeepSeek对话模型"
  />
</template>

<script setup lang="ts">
import { DeepSeekModels } from "@/components/ai-bot-platform/req";
import BaseModelSelector from "@/components/ai-bot-platform/base/BaseModelSelector.vue";
import { ModelDataTransformers, ModelFilters } from "@/components/ai-bot-platform/base/types";
</script>
```

### 3. Volcengine 平台（带描述信息 + 只显示启用的模型）

```vue
<template>
  <BaseModelSelector
    :ctx="ctx"
    :models-fetcher="VolcengineModels"
    :data-transformer="ModelDataTransformers.withDescription"
    :model-filter="ModelFilters.enabledOnly"
    placeholder="请选择Volcengine模型"
  >
    <template #model-option="{ model }">
      <div class="flex justify-between" :title="model.description">
        <span>{{ model.name }}</span>
        <span v-if="model.description" class="text-gray-400 text-xs truncate">
          {{ model.description }}
        </span>
      </div>
    </template>
  </BaseModelSelector>
</template>

<script setup lang="ts">
import { VolcengineModels } from "@/components/ai-bot-platform/req";
import BaseModelSelector from "@/components/ai-bot-platform/base/BaseModelSelector.vue";
import { ModelDataTransformers, ModelFilters } from "@/components/ai-bot-platform/base/types";
</script>
```

### 4. 可选平台配置（如Ollama）+ 复合过滤

```vue
<template>
  <BaseModelSelector
    v-if="ctx"
    :ctx="ctx"
    :models-fetcher="OllamaModels"
    :model-filter="chatModelsOnly"
    placeholder="请选择Ollama对话模型"
  />
  <div v-else class="text-center text-gray-400 py-4">
    请先配置平台信息
  </div>
</template>

<script setup lang="ts">
import { OllamaModels } from "@/components/ai-bot-platform/req";
import BaseModelSelector from "@/components/ai-bot-platform/base/BaseModelSelector.vue";
import { ModelFilters } from "@/components/ai-bot-platform/base/types";

// 只显示启用的对话类型模型
const chatModelsOnly = ModelFilters.and(
  ModelFilters.enabledOnly,
  ModelFilters.byType('chat')
);
</script>
```

## 方法暴露

组件通过 `defineExpose` 暴露以下方法：

```typescript
// 手动刷新模型列表
await modelSelectorRef.value.refreshModels();

// 获取当前模型列表
const models = modelSelectorRef.value.modelList;

// 获取当前选中模型
const selected = modelSelectorRef.value.selectedModel;

// 获取加载状态
const isLoading = modelSelectorRef.value.loading;
```

## 最佳实践

1. **数据转换**：为不同平台提供合适的数据转换函数，确保数据格式统一
2. **模型过滤**：使用合适的过滤器来控制显示哪些模型，提升用户体验
3. **错误处理**：监听 `load-error` 事件，提供用户友好的错误提示
4. **自定义显示**：对于有特殊显示需求的平台，使用 `model-option` 插槽
5. **性能优化**：合理使用 `autoFetch` 参数，避免不必要的API调用
6. **状态管理**：监听 `models-loaded` 事件来获取统计信息或执行其他业务逻辑

### 模型过滤最佳实践

- **分层过滤**：优先使用内置过滤器，需要时再自定义
- **用户控制**：为用户提供过滤器控制界面，让用户自主选择
- **合理默认**：设置合理的默认过滤条件，如只显示启用的模型
- **性能考虑**：复杂的过滤逻辑应该在数据转换阶段完成，而不是在显示阶段

## 迁移指南

从旧的独立组件迁移到 `BaseModelSelector`：

1. **清理旧代码**：移除重复的模板代码和样式
2. **API集成**：将API调用函数作为 `models-fetcher` prop传入
3. **数据转换**：如有数据转换需求，提供 `data-transformer` 函数
4. **模型过滤**：根据业务需求添加 `model-filter` 函数
5. **事件处理**：将事件处理逻辑迁移到对应的事件回调中
6. **自定义显示**：如有自定义显示需求，使用 `model-option` 插槽

### 迁移前后对比

**迁移前**（以DeepSeek为例）：
```vue
<!-- 70行重复代码 -->
<div class="w-full">
  <div class="w-full flex flex-row relative">
    <span class="icon iconfont moxing mr-1"></span>
    <div class="select-wrapper flex-1">
      <a-select v-model:value="model" @select="select">
        <!-- 更多重复代码... -->
      </a-select>
    </div>
  </div>
</div>
```

**迁移后**：
```vue
<!-- 只需10行简洁代码 -->
<BaseModelSelector
  :ctx="ctx"
  :models-fetcher="DeepSeekModels"
  :data-transformer="ModelDataTransformers.standard"
  :model-filter="ModelFilters.enabledOnly"
  placeholder="请选择DeepSeek模型"
/>
```

### 收益总结

- ✅ **代码减少70%**：显著减少重复代码
- ✅ **功能增强**：新增模型过滤、错误处理等功能
- ✅ **维护性提升**：统一的组件接口和实现
- ✅ **体验一致**：所有平台保持相同的交互体验
- ✅ **扩展性强**：新增平台只需几行代码

## 快捷配置组件支持

所有现有的快捷配置组件都已支持 `model-filter` 属性透传：

### 支持的组件列表

| 组件 | 位置 | 说明 |
|------|------|------|
| `DeepSeekQuickConfig` | `deep-seek-config/` | DeepSeek平台模型选择 |
| `GiteeAIQuickConfig` | `gitee-ai-config/` | Gitee AI平台模型选择 |
| `VolcengineQuickConfig` | `volcengine-ai-config/` | Volcengine平台模型选择 |
| `OllamaQuickConfig` | `ollama-config/` | Ollama平台模型选择 |

### 统一的使用方式

```vue
<!-- 基础用法 -->
<DeepSeekQuickConfig :ctx="platformConfig" />

<!-- 带过滤器用法 -->
<DeepSeekQuickConfig 
  :ctx="platformConfig"
  :model-filter="ModelFilters.enabledOnly"
/>

<!-- 组合过滤器 -->
<GiteeAIQuickConfig 
  :ctx="platformConfig"
  :model-filter="ModelFilters.and(
    ModelFilters.enabledOnly,
    ModelFilters.byType('chat')
  )"
/>
```

### Props 接口

所有快捷配置组件都支持以下 Props：

```typescript
interface QuickConfigProps {
  ctx: Platform;                    // 平台配置对象（必填）
  modelFilter?: ModelFilter;        // 可选的模型过滤器
}
```

这样可以显著减少代码重复，提高维护性，并确保各平台的用户体验一致性。 