<template>
  <!-- 示例平台快捷配置面板 -->
  <BaseModelSelector
    :ctx="ctx"
    :models-fetcher="ExamplePlatformModels"
    :data-transformer="transformExampleData"
    :model-filter="modelFilter"
    placeholder="请选择示例平台模型"
    @model-change="onModelChange"
    @models-loaded="onModelsLoaded"
    @load-error="onLoadError"
  >
    <!-- 可选：自定义模型选项显示 -->
    <template #model-option="{ model }" v-if="showCustomDisplay">
      <div class="flex justify-between items-center">
        <div>
          <span class="font-medium">{{ model.name }}</span>
          <span v-if="model.version" class="text-xs text-blue-500 ml-1">
            v{{ model.version }}
          </span>
        </div>
        <div class="text-right">
          <div v-if="model.description" class="text-xs text-gray-500 truncate max-w-32">
            {{ model.description }}
          </div>
          <div v-if="model.type" class="text-xs text-green-600">
            {{ model.type }}
          </div>
        </div>
      </div>
    </template>
  </BaseModelSelector>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { Platform } from "@/components/common/model/model";
import BaseModelSelector from "@/components/ai-bot-platform/base/BaseModelSelector.vue";
import { 
  BaseModelInfo, 
  ModelDataTransformers,
  ModelSelectorUtils,
  ModelFilters
} from "@/components/ai-bot-platform/base/types";

// Props 定义
const props = defineProps<{
  ctx: Platform;
  // 可选：是否显示自定义显示样式
  showCustomDisplay?: boolean;
  // 可选：只显示特定类型的模型
  filterByType?: string;
}>();

// 示例平台的API函数（实际使用时需要替换为真实的API）
async function ExamplePlatformModels(platformId: string): Promise<BaseModelInfo[]> {
  // 模拟API调用
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve([
        { 
          name: "example-gpt-4", 
          type: "chat", 
          description: "高级对话模型",
          version: "1.0.0",
          enabled: true,
          context_length: 8192
        },
        { 
          name: "example-gpt-3.5", 
          type: "chat", 
          description: "标准对话模型",
          version: "1.2.1",
          enabled: true,
          context_length: 4096
        },
        { 
          name: "example-embeddings", 
          type: "embedding", 
          description: "文本嵌入模型",
          version: "2.0.0",
          enabled: false,
          dimensions: 1536
        }
      ]);
    }, 1000);
  });
}

// 数据转换器 - 使用标准转换器，包含描述信息
const transformExampleData = ModelDataTransformers.withDescription;

// 模型过滤器 - 根据props动态配置
const modelFilter = computed(() => {
  if (props.filterByType) {
    // 如果需要过滤特定类型的模型
    return ModelFilters.byType(props.filterByType);
  }
  
  // 默认显示所有模型
  return ModelFilters.all;
});

// 事件处理函数
function onModelChange(modelName: string) {
  console.log(`示例平台模型已切换到: ${modelName}`);
  
  // 可选：额外的业务逻辑
  // 例如：记录用户选择、发送统计数据等
}

function onModelsLoaded(models: BaseModelInfo[]) {
  console.log("示例平台模型列表加载完成:", models);
  
  // 可选：对加载的模型进行额外处理
  const validModels = ModelSelectorUtils.filterValidModels(models);
  const enabledModels = validModels.filter(m => m.enabled !== false);
  
  console.log(`共加载 ${models.length} 个模型，其中 ${enabledModels.length} 个可用`);
}

function onLoadError(error: any) {
  console.error("示例平台模型列表加载失败:", error);
  
  // 可选：错误处理逻辑
  // 例如：显示用户友好的错误消息、重试机制等
}
</script>

<style scoped>
/* 自定义样式 */
.truncate {
  max-width: 128px;
}
</style>

<!--
使用说明：

1. 复制此文件并重命名为具体平台名称，如 "NewPlatformQuickConfig.vue"

2. 替换相关内容：
   - 将 "ExamplePlatform" 替换为实际平台名称
   - 将 "ExamplePlatformModels" 替换为实际的API函数
   - 根据实际API返回的数据结构调整数据转换器

3. 根据需要自定义：
   - 调整模型选项的显示样式（使用 model-option 插槽）
   - 配置模型过滤器来控制显示哪些模型
   - 添加平台特定的业务逻辑
   - 处理平台特有的错误情况

4. 在父组件中使用：
   <!-- 基础用法 -->
   <NewPlatformQuickConfig :ctx="platformConfig" />
   
   <!-- 只显示对话模型 -->
   <NewPlatformQuickConfig :ctx="platformConfig" filter-by-type="chat" />
   
   <!-- 启用自定义显示样式 -->
   <NewPlatformQuickConfig :ctx="platformConfig" show-custom-display />

5. 如果需要更复杂的功能，可以：
   - 添加更多的 props 来控制行为
   - 暴露更多的事件给父组件
   - 使用 ref 获取组件实例来调用方法
   - 使用复合过滤器（ModelFilters.and, ModelFilters.or等）

6. 常用的过滤器示例：
   - 只显示启用的模型：ModelFilters.enabledOnly
   - 显示多种类型：ModelFilters.byTypes(['chat', 'completion'])
   - 排除测试模型：ModelFilters.excludeNames(['test-model'])
   - 复合条件：ModelFilters.and(ModelFilters.enabledOnly, ModelFilters.byType('chat'))

这个模板提供了一个标准化的方式来创建新平台的快捷配置组件，
确保所有平台的用户体验保持一致，同时支持灵活的模型过滤功能。
--> 