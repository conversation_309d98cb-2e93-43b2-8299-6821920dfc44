<template>
  <!-- 基础模型选择器组件 -->
  <div class="w-full">
    <div class="w-full flex flex-row relative">
      <span class="icon iconfont moxing mr-1"></span>
      <div class="select-wrapper flex-1" ref="selectWrapper">
        <a-select
          v-model:value="selectedModel"
          @select="handleSelect"
          class="w-full"
          size="small" 
          show-search
          :filter-option="filterOption"
          :loading="loading"
          :placeholder="placeholder"
          :disabled="disabled"
        >
          <a-select-option
            class="my-1"
            v-for="item in modelList"
            :key="item.name"
            :value="item.name"
            :title="item.description || item.name"
          >
            <slot name="model-option" :model="item">
              <div class="size-full flex flex-row justify-lg-between">
                <span>{{ item.name }}</span>
                <span v-if="item.description" class="text-gray-400 text-xs ml-2">
                  {{ item.description }}
                </span>
              </div>
            </slot>
          </a-select-option>
        </a-select>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch, computed } from "vue";
import { Platform } from "@/components/common/model/model";
import { usePlatform } from "@/components/store/platform";
import { 
  BaseModelInfo, 
  BaseModelSelectorProps, 
  BaseModelSelectorEmits,
  ModelDataTransformers,
  ModelFilters
} from "./types";

// 使用类型定义接口
const props = withDefaults(defineProps<BaseModelSelectorProps>(), {
  placeholder: "请选择模型",
  disabled: false,
  autoFetch: true,
  dataTransformer: ModelDataTransformers.standard,
  modelFilter: ModelFilters.all
});

const emit = defineEmits<BaseModelSelectorEmits>();

const plat = usePlatform();
const modelList = ref<BaseModelInfo[]>([]);
const selectedModel = ref<string>();
const loading = ref(false);
const selectWrapper = ref<HTMLElement | null>(null);

// 搜索过滤函数
function filterOption(input: string, option: any) {
  return option.value.toLowerCase().indexOf(input.toLowerCase()) >= 0;
}

// 获取弹出容器
function getPopupContainer() {
  return selectWrapper.value || document.body;
}

// 处理模型选择
function handleSelect(value: string) {
  selectedModel.value = value;
  updatePlatformConfig(value);
  emit('modelChange', value);
}

// 更新平台配置
function updatePlatformConfig(modelName: string) {
  if (!props.ctx) return;
  
  const obj = { ...props.ctx };
  obj.settings.currentModel = modelName;
  obj.config = JSON.stringify(obj.settings);
  plat.updatePlatform(obj);
}

// 获取模型列表
async function fetchModels() {
  if (!props.ctx?.id) return;
  
  loading.value = true;
  try {
    const rawModels = await props.modelsFetcher(props.ctx.id, props.modelType);
    const transformedModels = props.dataTransformer(rawModels);
    const filteredModels = (transformedModels || []).filter(props.modelFilter);
    modelList.value = filteredModels;
    
    // 设置默认选中模型
    setDefaultModel();
    
    emit('modelsLoaded', modelList.value);
  } catch (error) {
    console.error('获取模型列表失败:', error);
    modelList.value = [];
    emit('loadError', error);
  } finally {
    loading.value = false;
  }
}

// 设置默认模型
function setDefaultModel() {
  if (!modelList.value || modelList.value.length === 0) return;
  
  const currentModel = props.ctx?.settings?.currentModel;
  
  // 如果配置中有指定模型且在列表中存在，则使用它
  if (currentModel && modelList.value.some(m => m.name === currentModel)) {
    selectedModel.value = currentModel;
    return;
  }
  
  // 否则使用第一个模型作为默认值
  const defaultModel = modelList.value[0].name;
  selectedModel.value = defaultModel;
  updatePlatformConfig(defaultModel);
}

// 手动刷新模型列表
function refreshModels() {
  return fetchModels();
}

// 监听ctx变化，重新获取模型
watch(() => props.ctx?.id, (newId, oldId) => {
  if (newId && newId !== oldId) {
    fetchModels();
  }
});

// 监听当前模型配置变化
watch(() => props.ctx?.settings?.currentModel, (newModel) => {
  if (newModel && newModel !== selectedModel.value) {
    selectedModel.value = newModel;
  }
});

// 组件挂载时自动获取模型
onMounted(() => {
  if (props.autoFetch) {
    fetchModels();
  }
});

// 暴露方法给父组件
defineExpose({
  refreshModels,
  modelList: computed(() => modelList.value),
  selectedModel: computed(() => selectedModel.value),
  loading: computed(() => loading.value)
});
</script>

<style scoped>
.select-wrapper {
  position: relative;
}

/* 确保下拉框宽度与选择器一致 */
.select-wrapper :deep(.ant-select-dropdown) {
  min-width: 100%;
}
</style> 