# 快捷配置组件的模型过滤使用示例

所有快捷配置组件现在都支持 `model-filter` 属性的透传，让您可以灵活控制显示哪些模型。

## 支持的组件

- `DeepSeekQuickConfig` - DeepSeek平台快捷配置
- `GiteeAIQuickConfig` - Gitee AI平台快捷配置  
- `VolcengineQuickConfig` - Volcengine平台快捷配置
- `OllamaQuickConfig` - Ollama平台快捷配置

## 基础用法示例

### 1. 不使用过滤器（默认行为）

```vue
<template>
  <!-- 显示所有可用模型 -->
  <DeepSeekQuickConfig :ctx="platformConfig" />
</template>
```

### 2. 只显示对话类型模型  

```vue
<template>
  <DeepSeekQuickConfig 
    :ctx="platformConfig"
    :model-filter="ModelFilters.byType('chat')"
  />
</template>

<script setup lang="ts">
import { ModelFilters } from "@/components/ai-bot-platform/base/types";
</script>
```

### 3. 只显示已启用的模型

```vue
<template>
  <GiteeAIQuickConfig 
    :ctx="platformConfig"
    :model-filter="ModelFilters.enabledOnly"
  />
</template>

<script setup lang="ts">
import { ModelFilters } from "@/components/ai-bot-platform/base/types";
</script>
```

### 4. 组合过滤器 - 已启用的对话模型

```vue
<template>
  <VolcengineQuickConfig 
    :ctx="platformConfig"
    :model-filter="chatAndEnabledFilter"
  />
</template>

<script setup lang="ts">
import { ModelFilters } from "@/components/ai-bot-platform/base/types";

// 只显示已启用的对话模型
const chatAndEnabledFilter = ModelFilters.and(
  ModelFilters.enabledOnly,
  ModelFilters.byType('chat')
);
</script>
```

### 5. 自定义过滤器

```vue
<template>
  <OllamaQuickConfig 
    :ctx="platformConfig"
    :model-filter="customFilter"
  />
</template>

<script setup lang="ts">
import { BaseModelInfo } from "@/components/ai-bot-platform/base/types";

// 自定义过滤器：只显示名称包含'llama'的模型
const customFilter = (model: BaseModelInfo): boolean => {
  return model.name.toLowerCase().includes('llama');
};
</script>
```

## 动态过滤示例

### 用户控制的动态过滤

```vue
<template>
  <div class="space-y-4">
    <!-- 过滤器控制面板 -->
    <div class="flex items-center space-x-4 p-3 bg-gray-50 rounded-lg">
      <span class="text-sm font-medium">模型类型：</span>
      <a-radio-group v-model:value="filterType" size="small">
        <a-radio-button value="all">全部</a-radio-button>
        <a-radio-button value="chat">对话</a-radio-button>
        <a-radio-button value="code">代码</a-radio-button>
        <a-radio-button value="embedding">嵌入</a-radio-button>
      </a-radio-group>
      
      <a-divider type="vertical" />
      
      <a-checkbox v-model:checked="onlyEnabled">
        仅显示启用模型
      </a-checkbox>
    </div>

    <!-- 快捷配置组件 -->
    <DeepSeekQuickConfig 
      :ctx="platformConfig"
      :model-filter="dynamicFilter"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { ModelFilters } from "@/components/ai-bot-platform/base/types";
import DeepSeekQuickConfig from "@/components/ai-bot-platform/deep-seek-config/DeepSeekQuickConfig.vue";

const filterType = ref('all');
const onlyEnabled = ref(false);

const dynamicFilter = computed(() => {
  const filters = [];
  
  // 类型过滤
  if (filterType.value !== 'all') {
    filters.push(ModelFilters.byType(filterType.value));
  }
  
  // 启用状态过滤
  if (onlyEnabled.value) {
    filters.push(ModelFilters.enabledOnly);
  }
  
  // 如果没有过滤条件，显示所有
  return filters.length === 0 ? ModelFilters.all : ModelFilters.and(...filters);
});
</script>
```

## 多平台统一过滤

```vue
<template>
  <div class="grid grid-cols-2 gap-4">
    <!-- 左侧：DeepSeek -->
    <div class="space-y-2">
      <h3 class="font-medium">DeepSeek 模型</h3>
      <DeepSeekQuickConfig 
        :ctx="deepSeekConfig"
        :model-filter="commonFilter"
      />
    </div>
    
    <!-- 右侧：Gitee AI -->
    <div class="space-y-2">
      <h3 class="font-medium">Gitee AI 模型</h3>
      <GiteeAIQuickConfig 
        :ctx="giteeConfig"
        :model-filter="commonFilter"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ModelFilters } from "@/components/ai-bot-platform/base/types";
import DeepSeekQuickConfig from "@/components/ai-bot-platform/deep-seek-config/DeepSeekQuickConfig.vue";
import GiteeAIQuickConfig from "@/components/ai-bot-platform/gitee-ai-config/GiteeAIQuickConfig.vue";

// 所有平台使用相同的过滤器：只显示已启用的对话模型
const commonFilter = ModelFilters.and(
  ModelFilters.enabledOnly,
  ModelFilters.byType('chat')
);
</script>
```

## 高级过滤示例

### 按模型名称模式过滤

```vue
<template>
  <OllamaQuickConfig 
    :ctx="platformConfig"
    :model-filter="gptModelsOnly"
  />
</template>

<script setup lang="ts">
import { ModelFilters } from "@/components/ai-bot-platform/base/types";

// 只显示名称以 'gpt' 开头的模型
const gptModelsOnly = ModelFilters.byNamePattern(/^gpt/i);
</script>
```

### 排除特定模型

```vue
<template>
  <VolcengineQuickConfig 
    :ctx="platformConfig"
    :model-filter="excludeTestModels"
  />
</template>

<script setup lang="ts">
import { ModelFilters } from "@/components/ai-bot-platform/base/types";

// 排除测试和实验性模型
const excludeTestModels = ModelFilters.excludeNames([
  'test-model',
  'experimental-model',
  'debug-model'
]);
</script>
```

### 复杂业务逻辑过滤

```vue
<template>
  <DeepSeekQuickConfig 
    :ctx="platformConfig"
    :model-filter="businessFilter"
  />
</template>

<script setup lang="ts">
import { BaseModelInfo } from "@/components/ai-bot-platform/base/types";

// 复杂的业务逻辑过滤
const businessFilter = (model: BaseModelInfo): boolean => {
  // 优先级规则：
  // 1. 必须是已启用的模型
  if (model.enabled === false) return false;
  
  // 2. 对话类型模型优先显示
  if (model.type === 'chat') return true;
  
  // 3. 如果是代码类型，只显示最新版本
  if (model.type === 'code') {
    const version = parseFloat(model.version || '0');
    return version >= 2.0;
  }
  
  // 4. 其他类型默认不显示
  return false;
};
</script>
```

## 注意事项

1. **向后兼容**：不传递 `model-filter` 时，组件会显示所有模型
2. **性能考虑**：过滤器函数会在每次模型列表更新时执行，避免在过滤器中执行复杂计算
3. **类型安全**：使用 TypeScript 时，确保导入正确的类型定义
4. **组合使用**：可以使用 `ModelFilters.and()`, `ModelFilters.or()` 来组合多个过滤条件

## 常用过滤器快速参考

```typescript
// 显示所有模型
ModelFilters.all

// 只显示启用的模型  
ModelFilters.enabledOnly

// 按单一类型过滤
ModelFilters.byType('chat')

// 按多个类型过滤
ModelFilters.byTypes(['chat', 'code'])

// 按名称关键词过滤
ModelFilters.byNameContains('gpt')

// 按名称模式过滤
ModelFilters.byNamePattern(/^llama/i)

// 排除特定模型
ModelFilters.excludeNames(['test-model'])

// 组合过滤器（AND）
ModelFilters.and(filter1, filter2, filter3)

// 组合过滤器（OR）
ModelFilters.or(filter1, filter2)

// 反转过滤器
ModelFilters.not(filter)
```

通过这些过滤功能，您可以为用户提供更精准和个性化的模型选择体验！ 