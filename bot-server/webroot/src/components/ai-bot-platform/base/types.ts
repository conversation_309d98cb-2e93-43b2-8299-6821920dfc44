/**
 * 基础模型选择器相关类型定义
 */

/**
 * 基础模型信息接口
 * 所有平台的模型数据都应该转换为这个格式
 */
export interface BaseModelInfo {
  /** 模型名称（必填，用作唯一标识） */
  name: string;
  /** 模型类型（如chat、embedding等） */
  type?: string;
  /** 模型描述信息 */
  description?: string;
  /** 模型版本 */
  version?: string;
  /** 是否可用 */
  enabled?: boolean;
  /** 其他扩展字段 */
  [key: string]: any;
}

/**
 * 模型获取函数类型
 * 所有平台的API函数都应该符合这个签名
 * 参数1：平台ID
 * 参数2：模型类型
 */
export type ModelsFetcher = (platformId: string, modelType: string) => Promise<BaseModelInfo[]>;

/**
 * 数据转换函数类型
 * 用于将平台特定的数据格式转换为BaseModelInfo[]
 */
export type DataTransformer = (rawData: any[]) => BaseModelInfo[];

/**
 * 模型过滤函数类型
 * 用于过滤要显示的模型
 */
export type ModelFilter = (model: BaseModelInfo) => boolean;

/**
 * 基础模型选择器组件的Props接口
 */
export interface BaseModelSelectorProps {
  /** 平台配置对象 */
  ctx: import("@/components/common/model/model").Platform;
  /** 获取模型列表的函数 */
  modelsFetcher: ModelsFetcher;
  /** 数据转换器，用于处理不同平台返回的数据结构 */
  dataTransformer?: DataTransformer;
  /** 模型过滤函数，用于过滤要显示的模型 */
  modelFilter?: ModelFilter;
  /** 占位符文本 */
  placeholder?: string;
  /** 是否禁用 */
  disabled?: boolean;
  /** 是否在挂载时自动获取模型 */
  autoFetch?: boolean;
  /** 模型类型 */
  modelType?: string;
}

/**
 * 基础模型选择器组件的事件接口
 */
export interface BaseModelSelectorEmits {
  /** 模型选择改变时触发 */
  modelChange: [modelName: string];
  /** 模型列表加载完成时触发 */
  modelsLoaded: [models: BaseModelInfo[]];
  /** 加载失败时触发 */
  loadError: [error: any];
}

/**
 * 基础模型选择器组件暴露的方法接口
 */
export interface BaseModelSelectorExpose {
  /** 手动刷新模型列表 */
  refreshModels: () => Promise<void>;
  /** 当前模型列表 */
  modelList: import("vue").ComputedRef<BaseModelInfo[]>;
  /** 当前选中的模型 */
  selectedModel: import("vue").ComputedRef<string | undefined>;
  /** 加载状态 */
  loading: import("vue").ComputedRef<boolean>;
}

/**
 * 平台模型配置的统一接口
 */
export interface PlatformModelConfig {
  /** 当前选中的模型 */
  currentModel?: string;
  /** 可用的模型列表 */
  availableModels?: BaseModelInfo[];
  /** 最后更新时间 */
  lastUpdated?: Date;
}

/**
 * 常用的数据转换器工厂函数
 */
export class ModelDataTransformers {
  /**
   * 标准转换器 - 适用于大多数平台
   */
  static standard: DataTransformer = (rawData: any[]) => {
    return rawData.map(item => ({
      name: item.name || item.id,
      type: item.type,
      description: item.description,
      version: item.version,
      enabled: item.enabled !== false
    }));
  };

  /**
   * 简单转换器 - 只保留基本字段
   */
  static simple: DataTransformer = (rawData: any[]) => {
    return rawData.map(item => ({
      name: item.name || item.id,
      type: item.type
    }));
  };

  /**
   * 带描述的转换器 - 适用于有详细描述的平台
   */
  static withDescription: DataTransformer = (rawData: any[]) => {
    return rawData.map(item => ({
      name: item.name || item.id,
      type: item.type,
      description: item.description || item.summary || item.detail,
      version: item.version
    }));
  };

  /**
   * 自定义转换器工厂
   */
  static custom(transformer: (item: any, index: number, array: any[]) => BaseModelInfo): DataTransformer {
    return (rawData: any[]) => rawData.map(transformer);
  }
}

/**
 * 模型选择器工具类
 */
export class ModelSelectorUtils {
  /**
   * 验证模型信息是否有效
   */
  static isValidModel(model: any): model is BaseModelInfo {
    return model && typeof model.name === 'string' && model.name.trim().length > 0;
  }

  /**
   * 过滤有效的模型
   */
  static filterValidModels(models: any[]): BaseModelInfo[] {
    return models.filter(this.isValidModel);
  }

  /**
   * 按名称查找模型
   */
  static findModelByName(models: BaseModelInfo[], name: string): BaseModelInfo | undefined {
    return models.find(model => model.name === name);
  }

  /**
   * 按类型过滤模型
   */
  static filterModelsByType(models: BaseModelInfo[], type: string): BaseModelInfo[] {
    return models.filter(model => model.type === type);
  }

  /**
   * 获取默认模型（优先已启用的模型）
   */
  static getDefaultModel(models: BaseModelInfo[]): BaseModelInfo | undefined {
    const enabledModels = models.filter(model => model.enabled !== false);
    return enabledModels.length > 0 ? enabledModels[0] : models[0];
  }
}

/**
 * 常用的模型过滤器
 */
export class ModelFilters {
  /**
   * 显示所有模型（默认过滤器）
   */
  static all: ModelFilter = () => true;

  /**
   * 只显示已启用的模型
   */
  static enabledOnly: ModelFilter = (model) => model.enabled !== false;

  /**
   * 按类型过滤模型
   */
  static byType(type: string): ModelFilter {
    return (model) => model.type === type;
  }

  /**
   * 按多个类型过滤模型
   */
  static byTypes(types: string[]): ModelFilter {
    return (model) => types.includes(model.type || '');
  }

  /**
   * 按名称包含关键词过滤
   */
  static byNameContains(keyword: string): ModelFilter {
    return (model) => model.name.toLowerCase().includes(keyword.toLowerCase());
  }

  /**
   * 按名称正则表达式过滤
   */
  static byNamePattern(pattern: RegExp): ModelFilter {
    return (model) => pattern.test(model.name);
  }

  /**
   * 排除特定名称的模型
   */
  static excludeNames(names: string[]): ModelFilter {
    return (model) => !names.includes(model.name);
  }

  /**
   * 组合多个过滤器（AND逻辑）
   */
  static and(...filters: ModelFilter[]): ModelFilter {
    return (model) => filters.every(filter => filter(model));
  }

  /**
   * 组合多个过滤器（OR逻辑）
   */
  static or(...filters: ModelFilter[]): ModelFilter {
    return (model) => filters.some(filter => filter(model));
  }

  /**
   * 反转过滤器结果
   */
  static not(filter: ModelFilter): ModelFilter {
    return (model) => !filter(model);
  }
} 