<template>
  <div class="w-full mt-2">
    <div class="w-full flex flex-row relative">
      <a-typography-text class="my-auto mr-2">
        <span class="icon iconfont moxing"></span>
        模型
      </a-typography-text>
      <div class="select-wrapper flex-1" ref="selectWrapper">
        <a-select
            size="small"
            v-model:value="model"
            class="w-full"
            @select="select"
            :disabled="enabledModels.length === 0"
        >
          <a-select-option
              size="small"
              class="my-1"
              v-for="item in enabledModels"
              :key="item.name"
              :value="item.name"
          >
            <div class="size-full flex flex-row justify-lg-between">
              <span>{{ item.name }}</span>
            </div>
          </a-select-option>
        </a-select>
      </div>
    </div>
    <div v-if="enabledModels.length === 0" class="text-center text-gray-500 my-2 text-sm">
      暂无可用模型，请在配置中添加并启用模型
    </div>
  </div>
</template>

<script setup lang="ts">
import {onMounted, ref, computed, watch} from "vue";
import {Platform} from "@/components/common/model/model";
import {usePlatform} from "@/components/store/platform";
import {useAiBot} from "@/components/store/bot";
import {CustomModel} from "@/components/ai-bot-platform/config";

const props = defineProps<{
  ctx: Platform
}>()

const plat = usePlatform()
const bot = useAiBot()
const model = ref<string>()
const selectWrapper = ref<HTMLElement | null>(null)

// 获取并监听已启用的模型列表
const enabledModels = computed<CustomModel[]>(() => {
  if (!props.ctx?.settings?.models) {
    return [];
  }
  return props.ctx.settings.models.filter(model => model.enabled);
})

// 监听当前模型变化
watch(() => props.ctx?.settings?.model, (newVal) => {
  if (newVal) {
    model.value = newVal;
  }
}, { immediate: true })

// 选择模型
function select(value: string) {
  if (!props.ctx) return;
  
  let obj = {...props.ctx};
  obj.settings.model = value;
  obj.config = JSON.stringify(obj.settings);
  plat.updatePlatform(obj);
}

onMounted(() => {
  // 如果未选择模型但有可用模型，自动选择第一个
  if (!model.value && enabledModels.value.length > 0) {
    model.value = enabledModels.value[0].name;
    select(model.value);
  }
})
</script>

<style scoped>
</style> 