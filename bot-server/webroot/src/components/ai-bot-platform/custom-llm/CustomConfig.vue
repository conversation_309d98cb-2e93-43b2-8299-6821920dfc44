<template>
  <div class="w-full flex flex-col">
    <a-form class="size-full" :model="setting">
      <a-form-item label="API地址" name="api" :rules="[
        { required: true, message: 'API地址不能为空' },
        { validator: validateApiRule }
      ]">
        <a-input v-model:value="setting.api" placeholder="例如: https://api.openai.com/v1/chat/completions" />
      </a-form-item>

      <a-form-item label="API密钥" name="apiKey" :rules="[
        { required: true, message: 'API密钥不能为空' }
      ]">
        <a-input-password v-model:value="setting.apiKey" placeholder="请输入API密钥" />
      </a-form-item>

      <a-form-item label="当前模型" name="model" :rules="[
        { required: true, message: '模型名称不能为空' }
      ]">
        <a-input 
          v-model:value="setting.model" 
          placeholder="例如: gpt-3.5-turbo" 
          @blur="handleModelInputBlur"
        />
      </a-form-item>

      <!-- 模型管理部分 -->
      <a-divider>模型管理</a-divider>
      <div class="model-management">
        <!-- 添加模型下拉框 -->
        <div class="flex items-center">
          <a-select v-model:value="value" placeholder="选择或输入模型" class="w-full"
            :options="displayOptions"
            :optionLabelProp="'label'">
            <template #option="{ value: optionValue, label }">
              <div class="flex justify-between items-center w-full">
                <span>{{ label }}</span>
                <a-button 
                  v-if="models.some(model => model.name === optionValue)"
                  type="text" 
                  size="small" 
                  danger 
                  @click.stop="removeModelByName(optionValue)"
                >
                  <template #icon>
                    <delete-outlined />
                  </template>
                </a-button>
              </div>
            </template>
            <template #dropdownRender="{ menuNode: menu }">
              <v-nodes :vnodes="menu" />
              <a-divider style="margin: 4px 0" />
              <a-space style="padding: 4px 8px">
                <a-input ref="inputRef" v-model:value="name" placeholder="请输入模型名称" />
                <a-button type="text" @click="addItem">
                  <template #icon>
                    <plus-outlined />
                  </template>
                  添加模型
                </a-button>
              </a-space>
            </template>
          </a-select>
          <a-button 
            type="text" 
            danger 
            class="ml-2" 
            @click="removeSelectedModel"
            :disabled="!canRemoveModel"
          >
            <template #icon>
              <delete-outlined />
            </template>
            删除所选模型
          </a-button>
        </div>
      </div>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import { Platform } from "@/components/common/model/model";
import { ref, computed, h, defineComponent, watch } from 'vue'
import { useAiBot } from "@/components/store/bot";
import { usePlatform } from "@/components/store/platform";
import '@/components/ai-bot-platform/config-style.css';
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { CustomModel } from "@/components/ai-bot-platform/config";

const props = defineProps<{
  ctx: Platform
}>()
const plat = usePlatform()
const bot = useAiBot()
const setting = ref(props.ctx.settings || {
  api: '',
  apiKey: '',
  model: '',
  models: []
})

// 计算属性：获取模型列表
const models = computed<CustomModel[]>(() => {
  if (!setting.value.models) {
    setting.value.models = [];
  }
  return setting.value.models;
})

// 推荐模型选项
const modelOptions = ref([

])

// 初始化下拉框选项
let index = 0;
const items = computed(() => {
  // 过滤掉已添加的模型
  return modelOptions.value.filter(
    option => !models.value.some(model => model.name === option)
  );
});

// 显示的选项，包括已添加的模型和可选的推荐模型
const displayOptions = computed(() => {
  // 已添加的模型
  const existingModels = models.value.map(model => ({
    value: model.name,
    label: model.name,
    disabled: false
  }));
  
  // 推荐的可选模型
  const suggestedModels = items.value.map(item => ({
    value: item,
    label: item,
    disabled: false
  }));
  
  // 如果有已添加模型，添加一个分隔线
  if (existingModels.length > 0 && suggestedModels.length > 0) {
    return [
      { label: '已添加的模型', type: 'group' },
      ...existingModels,
      { label: '推荐模型', type: 'group' },
      ...suggestedModels
    ];
  }
  
  // 没有已添加模型，只显示推荐模型
  if (existingModels.length === 0) {
    return suggestedModels;
  }
  
  // 没有推荐模型，只显示已添加模型
  return existingModels;
});

const value = ref();
const inputRef = ref();
const name = ref('');

// 初始化value值，如果已有当前模型则选中
if (setting.value.model) {
  value.value = setting.value.model;
}

// 监听选择的模型变化，自动更新当前模型值
watch(value, (newValue) => {
  if (newValue) {
    setting.value.model = newValue;
    updateConfig();
  }
});

// 监听当前模型字段变化，自动更新选择框
watch(() => setting.value.model, (newModel) => {
  if (!newModel) return;
  
  // 只更新选择框值，不做自动添加
  if (newModel !== value.value) {
    value.value = newModel;
  }
});

// 处理模型输入框失去焦点事件
function handleModelInputBlur() {
  const newModel = setting.value.model?.trim();
  if (!newModel) return;
  
  // 检查输入的模型是否已在列表中
  const modelExists = models.value.some(model => model.name === newModel);
  
  // 如果模型不在列表中且不为空，则添加到列表
  if (!modelExists) {
    models.value.push({
      name: newModel,
      enabled: true
    });
    updateConfig();
  }
}

// 判断当前是否可以删除所选模型
const canRemoveModel = computed(() => {
  // 值不为空且是已添加的模型
  return value.value && models.value.some(model => model.name === value.value);
});

// 删除选中的模型
function removeSelectedModel() {
  if (!value.value) return;
  
  const index = models.value.findIndex(model => model.name === value.value);
  if (index === -1) return;
  
  models.value.splice(index, 1);
  value.value = '';
  updateConfig();
}

// 添加模型项
const addItem = e => {
  e.preventDefault();
  
  const modelName = value.value || name.value;
  if (!modelName) {
    return;
  }
  
  // 检查是否已存在
  if (models.value.some(model => model.name === modelName)) {
    return;
  }
  
  // 添加到模型列表
  models.value.push({
    name: modelName,
    enabled: true
  });
  
  // 清空输入
  name.value = '';
  value.value = '';
  
  // 更新配置
  updateConfig();
  
  setTimeout(() => {
    inputRef.value?.focus();
  }, 0);
};

const VNodes = defineComponent({
  props: {
    vnodes: {
      type: Object,
      required: true,
    },
  },
  render() {
    return this.vnodes;
  },
});

// 更新配置到平台
function updateConfig() {
  if (!props.ctx) return;

  const obj = { ...props.ctx };
  obj.settings = { ...setting.value };
  obj.config = JSON.stringify(obj.settings);
  plat.updatePlatform(obj);
}

// 验证URL格式
function isValidUrl(url: string): boolean {
  try {
    const urlObj = new URL(url)
    return urlObj.protocol === 'http:' || urlObj.protocol === 'https:'
  } catch {
    return false
  }
}

// API地址验证规则
const validateApiRule = async (_rule: any, value: string) => {
  if (value && !isValidUrl(value)) {
    return Promise.reject('请输入有效的http/https地址');
  }
  return Promise.resolve();
};

// 验证所有字段
function validateFields() {
  const api = setting.value.api?.trim()
  const apiKey = setting.value.apiKey?.trim()
  const model = setting.value.model?.trim()

  if (!api || !apiKey || !model) {
    return false
  }

  if (!isValidUrl(api)) {
    return false
  }

  return true
}

// 清空配置数据
function clear() {
  setting.value.api = ''
  setting.value.apiKey = ''
  setting.value.model = ''
  setting.value.models = []
}

// 导出验证状态方法
function isValid(): boolean {
  return validateFields()
}

function Setting(): any {
  // 验证所有字段
  if (!validateFields()) {
    return null
  }

  return setting.value
}

defineExpose({
  Setting,
  isValid,
  clear
})

// 删除模型
function removeModelByName(name: string) {
  const index = models.value.findIndex(model => model.name === name);
  if (index !== -1) {
    models.value.splice(index, 1);
    // 如果删除的是当前选中的模型，清空选择
    if (value.value === name) {
      value.value = '';
    }
    updateConfig();
  }
}
</script>

<style scoped>
.model-item {
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  margin-bottom: 8px;
  padding: 8px 12px;
}

.model-management {
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 16px;
  background-color: #fafafa;
}
</style>
