<template>
  <a-modal
      v-model:open="model"
      title="配置"
      centered
      :destroyOnClose="false"
  >
    <component ref="config" :is="platform.configView" :ctx="platform"/>
    <template #footer>
      <a-button type="primary" @click="save">确认</a-button>
      <a-button type="primary" danger @click="del">删除</a-button>
      <a-button @click="model = false">关闭</a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import {Platform} from "@/components/common/model/model";
import {ref} from "vue";
import {usePlatform} from "@/components/store/platform";
import {useAiBot} from "@/components/store/bot";
import {useAiPluginStore} from "@/components/store/plugin";
import {notification} from "ant-design-vue";

const props = defineProps<{
  platform?: Platform
}>()
const emit = defineEmits(
    {
      save: () => {
      }
    }
)

const config = ref()
const model = defineModel({default: false})
const plan = usePlatform()
const plugin = useAiPluginStore()
const bot = useAiBot()


// 保存配置
function save() {
  if (config.value) {
    let obj = {...props.platform}
    let setting = config.value.Setting();
    obj.config = JSON.stringify(setting)
    plan.updatePlatform(obj)
  }
  emit('save')
  model.value = !model.value
}

function del() {
  // 删除当前 platform 检查 当前的平台数量是否为 1，如果是，禁止删除
  if (plan.platforms.length === 1) {
    notification["warning"]({
      message: 'Platform',
      key: "Platform Delete",
      description: "至少需要有一个可用的平台",
    });
    return
  }
  // 查看当前的 插件选中的 platform是否为当前正在删除的 platform
  if (plugin.currentPlugin.platformID === props.platform.id) {
    // 更新 当前插件的 平台id
    let number = plan.platforms.findIndex(item => item.id === props.platform.id);
    if (number == -1) {
      return;
    }
    if (number == 0) {
      plugin.currentPlugin.platformID = plan.platforms[1].id;
    } else {
      plugin.currentPlugin.platformID = plan.platforms[0].id;
    }
  }
  // 更新 当前的 plugin 的 平台id
  plugin.updatePlugin(plugin.currentPlugin)
  // 删除当前 platform
  plan.deletePlatform(props.platform)
  model.value = !model.value
}
</script>

<style scoped>
</style>