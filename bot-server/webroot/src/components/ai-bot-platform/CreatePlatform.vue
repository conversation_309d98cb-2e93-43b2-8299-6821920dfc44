<template>
  <a-modal
      v-model:open="model"
      title="添加平台配置"
      centered
      :destroyOnClose="true"
      :maskClosable="false"
      :body-style="{
        height:'60vh'
      }"
      @ok="create"
  >
    <div class="size-full flex flex-col">
      <a-form
          ref="formRef"
          :model="formState"
          class="w-full"
      >
        <a-form-item
            size="small"
            label="平台类型"
            name="platformType"
            :rules="[{ required: true, message: '请选择平台类型' }]"
        >
          <a-select
              size="small"
              v-model:value="formState.platformType"
              style="width: 100%"
              @select="select"
              @keyup.enter="create"
              placeholder="选择平台"
              popup-class-name="blur-popup"
          >
            <a-select-option
                size="small"
                class="my-1"
                v-for="item in types"
                :label="item.type"
                :key="item.type"
                :value="item.type"
            >
              <div class="size-full flex flex-row justify-lg-between items-center">
                <div class="flex items-center">
                  <img v-if="item.icon" :src="item.icon" class="w-5 h-5 mr-2" alt="平台图标"/>
                  <span>{{ item.type }}</span>
                </div>
              </div>
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item
            label="平台名称"
            name="platformName"
            :rules="[{ required: true, message: '平台名称不能为空' }]"
        >
          <a-input
              size="small"
              v-model:value="formState.platformName"
              placeholder="平台名称"
              @pressEnter="create"
          />
        </a-form-item>
      </a-form>
      <a-divider
          size="small"
          v-if="value!=null"
          orientation="left"
      >
        {{ value.type }}
      </a-divider>
      <div class="w-full overflow-auto" style="flex-grow: 1">
        <component ref="config" v-if="value!=null" :is="value.config" :ctx="value"/>
      </div>
    </div>

  </a-modal>
</template>

<script setup lang="ts">
import {nextTick, reactive, ref, watch} from "vue";
import {Platforms} from "@/components/ai-bot-platform/config";
import {Platform} from "@/components/common/model/model";
import {usePlatform} from "@/components/store/platform";
import {useAiBot} from "@/components/store/bot";
import {message} from "ant-design-vue";

const plat = usePlatform()
const bot = useAiBot()
const model = defineModel({default: false})

const formRef = ref(null);
const formState = reactive({
  platformType: undefined,
  platformName: ''
});

const value = ref<Platform>()
const config = ref()

// types 定义平台类型数据
const types = ref(Platforms)

function select(v) {
  value.value = types.value.find(item => item.type == v)
}

async function create() {
  if (formRef.value) {
    try {
      // 触发表单验证
      await formRef.value.validate();

      // 表单验证通过后，检查配置是否有效
      if (config.value) {
        if (!config.value.isValid()) {
          message.error({
            content: '配置验证失败，请检查填写内容',
            key: 'create-platform-form',
          });
          return;
        }

        let setting = config.value.Setting();
        if (!setting) {
          message.error({
            content: '无法获取配置内容',
            key: 'create-platform-form',
          });
          return;
        }

        let obj: Platform = {};
        obj.name = formState.platformName.trim();
        obj.type = value.value.type;
        obj.icon = value.value.icon;
        obj.config = JSON.stringify(setting);
        obj.configView = value.value.config;
        obj.quickConfigView = value.value.quickConfigView;

        try {
          plat.createPlatform(obj);
          message.success({
            content: `平台"${obj.name}"创建成功`,
            key: 'create-platform-form',
          });
          model.value = false;
        } catch (error) {
          console.error('创建平台时出错:', error);
          message.error({
            content: '创建平台失败',
            key: 'create-platform-form',
          });
        }
      } else {
        message.error({
          content: '配置组件不可用',
          key: 'create-platform-form',
        });
      }
    } catch (error) {
      console.log('表单验证失败:', error);
    }
  }
}

function close() {
  value.value = null;
  formState.platformType = undefined;
  formState.platformName = '';
  if (config.value) {
    config.value.clear();
  }
}

watch(() => model.value, (newValue) => {
  if (newValue) {
    nextTick(() => {
      modalContent.value?.focus();
    });
  } else {
    close();
  }
});

// 添加回车处理函数
function handleEnterPress(e: KeyboardEvent) {
  // 如果当前焦点在select的下拉选项上，不触发创建
  if (e.target && (e.target as HTMLElement).closest('.ant-select-dropdown')) {
    return;
  }
  create();
}

const modalContent = ref<HTMLElement>(null);
const selectWrapper = ref(null);

function getPopupContainer(node: HTMLElement) {
  return selectWrapper.value;
}

// 监听sv变化，同步到formState
watch(() => formState.platformType, (newValue) => {
  if (newValue) {
    select(newValue);
  }
});
</script>

<style scoped>
/* 删除自定义错误消息样式 */
</style>