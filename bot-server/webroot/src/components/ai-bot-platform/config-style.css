/* 错误提示消息样式 */
.error-message {
  position: absolute;
  left: 0;
  top: 100%;
  margin-top: 1px;
  color: #ff4d4f;
  font-size: 12px;
  line-height: 1.2;
  padding: 2px 8px;
  background-color: rgba(255, 77, 79, 0.04);
  border-radius: 2px;
  z-index: 1;
  white-space: nowrap;
  pointer-events: none;
  display: flex;
  align-items: center;
}

/* 输入框包装器统一样式 */
.input-wrapper {
  position: relative;
  margin-bottom: 2px;
  width: 100%;
}

.input-wrapper :deep(.ant-input),
.input-wrapper :deep(.ant-input-affix-wrapper) {
  width: 100%;
}

/* 表单布局优化 */
:deep(.ant-form) {
  width: 100%;
}

:deep(.ant-form-item) {
  margin-bottom: 2px;
}

:deep(.ant-form-item:last-child) {
  margin-bottom: 0;
}

:deep(.ant-form-item-label) {
  padding-bottom: 1px;
}

:deep(.ant-form-item-label > label) {
  font-size: 14px;
  height: 20px;
  line-height: 20px;
}

:deep(.ant-input) {
  padding: 2px 8px;
  height: 28px;
}

/* 处理错误消息时的间距 */
:deep(.ant-form-item-has-error) {
  margin-bottom: 16px;
} 