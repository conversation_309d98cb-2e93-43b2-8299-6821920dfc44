<template>
  <!-- Volcengine 快捷配置面板 -->
     <BaseModelSelector
     :ctx="ctx"
     :models-fetcher="VolcengineModels"
     :data-transformer="ModelDataTransformers.withDescription"
     :model-filter="modelFilter || ModelFilters.all"
     placeholder="请选择Volcengine模型"
     @model-change="onModelChange"
     @models-loaded="onModelsLoaded"
     @load-error="onLoadError"
   >
    <!-- 自定义模型选项显示，显示描述信息 -->
    <template #model-option="{ model }">
      <div class="size-full flex flex-row justify-lg-between" :title="model.description">
        <span>{{ model.name }}</span>
        <span v-if="model.description" class="text-gray-400 text-xs ml-2 truncate">
          {{ model.description }}
        </span>
      </div>
    </template>
  </BaseModelSelector>
</template>

<script setup lang="ts">
import { Platform } from "@/components/common/model/model";
import { VolcengineModels } from "@/components/ai-bot-platform/req";
import BaseModelSelector from "@/components/ai-bot-platform/base/BaseModelSelector.vue";
import { BaseModelInfo, ModelDataTransformers, ModelFilter, ModelFilters } from "@/components/ai-bot-platform/base/types";

const props = defineProps<{
  ctx: Platform;
  // 可选的模型过滤器，用于控制显示哪些模型
  modelFilter?: ModelFilter;
}>();

// 模型选择改变时的回调
function onModelChange(modelName: string) {
  console.log("Volcengine 模型已切换到:", modelName);
}

// 模型列表加载完成的回调
function onModelsLoaded(models: any[]) {
  console.log("Volcengine 模型列表加载完成:", models);
}

// 加载失败的回调
function onLoadError(error: any) {
  console.error("Volcengine 模型列表加载失败:", error);
}
</script>

<style scoped>
/* 确保描述文本不会过长 */
.truncate {
  max-width: 150px;
}
</style> 