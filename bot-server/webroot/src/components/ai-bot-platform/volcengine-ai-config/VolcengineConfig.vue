<template>
  <div class="w-full flex flex-row">
    <a-form class="size-full">
      <a-form-item
          label="服务地址"
          name="api"
      >
        <a-input
            v-model:value="setting.api"
            :status="apiError ? 'error' : ''"
            placeholder="例如: https://open.volcengineapi.com"
            @blur="validateApi"
        />
      </a-form-item>

      <a-form-item
          label="区域"
          name="region"
      >
        <a-select
            v-model:value="setting.region"
            :status="regionError ? 'error' : ''"
            placeholder="请选择区域"
            @blur="validateRegion"
        >
          <a-select-option value="cn-beijing">华北-北京</a-select-option>
          <a-select-option value="cn-shanghai">华东-上海</a-select-option>
          <a-select-option value="cn-guangzhou">华南-广州</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item
          label="Access Key ID"
          name="accessKeyId"
      >
        <a-input
            v-model:value="setting.accessKeyId"
            :status="accessKeyIdError ? 'error' : ''"
            placeholder="请输入火山引擎 Access Key ID"
            @blur="validateAccessKeyId"
        />
      </a-form-item>

      <a-form-item
          label="Secret Access Key"
          name="secretAccessKey"
      >
        <a-input-password
            v-model:value="setting.secretAccessKey"
            :status="secretAccessKeyError ? 'error' : ''"
            placeholder="请输入火山引擎 Secret Access Key"
            @blur="validateSecretAccessKey"
        />
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import {Platform} from "@/components/common/model/model";
import {ref} from 'vue'
import {useAiBot} from "@/components/store/bot";
import '@/components/ai-bot-platform/config-style.css';

const props = defineProps<{
  ctx: Platform
}>()

const bot = useAiBot()
const setting = ref(props.ctx.settings)
const accessKeyIdError = ref(false)
const accessKeyIdErrorMessage = ref('')
const secretAccessKeyError = ref(false)
const secretAccessKeyErrorMessage = ref('')
const apiError = ref(false)
const apiErrorMessage = ref('')
const regionError = ref(false)
const regionErrorMessage = ref('')

// 验证URL格式
function isValidUrl(url: string): boolean {
  try {
    const urlObj = new URL(url)
    return urlObj.protocol === 'http:' || urlObj.protocol === 'https:'
  } catch {
    return false
  }
}

// 验证API地址
function validateApi() {
  const api = setting.value.api?.trim()

  if (!api) {
    apiError.value = true
    apiErrorMessage.value = '服务地址不能为空'
    return false
  }

  if (!isValidUrl(api)) {
    apiError.value = true
    apiErrorMessage.value = '请输入有效的http/https地址'
    return false
  }

  apiError.value = false
  apiErrorMessage.value = ''
  return true
}

// 验证区域选择
function validateRegion() {
  const region = setting.value.region?.trim()

  if (!region) {
    regionError.value = true
    regionErrorMessage.value = '请选择区域'
    return false
  }

  regionError.value = false
  regionErrorMessage.value = ''
  return true
}

// 验证 Access Key ID
function validateAccessKeyId() {
  const accessKeyId = setting.value.accessKeyId?.trim()

  if (!accessKeyId) {
    accessKeyIdError.value = true
    accessKeyIdErrorMessage.value = 'Access Key ID 不能为空'
    return false
  }

  accessKeyIdError.value = false
  accessKeyIdErrorMessage.value = ''
  return true
}

// 验证 Secret Access Key
function validateSecretAccessKey() {
  const secretAccessKey = setting.value.secretAccessKey?.trim()

  if (!secretAccessKey) {
    secretAccessKeyError.value = true
    secretAccessKeyErrorMessage.value = 'Secret Access Key 不能为空'
    return false
  }

  secretAccessKeyError.value = false
  secretAccessKeyErrorMessage.value = ''
  return true
}

// 清空配置数据
function clear() {
  setting.value.api = ''
  setting.value.region = ''
  setting.value.accessKeyId = ''
  setting.value.secretAccessKey = ''
  apiError.value = false
  apiErrorMessage.value = ''
  regionError.value = false
  regionErrorMessage.value = ''
  accessKeyIdError.value = false
  accessKeyIdErrorMessage.value = ''
  secretAccessKeyError.value = false
  secretAccessKeyErrorMessage.value = ''
}

// 导出验证状态方法
function isValid(): boolean {
  return validateApi() && validateRegion() && validateAccessKeyId() && validateSecretAccessKey()
}

function Setting(): any {
  if (!validateApi() || !validateRegion() || !validateAccessKeyId() || !validateSecretAccessKey()) {
    return null
  }
  return setting.value
}

defineExpose({
  Setting,
  isValid,
  clear
})
</script>