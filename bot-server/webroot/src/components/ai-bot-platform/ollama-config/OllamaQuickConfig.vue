<template>
  <!-- Ollama 快捷配置面板 -->
     <BaseModelSelector
     v-if="ctx"
     :ctx="ctx"
     :models-fetcher="OllamaModels"
     :data-transformer="ModelDataTransformers.standard"
     :model-filter="modelFilter || ModelFilters.all"
     placeholder="请选择Ollama模型"
     @model-change="onModelChange"
     @models-loaded="onModelsLoaded"
     @load-error="onLoadError"
   />
  <div v-else class="w-full text-center text-gray-400 py-4">
    请先配置平台信息
  </div>
</template>

<script setup lang="ts">
import { Platform } from "@/components/common/model/model";
import { OllamaModels } from "@/components/ai-bot-platform/req";
import BaseModelSelector from "@/components/ai-bot-platform/base/BaseModelSelector.vue";
import { BaseModelInfo, ModelDataTransformers, ModelFilter, ModelFilters } from "@/components/ai-bot-platform/base/types";

const props = defineProps<{
  ctx?: Platform;
  // 可选的模型过滤器，用于控制显示哪些模型
  modelFilter?: ModelFilter;
}>();

// 模型选择改变时的回调
function onModelChange(modelName: string) {
  console.log("Ollama 模型已切换到:", modelName);
}

// 模型列表加载完成的回调
function onModelsLoaded(models: any[]) {
  console.log("Ollama 模型列表加载完成:", models);
}

// 加载失败的回调
function onLoadError(error: any) {
  console.error("Ollama 模型列表加载失败:", error);
}
</script>

<style scoped>
/* 保持原有样式 */
.drop-down-Style {
  width: auto;
}
</style>