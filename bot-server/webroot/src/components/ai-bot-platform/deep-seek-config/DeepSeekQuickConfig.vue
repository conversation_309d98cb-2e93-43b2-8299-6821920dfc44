<template>
  <!-- DeepSeek 快捷配置面板 -->
  <BaseModelSelector
    :ctx="ctx"
    :models-fetcher="DeepSeekModels"
    :data-transformer="transformDataForDeepSeek"
    :model-filter="modelFilter || ModelFilters.all"
    placeholder="请选择DeepSeek模型"
    @model-change="onModelChange"
    @models-loaded="onModelsLoaded"
    @load-error="onLoadError"
  />
</template>

<script setup lang="ts">
import { Platform } from "@/components/common/model/model";
import { DeepSeekModels } from "@/components/ai-bot-platform/req";
import BaseModelSelector from "@/components/ai-bot-platform/base/BaseModelSelector.vue";
import { BaseModelInfo, ModelDataTransformers, ModelFilter, ModelFilters } from "@/components/ai-bot-platform/base/types";

const props = defineProps<{
  ctx: Platform;
  // 可选的模型过滤器，用于控制显示哪些模型
  modelFilter?: ModelFilter;
}>();

// 使用标准数据转换器
const transformDataForDeepSeek = ModelDataTransformers.standard;

// 模型选择改变时的回调
function onModelChange(modelName: string) {
  console.log("DeepSeek 模型已切换到:", modelName);
}

// 模型列表加载完成的回调
function onModelsLoaded(models: BaseModelInfo[]) {
  console.log("DeepSeek 模型列表加载完成:", models);
}

// 加载失败的回调
function onLoadError(error: any) {
  console.error("DeepSeek 模型列表加载失败:", error);
}
</script>

<style scoped></style>