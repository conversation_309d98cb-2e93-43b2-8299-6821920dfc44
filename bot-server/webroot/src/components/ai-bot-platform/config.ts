// PlatformType
// 平台类型定义 类型对应 PlatformConfigView 中定义的 配置面板
import {Platform} from "@/components/common/model/model";

export enum PlatformType {
    Ollama = "Ollama",
    DeepSeek = "DeepSeek",
    GiteeAI = "GiteeAI",
    Volcengine = "Volcengine",
    CustomLLM = "CustomLLM",
}

// PlatformConfigView
// 定义 各类型平台的配置面板
export enum PlatformConfigView {
    Ollama = 'OllamaConfig',
    DeepSeek = "DeepSeekConfig",
    GiteeAI = "GiteeAIConfig",
    Volcengine = "VolcengineConfig",
    CustomLLM = "CustomConfig",
}

export enum PlatformQuickConfig {
    Ollama = "OllamaQuickConfig",
    DeepSeek = "DeepSeekQuickConfig",
    GiteeAI = "GiteeAIQuickConfig",
    Volcengine = "VolcengineQuickConfig",
    CustomLLM = "CustomLLMQuickConfig",
}

export enum PlatformIcon {
    Ollama = "platform-icon/ollama.svg",
    DeepSeek = "platform-icon/deepseek.svg",
    GiteeAI = "platform-icon/giteeai.svg",
    Volcengine = "platform-icon/giteeai.svg",
    CustomLLM = "platform-icon/ChatGPT.svg",
}

// 模型类型 需要和服务端保持一致
export enum ModelType {
    ChatModel        = "text2text",   // ChatModel 聊天模型
    Text2TextModel   = "text2text",   // Text2TextModel 文本转文本模型
    Text2ImageModel  = "text2image",  // Text2ImageModel 文本转图片模型
    Image2TextModel  = "image2text",  // Image2TextModel 图片转文本模型
    Image2ImageModel = "image2image", // Image2ImageModel 图片转图片模型
    Image2video      = "image2video", // Image2VideoModel 图片转视频模型
    Text2Video       = "text2video",  // Text2Video 文本转视频模型
    Image2VideoModel = "image2video", // Image2VideoModel 图片转视频模型
}


export const Platforms: Platform[] = [
    {
        type: "Ollama",
        icon: "platform-icon/ollama.svg",
        config: "OllamaConfig",
        quickConfigView: "OllamaQuickConfig",
        settings: {} as PlatformConfig
    },
    {
        type: "DeepSeek",
        icon: "platform-icon/deepseek.svg",
        config: "DeepSeekConfig",
        quickConfigView: "DeepSeekQuickConfig",
        settings: {} as PlatformConfig
    },
    {
        type: "GiteeAI",
        icon: "platform-icon/giteeai.svg",
        config: "GiteeAIConfig",
        quickConfigView: "GiteeAIQuickConfig",
        settings: {} as PlatformConfig
    },
    {
        type: "Volcengine",
        icon: "platform-icon/ollama.svg",
        config: "VolcengineConfig",
        quickConfigView: "VolcengineQuickConfig",
        settings: {} as PlatformConfig
    },
    {
        type: "CustomLLM",
        icon: "platform-icon/ChatGPT.svg",
        config: "CustomConfig",
        quickConfigView: "CustomLLMQuickConfig",
        settings: {} as PlatformConfig
    }
]

/*
* OllamaPlatformConfig
* Ollama 平台的配置基础设置
* */
export interface OllamaPlatformConfig {
    api?: string
    currentModel?: string
    currentViewModel?: string
}

export interface PlatformConfig {
    // api 接口
    api: string;
    // api token
    key: string;
    // 调用模型
    currentModel: string;

    // 聊天模型
    chatModel: string;

    // 视觉模型
    viewImageModel: string;

    // OCR 模型
    ocrImageModel: string;

    // 视频模型
    videoModel: string;

    accessKeyID: string;
    secretAccessKey: string;
    region: string;
}

// 自定义LLM平台配置
export interface CustomLLMConfig {
    api: string;
    apiKey: string;
    model: string;
    models?: CustomModel[];
}

export interface CustomModel {
    name: string;
    enabled: boolean;
}