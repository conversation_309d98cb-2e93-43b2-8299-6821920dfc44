<template>
  <div class="size-full">
    <div class="size-full flex flex-row">

      <div class="flex flex-col justify-between">
        <!-- 左侧平台列表 -->
        <MenuList class="py-1" style="width: 160px;">
          <MenuListItem v-for="platform in filteredPlatforms" :key="platform.id" :text="platform.name"
            :icon="platform.icon" :isActive="selectedPlatformId[0] === platform.id"
            @click="handleSelectPlatform(platform)" />
        </MenuList>
        <div class="flex justify-center">
          <a-button class="w-full" type="link" @click="openCreatePlatform = true">
            添加平台
          </a-button>
        </div>
      </div>


      <!-- 右侧内容区域 -->
      <div class="setting-panel flex-1 py-1">
        <div v-if="loading" class="empty-content">
          <a-spin tip="加载中..."></a-spin>
        </div>
        <template v-else-if="currentPlatform">
          <!-- 平台标题和调试开关 -->
          <div class="platform-header-section">
            <div class="platform-title">
              <h2>{{ currentPlatform.name }}</h2>
              <a-tag color="blue">{{ currentPlatform.type }}</a-tag>
            </div>
            <div class="platform-actions">
              <a-button type="primary" @click="savePlatformConfig" class="mr-1">
                保存配置
              </a-button>
              <a-button type="primary" danger @click="confirmDelete(currentPlatform)" class="mr-1">
                删除平台
              </a-button>
            </div>
          </div>

          <!-- 平台基本信息区域 -->
          <div class="platform-basic-info">
            <a-form layout="vertical">
              <a-form-item label="平台名称" required>
                <a-input v-model:value="platformName" placeholder="请输入平台名称" />
              </a-form-item>
            </a-form>
          </div>

          <!-- 配置视图容器 -->
          <div v-if="hasConfigViews" class="config-views-container">
            <!-- 主配置视图 -->
            <div v-if="currentPlatform.configView" class="config-section">
              <component ref="configViewRef" :is="currentPlatform.configView" :platform="safeCurrentPlatform"
                :ctx="safeCurrentPlatform" />
            </div>

            <!-- 快速配置视图 -->
            <div v-if="currentPlatform.quickConfigView" class="config-section quick-config-section">
              <component ref="quickConfigViewRef" :is="currentPlatform.quickConfigView" :platform="safeCurrentPlatform"
                :ctx="safeCurrentPlatform" />
            </div>
          </div>
          <div v-else class="empty-content">
            <span class="hint-text">该平台暂无配置界面</span>
          </div>
        </template>
        <div v-else class="empty-content">
          <span class="hint-text">请选择左侧平台查看详情</span>
        </div>
      </div>
    </div>
  </div>
  <CreatePlatform v-model="openCreatePlatform" />
</template>

<script setup lang="ts">
import { usePlatform } from "@/components/store/platform";
import { computed, onMounted, ref, watch } from 'vue';
import type { Platform } from '@/components/common/model/model';
import { message, Modal, theme } from 'ant-design-vue';

const { useToken } = theme;
const { token } = useToken();
const plat = usePlatform();
const openCreatePlatform = ref(false);
const searchValue = ref('');
const loading = ref(false);
const currentPlatform = ref<Platform | null>(null);
const platformName = ref('');

// 创建一个安全的平台对象，确保所有需要的属性都存在
const safeCurrentPlatform = computed(() => {
  if (!currentPlatform.value) return null;

  // 创建一个新对象，避免直接修改原始对象
  const safePlatform = { ...currentPlatform.value };

  // 确保settings存在
  if (!safePlatform.settings) {
    safePlatform.settings = {};
  }

  // 确保平台对象被正确传递（同时支持ctx和platform两种属性名）
  return safePlatform;
});

// 过滤平台列表
const filteredPlatforms = computed(() => {
  if (!searchValue.value) {
    return plat.platforms;
  }
  return plat.platforms.filter(platform =>
    platform.name.toLowerCase().includes(searchValue.value.toLowerCase()) ||
    platform.type.toLowerCase().includes(searchValue.value.toLowerCase())
  );
});

// 添加选中平台状态
const selectedPlatformId = ref<string[]>([]);

const handleEdit = (record: Platform) => {
  // TODO: 实现编辑功能
  console.log('编辑', record);
};

const handleDelete = (record: Platform) => {
  plat.deletePlatform(record);
};

// 添加确认删除方法
const confirmDelete = (platform: Platform) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除平台"${platform.name}"吗？此操作不可恢复。`,
    okText: '确认',
    okType: 'danger',
    cancelText: '取消',
    onOk() {
      handleDelete(platform);
      // 如果删除的是当前选中的平台，清空选择
      if (selectedPlatformId.value?.[0] === platform.id) {
        selectedPlatformId.value = [];
        currentPlatform.value = null;
        // 如果还有其他平台，选择第一个
        setInitialPlatform();
      }
    }
  });
};

// 明确的选择平台函数
const selectPlatform = (platform: Platform) => {
  console.log('选择平台:', platform);
  loading.value = true;

  try {
    // 确保平台对象是完整的
    const safePlatform = { ...platform };

    // 初始化必要的属性
    if (!safePlatform.settings) {
      console.warn('初始化平台settings属性');
      safePlatform.settings = {};
    }

    // 更新当前平台引用
    currentPlatform.value = safePlatform;
    // 设置平台名称
    platformName.value = safePlatform.name || '';
  } catch (error) {
    console.error('选择平台时出错:', error);
  } finally {
    // 简单延迟以确保UI更新
    setTimeout(() => {
      loading.value = false;
    }, 200);
  }
};

// 添加处理选择平台的函数
const handleSelectPlatform = (platform: Platform) => {
  selectedPlatformId.value = [platform.id];
  selectPlatform(platform);
};

// 在script部分添加计算属性判断是否存在配置视图
const hasConfigViews = computed(() => {
  return !!currentPlatform.value &&
    (!!currentPlatform.value.configView || !!currentPlatform.value.quickConfigView);
});

// 设置初始选中平台
const setInitialPlatform = () => {
  if (plat.platforms.length > 0 && (!selectedPlatformId.value || selectedPlatformId.value.length === 0)) {
    const firstPlatform = plat.platforms[0];
    selectedPlatformId.value = [firstPlatform.id];
    currentPlatform.value = firstPlatform;
    platformName.value = firstPlatform.name || '';
    console.log('初始化选中平台:', firstPlatform);
  }
};

// 监听平台数据变化
watch(() => plat.platforms, (newPlatforms) => {
  console.log('平台列表更新:', newPlatforms);
  if (newPlatforms.length > 0) {
    setInitialPlatform();
  }
}, { immediate: true });

// 监听选中平台ID变化
watch(selectedPlatformId, (newSelectedIds) => {
  console.log('选中平台ID变化:', newSelectedIds);
  if (newSelectedIds && newSelectedIds.length > 0) {
    const platform = plat.platforms.find(p => p.id === newSelectedIds[0]);
    if (platform) {
      try {
        console.log('通过ID找到平台:', platform);

        // 创建安全的平台对象
        const safePlatform = { ...platform };

        // 首先检查平台对象是否完整
        if (!safePlatform.settings) {
          console.warn('平台缺少settings属性，正在初始化');
          safePlatform.settings = {}; // 提供默认值避免报错
        }

        currentPlatform.value = safePlatform;
        platformName.value = safePlatform.name || '';
      } catch (error) {
        console.error('设置平台出错:', error);
      } finally {
        loading.value = false;
      }
    } else {
      console.warn('未找到对应ID的平台:', newSelectedIds[0]);
    }
  }
});

// 添加配置视图的引用
const configViewRef = ref(null);
const quickConfigViewRef = ref(null);

// 添加保存平台配置方法
const savePlatformConfig = () => {
  try {
    if (!currentPlatform.value) {
      message.warning({
        content: '没有选中的平台',
        key: 'platform-save'
      });
      return;
    }

    // 验证平台名称
    if (!platformName.value.trim()) {
      message.error({
        content: '请输入平台名称',
        key: 'platform-name-empty'
      });
      return;
    }

    const platformId = currentPlatform.value.id;
    let configSettings = null;

    // 尝试从主配置视图获取设置
    if (configViewRef.value && typeof configViewRef.value.Setting === 'function') {
      configSettings = configViewRef.value.Setting();
      if (!configSettings) {
        message.error({
          content: '配置验证失败，请检查填写内容',
          key: `platform-save-${platformId}`
        });
        return;
      }
    }

    // 如果主配置视图无法获取设置，尝试从快速配置视图获取
    if (!configSettings && quickConfigViewRef.value && typeof quickConfigViewRef.value.Setting === 'function') {
      configSettings = quickConfigViewRef.value.Setting();
      if (!configSettings) {
        message.error({
          content: '配置验证失败，请检查填写内容',
          key: `platform-save-${platformId}`
        });
        return;
      }
    }

    // 创建新的平台对象并更新
    let updatedPlatform = { ...currentPlatform.value };
    
    // 更新平台名称
    updatedPlatform.name = platformName.value.trim();
    
    // 更新配置设置（如果有）
    if (configSettings) {
      updatedPlatform.config = JSON.stringify(configSettings);
    }

    // 调用平台更新方法
    plat.updatePlatform(updatedPlatform);

    message.success({
      content: '配置已保存',
      key: `platform-save-${platformId}`
    });
  } catch (error) {
    console.error('保存平台配置时出错:', error);
    message.error({
      content: '保存配置失败',
      key: 'platform-save-error'
    });
  }
};

onMounted(() => {
  console.log('组件挂载');
  // 调用获取平台列表
  plat.queryPlatforms();
  // 初始化选择第一个平台（如果有的话）
  setInitialPlatform();
});
</script>

<style scoped>
.size-full {
  width: 100%;
  height: 100%;
}

/* 右侧配置面板样式 */
.setting-panel {
  overflow-y: auto;
  height: 100%;
}

.empty-content {
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(0, 0, 0, 0.25);
  flex-grow: 1;
  padding: 32px 0;
  width: 100%;
}

.hint-text {
  font-size: 16px;
}

/* 平台标题区域样式 */
.platform-header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid v-bind('token.colorBorder');
  flex-shrink: 0;
}

.platform-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.platform-title h2 {
  margin-bottom: 0;
  font-size: 20px;
  font-weight: 500;
}

.platform-actions {
  display: flex;
  gap: 8px;
}

/* 平台基本信息区域样式 */
.platform-basic-info {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid v-bind('token.colorBorder');
}

/* 配置视图容器样式 */
.config-views-container {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  overflow: auto;
  gap: 16px;
}


.quick-config-section {
  flex-grow: 1;
}
</style>