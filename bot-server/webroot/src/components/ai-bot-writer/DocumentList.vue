<template>
  <ResizablePanel 
    ref="resizablePanelRef"
    :initial-width="initialWidth"
    :min-width="minWidth"
    :max-width-percent="maxWidthPercent"
    :default-drag-mode="defaultDragMode"
    :remember-user-preference="rememberUserPreference"
    storage-key-prefix="document-list-panel"
  >
    <DocumentListHeader title="我的文档">
      <div class="document-list-actions">
        <NewDocumentButton @create="createNewDocument" />
      </div>
    </DocumentListHeader>
    <div class="document-list">
      <DocumentItem
        v-for="doc in documents"
        :key="doc.id"
        :document="doc"
        :is-active="doc.id === activeDocId"
        @select="selectDocument"
      />
    </div>
  </ResizablePanel>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import DocumentItem from './DocumentItem.vue';
import DocumentListHeader from './DocumentListHeader.vue';
import NewDocumentButton from './NewDocumentButton.vue';
import ResizablePanel from '../ai-bot-widget/ResizablePanel.vue';

// 定义组件属性
const props = defineProps({
  // 默认拖拽模式: 'preview' 预览模式，'direct' 实时模式
  defaultDragMode: {
    type: String,
    default: 'direct', // 默认使用实时模式
    validator: (value: string) => ['preview', 'direct'].includes(value)
  },
  // 是否记住用户的拖拽模式选择
  rememberUserPreference: {
    type: Boolean,
    default: true
  },
  // 初始宽度
  initialWidth: {
    type: String,
    default: '200px'
  },
  // 最小宽度 (px)
  minWidth: {
    type: Number,
    default: 180
  },
  // 最大宽度 (窗口宽度百分比)
  maxWidthPercent: {
    type: Number,
    default: 40
  }
});

// ResizablePanel 引用
const resizablePanelRef = ref(null);

// 模拟文档数据
const documents = ref([
  { 
    id: '1', 
    title: '项目计划书', 
    lastModified: '今天 14:30' 
  },
  { 
    id: '2', 
    title: '周报模板', 
    lastModified: '昨天 10:15' 
  },
  { 
    id: '3', 
    title: '会议纪要', 
    lastModified: '3天前' 
  },
  { 
    id: '4', 
    title: '需求分析文档', 
    lastModified: '上周' 
  },
  { 
    id: '5', 
    title: '产品说明书', 
    lastModified: '2023/10/15' 
  }
]);

// 当前选中的文档
const activeDocId = ref('1');

// 选择文档
const selectDocument = (id: string) => {
  activeDocId.value = id;
};

// 创建新文档
const createNewDocument = () => {
  const newId = String(Date.now());
  documents.value.unshift({
    id: newId,
    title: '新文档',
    lastModified: '刚刚'
  });
  activeDocId.value = newId;
};

// 公开方法给父组件 - 通过代理转发到 ResizablePanel
defineExpose({
  // 设置拖拽模式
  setDragMode: (mode: 'preview' | 'direct') => {
    resizablePanelRef.value?.setDragMode(mode);
  },
  // 获取当前拖拽模式
  getDragMode: () => resizablePanelRef.value?.getDragMode(),
  // 获取当前宽度
  getCurrentWidth: () => resizablePanelRef.value?.getCurrentWidth() || 0,
  // 设置宽度
  setWidth: (width: number | string) => {
    resizablePanelRef.value?.setWidth(width);
  },
  // 切换拖拽模式
  toggleDragMode: () => resizablePanelRef.value?.toggleDragMode()
});
</script>

<style scoped>
.document-list-actions {
  display: flex;
  align-items: center;
}

.document-list {
  flex: 1;
  overflow-y: auto;
}
</style> 