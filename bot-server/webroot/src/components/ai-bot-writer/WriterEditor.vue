<template>
  <div class="editor-container">
    <div class="editor-wrapper">
      <div 
        class="editor-content text-editor"
        contenteditable="true"
        @input="handleInput"
        @keydown="handleKeyDown"
        @paste="handlePaste"
        @keydown.ctrl.z.prevent="handleUndo"
        @keydown.ctrl.y.prevent="handleRedo"
        @keyup="updateCursorPosition"
        @click="updateCursorPosition"
        @scroll="syncScroll"
        ref="editorRef"
        :placeholder="placeholder"
        spellcheck="false"
      ></div>
    </div>
    
    <!-- 添加编辑器页脚/状态栏 -->
    <div class="editor-footer">
      <div class="editor-stats">
        <span>行 {{ cursorPosition.row }}, 列 {{ cursorPosition.col }}</span>
        <span class="separator">|</span>
        <span>{{ characterCount }}个字符</span>
      </div>
      <div class="editor-info">
        <span>{{ encoding }}</span>
        <span class="separator">|</span>
        <span>{{ lineEnding }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, defineProps, defineEmits, computed, watch } from 'vue';

// 为window添加suggestionDebounce属性
declare global {
  interface Window {
    suggestionDebounce: ReturnType<typeof setTimeout> | null;
  }
}

const props = defineProps({
  placeholder: {
    type: String,
    default: '开始输入内容...'
  },
  initialContent: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:content']);

const editorRef = ref<HTMLElement>();
const content = ref(props.initialContent);
const history = ref<string[]>([]);
const historyIndex = ref(-1);
const suggestion = ref('');
const showSuggestion = ref(false);
const isLoadingSuggestion = ref(false);
const debounceDelay = ref(300); // 防抖延迟时间，可根据需要调整

// 光标位置和文档信息
const cursorPosition = ref({ row: 1, col: 1 });
const encoding = ref('UTF-8');
const lineEnding = ref('Windows (CRLF)');

// 计算字符数
const characterCount = computed(() => {
  return content.value.length;
});

// 同步滚动
const syncScroll = () => {
  // 保留方法签名，但删除行号相关逻辑
};

// 监听内容变化
watch(() => content.value, () => {
  // 删除对updateLineNumbers的调用
});

// Gitee AI API配置
const giteeApiConfig = {
  endpoint: 'https://ai.gitee.com/v1/chat/completions',
  apiKey: 'IFMRT50L3XUISCC1X9SVXJOZAYRAPPVMGSACHUG2',
  package: '1910',
  model: 'Qwen2.5-72B-Instruct'
};

// 调用Gitee AI API获取自动补全建议
const fetchSuggestionFromAPI = async (text: string) => {
  if (!text.trim() || text.length < 5) return ''; // 避免过短内容触发API调用
  isLoadingSuggestion.value = true;
  
  try {
    const response = await fetch(giteeApiConfig.endpoint, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${giteeApiConfig.apiKey}`,
        'Content-Type': 'application/json',
        'X-Package': giteeApiConfig.package
      },
      body: JSON.stringify({
        model: giteeApiConfig.model,
        stream: false,
        max_tokens: 50,
        temperature: 0.7,
        top_p: 0.7,
        top_k: 50,
        frequency_penalty: 1,
        messages: [
          {
            role: 'system',
            content: '你是一个助手，请为用户提供文本输入的补全建议。请直接提供补全内容，不要有任何前导语如"这是我的建议"等。请简洁直接地给出后续文本补全。'
          },
          {
            role: 'user',
            content: `请为以下文本提供后续的补全建议(直接给出补全内容，不要有任何前缀)：${text}`
          }
        ]
      })
    });

    const data = await response.json();
    
    if (data.choices && data.choices.length > 0) {
      // 移除返回结果中可能包含的引号、空格等符号，保证输出干净
      let result = data.choices[0].message.content;
      result = result.replace(/^[""「」'']*/, '').trim();
      result = result.replace(/^这是我的建议：/, '');
      result = result.replace(/^建议：/, '');
      result = result.replace(/^补全内容：/, '');
      return result;
    }
    
    return '';
  } catch (error) {
    console.error('获取建议时出错:', error);
    return '';
  } finally {
    isLoadingSuggestion.value = false;
  }
};

// 处理输入
const handleInput = (event: Event) => {
  if (editorRef.value) {
    // 保存当前内容
    content.value = editorRef.value.textContent || '';
    addToHistory(content.value);
    
    // 触发内容更新事件
    emit('update:content', content.value);
    
    // 更新光标位置
    updateCursorPosition();

    
    // 只有在非删除操作时才显示建议
    const isDeleteOperation = event instanceof InputEvent && 
      (event.inputType?.includes('delete') || event.inputType?.includes('Delete'));
    
    // 输入后检查是否显示建议（仅非删除操作时）
    if (!isDeleteOperation) {
      // 防抖处理
      if (window.suggestionDebounce) {
        clearTimeout(window.suggestionDebounce);
      }
      
      window.suggestionDebounce = setTimeout(() => {
        nextTick(() => {
          showCompletionPreview();
        });
      }, debounceDelay.value);
    } else {
      // 如果是删除操作，清除所有预览提示
      clearSuggestions();
    }
  }
};

// 清除所有建议
const clearSuggestions = () => {
  document.querySelectorAll('.suggestion-preview-wrapper').forEach(node => {
    node.parentNode?.removeChild(node);
  });
  showSuggestion.value = false;
  suggestion.value = '';
};

// 添加到历史记录
const addToHistory = (state: string) => {
  history.value = history.value.slice(0, historyIndex.value + 1);
  history.value.push(state);
  historyIndex.value++;
};

// 获取光标前的文本
const getTextBeforeCursor = () => {
  const selection = window.getSelection();
  if (!selection?.rangeCount || !editorRef.value) return '';
  
  const range = selection.getRangeAt(0);
  const preSelectionRange = range.cloneRange();
  preSelectionRange.selectNodeContents(editorRef.value);
  preSelectionRange.setEnd(range.startContainer, range.startOffset);
  
  return preSelectionRange.toString();
};

// 显示自动完成提示
const showCompletionPreview = async () => {
  // 获取光标前的文本内容
  const textBeforeCursor = getTextBeforeCursor();
  if (!textBeforeCursor || textBeforeCursor.length < 5) return;
  
  // 清除现有预览
  clearSuggestions();
  
  // 获取API建议
  const apiSuggestion = await fetchSuggestionFromAPI(textBeforeCursor);
  if (!apiSuggestion) return;
  
  suggestion.value = apiSuggestion.trim();
  showSuggestion.value = !!suggestion.value;

  if (showSuggestion.value && suggestion.value && editorRef.value) {
    const selection = window.getSelection();
    if (!selection?.rangeCount) return;
    
    const range = selection.getRangeAt(0);
    
    const previewWrapper = document.createElement('span');
    previewWrapper.className = 'suggestion-preview-wrapper';
    previewWrapper.contentEditable = 'false';
    
    const preview = document.createElement('span');
    preview.className = 'suggestion-preview';
    preview.innerText = suggestion.value;
    preview.style.color = '#aaaaaa';
    preview.style.fontStyle = 'italic';
    preview.style.pointerEvents = 'none';
    preview.style.userSelect = 'none';
    preview.setAttribute('aria-hidden', 'true');
    
    previewWrapper.appendChild(preview);
    range.insertNode(previewWrapper);
    
    const newRange = document.createRange();
    newRange.setStart(range.startContainer, range.startOffset);
    newRange.collapse(true);
    selection.removeAllRanges();
    selection.addRange(newRange);
  }
};

// 插入提示
const insertSuggestion = () => {
  if (!showSuggestion.value || !suggestion.value) return;

  try {
    // 保存建议内容到本地变量，防止清除后丢失
    const suggestionText = suggestion.value;
    
    // 获取当前选区
    const selection = window.getSelection();
    if (!selection?.rangeCount) return;
    
    // 先移除所有预览元素UI
    clearSuggestions();
    
    // 获取当前范围
    const range = selection.getRangeAt(0);
    
    // 确保范围有效
    if (!range) return;
    
    // 创建要插入的文本节点
    const textNode = document.createTextNode(suggestionText);
    
    // 插入文本
    range.insertNode(textNode);
    
    // 移动光标到插入文本后
    const newRange = document.createRange();
    newRange.setStartAfter(textNode);
    newRange.collapse(true);
    selection.removeAllRanges();
    selection.addRange(newRange);
    
    // 手动触发一次更新，确保内容被保存
    if (editorRef.value) {
      // 使用setTimeout确保DOM操作完成后再获取内容
      setTimeout(() => {
        // 手动更新内容状态
        content.value = editorRef.value?.textContent || '';
        addToHistory(content.value);
        emit('update:content', content.value);
        
        // 更新光标位置
        updateCursorPosition();
      }, 0);
    }
  } catch (error) {
    console.error('插入建议时出错:', error);
  }
  
  // 确保状态被重置
  showSuggestion.value = false;
  suggestion.value = '';
};

// 撤销操作
const handleUndo = (e: Event) => {
  e.preventDefault();
  if (historyIndex.value > 0) {
    historyIndex.value--;
    restoreState();
  }
};

// 重做操作
const handleRedo = (e: Event) => {
  e.preventDefault();
  if (historyIndex.value < history.value.length - 1) {
    historyIndex.value++;
    restoreState();
  }
};

// 恢复状态
const restoreState = () => {
  if (editorRef.value) {
    editorRef.value.textContent = history.value[historyIndex.value];
    content.value = history.value[historyIndex.value];
    emit('update:content', content.value);
    
    // 更新光标位置
    updateCursorPosition();
  }
};

// 设置编辑器内容
const setContent = (newContent: string) => {
  if (editorRef.value) {
    editorRef.value.textContent = newContent;
    content.value = newContent;
    addToHistory(content.value);
  }
};

// 监听键盘事件，特别处理回车换行
const handleKeyDown = (e: KeyboardEvent) => {
  // 处理回车键，确保行号更新
  if (e.key === 'Enter') {
    // 必须先阻止默认行为，防止双重换行
    e.preventDefault();
    handleEnterKey();
    return false;
  }
  
  // 处理Tab键提示
  if (e.key === 'Tab' && showSuggestion.value) {
    e.preventDefault();
    e.stopPropagation();
    insertSuggestion();
    return false;
  }
};

// 处理回车键，强制更新行号
const handleEnterKey = () => {
  // 如果不是在编辑状态，直接返回
  if (!editorRef.value) return;
  
  // 获取当前选区
  const selection = window.getSelection();
  if (!selection?.rangeCount) return;
  
  const range = selection.getRangeAt(0);
  
  // 插入两个换行符以确保新行效果
  // 在有些浏览器中，需要添加额外节点确保换行效果
  const br1 = document.createElement('br');
  range.insertNode(br1);
  
  // 设置光标到新行
  range.setStartAfter(br1);
  range.collapse(true);
  
  // 清除所有选区并应用新位置
  selection.removeAllRanges();
  selection.addRange(range);
  
  // 更新内容
  content.value = editorRef.value.textContent || '';
  addToHistory(content.value);
  emit('update:content', content.value);
};

// 手动处理粘贴事件，确保行号正确更新
const handlePaste = (e: ClipboardEvent) => {
  e.preventDefault();
  
  // 获取粘贴的纯文本内容
  const text = e.clipboardData?.getData('text/plain') || '';
  
  // 处理换行符，将所有换行符转换为<br>
  const formattedText = text.replace(/\n/g, '<br>');
  
  // 获取当前选区
  const selection = window.getSelection();
  if (!selection?.rangeCount) return;
  
  // 获取当前范围
  const range = selection.getRangeAt(0);
  
  // 创建文档片段
  const fragment = document.createDocumentFragment();
  
  // 创建临时容器来解析HTML
  const temp = document.createElement('div');
  temp.innerHTML = formattedText;
  
  // 将所有子节点移动到片段
  while (temp.firstChild) {
    fragment.appendChild(temp.firstChild);
  }
  
  // 插入片段
  range.deleteContents();
  range.insertNode(fragment);
  
  // 将光标移到插入内容后面
  range.collapse(false);
  selection.removeAllRanges();
  selection.addRange(range);
  
  // 更新内容
  if (editorRef.value) {
    content.value = editorRef.value.textContent || '';
    addToHistory(content.value);
    emit('update:content', content.value);
  }
};

// 更新光标位置信息
const updateCursorPosition = () => {
  nextTick(() => {
    const selection = window.getSelection();
    if (!selection?.rangeCount || !editorRef.value) return;
    
    const range = selection.getRangeAt(0);
    const preSelectionRange = range.cloneRange();
    preSelectionRange.selectNodeContents(editorRef.value);
    preSelectionRange.setEnd(range.startContainer, range.startOffset);
    
    const text = preSelectionRange.toString();
    
    // 计算行号和列号
    const lines = text.split('\n');
    const row = lines.length;
    const col = lines[lines.length - 1].length + 1;
    
    cursorPosition.value = { row, col };
  });
};

// 对外暴露方法
defineExpose({
  setContent,
  focus: () => editorRef.value?.focus()
});

onMounted(() => {
  if (editorRef.value) {
    // 初始化window.suggestionDebounce
    window.suggestionDebounce = null;
    
    // 初始化编辑器内容
    if (props.initialContent) {
      // 使用innerHTML而不是textContent可以保留换行
      const formattedContent = props.initialContent.replace(/\n/g, '<br>');
      editorRef.value.innerHTML = formattedContent;
      content.value = props.initialContent;
      addToHistory(content.value);
    }
    
    // 设置编辑器焦点
    editorRef.value.focus();
    
    // 创建MutationObserver监听DOM变化
    const observer = new MutationObserver(() => {
      // 删除对lineNumbers的更新逻辑
    });
    
    // 观察所有可能影响行数的变化
    observer.observe(editorRef.value, {
      childList: true,
      subtree: true,
      characterData: true,
      attributes: false
    });
    
    // 初始化光标位置
    setTimeout(() => {
      updateCursorPosition();
    }, 50);
    
    // 监听鼠标事件，处理建议预览
    editorRef.value.addEventListener('mousedown', (e) => {
      let target = e.target as HTMLElement;
      while (target && target !== editorRef.value) {
        if (target.classList.contains('suggestion-preview') || 
            target.classList.contains('suggestion-preview-wrapper')) {
          e.preventDefault();
          e.stopPropagation();
          return false;
        }
        target = target.parentElement as HTMLElement;
      }
    }, true);
  }
});
</script>

<style scoped>
.editor-container {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}

.editor-wrapper {
  flex: 1;
  display: flex;
  overflow: hidden;
  position: relative;
}

.editor-content.text-editor {
  flex: 1;
  padding: 20px;
  padding-left: 10px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace !important;
  font-size: 14px;
  line-height: 1.5em !important;
  color: #242424;
  background: #fff;
  overflow-y: auto;
  position: relative;
  white-space: pre-wrap;
  word-break: break-word;
  tab-size: 2;
  -moz-tab-size: 2;
  caret-color: #1a73e8;
  min-height: 100%;
}

/* 添加占位符样式 */
.editor-content.text-editor:empty:before {
  content: attr(placeholder);
  color: #aaa;
  font-style: italic;
}

.editor-content:focus {
  outline: none;
  box-shadow: none;
}

/* 为光标添加高亮背景色 */
.editor-content::selection {
  background-color: rgba(74, 144, 226, 0.2);
  color: #242424;
}

.suggestion-preview {
  color: #aaa !important;
  opacity: 0.9;
  pointer-events: none !important;
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  position: relative;
  font-style: italic !important;
  background: linear-gradient(to right, rgba(245, 245, 245, 0.7) 30%, transparent);
  border-radius: 2px;
  padding: 0 2px;
  margin-left: 1px;
  animation: fadeIn 0.2s ease-in;
  display: inline-block;
  cursor: default !important;
}

.suggestion-preview::after {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  bottom: -1px;
  height: 1px;
  background: #ddd;
  opacity: 0.6;
}

@keyframes fadeIn {
  from { 
    opacity: 0; 
    transform: translateX(4px);
    filter: blur(2px);
  }
  to { 
    opacity: 0.9; 
    transform: translateX(0);
    filter: blur(0);
  }
}

/* 预览包装容器样式 */
.suggestion-preview-wrapper {
  display: inline;
  pointer-events: none;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  cursor: default !important;
}

/* 添加页脚样式 */
.editor-footer {
  height: 24px;
  border-top: 1px solid #e0e0e0;
  background-color: #f5f5f5;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px;
  font-size: 12px;
  color: #666;
  user-select: none;
}

.editor-stats, .editor-info {
  display: flex;
  align-items: center;
}

.separator {
  margin: 0 8px;
  color: #ccc;
}
</style> 