<template>
  <button class="new-doc-btn" @click="$emit('create')">
    新建
  </button>
</template>

<script setup lang="ts">
import { theme } from 'ant-design-vue';

const { useToken } = theme;
const { token } = useToken();

// 定义事件
defineEmits<{
  (e: 'create'): void;
}>();
</script>

<style scoped>
.new-doc-btn {
  background: v-bind('token.colorPrimary');
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 12px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.new-doc-btn:hover {
  background: v-bind('token.colorPrimaryHover');
}
</style> 