<template>
  <div class="document-list-header">
    <h3>{{ title }}</h3>
    <slot></slot>
  </div>
</template>

<script setup lang="ts">
import { theme } from 'ant-design-vue';

const { useToken } = theme;
const { token } = useToken();

// 定义属性
defineProps<{
  title: string;
}>();
</script>

<style scoped>
.document-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid v-bind('token.colorBorderSecondary');
}

.document-list-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}
</style> 