<template>
  <div 
    class="document-item"
    :class="{ 'active': isActive }"
    @click="$emit('select', document.id)"
  >
    <div class="doc-icon">
      <svg viewBox="0 0 24 24" width="16" height="16">
        <path fill="currentColor" d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
      </svg>
    </div>
    <div class="doc-info">
      <div class="doc-title">{{ document.title }}</div>
      <div class="doc-meta">{{ document.lastModified }}</div>
    </div>
    <div class="doc-actions">
      <button class="doc-menu-btn" @click.stop>
        <svg viewBox="0 0 24 24" width="16" height="16">
          <path fill="currentColor" d="M12,16A2,2 0 0,1 14,18A2,2 0 0,1 12,20A2,2 0 0,1 10,18A2,2 0 0,1 12,16M12,10A2,2 0 0,1 14,12A2,2 0 0,1 12,14A2,2 0 0,1 10,12A2,2 0 0,1 12,10M12,4A2,2 0 0,1 14,6A2,2 0 0,1 12,8A2,2 0 0,1 10,6A2,2 0 0,1 12,4Z" />
        </svg>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { theme } from 'ant-design-vue';

const { useToken } = theme;
const { token } = useToken();

// 定义组件属性
const props = defineProps<{
  document: {
    id: string;
    title: string;
    lastModified: string;
  };
  isActive: boolean;
}>();

// 定义事件
defineEmits<{
  (e: 'select', id: string): void;
}>();
</script>

<style scoped>
.document-item {
  display: flex;
  align-items: center;
  padding: 10px;
  cursor: pointer;
  transition: background-color 0.2s;
  border-bottom: 1px solid v-bind('token.colorBorderSecondary');
  background-color: v-bind('token.colorBgContainer');
}


.document-item.active {
  background-color: v-bind('token.colorPrimaryBg');
  border-left: 3px solid v-bind('token.colorPrimary');
}

.doc-icon {
  margin-right: 10px;
}

.doc-info {
  flex: 1;
}

.doc-title {
  font-size: 14px;
  margin-bottom: 4px;
}

.doc-meta {
  font-size: 12px;
}

.doc-actions {
  opacity: 0;
  transition: opacity 0.2s;
}

.document-item:hover .doc-actions {
  opacity: 1;
}

.doc-menu-btn {
  background: transparent;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  width: 24px;
  height: 24px;
}

.doc-menu-btn:hover {
}
</style> 