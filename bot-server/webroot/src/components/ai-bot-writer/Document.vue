<template>
  <div class="writer-editor-container size-full flex flex-row">
    <DocumentList class="workspace-container" />
    <div class="document-container size-full flex flex-col">
        <div ref="menuBarContainer"></div>
        <div ref="editorToolbar"></div>
        <div class="size-full flex flex-row overflow-auto">
          <div class="editor-area size-full">
            <div class="editor-container size-full">
              <div id="editor-content" class="size-full" ref="editorContainer"></div>
            </div>
          </div>

        </div>
      </div>
    
    <!-- <TypewriterPanel class="typewriter-panel"/> -->
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue';
import DocumentList from './DocumentList.vue';

// 使用DecoupledEditor实现文档界面
import { DecoupledEditor } from '@ckeditor/ckeditor5-editor-decoupled';
import { Essentials } from '@ckeditor/ckeditor5-essentials';
import { Paragraph } from '@ckeditor/ckeditor5-paragraph';
import { Bold, Italic, Strikethrough, Underline } from '@ckeditor/ckeditor5-basic-styles';
import { Heading } from '@ckeditor/ckeditor5-heading';
import { Image, ImageCaption, ImageResize, ImageStyle } from '@ckeditor/ckeditor5-image';
import { List } from '@ckeditor/ckeditor5-list';
import { Link } from '@ckeditor/ckeditor5-link';
import { Table, TableToolbar } from '@ckeditor/ckeditor5-table';
import { Alignment } from '@ckeditor/ckeditor5-alignment';
import { Font } from '@ckeditor/ckeditor5-font';
import { Indent } from '@ckeditor/ckeditor5-indent';
import { TextTransformation } from '@ckeditor/ckeditor5-typing';
import { WordCount } from '@ckeditor/ckeditor5-word-count';
import { Base64UploadAdapter } from '@ckeditor/ckeditor5-upload';
import { PageBreak } from '@ckeditor/ckeditor5-page-break';

// 导入自定义的AI助手插件
import { Dialog } from "@ckeditor/ckeditor5-ui";

import AiAssistant from "@/components/ai-bot-editor/editor/editor-plugins/ai-assistant/ai-assistant";
import { theme } from 'ant-design-vue';
import TypewriterPanel from "@/components/ai-bot-writer/TypewriterPanel.vue";

const { useToken } = theme;
const { token } = useToken();
// 编辑器相关
const editorContainer = ref<HTMLElement | null>(null);
const editorToolbar = ref<HTMLElement | null>(null);
const menuBarContainer = ref<HTMLElement | null>(null);
const wordCountDisplay = ref<HTMLElement | null>(null);
let editor: any = null;

// 编辑器内容
const editorContent = ref('');


// 初始化编辑器
onMounted(async () => {
  if (editorContainer.value && editorToolbar.value) {
    try {
      // 创建自定义编辑器实例，使用DecoupledEditor
      editor = await DecoupledEditor.create(editorContainer.value, {
        licenseKey: 'GPL',
        plugins: [
          Essentials, Paragraph,
          Bold, Italic, Underline, Strikethrough,
          Heading,
          Image, ImageCaption, ImageResize, ImageStyle,
          List,
          Link,
          Table, TableToolbar,
          Alignment,
          Font,
          Indent,
          TextTransformation,
          WordCount,
          Base64UploadAdapter,
          PageBreak,
          AiAssistant,
          Dialog,
        ],
        toolbar: {
          items: [
            'heading',
            '|',
            'alignment',
            'bold', 'italic', 'underline', 'strikethrough',
            '|',
            'fontFamily', 'fontSize', 'fontColor',
            '|',
            'bulletedList', 'numberedList',
            '|',
            'link', 'insertImage', 'insertTable'
          ],
          shouldNotGroupWhenFull: false
        },
        placeholder: '开始编辑文档...',
        heading: {
          options: [
            { model: 'paragraph', title: '正文', class: 'ck-heading_paragraph' },
            { model: 'heading1', view: 'h1', title: '标题 1', class: 'ck-heading_heading1' },
            { model: 'heading2', view: 'h2', title: '标题 2', class: 'ck-heading_heading2' },
            { model: 'heading3', view: 'h3', title: '标题 3', class: 'ck-heading_heading3' },
            { model: 'heading4', view: 'h4', title: '标题 4', class: 'ck-heading_heading4' }
          ]
        },
        fontSize: {
          options: [
            12, 14, 16, 18, 20, 22, 24, 26, 28
          ],
          supportAllValues: true
        },
        fontFamily: {
          options: [
            'default',
            'Arial, Helvetica, sans-serif',
            'Times New Roman, Times, serif',
            '宋体, SimSun, sans-serif',
            '微软雅黑, Microsoft YaHei, sans-serif'
          ]
        },
        language: 'zh-cn',
      } as any);
      // 将编辑器工具栏添加到工具栏容器
      editorToolbar.value.appendChild(editor.ui.view.toolbar.element);
    } catch (error) {
      console.error('初始化编辑器失败:', error);
    }
  }
});


// 组件销毁时清理编辑器
onUnmounted(() => {
  if (editor) {
    editor.destroy()
      .then(() => {
        editor = null;
      })
      .catch((error: any) => {
        console.error('销毁编辑器时出错:', error);
      });
  }
});
</script>


<style scoped>
.writer-editor-container * {
  background: v-bind('token.colorBgContainer');
  color: v-bind('token.colorText');
}

/* 确保整个容器不会出现滚动 */
.writer-editor-container {
  display: flex;
  height: 100%;
  width: 100%;

  overflow: hidden;
  /* 保持不溢出 */
  position: relative;
  /* 为自定义消息提示做准备 */
}

/* 右侧编辑器面板样式 */
.editor-panel {
  display: flex;
  height: 100%;
  overflow: hidden;
  /* 防止面板溢出 */
  min-width: 400px;
  /* 减小最小宽度，防止挤压 */
}

/* 添加基础样式让CKEditor标准工具栏在容器中正常显示 */
:deep(.ck.ck-toolbar) {
  border: 0;
  border-bottom: 1px solid #e0e0e0;
}

.document-container {
  position: relative;
  flex-grow: 1;
  margin: 0 auto;
  padding: 0 2px;
  overflow: hidden;
}

.editor-area {
  flex-grow: 1;
  overflow: auto;
  display: flex;
}

#editor-content {
  width: auto;
  height: auto;
  min-height: 100%;
  padding: 1mm 5mm;
  background: v-bind('token.colorBgContainer');
  border: none;
  outline: none;
  box-shadow: none;
  overflow: hidden;
}

/* 工作空间容器样式 */
.workspace-container {
  width: 200px;
  flex: 0 0 auto;
  position: relative;
  border: 1px solid v-bind('token.colorBorder');
  border-left: none;
  border-top: none;
  overflow: hidden;
}

.typewriter-panel {
  width: 200px;
  flex: 0 0 auto;
  position: relative;
}

.minimap-wrapper {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  width: 100%;
  top: -1px;
  overflow: auto;
  scrollbar-width: none;
  /* Firefox */
}

.minimap-wrapper::-webkit-scrollbar {
  display: none;
  /* Chrome, Safari, Edge */
}

/* 全局隐藏滚动条但保留滚动功能 */
:deep(.size-full.flex.flex-row.overflow-auto) {
  scrollbar-width: none;
  /* Firefox */
}

:deep(.size-full.flex.flex-row.overflow-auto::-webkit-scrollbar) {
  display: none;
  /* Chrome, Safari, Edge */
}


/* 适配 编辑器的 ant 颜色  todo 完善 */
:deep(.ck.ck-toolbar) {
  background: v-bind('token.colorBgContainer');
  border-bottom-color: v-bind('token.colorBorder');
}

:deep(.ck.ck-toolbar__separator) {
  background: v-bind('token.colorBorder') !important;
}

:deep(.ck.ck-reset_all.ck-minimap__iframe) {
  background: v-bind('token.colorBgContainer');
  color: v-bind('token.colorText');
  outline: 1px solid v-bind('token.colorBorder');
}

:deep(.ck.ck-reset_all.ck-minimap__iframe *) {
  color: v-bind('token.colorText') !important;
}

:deep(.ck.ck-minimap) {
  background: v-bind('token.colorBgContainer');
}

:deep(:is(.ck.ck-button, a.ck.ck-button):not(.ck-disabled):hover) {
  background-color: v-bind('token.colorFillTertiary');
}

:deep(.ck.ck-button) {
  background: v-bind('token.colorBgContainer');
  color: v-bind('token.colorText') !important;
}


:deep(.ck.ck-button.ck-list-item-button.ck-button.ck-on:hover) {
  background: v-bind('token.colorFillTertiary') !important;
}

:deep(.ck.ck-button.ck-list-item-button.ck-button:hover) {
  background: v-bind('token.colorFillTertiary') !important;
}

:deep(.ck.ck-button, a.ck.ck-button:active, .ck.ck-button, a.ck.ck-button:focus) {
  box-shadow: v-bind('token.colorBgContainer'), 0 0 !important;
}

:deep(.ck-on:is(.ck.ck-button, a.ck.ck-button)) {
  background: v-bind('token.colorBgContainer') !important;
}

:deep(.ck.ck-reset.ck-dropdown__panel.ck-dropdown__panel_se.ck-dropdown__panel-visible) {
  border-color: v-bind('token.colorBorder');
}
</style>