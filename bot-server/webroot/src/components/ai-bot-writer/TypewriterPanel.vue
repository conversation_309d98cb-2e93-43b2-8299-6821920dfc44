<template>
    <!-- 左侧可拖拽面板 -->
    <ResizablePanel 
        ref="resizablePanelRef"
        :initial-width="initialWidth"
        :min-width="minWidth"
        :max-width-percent="maxWidthPercent"
        :default-drag-mode="defaultDragMode"
        :remember-user-preference="rememberUserPreference"
        :storageKeyPrefix="storageKeyPrefix" 
        :resize-edge="resizeEdge"
        @resize="handleResize" 
        @resizeStart="handleResizeStart"
        @resizeEnd="handleResizeEnd">
        <div class="left-panel-content">
            <!-- 左侧面板内容 -->
            <h3>左侧面板</h3>
            <div class="typewriter-options">
                <!-- 这里可以添加类似文档列表的内容 -->
            </div>
        </div>
    </ResizablePanel>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import ResizablePanel from '../ai-bot-widget/ResizablePanel.vue';

// 定义组件属性
const props = defineProps({
  // 默认拖拽模式: 'preview' 预览模式，'direct' 实时模式
  defaultDragMode: {
    type: String,
    default: 'direct', // 默认使用实时模式
    validator: (value: string) => ['preview', 'direct'].includes(value)
  },
  // 是否记住用户的拖拽模式选择
  rememberUserPreference: {
    type: Boolean,
    default: true
  },
  // 初始宽度
  initialWidth: {
    type: String,
    default: '400px'
  },
  // 最小宽度 (px)
  minWidth: {
    type: Number,
    default: 300
  },
  // 最大宽度 (窗口宽度百分比)
  maxWidthPercent: {
    type: Number,
    default: 40
  },
  // 本地存储键名前缀
  storageKeyPrefix: {
    type: String,
    default: 'typewriter-left-panel'
  },
  // 调整边缘
  resizeEdge: {
    type: String,
    default: 'left'
  }
});

// ResizablePanel 引用
const resizablePanelRef = ref(null);

// 面板宽度变化处理
const handleResize = (event: { width: number; previewOnly: boolean }) => {
    console.log('Panel resized:', event.width);
};

// 面板调整开始处理
const handleResizeStart = (event: { width: number; mode: string }) => {
    console.log('Resize started:', event);
};

// 面板调整结束处理
const handleResizeEnd = (event: { width: number }) => {
    console.log('Resize ended:', event.width);
};

// 公开方法给父组件
defineExpose({
  // 设置拖拽模式
  setDragMode: (mode: 'preview' | 'direct') => {
    resizablePanelRef.value?.setDragMode(mode);
  },
  // 获取当前拖拽模式
  getDragMode: () => resizablePanelRef.value?.getDragMode(),
  // 获取当前宽度
  getCurrentWidth: () => resizablePanelRef.value?.getCurrentWidth() || 0,
  // 设置宽度
  setWidth: (width: number | string) => {
    resizablePanelRef.value?.setWidth(width);
  },
  // 切换拖拽模式
  toggleDragMode: () => resizablePanelRef.value?.toggleDragMode()
});
</script>

<style scoped>
.typewriter-container {
    display: flex;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.left-panel-content {
    padding: 16px;
    height: 100%;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

.typewriter-options {
    flex: 1;
    margin-top: 16px;
}

.main-content {
    flex: 1;
    padding: 16px;
    height: 100%;
    overflow-y: auto;
    min-width: 0;
    /* 防止flex子项溢出 */
}
</style>