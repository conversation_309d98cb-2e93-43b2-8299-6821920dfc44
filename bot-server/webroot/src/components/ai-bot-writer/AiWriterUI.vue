<template>
  <PageLayout>
    <div class="editor-container size-full">
      <Document/>
    </div>
  </PageLayout>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onBeforeUnmount } from 'vue';
import PageLayout from '@/components/ai-bot-layout/PageLayout.vue';
// 导入封装的网格容器组件
import DraggableGridContainer from '../ai-bot-widget/DraggableGridContainer.vue';
// 导入GridStack类型
import { GridStackNode } from 'gridstack';
import WriterEditor from "@/components/ai-bot-writer/WriterEditor.vue";
import Document from "@/components/ai-bot-writer/Document.vue";

// 网格容器组件引用
const gridContainerComponent = ref(null);

// GridStack配置选项 - 简化为与官方示例相似的配置
const gridOptions = reactive({
  cellHeight: 70,
  margin: 12,
  float: true,
  minRow: 1
});

// 网格项数据 - 添加唯一ID，使用更简单的默认布局
const items = reactive([
  { id: 'w_1', x: 2, y: 1, w: 2, h: 2 },
  { id: 'w_2', x: 4, y: 3, w: 2, h: 2 },
  { id: 'w_3', x: 2, y: 3, w: 2, h: 2 }
]);

// 处理网格变化事件
const handleGridChange = (gridItems: GridStackNode[]) => {
  console.log('网格变化:', gridItems);
};

// 处理拖动结束事件
const handleDragStop = (element: HTMLElement) => {
  console.log('拖动结束，元素:', element);
};

// 处理窗口大小变化时的重新布局
const handleResize = () => {
  if (gridContainerComponent.value) {
    const grid = gridContainerComponent.value.getGrid();
    if (grid) {
      grid.cellHeight(gridOptions.cellHeight);
    }
  }
};

// 组件挂载后添加窗口大小调整事件监听
onMounted(() => {
  window.addEventListener('resize', handleResize);
});

// 组件卸载前清除事件监听
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize);
});
</script>

<style scoped>
/* 编辑器容器样式 */
.editor-container {
  position: relative;
  background: #fff;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100%;
  /* 删除可能的最小高度限制 */
  min-height: 0 !important; /* 使用!important确保优先级 */
  max-height: 100%; /* 限制最大高度不超过容器 */
}

/* 针对AIWriterEditor组件的样式 */
:deep(.writer-editor-container) {
  height: 100%;
  max-height: 100%;
  min-height: 0 !important; /* 防止flex子项目的默认最小高度行为 */
  overflow: hidden; /* 只在外层容器隐藏溢出 */
}

/* 确保内部编辑区域可以滚动 */
:deep(.editor-area) {
  overflow: auto !important; /* 确保编辑区域可以滚动 */
}

/* 文档列表也需要保持滚动 */
:deep(.document-list) {
  overflow-y: auto !important; /* 确保文档列表可以垂直滚动 */
}

/* 网格容器包装器 - 占据所有剩余空间 */
.grid-container-wrapper {
  flex: 1;
  overflow: hidden;
  position: relative;
  display: flex;
  min-height: 0; /* 重要：防止flex项目溢出 */
}


</style>