/* BigBlueTerminal 字体 */
@font-face {
    font-family: 'BigBlueTerm437';
    src: url('BigBlueTerminal/BigBlueTerm437NerdFont-Regular.ttf');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'BigBlueTermPlus';
    src: url('BigBlueTerminal/BigBlueTermPlusNerdFont-Regular.ttf');
    font-weight: normal;
    font-style: normal;
}

/* CascadiaCode 字体 */
@font-face {
    font-family: 'CascadiaCode';
    src: url('CascadiaCode/CaskaydiaCoveNerdFont-Regular.ttf');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'CascadiaCode';
    src: url('CascadiaCode/CaskaydiaCoveNerdFont-Bold.ttf');
    font-weight: bold;
    font-style: normal;
}

@font-face {
    font-family: 'CascadiaCode';
    src: url('CascadiaCode/CaskaydiaCoveNerdFont-Italic.ttf');
    font-weight: normal;
    font-style: italic;
}

@font-face {
    font-family: 'CascadiaCode';
    src: url('CascadiaCode/CaskaydiaCoveNerdFont-BoldItalic.ttf');
    font-weight: bold;
    font-style: italic;
}

/* DepartureMono */
@font-face {
    font-family: 'DepartureMono';
    src: url('DepartureMono-1.422/DepartureMono-Regular.otf');
    font-weight: bold;
    font-style: italic;
}

@font-face {
    font-family: 'DepartureMono';
    src: url('DepartureMono-1.422/DepartureMono-Regular.otf');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'DepartureMono';
    src: url('DepartureMono-1.422/DepartureMono-Regular.otf');
    font-weight: bold;
    font-style: normal;
}

@font-face {
    font-family: 'DepartureMono';
    src: url('DepartureMono-1.422/DepartureMono-Regular.otf');
    font-weight: normal;
    font-style: italic;
}

@font-face {
    font-family: 'DepartureMono';
    src: url('DepartureMono-1.422/DepartureMono-Regular.otf');
    font-weight: bold;
    font-style: italic;
}