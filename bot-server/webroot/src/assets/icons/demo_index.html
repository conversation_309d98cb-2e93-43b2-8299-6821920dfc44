<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=4748538" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe65c;</span>
                <div class="name">微博</div>
                <div class="code-name">&amp;#xe65c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60e;</span>
                <div class="name">微信公众号管理</div>
                <div class="code-name">&amp;#xe60e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe689;</span>
                <div class="name">小红书-copy</div>
                <div class="code-name">&amp;#xe689;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60d;</span>
                <div class="name">debug</div>
                <div class="code-name">&amp;#xe60d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61b;</span>
                <div class="name">Finish thinking</div>
                <div class="code-name">&amp;#xe61b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe681;</span>
                <div class="name">绘画创作</div>
                <div class="code-name">&amp;#xe681;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63b;</span>
                <div class="name">音乐</div>
                <div class="code-name">&amp;#xe63b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xebb3;</span>
                <div class="name">message-bot</div>
                <div class="code-name">&amp;#xebb3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe71e;</span>
                <div class="name">自定义</div>
                <div class="code-name">&amp;#xe71e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe605;</span>
                <div class="name">dark</div>
                <div class="code-name">&amp;#xe605;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe606;</span>
                <div class="name">light</div>
                <div class="code-name">&amp;#xe606;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65a;</span>
                <div class="name">zip</div>
                <div class="code-name">&amp;#xe65a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60c;</span>
                <div class="name">pdf文件</div>
                <div class="code-name">&amp;#xe60c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe624;</span>
                <div class="name">docx</div>
                <div class="code-name">&amp;#xe624;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe607;</span>
                <div class="name">xlsx</div>
                <div class="code-name">&amp;#xe607;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe67f;</span>
                <div class="name">rar</div>
                <div class="code-name">&amp;#xe67f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe836;</span>
                <div class="name">image</div>
                <div class="code-name">&amp;#xe836;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe69c;</span>
                <div class="name">插件</div>
                <div class="code-name">&amp;#xe69c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60b;</span>
                <div class="name">互联网</div>
                <div class="code-name">&amp;#xe60b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ae;</span>
                <div class="name">arrow-2</div>
                <div class="code-name">&amp;#xe7ae;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe860;</span>
                <div class="name">模型</div>
                <div class="code-name">&amp;#xe860;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61d;</span>
                <div class="name">逐浪写作-02</div>
                <div class="code-name">&amp;#xe61d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6fd;</span>
                <div class="name">icon_AI助手</div>
                <div class="code-name">&amp;#xe6fd;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe609;</span>
                <div class="name">菜单</div>
                <div class="code-name">&amp;#xe609;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6a8;</span>
                <div class="name">pdf</div>
                <div class="code-name">&amp;#xe6a8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe615;</span>
                <div class="name">导出</div>
                <div class="code-name">&amp;#xe615;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65f;</span>
                <div class="name">图片</div>
                <div class="code-name">&amp;#xe65f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe67c;</span>
                <div class="name">写作</div>
                <div class="code-name">&amp;#xe67c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6e9;</span>
                <div class="name">web页面</div>
                <div class="code-name">&amp;#xe6e9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe604;</span>
                <div class="name">刷新</div>
                <div class="code-name">&amp;#xe604;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe637;</span>
                <div class="name">kfzgj</div>
                <div class="code-name">&amp;#xe637;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe680;</span>
                <div class="name">delete</div>
                <div class="code-name">&amp;#xe680;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe622;</span>
                <div class="name">copy</div>
                <div class="code-name">&amp;#xe622;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65b;</span>
                <div class="name">sql</div>
                <div class="code-name">&amp;#xe65b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66c;</span>
                <div class="name">json</div>
                <div class="code-name">&amp;#xe66c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe787;</span>
                <div class="name">php</div>
                <div class="code-name">&amp;#xe787;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe603;</span>
                <div class="name">shell</div>
                <div class="code-name">&amp;#xe603;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe68f;</span>
                <div class="name">rust</div>
                <div class="code-name">&amp;#xe68f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe839;</span>
                <div class="name">textedit_text_align_left</div>
                <div class="code-name">&amp;#xe839;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61e;</span>
                <div class="name">css</div>
                <div class="code-name">&amp;#xe61e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe632;</span>
                <div class="name">html</div>
                <div class="code-name">&amp;#xe632;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe639;</span>
                <div class="name">java</div>
                <div class="code-name">&amp;#xe639;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe664;</span>
                <div class="name">ts</div>
                <div class="code-name">&amp;#xe664;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6d0;</span>
                <div class="name">Golang</div>
                <div class="code-name">&amp;#xe6d0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe767;</span>
                <div class="name">python</div>
                <div class="code-name">&amp;#xe767;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64d;</span>
                <div class="name">JavaScript</div>
                <div class="code-name">&amp;#xe64d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe610;</span>
                <div class="name">cpp</div>
                <div class="code-name">&amp;#xe610;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65e;</span>
                <div class="name">月亮</div>
                <div class="code-name">&amp;#xe65e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60a;</span>
                <div class="name">太阳</div>
                <div class="code-name">&amp;#xe60a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe602;</span>
                <div class="name">windows_max</div>
                <div class="code-name">&amp;#xe602;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xeef0;</span>
                <div class="name">windows_close</div>
                <div class="code-name">&amp;#xeef0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe8c9;</span>
                <div class="name">windows_min</div>
                <div class="code-name">&amp;#xe8c9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe601;</span>
                <div class="name">参数配置-copy</div>
                <div class="code-name">&amp;#xe601;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ad;</span>
                <div class="name">平台</div>
                <div class="code-name">&amp;#xe6ad;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6b7;</span>
                <div class="name">编辑</div>
                <div class="code-name">&amp;#xe6b7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6b1;</span>
                <div class="name">编辑-2</div>
                <div class="code-name">&amp;#xe6b1;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe617;</span>
                <div class="name">删除</div>
                <div class="code-name">&amp;#xe617;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe946;</span>
                <div class="name">清空</div>
                <div class="code-name">&amp;#xe946;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe608;</span>
                <div class="name">点</div>
                <div class="code-name">&amp;#xe608;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6e8;</span>
                <div class="name">最小化</div>
                <div class="code-name">&amp;#xe6e8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62c;</span>
                <div class="name">设置001</div>
                <div class="code-name">&amp;#xe62c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe611;</span>
                <div class="name">系统设置</div>
                <div class="code-name">&amp;#xe611;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe669;</span>
                <div class="name">在线咨询_面</div>
                <div class="code-name">&amp;#xe669;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe614;</span>
                <div class="name">菜单</div>
                <div class="code-name">&amp;#xe614;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe600;</span>
                <div class="name">tools</div>
                <div class="code-name">&amp;#xe600;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe618;</span>
                <div class="name">知识库</div>
                <div class="code-name">&amp;#xe618;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe613;</span>
                <div class="name">最大化</div>
                <div class="code-name">&amp;#xe613;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe665;</span>
                <div class="name">arrow-double-left</div>
                <div class="code-name">&amp;#xe665;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe666;</span>
                <div class="name">arrow-double-right</div>
                <div class="code-name">&amp;#xe666;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe84f;</span>
                <div class="name">code</div>
                <div class="code-name">&amp;#xe84f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6a5;</span>
                <div class="name">ChatGPT</div>
                <div class="code-name">&amp;#xe6a5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6b8;</span>
                <div class="name">设置-2</div>
                <div class="code-name">&amp;#xe6b8;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.eot?t=1749991066506'); /* IE9 */
  src: url('iconfont.eot?t=1749991066506#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('data:application/x-font-woff2;charset=utf-8;base64,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') format('woff2'),
       url('iconfont.woff?t=1749991066506') format('woff'),
       url('iconfont.ttf?t=1749991066506') format('truetype'),
       url('iconfont.svg?t=1749991066506#iconfont') format('svg');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont weibo"></span>
            <div class="name">
              微博
            </div>
            <div class="code-name">.weibo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont weixingongzhonghaoguanli"></span>
            <div class="name">
              微信公众号管理
            </div>
            <div class="code-name">.weixingongzhonghaoguanli
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont xiaohongshu"></span>
            <div class="name">
              小红书-copy
            </div>
            <div class="code-name">.xiaohongshu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont debug"></span>
            <div class="name">
              debug
            </div>
            <div class="code-name">.debug
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont yishendusikao"></span>
            <div class="name">
              Finish thinking
            </div>
            <div class="code-name">.yishendusikao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont huihuachuangzuo"></span>
            <div class="name">
              绘画创作
            </div>
            <div class="code-name">.huihuachuangzuo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont yinle"></span>
            <div class="name">
              音乐
            </div>
            <div class="code-name">.yinle
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont message-bot"></span>
            <div class="name">
              message-bot
            </div>
            <div class="code-name">.message-bot
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont zidingyi"></span>
            <div class="name">
              自定义
            </div>
            <div class="code-name">.zidingyi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont dark"></span>
            <div class="name">
              dark
            </div>
            <div class="code-name">.dark
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont light"></span>
            <div class="name">
              light
            </div>
            <div class="code-name">.light
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont zip"></span>
            <div class="name">
              zip
            </div>
            <div class="code-name">.zip
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pdfwenjian"></span>
            <div class="name">
              pdf文件
            </div>
            <div class="code-name">.pdfwenjian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont docx1"></span>
            <div class="name">
              docx
            </div>
            <div class="code-name">.docx1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont xlsx"></span>
            <div class="name">
              xlsx
            </div>
            <div class="code-name">.xlsx
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont rar"></span>
            <div class="name">
              rar
            </div>
            <div class="code-name">.rar
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont image"></span>
            <div class="name">
              image
            </div>
            <div class="code-name">.image
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont chajian"></span>
            <div class="name">
              插件
            </div>
            <div class="code-name">.chajian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont hulianwang"></span>
            <div class="name">
              互联网
            </div>
            <div class="code-name">.hulianwang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont arrow-2"></span>
            <div class="name">
              arrow-2
            </div>
            <div class="code-name">.arrow-2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont moxing"></span>
            <div class="name">
              模型
            </div>
            <div class="code-name">.moxing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont zhulangxiezuo-"></span>
            <div class="name">
              逐浪写作-02
            </div>
            <div class="code-name">.zhulangxiezuo-
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_AIzhushou"></span>
            <div class="name">
              icon_AI助手
            </div>
            <div class="code-name">.icon_AIzhushou
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont caidan1"></span>
            <div class="name">
              菜单
            </div>
            <div class="code-name">.caidan1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont pdf"></span>
            <div class="name">
              pdf
            </div>
            <div class="code-name">.pdf
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont daochu"></span>
            <div class="name">
              导出
            </div>
            <div class="code-name">.daochu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont tupian"></span>
            <div class="name">
              图片
            </div>
            <div class="code-name">.tupian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont xiezuo"></span>
            <div class="name">
              写作
            </div>
            <div class="code-name">.xiezuo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconset0334"></span>
            <div class="name">
              web页面
            </div>
            <div class="code-name">.iconset0334
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont shuaxin"></span>
            <div class="name">
              刷新
            </div>
            <div class="code-name">.shuaxin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_kaifazhegongju"></span>
            <div class="name">
              kfzgj
            </div>
            <div class="code-name">.icon_kaifazhegongju
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont delete"></span>
            <div class="name">
              delete
            </div>
            <div class="code-name">.delete
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont copy"></span>
            <div class="name">
              copy
            </div>
            <div class="code-name">.copy
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont sql"></span>
            <div class="name">
              sql
            </div>
            <div class="code-name">.sql
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont json"></span>
            <div class="name">
              json
            </div>
            <div class="code-name">.json
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont php"></span>
            <div class="name">
              php
            </div>
            <div class="code-name">.php
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont shell"></span>
            <div class="name">
              shell
            </div>
            <div class="code-name">.shell
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont rust"></span>
            <div class="name">
              rust
            </div>
            <div class="code-name">.rust
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont textedit_text_align_left"></span>
            <div class="name">
              textedit_text_align_left
            </div>
            <div class="code-name">.textedit_text_align_left
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont css"></span>
            <div class="name">
              css
            </div>
            <div class="code-name">.css
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont html"></span>
            <div class="name">
              html
            </div>
            <div class="code-name">.html
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont java"></span>
            <div class="name">
              java
            </div>
            <div class="code-name">.java
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont ts"></span>
            <div class="name">
              ts
            </div>
            <div class="code-name">.ts
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont Golang"></span>
            <div class="name">
              Golang
            </div>
            <div class="code-name">.Golang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont python"></span>
            <div class="name">
              python
            </div>
            <div class="code-name">.python
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont JavaScript"></span>
            <div class="name">
              JavaScript
            </div>
            <div class="code-name">.JavaScript
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont cpp"></span>
            <div class="name">
              cpp
            </div>
            <div class="code-name">.cpp
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont yueliang"></span>
            <div class="name">
              月亮
            </div>
            <div class="code-name">.yueliang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont taiyang"></span>
            <div class="name">
              太阳
            </div>
            <div class="code-name">.taiyang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont windows_max"></span>
            <div class="name">
              windows_max
            </div>
            <div class="code-name">.windows_max
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont windows_close"></span>
            <div class="name">
              windows_close
            </div>
            <div class="code-name">.windows_close
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont windows_min"></span>
            <div class="name">
              windows_min
            </div>
            <div class="code-name">.windows_min
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont canshupeizhi"></span>
            <div class="name">
              参数配置-copy
            </div>
            <div class="code-name">.canshupeizhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont RectangleCopy"></span>
            <div class="name">
              平台
            </div>
            <div class="code-name">.RectangleCopy
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont bianji1"></span>
            <div class="name">
              编辑
            </div>
            <div class="code-name">.bianji1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont bianji-2"></span>
            <div class="name">
              编辑-2
            </div>
            <div class="code-name">.bianji-2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont shanchu"></span>
            <div class="name">
              删除
            </div>
            <div class="code-name">.shanchu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont qingkong"></span>
            <div class="name">
              清空
            </div>
            <div class="code-name">.qingkong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont dian"></span>
            <div class="name">
              点
            </div>
            <div class="code-name">.dian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont zuixiaohua"></span>
            <div class="name">
              最小化
            </div>
            <div class="code-name">.zuixiaohua
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont shezhi001"></span>
            <div class="name">
              设置001
            </div>
            <div class="code-name">.shezhi001
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont xitongshezhi"></span>
            <div class="name">
              系统设置
            </div>
            <div class="code-name">.xitongshezhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont zaixianzixun_mian"></span>
            <div class="name">
              在线咨询_面
            </div>
            <div class="code-name">.zaixianzixun_mian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont caidan"></span>
            <div class="name">
              菜单
            </div>
            <div class="code-name">.caidan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont tools"></span>
            <div class="name">
              tools
            </div>
            <div class="code-name">.tools
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont zhishi"></span>
            <div class="name">
              知识库
            </div>
            <div class="code-name">.zhishi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont zuidahua"></span>
            <div class="name">
              最大化
            </div>
            <div class="code-name">.zuidahua
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont arrow-double-left"></span>
            <div class="name">
              arrow-double-left
            </div>
            <div class="code-name">.arrow-double-left
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont arrow-double-right"></span>
            <div class="name">
              arrow-double-right
            </div>
            <div class="code-name">.arrow-double-right
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont code"></span>
            <div class="name">
              code
            </div>
            <div class="code-name">.code
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont ChatGPT"></span>
            <div class="name">
              ChatGPT
            </div>
            <div class="code-name">.ChatGPT
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont shezhi-2"></span>
            <div class="name">
              设置-2
            </div>
            <div class="code-name">.shezhi-2
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#weibo"></use>
                </svg>
                <div class="name">微博</div>
                <div class="code-name">#weibo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#weixingongzhonghaoguanli"></use>
                </svg>
                <div class="name">微信公众号管理</div>
                <div class="code-name">#weixingongzhonghaoguanli</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#xiaohongshu"></use>
                </svg>
                <div class="name">小红书-copy</div>
                <div class="code-name">#xiaohongshu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#debug"></use>
                </svg>
                <div class="name">debug</div>
                <div class="code-name">#debug</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#yishendusikao"></use>
                </svg>
                <div class="name">Finish thinking</div>
                <div class="code-name">#yishendusikao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#huihuachuangzuo"></use>
                </svg>
                <div class="name">绘画创作</div>
                <div class="code-name">#huihuachuangzuo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#yinle"></use>
                </svg>
                <div class="name">音乐</div>
                <div class="code-name">#yinle</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#message-bot"></use>
                </svg>
                <div class="name">message-bot</div>
                <div class="code-name">#message-bot</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#zidingyi"></use>
                </svg>
                <div class="name">自定义</div>
                <div class="code-name">#zidingyi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#dark"></use>
                </svg>
                <div class="name">dark</div>
                <div class="code-name">#dark</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#light"></use>
                </svg>
                <div class="name">light</div>
                <div class="code-name">#light</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#zip"></use>
                </svg>
                <div class="name">zip</div>
                <div class="code-name">#zip</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pdfwenjian"></use>
                </svg>
                <div class="name">pdf文件</div>
                <div class="code-name">#pdfwenjian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#docx1"></use>
                </svg>
                <div class="name">docx</div>
                <div class="code-name">#docx1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#xlsx"></use>
                </svg>
                <div class="name">xlsx</div>
                <div class="code-name">#xlsx</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#rar"></use>
                </svg>
                <div class="name">rar</div>
                <div class="code-name">#rar</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#image"></use>
                </svg>
                <div class="name">image</div>
                <div class="code-name">#image</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#chajian"></use>
                </svg>
                <div class="name">插件</div>
                <div class="code-name">#chajian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#hulianwang"></use>
                </svg>
                <div class="name">互联网</div>
                <div class="code-name">#hulianwang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#arrow-2"></use>
                </svg>
                <div class="name">arrow-2</div>
                <div class="code-name">#arrow-2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#moxing"></use>
                </svg>
                <div class="name">模型</div>
                <div class="code-name">#moxing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#zhulangxiezuo-"></use>
                </svg>
                <div class="name">逐浪写作-02</div>
                <div class="code-name">#zhulangxiezuo-</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_AIzhushou"></use>
                </svg>
                <div class="name">icon_AI助手</div>
                <div class="code-name">#icon_AIzhushou</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#caidan1"></use>
                </svg>
                <div class="name">菜单</div>
                <div class="code-name">#caidan1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#pdf"></use>
                </svg>
                <div class="name">pdf</div>
                <div class="code-name">#pdf</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#daochu"></use>
                </svg>
                <div class="name">导出</div>
                <div class="code-name">#daochu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#tupian"></use>
                </svg>
                <div class="name">图片</div>
                <div class="code-name">#tupian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#xiezuo"></use>
                </svg>
                <div class="name">写作</div>
                <div class="code-name">#xiezuo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconset0334"></use>
                </svg>
                <div class="name">web页面</div>
                <div class="code-name">#iconset0334</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#shuaxin"></use>
                </svg>
                <div class="name">刷新</div>
                <div class="code-name">#shuaxin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_kaifazhegongju"></use>
                </svg>
                <div class="name">kfzgj</div>
                <div class="code-name">#icon_kaifazhegongju</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#delete"></use>
                </svg>
                <div class="name">delete</div>
                <div class="code-name">#delete</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#copy"></use>
                </svg>
                <div class="name">copy</div>
                <div class="code-name">#copy</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#sql"></use>
                </svg>
                <div class="name">sql</div>
                <div class="code-name">#sql</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#json"></use>
                </svg>
                <div class="name">json</div>
                <div class="code-name">#json</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#php"></use>
                </svg>
                <div class="name">php</div>
                <div class="code-name">#php</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#shell"></use>
                </svg>
                <div class="name">shell</div>
                <div class="code-name">#shell</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#rust"></use>
                </svg>
                <div class="name">rust</div>
                <div class="code-name">#rust</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#textedit_text_align_left"></use>
                </svg>
                <div class="name">textedit_text_align_left</div>
                <div class="code-name">#textedit_text_align_left</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#css"></use>
                </svg>
                <div class="name">css</div>
                <div class="code-name">#css</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#html"></use>
                </svg>
                <div class="name">html</div>
                <div class="code-name">#html</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#java"></use>
                </svg>
                <div class="name">java</div>
                <div class="code-name">#java</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#ts"></use>
                </svg>
                <div class="name">ts</div>
                <div class="code-name">#ts</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Golang"></use>
                </svg>
                <div class="name">Golang</div>
                <div class="code-name">#Golang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#python"></use>
                </svg>
                <div class="name">python</div>
                <div class="code-name">#python</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#JavaScript"></use>
                </svg>
                <div class="name">JavaScript</div>
                <div class="code-name">#JavaScript</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#cpp"></use>
                </svg>
                <div class="name">cpp</div>
                <div class="code-name">#cpp</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#yueliang"></use>
                </svg>
                <div class="name">月亮</div>
                <div class="code-name">#yueliang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#taiyang"></use>
                </svg>
                <div class="name">太阳</div>
                <div class="code-name">#taiyang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#windows_max"></use>
                </svg>
                <div class="name">windows_max</div>
                <div class="code-name">#windows_max</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#windows_close"></use>
                </svg>
                <div class="name">windows_close</div>
                <div class="code-name">#windows_close</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#windows_min"></use>
                </svg>
                <div class="name">windows_min</div>
                <div class="code-name">#windows_min</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#canshupeizhi"></use>
                </svg>
                <div class="name">参数配置-copy</div>
                <div class="code-name">#canshupeizhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#RectangleCopy"></use>
                </svg>
                <div class="name">平台</div>
                <div class="code-name">#RectangleCopy</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bianji1"></use>
                </svg>
                <div class="name">编辑</div>
                <div class="code-name">#bianji1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#bianji-2"></use>
                </svg>
                <div class="name">编辑-2</div>
                <div class="code-name">#bianji-2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#shanchu"></use>
                </svg>
                <div class="name">删除</div>
                <div class="code-name">#shanchu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#qingkong"></use>
                </svg>
                <div class="name">清空</div>
                <div class="code-name">#qingkong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#dian"></use>
                </svg>
                <div class="name">点</div>
                <div class="code-name">#dian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#zuixiaohua"></use>
                </svg>
                <div class="name">最小化</div>
                <div class="code-name">#zuixiaohua</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#shezhi001"></use>
                </svg>
                <div class="name">设置001</div>
                <div class="code-name">#shezhi001</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#xitongshezhi"></use>
                </svg>
                <div class="name">系统设置</div>
                <div class="code-name">#xitongshezhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#zaixianzixun_mian"></use>
                </svg>
                <div class="name">在线咨询_面</div>
                <div class="code-name">#zaixianzixun_mian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#caidan"></use>
                </svg>
                <div class="name">菜单</div>
                <div class="code-name">#caidan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#tools"></use>
                </svg>
                <div class="name">tools</div>
                <div class="code-name">#tools</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#zhishi"></use>
                </svg>
                <div class="name">知识库</div>
                <div class="code-name">#zhishi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#zuidahua"></use>
                </svg>
                <div class="name">最大化</div>
                <div class="code-name">#zuidahua</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#arrow-double-left"></use>
                </svg>
                <div class="name">arrow-double-left</div>
                <div class="code-name">#arrow-double-left</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#arrow-double-right"></use>
                </svg>
                <div class="name">arrow-double-right</div>
                <div class="code-name">#arrow-double-right</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#code"></use>
                </svg>
                <div class="name">code</div>
                <div class="code-name">#code</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#ChatGPT"></use>
                </svg>
                <div class="name">ChatGPT</div>
                <div class="code-name">#ChatGPT</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#shezhi-2"></use>
                </svg>
                <div class="name">设置-2</div>
                <div class="code-name">#shezhi-2</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
