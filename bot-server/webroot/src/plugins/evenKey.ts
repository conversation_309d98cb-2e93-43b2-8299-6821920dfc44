// 发送消息触发消息面板滚动条
export const ScrollToBottom = "ScrollToBottom"

// 设置滚动条位置的事件（接收0-1之间的比例值）
export const SetScrollPosition = "SetScrollPosition"

// 聊天消息点击引用触发的事件
// 事件将在编辑器组件中进行处理
export const MessageRef = "MessageRef"

export const MessageAt = "MessageAt"


export const MessageTest = "MessageTest"


export const LoginOut = "LoginOut"


// 用户切换组织 角色 触发 权限更新事件
export const UpdateAuthEvent = "UpdateAuthEvent"
export const UpdateAuthWindowEvent = "UpdateAuthWindowEvent"


export const UpdateTool = "UpdateTool"


// UserLogout 用户触发登出事件 用于处理用户缓存的相关数据处理
export const UserLogout = "UserLogout"

// 触发消息发送事件
export const TriggerSendMessage = "TriggerSendMessage"

// AI绘画流处理事件
export const AiPaintingStream = "AiPaintingStream"
