import axios, {AxiosInstance, AxiosResponse} from 'axios';
import {VITE_APP_SERVER} from "@/env";
import {notification} from "ant-design-vue";

const Axios: AxiosInstance = axios.create({
    baseURL: VITE_APP_SERVER,
    timeout: 5000,
})


Axios.interceptors.request.use(function (request) {
    return request
})

Axios.interceptors.response.use(function (response: AxiosResponse<any>) {
    return response
}, function (error) {
    return error.response
})

export default Axios


function errHandler(response) {
    switch (response.status) {
        case 400:
            break
        case 500:
            ResponseNotify(response)
            break
        default:
    }
}

function ResponseNotify(response) {
    let data = response.data
    notification["error"]({
        message: 'System Error',
        description: data.msg,
    });
}

