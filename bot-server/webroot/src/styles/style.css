@import '@/components/ai-bot-style/style.css';
@import '../assets/fonts/font.css';
/* 重置全局样式 */
html, body {
    margin: 0;
    padding: 0;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
}

#app {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* 可拖拽区域 */
.app-drag {
    -webkit-app-region: drag;
    --wails-draggable:drag;
  }
  
  /* 不可拖拽区域（可交互元素） */
  .app-no-drag {
    -webkit-app-region: no-drag;
    --wails-draggable:no-drag;
  }


/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: transparent;
    border-radius: 3px;
}

.body--dark ::-webkit-scrollbar-thumb {
    background: #2d2d2d;
}

/* 修复 tailwindcss/tailwind.css 对 ant 中样式的影响 */
.ant-btn > span {
    display: inline-flex;
}
