:root {
    --scroller-width: 5px;
    --scroller-height: 8px;
    --scroller-border-radius: 0;
    --scroller-box-shadow: inset 0 0 10px rgb(221, 221, 223);
    --scroller-background: inset 0 0 10px rgb(221, 221, 223);

    --scroller-track-box-shadow: inset 0 0 5px rgb(255, 255, 255);
    --scroller-track-background: rgb(255, 255, 255);
    --scroller-track-border-radius: 0;

}

/* .ck.ck-reset.ck-list.ck-mentions, .code-lang, .vue-recycle-scroller.ready.direction-vertical.scroller, .plugin-view */

/*----------------.plugin-view-------------------------*/
/*.plugin-view ::-webkit-scrollbar {
    width: var(--scroller-width);
    height: var(--scroller-height);
}

.plugin-view ::-webkit-scrollbar-thumb {
    border-radius: var(--scroller-border-radius);
    box-shadow: var(--scroller-box-shadow);
    background: var(--scroller-background);
}

.plugin-view ::-webkit-scrollbar-track {
    box-shadow: var(--scroller-track-box-shadow);
    border-radius: var(--scroller-track-border-radius);
    background: var(--scroller-track-background);
}*/


/*--------.ck.ck-reset.ck-list.ck-mentions-----------*/
.ck.ck-reset.ck-list.ck-mentions ::-webkit-scrollbar {
    width: var(--scroller-width);
    height: var(--scroller-height);
}

.ck.ck-reset.ck-list.ck-mentions ::-webkit-scrollbar-thumb {
    border-radius: var(--scroller-border-radius);
    box-shadow: var(--scroller-box-shadow);
    background: var(--scroller-background);
}

.ck.ck-reset.ck-list.ck-mentions ::-webkit-scrollbar-track {
    box-shadow: var(--scroller-track-box-shadow);
    border-radius: var(--scroller-track-border-radius);
    background: var(--scroller-track-background);
}

/*-------------.code-lang--------------------------*/
.code-lang::-webkit-scrollbar {
    width: var(--scroller-width);
    height: var(--scroller-height);
}

.code-lang::-webkit-scrollbar-thumb {
    border-radius: var(--scroller-border-radius);
    box-shadow: var(--scroller-box-shadow);
    background: var(--scroller-background);
}

.code-lang::-webkit-scrollbar-track {
    box-shadow: var(--scroller-track-box-shadow);
    border-radius: var(--scroller-track-border-radius);
    background: var(--scroller-track-background);
}


/*------.vue-recycle-scroller.ready.direction-vertical.scroller--------*/
/*
.vue-recycle-scroller.ready.direction-vertical.scroller::-webkit-scrollbar {
    width: var(--scroller-width);
    height: var(--scroller-height);
}

.vue-recycle-scroller.ready.direction-vertical.scroller::-webkit-scrollbar-thumb {
    border-radius: var(--scroller-border-radius);
    box-shadow: var(--scroller-box-shadow);
    background: var(--scroller-background);
}

.vue-recycle-scroller.ready.direction-vertical.scroller ::-webkit-scrollbar-track {
    box-shadow: var(--scroller-track-box-shadow);
    border-radius: var(--scroller-track-border-radius);
    background: var(--scroller-track-background);
}
*/

/*
.conversation::-webkit-scrollbar {
    width: var(--scroller-width);
    height: var(--scroller-height);
}*/

/* 编辑器滚动条 */
/*#editor::-webkit-scrollbar {
    width: var(--scroller-width);
    height: var(--scroller-height);
}

#editor::-webkit-scrollbar-thumb {
    border-radius: var(--scroller-border-radius);
    box-shadow: var(--scroller-box-shadow);
    background: var(--scroller-background);
}

#editor::-webkit-scrollbar-track {
    box-shadow: var(--scroller-track-box-shadow);
    border-radius: var(--scroller-track-border-radius);
    background: var(--scroller-track-background);
}*/

