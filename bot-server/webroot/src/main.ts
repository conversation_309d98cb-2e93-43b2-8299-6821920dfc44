import {createApp} from 'vue'
import App from './App.vue'
import './styles/style.css'
import './styles/scroller.css'
// 导入自定义图标
import './assets/icons/iconfont.css'
import './assets/icons/iconfont.js'
// 动画库
import 'animate.css';
import 'hover.css'

// 导入Markdown样式
import '@/components/ai-bot-message/markdown/md-style/index.css'

// 代码高亮 - 基础样式和自定义主题
// import 'prismjs/themes/prism.min.css'
import router from "./route";
import pina from "./pinia";
import {init} from "@/init";

import VueVirtualScroller from 'vue-virtual-scroller'
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'

import Antd from 'ant-design-vue'
import 'ant-design-vue/dist/reset.css'
import 'tailwindcss/tailwind.css'
import {createFromIconfontCN} from "@ant-design/icons-vue";

import '@/components/ai-bot-message/scene/scene'
import customDirectives from './components/custom-directives'

const app = createApp(App)

init(app)
app.use(VueVirtualScroller)
app.use(Antd)
app.use(pina)
app.use(router)

// 注册自定义指令
app.use(customDirectives)

const IconFont = createFromIconfontCN({
    scriptUrl: 'src/assets/icons/iconfont.js',
});

app.component('IconFont', IconFont)

app.mount('#app')
