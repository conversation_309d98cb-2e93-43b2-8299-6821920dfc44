<template>
  <AConfigProvider :getPopupContainer="getPopupContainer" :locale="locale" :theme="antTheme" component-size="small">
    <router-view v-slot="{ Component }">
      <keep-alive>
        <component :is="Component" />
      </keep-alive>
    </router-view>
  </AConfigProvider>
</template>

<script lang="ts" setup>
import { computed, onMounted, onUnmounted, ref } from "vue";
import { useGptStore } from "@/components/store/gpt";
import { desktop_open_dev } from "@/components/desktop/desktop";
import { useAiBot } from "@/components/store/bot";
import { message as antMessage } from "ant-design-vue/es/components";
import zhCN from 'ant-design-vue/es/locale/zh_CN';
import 'dayjs/locale/zh-cn';
import { theme } from 'ant-design-vue';
import { getLimitedFreeAIModels } from "@/components/common/util";
import { useRouter } from "vue-router";

const { darkAlgorithm, defaultAlgorithm, compactAlgorithm } = theme;
const gpt = useGptStore()
const bot = useAiBot()
const locale = zhCN
const prismThemeLink = ref<HTMLLinkElement | null>(null)

const { useToken } = theme
const { token } = useToken()

// 动态加载Prismjs主题
const loadPrismTheme = (themeName: string) => {
  // 如果已经存在主题link，先移除
  if (prismThemeLink.value && document.head.contains(prismThemeLink.value)) {
    document.head.removeChild(prismThemeLink.value)
  }

  // 创建新的link元素
  const link = document.createElement('link')
  link.rel = 'stylesheet'
  link.href = themeName === 'dark'
    ? 'https://cdn.jsdelivr.net/npm/prismjs@1.29.0/themes/prism-dark.min.css'
    : 'https://cdn.jsdelivr.net/npm/prismjs@1.29.0/themes/prism.min.css'

  // 保存引用并添加到head
  prismThemeLink.value = link
  document.head.appendChild(link)
}

// 动态设置 ck editor 的主题适配
const setCkEditorTheme = (theme: string) => {
  // 添加 编辑器 @ / 下拉面板的 主题是配颜色
  document.documentElement.style.setProperty('--ck-color-panel-background', theme === 'dark' ? '#1f1f1f' : '#ffffff');
  document.documentElement.style.setProperty('--ck-color-panel-border', theme === 'dark' ? '#2c2c2c' : '#f0f0f0');
  document.documentElement.style.setProperty('--ck-color-list-button-hover-background', theme === 'dark' ? '#2c2c2c' : '#f0f0f0');
}

function getPopupContainer(el, dialogContext) {
  if (dialogContext) {
    return dialogContext.getDialogWrap();
  } else {
    return document.body;
  }
}

const antTheme = computed(() => {
  let algorithm = []
  loadPrismTheme(bot.theme)
  setCkEditorTheme(bot.theme)

  document.documentElement.style.setProperty('--ck-border-radius', '5px');
  switch (bot.theme) {
    case "light":
      algorithm.push(defaultAlgorithm)
      break
    case "dark":
      algorithm.push(darkAlgorithm)
      break
  }
  return {
    algorithm: algorithm,
    token: {}
  }
})

// 处理全局快捷键
const handleGlobalKeyDown = (event: KeyboardEvent) => {
  // 检查是否按下 Alt+Q (Windows) 或 Command+Q (Mac)
  if ((event.altKey || event.metaKey) && event.key.toLowerCase() === 'q') {
    event.preventDefault(); // 阻止默认行为
    bot.pluginConfigDrawerOpen = !bot.pluginConfigDrawerOpen;
  }
};

// 处理浏览器加载事件
const handleLoad = (event: Event) => {
  // 页面完全加载后执行的逻辑
  console.log('页面已完全加载');
  // 刷新页面，对话已经被终止了，此处应该回复持久化数据的回复标记
  if (gpt.ui.replying) {
    gpt.ui.replying = false
  }
};

let router = useRouter();
// 组件挂载时添加事件监听
onMounted(() => {
  // 初始化Prismjs主题
  loadPrismTheme(bot.theme)

  getLimitedFreeAIModels().then(models => {
    console.log(models)
  }).catch(err => {
    console.error(err)
  })
  gpt.GetConversationList()

  window.onkeydown = function (event) {
    if (event.key == "F12") {
      desktop_open_dev()
    }
    if (event.code == "KeyI" && event.metaKey && event.altKey) {
      desktop_open_dev()
    }
  }

  window.addEventListener('keydown', handleGlobalKeyDown);
  window.addEventListener('load', handleLoad);
});

// 组件卸载时移除事件监听
onUnmounted(() => {
  window.removeEventListener('keydown', handleGlobalKeyDown);
  window.removeEventListener('load', handleLoad);
});

// 添加代码复制功能
(window as any).copyCode = function (button) {
  try {
    // 从按钮向上查找代码块容器
    const toolbar = button.closest('.code-toolbar');
    if (!toolbar) return;

    // 获取代码块
    const codeBlock = toolbar.parentElement;
    if (!codeBlock) return;

    // 获取代码内容
    const codeLines = codeBlock.querySelectorAll('.code-line');
    if (!codeLines.length) return;

    // 获取每行代码并保持格式
    const codeContent = Array.from(codeLines)
      .map(line => {
        // 获取实际的代码内容
        const content = (line as HTMLElement).textContent || '';
        // 获取前导空格数量
        const leadingSpaces = content.match(/^\s*/)[0].length;
        // 保持原有的缩进
        return ' '.repeat(leadingSpaces) + content.trim();
      })
      .join('\n');

    // 创建临时文本区域并复制
    const textarea = document.createElement('textarea');
    textarea.value = codeContent;
    textarea.style.position = 'absolute';
    textarea.style.left = '-9999px';
    document.body.appendChild(textarea);

    try {
      textarea.select();
      document.execCommand('copy');

      // 添加复制成功的视觉反馈
      button.classList.add('copied');
      antMessage.success('复制成功')
      setTimeout(() => {
        button.classList.remove('copied');
      }, 2000);
    } finally {
      document.body.removeChild(textarea);
    }
  } catch (err) {
    console.error('复制失败:', err);
  }
}
</script>

<style lang="css">

</style>
