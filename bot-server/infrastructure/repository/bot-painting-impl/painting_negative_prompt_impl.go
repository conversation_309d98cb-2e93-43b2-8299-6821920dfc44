package bot_painting_impl

import (
	"bot/application/dto"
	"bot/domain/entity"
	"bot/infrastructure/po"
	ai_bot_platform "bot/infrastructure/repository/ai-bot-platform"
	"context"
)

type PaintingNegativePromptImpl struct {
	*ai_bot_platform.BaseRepository
}

func NewPaintingNegativePromptImpl(repository *ai_bot_platform.BaseRepository) *PaintingNegativePromptImpl {
	return &PaintingNegativePromptImpl{
		BaseRepository: repository,
	}
}

// QueryPaintingNegativePrompts 查询所有负面提示词
func (painting *PaintingNegativePromptImpl) QueryPaintingNegativePrompts(ctx context.Context) ([]*po.PaintingNegativePromptPO, error) {
	var list []*po.PaintingNegativePromptPO
	if err := painting.ChatDB.Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

// QueryPaintingNegativePromptsByCondition 根据条件查询负面提示词
func (painting *PaintingNegativePromptImpl) QueryPaintingNegativePromptsByCondition(ctx context.Context, paintingNegativePromptEntity *entity.PaintingNegativePromptEntity) ([]*po.PaintingNegativePromptPO, error) {
	var list []*po.PaintingNegativePromptPO
	query := painting.ChatDB.Model(&po.PaintingNegativePromptPO{})

	if paintingNegativePromptEntity.ID != "" {
		query = query.Where("id = ?", paintingNegativePromptEntity.ID)
	}

	if err := query.Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

// CreatePaintingNegativePrompt 创建负面提示词
func (painting *PaintingNegativePromptImpl) CreatePaintingNegativePrompt(ctx context.Context, paintingNegativePromptEntity *entity.PaintingNegativePromptEntity) (*po.PaintingNegativePromptPO, error) {
	paintingNegativePromptPO := dto.NewPaintingNegativePromptPO(paintingNegativePromptEntity)
	paintingNegativePromptPO.ID = painting.BaseRepository.Node.String()
	if err := painting.ChatDB.Create(paintingNegativePromptPO).Error; err != nil {
		return nil, err
	}
	return paintingNegativePromptPO, nil
}

// UpdatePaintingNegativePrompt 更新负面提示词
func (painting *PaintingNegativePromptImpl) UpdatePaintingNegativePrompt(ctx context.Context, paintingNegativePromptEntity *entity.PaintingNegativePromptEntity) (*po.PaintingNegativePromptPO, error) {
	paintingNegativePromptPO := dto.NewPaintingNegativePromptPO(paintingNegativePromptEntity)
	if err := painting.ChatDB.Save(paintingNegativePromptPO).Error; err != nil {
		return nil, err
	}
	return paintingNegativePromptPO, nil
}

// DeletePaintingNegativePrompt 删除负面提示词
func (painting *PaintingNegativePromptImpl) DeletePaintingNegativePrompt(ctx context.Context, paintingNegativePromptEntity *entity.PaintingNegativePromptEntity) error {
	paintingNegativePromptPO := dto.NewPaintingNegativePromptPO(paintingNegativePromptEntity)
	if err := painting.ChatDB.Delete(paintingNegativePromptPO).Error; err != nil {
		return err
	}
	return nil
}

// GetPaintingNegativePrompt 获取单个负面提示词
func (painting *PaintingNegativePromptImpl) GetPaintingNegativePrompt(ctx context.Context, paintingNegativePromptEntity *entity.PaintingNegativePromptEntity) (*po.PaintingNegativePromptPO, error) {
	paintingNegativePromptPO := dto.NewPaintingNegativePromptPO(paintingNegativePromptEntity)
	if err := painting.ChatDB.Where("id = ?", paintingNegativePromptEntity.ID).First(paintingNegativePromptPO).Error; err != nil {
		return nil, err
	}
	return paintingNegativePromptPO, nil
} 