package bot_painting_impl

import (
	"bot/application/dto"
	"bot/domain/entity"
	"bot/infrastructure/po"
	ai_bot_platform "bot/infrastructure/repository/ai-bot-platform"
	"context"
)

type PaintingRecordImpl struct {
	*ai_bot_platform.BaseRepository
}

func NewPaintingRecordImpl(repository *ai_bot_platform.BaseRepository) *PaintingRecordImpl {
	return &PaintingRecordImpl{
		BaseRepository: repository,
	}
}

// QueryPaintingRecords 查询所有绘画记录
func (painting *PaintingRecordImpl) QueryPaintingRecords(ctx context.Context) ([]*po.PaintingPO, error) {
	var list []*po.PaintingPO
	if err := painting.ChatDB.Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

// QueryPaintingRecordsByCondition 根据条件查询绘画记录
func (painting *PaintingRecordImpl) QueryPaintingRecordsByCondition(ctx context.Context, paintingRecordEntity *entity.PaintingRecordEntity) ([]*po.PaintingPO, error) {
	var list []*po.PaintingPO
	query := painting.ChatDB.Model(&po.PaintingPO{})

	if paintingRecordEntity.ID != "" {
		query = query.Where("id = ?", paintingRecordEntity.ID)
	}

	if paintingRecordEntity.Title != "" {
		query = query.Where("title LIKE ?", "%"+paintingRecordEntity.Title+"%")
	}

	if paintingRecordEntity.Status != "" {
		query = query.Where("status = ?", paintingRecordEntity.Status)
	}

	if err := query.Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

// CreatePaintingRecord 创建绘画记录
func (painting *PaintingRecordImpl) CreatePaintingRecord(ctx context.Context, paintingRecordEntity *entity.PaintingRecordEntity) (*po.PaintingPO, error) {
	paintingRecordPO := dto.NewPaintingRecordPO(paintingRecordEntity)
	paintingRecordPO.ID = painting.BaseRepository.Node.String()
	if err := painting.ChatDB.Create(paintingRecordPO).Error; err != nil {
		return nil, err
	}
	return paintingRecordPO, nil
}

// UpdatePaintingRecord 更新绘画记录
func (painting *PaintingRecordImpl) UpdatePaintingRecord(ctx context.Context, paintingRecordEntity *entity.PaintingRecordEntity) (*po.PaintingPO, error) {
	paintingRecordPO := dto.NewPaintingRecordPO(paintingRecordEntity)
	if err := painting.ChatDB.Save(paintingRecordPO).Error; err != nil {
		return nil, err
	}
	return paintingRecordPO, nil
}

// DeletePaintingRecord 删除绘画记录
func (painting *PaintingRecordImpl) DeletePaintingRecord(ctx context.Context, paintingRecordEntity *entity.PaintingRecordEntity) error {
	paintingRecordPO := dto.NewPaintingRecordPO(paintingRecordEntity)
	if err := painting.ChatDB.Delete(paintingRecordPO).Error; err != nil {
		return err
	}
	return nil
}

// GetPaintingRecord 获取单个绘画记录
func (painting *PaintingRecordImpl) GetPaintingRecord(ctx context.Context, paintingRecordEntity *entity.PaintingRecordEntity) (*po.PaintingPO, error) {
	paintingRecordPO := dto.NewPaintingRecordPO(paintingRecordEntity)
	if err := painting.ChatDB.Where("id = ?", paintingRecordEntity.ID).First(paintingRecordPO).Error; err != nil {
		return nil, err
	}
	return paintingRecordPO, nil
}
