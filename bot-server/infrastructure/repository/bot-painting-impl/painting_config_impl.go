package bot_painting_impl

import (
	"bot/application/dto"
	"bot/domain/entity"
	"bot/infrastructure/po"
	ai_bot_platform "bot/infrastructure/repository/ai-bot-platform"
	"context"
)

type PaintingConfigImpl struct {
	*ai_bot_platform.BaseRepository
}

func NewPaintingConfigImpl(repository *ai_bot_platform.BaseRepository) *PaintingConfigImpl {
	return &PaintingConfigImpl{
		BaseRepository: repository,
	}
}

// QueryPaintingConfigs 查询所有绘画配置
func (painting *PaintingConfigImpl) QueryPaintingConfigs(ctx context.Context) ([]*po.PaintingConfigPO, error) {
	var list []*po.PaintingConfigPO
	if err := painting.ChatDB.Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

// QueryPaintingConfigsByCondition 根据条件查询绘画配置
func (painting *PaintingConfigImpl) QueryPaintingConfigsByCondition(ctx context.Context, paintingConfigEntity *entity.PaintingConfigEntity) ([]*po.PaintingConfigPO, error) {
	var list []*po.PaintingConfigPO
	query := painting.ChatDB.Model(&po.PaintingConfigPO{})

	if paintingConfigEntity.ID != "" {
		query = query.Where("id = ?", paintingConfigEntity.ID)
	}

	if err := query.Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

// CreatePaintingConfig 创建绘画配置
func (painting *PaintingConfigImpl) CreatePaintingConfig(ctx context.Context, paintingConfigEntity *entity.PaintingConfigEntity) (*po.PaintingConfigPO, error) {
	paintingConfigPO := dto.NewPaintingConfigPO(paintingConfigEntity)
	paintingConfigPO.ID = painting.BaseRepository.Node.String()
	if err := painting.ChatDB.Create(paintingConfigPO).Error; err != nil {
		return nil, err
	}
	return paintingConfigPO, nil
}

// UpdatePaintingConfig 更新绘画配置
func (painting *PaintingConfigImpl) UpdatePaintingConfig(ctx context.Context, paintingConfigEntity *entity.PaintingConfigEntity) (*po.PaintingConfigPO, error) {
	paintingConfigPO := dto.NewPaintingConfigPO(paintingConfigEntity)
	if err := painting.ChatDB.Save(paintingConfigPO).Error; err != nil {
		return nil, err
	}
	return paintingConfigPO, nil
}

// DeletePaintingConfig 删除绘画配置
func (painting *PaintingConfigImpl) DeletePaintingConfig(ctx context.Context, paintingConfigEntity *entity.PaintingConfigEntity) error {
	paintingConfigPO := dto.NewPaintingConfigPO(paintingConfigEntity)
	if err := painting.ChatDB.Delete(paintingConfigPO).Error; err != nil {
		return err
	}
	return nil
}

// GetPaintingConfig 获取单个绘画配置
func (painting *PaintingConfigImpl) GetPaintingConfig(ctx context.Context, paintingConfigEntity *entity.PaintingConfigEntity) (*po.PaintingConfigPO, error) {
	paintingConfigPO := dto.NewPaintingConfigPO(paintingConfigEntity)
	if err := painting.ChatDB.Where("id = ?", paintingConfigEntity.ID).First(paintingConfigPO).Error; err != nil {
		return nil, err
	}
	return paintingConfigPO, nil
}
