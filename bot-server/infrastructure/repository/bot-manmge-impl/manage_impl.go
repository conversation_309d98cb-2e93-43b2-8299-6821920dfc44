package bot_manmge_impl

import (
	"bot/domain/entity"
	"bot/infrastructure/po"
	ai_bot_platform "bot/infrastructure/repository/ai-bot-platform"
	botfileimpl "bot/infrastructure/repository/bot-file-impl"
	"context"

	"go.opentelemetry.io/otel"
)

type ManageImpl struct {
	*ai_bot_platform.BaseRepository
	*botfileimpl.FileImpl
}

func NewManageImpl(repository *ai_bot_platform.BaseRepository) *ManageImpl {
	return &ManageImpl{
		BaseRepository: repository,
		FileImpl:       botfileimpl.NewFileImpl(repository).(*botfileimpl.FileImpl),
	}
}

func (manage *ManageImpl) CreateConversation(ctx context.Context, conversationEntity *entity.ConversationEntity) (string, error) {
	conversationPO := po.NewConversationPO(conversationEntity)
	conversationPO.ID = manage.BaseRepository.Node.String()
	if err := manage.ChatDB.Create(conversationPO).Error; err != nil {
		return "", err
	}
	return conversationPO.ID, nil
}

func (manage *ManageImpl) DeleteConversation(ctx context.Context, conversationEntities *entity.ConversationEntity) error {
	del := po.NewConversationPO(conversationEntities)
	if err := manage.ChatDB.Delete(del).Error; err != nil {
		return err
	}
	return nil
}

func (manage *ManageImpl) QueryConversations(ctx context.Context) ([]po.ConversationPO, error) {
	// 开启 OpenTelemetry Span
	tracer := otel.Tracer("ManageImpl.QueryConversations")
	_, span := tracer.Start(ctx, "ManageImpl.QueryConversations")
	defer span.End()

	var list []po.ConversationPO
	if err := manage.ChatDB.Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (manage *ManageImpl) UpdateConversation(ctx context.Context, conversationEntity *entity.ConversationEntity) (*po.ConversationPO, error) {
	conversationPO := po.NewConversationPO(conversationEntity)
	if err := manage.ChatDB.Save(conversationPO).Error; err != nil {
		return nil, err
	}
	return conversationPO, nil
}

func (manage *ManageImpl) QueryMessages(ctx context.Context, conversationEntity *entity.ConversationEntity) ([]*po.MessagePO, error) {
	var list []*po.MessagePO
	if err := manage.ChatDB.Where("conversation_id = ?", conversationEntity.ID).Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (manage *ManageImpl) QueryMessage(ctx context.Context, messageEntity *entity.MessageEntity) (*po.MessagePO, error) {
	messagePO := po.NewMessagePO(messageEntity)
	if err := manage.ChatDB.Find(&messagePO).Error; err != nil {
		return nil, err
	}
	return messagePO, nil
}

func (manage *ManageImpl) QueryConversation(ctx context.Context, messageEntity *entity.ConversationEntity) (*po.ConversationPO, error) {
	conversationPO := po.NewConversationPO(messageEntity)
	if err := manage.ChatDB.Find(&conversationPO).Error; err != nil {
		return nil, err
	}
	return conversationPO, nil
}

func (manage *ManageImpl) DeleteMessages(ctx context.Context, conversationEntity *entity.ConversationEntity) error {
	if err := manage.ChatDB.
		Table("bot_message").
		Where("conversation_id = ?", conversationEntity.ID).Delete(&po.MessagePO{}).
		Error; err != nil {
		return err
	}
	return nil
}

func (manage *ManageImpl) DeleteMessage(ctx context.Context, deleteMsg *entity.MessageEntity) error {
	if err := manage.ChatDB.
		Table("bot_message").
		Where("ID= ? ", deleteMsg.ID).
		Delete(&po.MessagePO{}).
		Error; err != nil {
		return err
	}
	return nil
}

func (manage *ManageImpl) CreatePlatform(ctx context.Context, platformEntity *entity.PlatformEntity) error {
	var err error
	platformPO := po.NewPlatformPO(platformEntity)
	platformPO.ID = manage.Node.String()
	if err = manage.ChatDB.Create(platformPO).Error; err != nil {
		return err
	}
	return nil
}

func (manage *ManageImpl) DeletePlatform(ctx context.Context, platformEntitie *entity.PlatformEntity) error {
	var err error
	pO := po.NewPlatformPO(platformEntitie)
	if err = manage.ChatDB.Delete(&pO).Error; err != nil {
		return err
	}
	return nil
}

func (manage *ManageImpl) QueryPlatforms(ctx context.Context) ([]*po.PlatformPO, error) {
	var err error
	var list []*po.PlatformPO
	if err = manage.ChatDB.Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (manage *ManageImpl) UpdatePlatform(ctx context.Context, platformEntity *entity.PlatformEntity) error {
	var err error
	platformPO := po.NewPlatformPO(platformEntity)
	if err = manage.ChatDB.Model(&platformPO).Updates(platformPO).Error; err != nil {
		return err
	}
	return nil
}

func (manage *ManageImpl) QueryPlugins(ctx context.Context) ([]*po.PluginPO, error) {
	var err error
	var list []*po.PluginPO
	if err = manage.ChatDB.Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (manage *ManageImpl) UpdatePlugin(ctx context.Context, pluginEntity *entity.PluginEntity) error {
	pluginPO := po.NewPluginPO(pluginEntity)
	if err := manage.ChatDB.Save(pluginPO).Error; err != nil {
		return err
	}
	return nil
}

func (manage *ManageImpl) UpdateAllPluginPlatform(ctx context.Context, platform *entity.PlatformEntity) error {
	tx := manage.ChatDB.Model(&po.PluginPO{}).Where("id !=?", "").Update("platform_id", platform.ID)
	if err := tx.Error; err != nil {
		return err
	}
	return nil
}
