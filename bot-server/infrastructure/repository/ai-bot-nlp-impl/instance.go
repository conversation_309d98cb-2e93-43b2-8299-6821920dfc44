package ai_bot_nlp_impl

import (
	"bot/domain/repository"
	"bot/infrastructure/repository/ai-bot-nlp-impl/system"
	"bot/infrastructure/repository/ai-bot-nlp-impl/writer"
)

var List []repository.INlpIntent

func init() {
	// 加载所以已实现的 Intent 实例
	baseNluIntent := system.NewBaseNluIntent()
	List = append(List, baseNluIntent)
	writerNlpImpl := writer.NewBotWriterNlpImpl(baseNluIntent)
	List = append(List, writerNlpImpl)
}
