package system

import (
	"bot/domain/service/bot/nlp"
	"context"
	_ "embed"
	"sort"
	"strconv"
)

type BaseNluIntent struct {
	nlp.BaseConfig
}

func NewBaseNluIntent() *BaseNluIntent {
	nlpIntent := &BaseNluIntent{}
	nlpIntent.IntentTemplate = templates
	nlpIntent.IntentMatch = intentMatchTemplates
	nlpIntent.Slots = slots
	return nlpIntent
}

func (n *BaseNluIntent) Intent(ctx context.Context, text string) *nlp.Intent {
	// 获取词槽匹配结果
	slotResults := n.MatchSlots(text)

	// 匹配模板
	intentMap := n.MatchIntentTemplates(slotResults)

	// 计算匹配度并使用模板分值作为阈值筛选，同时进行正则匹配
	bestTemplate := n.CalculateMatchScore(text, slotResults, intentMap)

	// 如果有匹配的模板，返回匹配的意图
	if bestTemplate != nil {
		return &nlp.Intent{
			Domain: nlp.DomainSystem,
			Intent: bestTemplate.Name,
			Result: bestTemplate,
		}
	}

	// 默认返回
	return &nlp.Intent{
		Domain: nlp.DomainSystem,
		Intent: nlp.IntentChat,
		Result: &nlp.IntentTemplate{},
	}
}

// MatchIntentTemplates 匹配意图模板
func (n *BaseNluIntent) MatchIntentTemplates(slotResults []nlp.SlotMatchResult) map[string][]*nlp.IntentTemplate {
	// 使用map存储不同意图的匹配结果
	intentMap := make(map[string][]*nlp.IntentTemplate)

	// 创建词槽类型到匹配结果的映射
	slotTypeResults := make(map[string][]nlp.SlotMatchResult)
	for _, result := range slotResults {
		slotTypeResults[result.SlotType] = append(slotTypeResults[result.SlotType], result)
	}

	// 遍历所有模板进行匹配
	for _, temp := range n.IntentTemplate {
		// 创建模板副本
		templateCopy := &nlp.IntentTemplate{
			Name:  temp.Name,
			Score: temp.Score,
			Slots: make(map[string][]*nlp.Slot),
		}

		// 深度复制词槽map
		for slotName, slots := range temp.Slots {
			templateCopy.Slots[slotName] = make([]*nlp.Slot, len(slots))
			for i, slot := range slots {
				// 创建新的词槽
				newSlot := &nlp.Slot{
					Name:   slot.Name,
					Index:  slot.Index,
					Must:   slot.Must,
					Value:  slot.Value,
					Hit:    slot.Hit,
					Score:  slot.Score,
					Result: slot.Result,
				}
				templateCopy.Slots[slotName][i] = newSlot
			}
		}

		// 初始化所有槽位的命中标记为 false
		for _, slots := range templateCopy.Slots {
			for _, slot := range slots {
				slot.Hit = false
			}
		}

		// 收集所有词槽并按规则排序
		type slotInfo struct {
			name     string
			index    int
			slotIdx  int
			slot     *nlp.Slot
			hasOrder bool
		}
		var allSlots []slotInfo

		// 收集所有词槽信息
		for slotName, slots := range templateCopy.Slots {
			for i := range slots {
				allSlots = append(allSlots, slotInfo{
					name:     slotName,
					index:    slots[i].Index,
					slotIdx:  i,
					slot:     slots[i],
					hasOrder: slots[i].Index > 0,
				})
			}
		}

		// 按照相同的规则排序词槽
		sort.Slice(allSlots, func(i, j int) bool {
			// 如果两个词槽都带顺序，按 index 升序排序
			if allSlots[i].hasOrder && allSlots[j].hasOrder {
				return allSlots[i].index < allSlots[j].index
			}
			// 如果只有一个带顺序，带顺序的排在前面
			if allSlots[i].hasOrder {
				return true
			}
			if allSlots[j].hasOrder {
				return false
			}
			// 都不带顺序，保持原有顺序
			return i < j
		})

		// 按排序后的顺序进行匹配
		isMatch := true
		usedPositions := make(map[int]bool) // 记录已使用的位置

		// 第一轮：匹配带顺序且必需的词槽
		for _, slotInfo := range allSlots {
			if slotInfo.hasOrder && slotInfo.slot.Must {
				if results, exists := slotTypeResults[slotInfo.name]; exists {
					matched := false
					// 查找位置匹配的结果
					for _, result := range results {
						expectedPos := slotInfo.index - 1 // 转换为0基索引
						if !usedPositions[result.StartPos] && isPositionMatch(result, expectedPos, slotResults) {
							// 更新模板副本中的槽位信息
							slotInfo.slot.Value = result.StandardText
							slotInfo.slot.Hit = true
							slotInfo.slot.Result = &result
							slotInfo.slot.Score = 1.0
							// 添加匹配位置信息
							if slotInfo.slot.Result != nil {
								slotInfo.slot.Result.StartPos = result.StartPos
								slotInfo.slot.Result.EndPos = result.EndPos
								slotInfo.slot.Result.MatchedText = result.MatchedText
								slotInfo.slot.Result.Index = slotInfo.slot.Index // 设置词槽的位置索引
							}
							usedPositions[result.StartPos] = true
							matched = true
							break
						}
					}
					if !matched {
						isMatch = false
						break
					}
				} else {
					isMatch = false
					break
				}
			}
		}

		if !isMatch {
			continue // 如果必需的带顺序词槽匹配失败，跳过此模板
		}

		// 第二轮：匹配不带顺序或非必需的词槽
		for _, slotInfo := range allSlots {
			if !slotInfo.slot.Hit && // 未匹配过
				(!slotInfo.slot.Must || !slotInfo.hasOrder) { // 非必需或不带顺序
				if results, exists := slotTypeResults[slotInfo.name]; exists {
					// 尝试匹配未使用的结果
					for _, result := range results {
						if !usedPositions[result.StartPos] {
							// 更新模板副本中的槽位信息
							slotInfo.slot.Value = result.StandardText
							slotInfo.slot.Hit = true
							slotInfo.slot.Result = &result
							slotInfo.slot.Score = 1.0
							// 添加匹配位置信息
							if slotInfo.slot.Result != nil {
								slotInfo.slot.Result.StartPos = result.StartPos
								slotInfo.slot.Result.EndPos = result.EndPos
								slotInfo.slot.Result.MatchedText = result.MatchedText
							}
							usedPositions[result.StartPos] = true
							break
						}
					}
				}
			}
		}

		// 最终检查：确保所有必需的词槽都匹配成功
		isMatch = true
		for _, slots := range templateCopy.Slots {
			for _, slot := range slots {
				if slot.Must && !slot.Hit {
					isMatch = false
					break
				}
			}
			if !isMatch {
				break
			}
		}

		// 如果所有必需的词槽都匹配成功，添加副本到对应意图的结果集中
		if isMatch {
			intentMap[templateCopy.Name] = append(intentMap[templateCopy.Name], templateCopy)
		}
	}

	// 对每个意图的模板列表按分值降序排序
	for intentName, templates := range intentMap {
		sort.Slice(templates, func(i, j int) bool {
			return templates[i].Score > templates[j].Score
		})
		intentMap[intentName] = templates
	}

	return intentMap
}

// isPositionMatch 检查结果是否在预期位置
func isPositionMatch(result nlp.SlotMatchResult, expectedPos int, allResults []nlp.SlotMatchResult) bool {
	// 计算当前结果是第几个有序词槽
	position := 0
	for _, r := range allResults {
		// 只统计在当前结果之前的有序词槽
		if r.StartPos < result.StartPos && r.Index > 0 {
			position++
		}
	}
	// 如果当前词槽不是有序词槽（Index <= 0），则不进行位置匹配
	if result.Index <= 0 {
		return true
	}
	return position == expectedPos
}

type TemplateItem struct {
	Score    float64 `toml:"score"`    // 分值
	Template string  `toml:"template"` // 模板字符串，包含词槽定义
}

// CalculateMatchScore 计算模板匹配度并返回得分最高的模板
func (n *BaseNluIntent) CalculateMatchScore(query string, slotResults []nlp.SlotMatchResult, intentMap map[string][]*nlp.IntentTemplate) *nlp.IntentTemplate {
	var bestTemplate *nlp.IntentTemplate
	var highestScore float64

	// 遍历所有意图的模板
	for _, templates := range intentMap {
		for _, template := range templates {
			// 保存原始分值作为阈值
			threshold := template.Score

			// 计算命中的词槽数量和总词槽数量
			hitCount := 0
			totalSlots := 0
			slotValues := make(map[string]string)

			// 遍历所有词槽
			for _, slots := range template.Slots {
				totalSlots += len(slots)
				for _, slot := range slots {
					if slot.Hit {
						hitCount++
						slotValues[slot.Name] = slot.Value
					}
				}
			}

			// 计算匹配度
			var matchScore float64
			if totalSlots > 0 && len(slotResults) > 0 {
				// 计算词槽命中率
				slotMatchRate := float64(hitCount) / float64(totalSlots)
				// 计算分词覆盖率
				slotCoverageRate := float64(hitCount) / float64(len(slotResults))

				// 综合匹配度
				matchScore = slotMatchRate*0.85 + slotCoverageRate*0.15

				// 更新模板的实际匹配分数
				template.Score = matchScore

				// 如果匹配度超过阈值且高于当前最高分，更新最佳模板
				if matchScore >= threshold && matchScore > highestScore {
					highestScore = matchScore
					bestTemplate = template
				}
			}
		}
	}

	// 如果找到最佳模板，进行正则匹配验证
	if bestTemplate != nil {
		// 重新构建最佳模板的词槽值映射
		slotValues := make(map[string]string)
		for _, slots := range bestTemplate.Slots {
			for _, slot := range slots {
				if slot.Hit {
					slotValues[slot.Name] = slot.Value
					if slot.Result != nil {
						slotValues[slot.Name] = slot.Result.MatchedText
						slotValues[slot.Name+"_start"] = strconv.Itoa(slot.Result.StartPos)
						slotValues[slot.Name+"_end"] = strconv.Itoa(slot.Result.EndPos)
					}
				}
			}
		}

		// 检查正则匹配
		if intentMatch, exists := n.IntentMatch[bestTemplate.Name]; exists {
			// 如果有泛化匹配模板，且模板不为空，才进行正则匹配检查
			if len(intentMatch.Pattern) > 0 {
				if matchedIntent := intentMatch.Match(query, slotValues); matchedIntent == "" {
					// 如果正则匹配失败，返回nil
					return nil
				}
			}
		}
	}

	return bestTemplate
}

// MatchSlots 匹配文本中的词槽
// text: 输入文本
// 返回值: 匹配到的词槽结果列表
func (n *BaseNluIntent) MatchSlots(text string) []nlp.SlotMatchResult {
	var results []nlp.SlotMatchResult

	// 遍历所有词槽类型
	for slotType, slotMap := range n.Slots.Slots {
		// 构建词槽匹配器
		matcher := nlp.BuildMatcher(slotMap, slotType)

		// 构建所有可能的匹配文本
		var possibleMatches []string
		for standard, aliases := range slotMap {
			possibleMatches = append(possibleMatches, standard)
			possibleMatches = append(possibleMatches, aliases...)
		}

		// 按长度降序排序，确保优先匹配最长的词
		sort.Slice(possibleMatches, func(i, j int) bool {
			return len(possibleMatches[i]) > len(possibleMatches[j])
		})

		// 在文本中查找所有可能的匹配
		textLen := len(text)
		pos := 0
		for pos < textLen {
			matched := false
			// 尝试匹配每个可能的词
			for _, match := range possibleMatches {
				if pos+len(match) <= textLen && text[pos:pos+len(match)] == match {
					// 找到匹配，获取标准值
					if standardText, ok := matcher.Match(match); ok {
						results = append(results, nlp.SlotMatchResult{
							SlotType:     slotType,
							StandardText: standardText,
							MatchedText:  match,
							StartPos:     pos,
							EndPos:       pos + len(match),
						})
					}
					pos += len(match)
					matched = true
					break
				}
			}
			// 如果没有匹配到任何词，移动到下一个字符
			if !matched {
				pos++
			}
		}
	}

	// 按照位置排序
	sort.Slice(results, func(i, j int) bool {
		return results[i].StartPos < results[j].StartPos
	})

	return results
}
