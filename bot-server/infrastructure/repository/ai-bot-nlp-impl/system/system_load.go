package system

import (
	nlp "bot/domain/service/bot/nlp"
	"bot/infrastructure/common/util/apputil"

	"github.com/go-ego/gse"
)

var slots *nlp.SlotConfig
var templates []*nlp.IntentTemplate
var intentMatchTemplates map[string]*nlp.IntentMatch
var promptSeg gse.Segmenter

func init() {
	var err error
	// 加载意图模版
	templates, err = nlp.LoadIntentTemplate(apputil.AbsPath("dict/bots/system/template.toml"))
	if err != nil {
		panic(err.Error())
	}
	// 加载意图泛化匹配
	intentMatchTemplates, err = nlp.LoadIntentMatch(apputil.AbsPath("dict/bots/system/match.toml"))
	if err != nil {
		panic(err.Error())
	}
	// 加载词槽配置
	slots, err = nlp.LoadSlotConfig(apputil.AbsPath("dict/bots/system/slot.toml"))
	if err != nil {
		panic(err.Error())
	}
	// 初始化 加载 promptSeg 分词 token
	if promptSeg, err = nlp.LoadPrompt(apputil.AbsPath("dict/prompts/system_prompt.txt")); err != nil {
		panic(err.Error())
	}
}
