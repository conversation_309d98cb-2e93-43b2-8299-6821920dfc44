package writer

import (
	"bot/domain/service/bot/nlp"
	aibotnlpimpl "bot/infrastructure/repository/ai-bot-nlp-impl/system"
)

type BotWriterNlpImpl struct {
	*aibotnlpimpl.BaseNluIntent
	nlp.BaseConfig
}

func NewBotWriterNlpImpl(baseNluIntent *aibotnlpimpl.BaseNluIntent) *BotWriterNlpImpl {
	return &BotWriterNlpImpl{
		BaseNluIntent: baseNluIntent,
		BaseConfig: nlp.BaseConfig{
			Slots:          slots,
			IntentTemplate: templates,
			IntentMatch:    intentMatchTemplates,
		},
	}
}
