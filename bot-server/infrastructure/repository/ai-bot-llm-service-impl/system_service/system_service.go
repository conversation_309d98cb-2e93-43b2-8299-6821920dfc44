package system_service

import (
	"bot/domain/repository"
	"bot/domain/service/bot/sdk/fun"
	"context"
	"fmt"
	"reflect"
	"time"
)

// SystemServiceImp
// 子类继承 SystemServiceImp 实现功能的增强 注意 生成的 FunctionCalling 最终内容相同的子类覆盖父类
type SystemServiceImp struct {
	repository.IMap
}

func NewSystemServiceImp(mapFunc repository.IMap) *SystemServiceImp {
	return &SystemServiceImp{
		IMap: mapFunc,
	}
}

func (receiver *SystemServiceImp) FunctionCalling() (fun.FuncCalling, fun.FuncDefinition) {
	callings := make(fun.FuncCalling)
	callings[QueryNowTimeFunc] = reflect.ValueOf(receiver.QueryNowTimeFunc)
	if !reflect.ValueOf(receiver.IMap).IsNil() {
		// 地图配置相关函数调用
		callings[QueryNowLocationFunc] = reflect.ValueOf(receiver.QueryNowLocationFunc)
		callings[QueryNowWeatherFunc] = reflect.ValueOf(receiver.QueryNowWeatherFunc)
		callings[QueryNowLocationWeatherFunc] = reflect.ValueOf(receiver.QueryNowLocationWeatherFunc)
	}
	return callings, BaseFuncListDefinition
}

// QueryNowTimeFunc
// 获取系统时间函数
func (receiver *SystemServiceImp) QueryNowTimeFunc(ctx context.Context) (any, error) {
	format := time.Now().Format(time.DateTime)
	return fmt.Sprintf("现在的时间是 :%s 可以不需要继续调用函数获取时间了", format), nil
}

// QueryNowLocationFunc
// 获取当前位置信息
func (receiver *SystemServiceImp) QueryNowLocationFunc(ctx context.Context) (any, error) {
	location, err := receiver.IpLocation(ctx)
	if err != nil {
		return nil, err
	}
	return fmt.Sprintf("现在的位置大概是 :%s", location), nil
}

// QueryNowLocationWeatherFunc
// 获取当前位置信息
func (receiver *SystemServiceImp) QueryNowLocationWeatherFunc(ctx context.Context) (any, error) {
	location, err := receiver.IpLocation(ctx)
	if err != nil {
		return nil, err
	}
	return receiver.QueryNowWeatherFunc(ctx, location)
}

// QueryNowWeatherFunc
// 查询当前位置天气信息
func (receiver *SystemServiceImp) QueryNowWeatherFunc(ctx context.Context, city string) (any, error) {
	weather, err := receiver.CityWeather(ctx, city)
	if err != nil {
		return nil, err
	}
	return fmt.Sprintf("当前的天气信息大概是 :%s", weather), nil
}
