package system_service

import (
	"bot/domain/service/bot/sdk/fun"
)

var BaseFuncListDefinition = map[string]fun.FunctionSpec{
	QueryNowTimeFunc: {
		Type: "function",
		Function: fun.FunctionDefinition{
			Name:        QueryNowTimeFunc,
			Description: "通过这个函数你可以获取到当前的时刻的时间",
			Parameters: fun.FunctionParameters{
				Type:       "object",
				Properties: nil,
				Required:   []string{},
			},
		},
	},
	QueryNowLocationFunc: {
		Type: "function",
		Function: fun.FunctionDefinition{
			Name:        QueryNowLocationFunc,
			Description: "如果你不知道位置信息，可以通过这个函数获取当前所在的位置区域信息",
			Parameters: fun.FunctionParameters{
				Type:       "object",
				Properties: nil,
				Required:   []string{},
			},
		},
	},
	QueryNowWeatherFunc: {
		Type: "function",
		Function: fun.FunctionDefinition{
			Name:        QueryNowWeatherFunc,
			Description: "根据提供的 city 信息 可以查询 city 当前的天气情况",
			Parameters: fun.FunctionParameters{
				Type: "object",
				Properties: map[string]fun.FunctionProperty{
					"city": {
						Type:        "string",
						Description: "参数 city 是你需要查询天气的城市信息或者位置信息",
					},
				},
				Required: []string{"city"},
			},
		},
	},
	QueryNowLocationWeatherFunc: {
		Type: "function",
		Function: fun.FunctionDefinition{
			Name:        QueryNowLocationWeatherFunc,
			Description: "当你不知道需要查看那个城市或者位置的天气，可以调用这个方法获得一个默认提供的天气信息",
			Parameters: fun.FunctionParameters{
				Type:       "object",
				Properties: nil,
				Required:   []string{},
			},
		},
	},
	QueryLocalPositionFunc: {
		Type: "function",
		Function: fun.FunctionDefinition{
			Name:        QueryLocalPositionFunc,
			Description: "通过系统本地定位服务获取当前设备的精确位置信息，包括经纬度和精确度",
			Parameters: fun.FunctionParameters{
				Type:       "object",
				Properties: nil,
				Required:   []string{},
			},
		},
	},
}
