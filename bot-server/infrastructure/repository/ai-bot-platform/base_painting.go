package ai_bot_platform

import (
	"bot/domain/entity"
	"bot/infrastructure/po"
	"context"
)

// SendPainting
func (base *BaseBot) SendPainting(ctx context.Context, painting *entity.PaintingEntity) (*po.PaintingPO, error) {
	panic("implement me")
}

func (base *BaseBot) SavePainting(ctx context.Context, painting *entity.PaintingEntity) (*po.PaintingPO, error) {
	panic("implement me")
}

// PaintingStream
func (base *BaseBot) PaintingStream(ctx context.Context, chat *entity.PaintingEntity) {
	panic("implement me")
}
