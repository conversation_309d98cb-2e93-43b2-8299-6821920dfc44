package ai_bot_platform

import (
	"bot/domain/entity"
	"bot/domain/service/bot/sdk"
	"bot/domain/service/bot/sdk/agent"
	"bot/domain/service/bot/sdk/agent/agentutil"
	"bot/domain/service/bot/sdk/arg"
	"bot/domain/service/bot/sdk/handler"
	"bot/domain/service/bot/sdk/sdkutil"
	"bot/domain/service/bot/sdk/sse"
	"bot/domain/vo"
	"bot/infrastructure/po"
	"bot/infrastructure/repository/ai-bot-common/mcpclien"
	giteeaiutil "bot/infrastructure/repository/ai-bot-platform/gitee/gitee-ai-util"
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"time"
)

// AgentStream
// 通用系统推送数据，不依赖任何 LLM 平台
// 在系统内可以正确的保障错误处理等数据推送
func (base *BaseBot) AgentStream(ctx context.Context, chat *entity.ChatEntity) {

}

// AgentSendError
// 传递一个错误，下发到 Agent 推送
func (base *BaseBot) AgentSendError(ctx context.Context, chat *entity.ChatEntity, err error) {
	messagePO := po.NewChatToMessagePO(chat)
	// 保存消息 以便 后续 客户端上报更新
	// 清空消息内容
	messagePO.Content = ""
	// 设置消息类型
	messagePO.MessageType = vo.AgentScene
	messagePO.SetError(err)
	base.SaveBotMsg(messagePO)
}

// HandleAgent
// chat 请求上下文参数
// param 请求参数
// callback agent 回调函数 用于处理不同平台的模型返回数据
// 该方法内部是一个递归流请求方式
func (base *BaseBot) HandleAgent(ctx context.Context, chat *entity.ChatEntity, param arg.IParam, callback agent.Callback) {
	var err error
	messagePO := po.NewChatToMessagePO(chat)

	// 保存消息 以便 后续 客户端上报更新
	// 清空消息内容
	messagePO.Content = ""
	// 设置消息类型
	messagePO.MessageType = vo.AgentScene
	base.SaveBotMsg(messagePO)

	// 开启全局 推送
	go base.Push()

	render := sdk.NewRender(vo.ChatScene)
	if err = base.Writer(render); err != nil {
		goto save
	}
	if err = base.Writer(sdk.NewBeginLLMData()); err != nil {
		goto save
	}
	err = base.AgentLLMStream(ctx, chat, param, callback)
save:
	messagePO.SetError(err)
	base.ChatDB.Model(&po.MessagePO{}).Where("id = ?", messagePO.ID).Updates(map[string]any{
		"role": vo.AssistantRoleType,
		"ex":   messagePO.Ex,
	})
	finish := sdk.NewFinishLLMData()
	base.Writer(finish)
}

// AgentLLMStream
// 整个 Agent 请求过程中 chat 参数会更具请求的轮次发生变化，
func (base *BaseBot) AgentLLMStream(ctx context.Context, chat *entity.ChatEntity, param arg.IParam, callback agent.Callback) error {
	var isContinue bool
	var err error
	if isContinue, err = base.agentLLMStream(ctx, chat, param, callback); isContinue {
		return base.AgentLLMStream(ctx, chat, param, callback)
	}
	return err
}

func (base *BaseBot) agentLLMStream(ctx context.Context, chat *entity.ChatEntity, param arg.IParam, callback agent.Callback) (bool, error) {
	var reader sse.Reader
	var stream chan string
	var request *http.Request
	var response *http.Response
	var err error
	var isContinue bool
	// 本轮工具 列表
	mcpTools := make([]any, 0)
	// 当前轮次对话 调用 mcp 工具的结果集合
	mcpCallResult := make([]any, 0)

	if chat.Agent {
		agentPrompt, err := base.GenAgentPrompt(chat)
		if err != nil {
			return false, err
		}
		param.Prompt(agentPrompt)
	}
	// 开始加载 提示词
	if request, err = param.Build(); err != nil {
		return isContinue, err
	}
	if response, err = base.DoRequest(request); err != nil {
		return isContinue, err
	}
	render := sdk.NewRender(vo.ChatScene)
	if err = base.Writer(render); err != nil {
		return isContinue, err
	}
	// LLMHandler 原来设计是不能复用的，这里需要重新初始化
	base.LLMHandler = handler.NewLLMHandler(base.Context)
	reader = base.Complete(context.Background(), response)
	stream = base.StreamIter(reader)

	agentTagHandler := agent.NewAgentTagHandler()
	agentTagHandler.AddTag(&agent.AgentTag{
		TagType:  "mcp",
		StartTag: "```mcp",
		EndTag:   "```",
	})

	agentTagHandler.AddTag(&agent.AgentTag{
		TagType:  "action",
		StartTag: "```action",
		EndTag:   "```",
	})

	// 解析流数据
	for data := range stream {
		var value *agent.Completion
		var agentItem *agent.Item
		var jsonErr error
		var callBackErr error
		if data == "[DONE]" || data == "" {
			continue
		}
		if isError, msg := giteeaiutil.CheckErrorMessage(data); isError {
			return false, sdk.DeepSeekRequestError.Instance().SetError(errors.New(msg))
		}
		extractJSON := sdkutil.ExtractJSON([]byte(data))
		if jsonErr = json.Unmarshal(extractJSON, &value); jsonErr != nil {
			return false, sdk.LLMDataError.Instance().SetError(errors.New(jsonErr.Error()))
		}
		if value.IsFinished() {
			goto end
		}
		content := value.GetContent()
		if content == "" {
			continue
		}
		if chat.Agent {
			feed, tag, ok := agentTagHandler.Feed(content)
			if ok {
				switch tag {
				case vo.AgentMcpCall:
					feed = agentutil.GetJsonBody(vo.AgentMcpCall, feed)
					var mcpCall map[string]mcpclien.McpCall
					if err = json.Unmarshal([]byte(feed), &mcpCall); err != nil {
						return false, err
					}
					for key, mcpTool := range mcpCall {
						var mcpEntity *entity.McpEntity
						var res []byte
						mcpInfo := entity.McpInfoEntity{}
						callId := base.Node.String()
						var callResult any
						mcpEntity, err = base.GetById(key)
						if err != nil {
							continue
						}
						// 记录 mcp 执行信息
						mcpInfo.Mcp = mcpEntity.Name
						mcpInfo.Tool = mcpTool.Name
						mcpInfo.Arguments = mcpTool.Arguments
						// 发送准备调用 流状态
						res, _ = json.Marshal(mcpInfo)
						base.McpToolCallPush(callId, vo.AgentItemStatusStart, string(res))
						callResult, err = base.mcpCall(context.Background(), mcpEntity, mcpTool)
						if err != nil {
							mcpInfo.Error = err.Error()
						}
						time.Sleep(1 * time.Second)
						mcpInfo.Value = callResult
						// 执行完成
						res, _ = json.Marshal(mcpInfo)
						base.McpToolCallPush(callId, vo.AgentItemStatusStop, string(res))
						mcpTools = append(mcpTools, mcpCall)
						mcpCallResult = append(mcpCallResult, mcpInfo)
					}
				case vo.AgentActionCall:
					feed = agentutil.GetJsonBody(vo.AgentActionCall, feed)
					var action *agent.Action
					if err = json.Unmarshal([]byte(feed), &action); err != nil {
						return false, err
					}
					if action != nil {
						switch action.Type {
						case agent.McpContinue:
							// 查看对应 value
							if action.Value == "continue" {
								isContinue = true
								goto end
							}
						}
					}
				}
			} else {
				if feed != "" {
					// 正常数据传递 各自回调处理生成内容
					agentItem, _, callBackErr = callback(value)
					if callBackErr != nil {
						return false, callBackErr
					}
				} else {
					continue
				}
			}
		} else {
			agentItem, _, callBackErr = callback(value)
			if callBackErr != nil {
				return false, callBackErr
			}
		}

		if agentItem != nil {
			// 发送到 全局 消息
			base.Global <- agentItem.Json()
		}
	}

end:
	// mcp 调用记录
	if len(mcpTools) > 0 {
		json, _ := json.Marshal(mcpTools)
		chat.History = append(chat.History, &entity.HistoryEntity{
			Role:    vo.AssistantRoleType,
			Content: string(json),
		})
	}

	// mcp 调用结果
	if len(mcpCallResult) > 0 {
		json, _ := json.Marshal(mcpCallResult)
		chat.History = append(chat.History, &entity.HistoryEntity{
			Role:    vo.UserRoleType,
			Content: string(json),
		})
	}

	return isContinue, err
}

func (base *BaseBot) McpToolCallPush(mcpToolId, status string, data string) {
	agentItem := agent.NewMcpCallAgentItemByStatus(mcpToolId, status, data)
	base.Global <- agentItem.Json()
}

// GenAgentPrompt
// 生成全局请求的提示词，包括agen角色，工具调用等
func (base *BaseBot) GenAgentPrompt(chat *entity.ChatEntity) (string, error) {
	//1.准备模版上下文数据
	ctx := make(map[string]any)
	//2. 历史数据消息，有且仅有一条用户消息的时候不渲染
	if len(chat.History) > 1 {
		ctx["Messages"] = chat.History
	}
	mcpTools, infos := base.GetEnableMcpTools()
	// 检查是否需要渲染 agent工具
	// 加载 agent 工具 信息映射
	ctx["McpTool"] = infos
	// 加载 具体 agent 工具 定义
	ctx["Tools"] = mcpTools
	if len(infos) == 0 && len(mcpTools) == 0 {
		return "", nil
	}
	return base.renderAgentPrompt(ctx)
}
