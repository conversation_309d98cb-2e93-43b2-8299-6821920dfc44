package ai_bot_platform

import (
	"bot/domain/entity"
	bot_context "bot/domain/service/bot-context"
	"bot/domain/service/bot/sdk/agent"
	"bot/domain/service/bot/sdk/arg"
	"bot/domain/service/bot/sdk/handler"
	"bot/domain/vo"
	giteeaiutil "bot/infrastructure/repository/ai-bot-platform/gitee/gitee-ai-util"
	"context"
	"strings"
	"sync"
)

type GiteeAI struct {
	*BaseBot
}

func NewGiteeAI(
	botCtx *bot_context.Context,
	base *BaseRepository) (*GiteeAI, error) {
	llmHandler := handler.NewLLMHandler(botCtx)
	d := &GiteeAI{BaseBot: NewBaseBot(botCtx, llmHandler, base)}
	return d, nil
}

func (receiver *GiteeAI) ChatRequest(ctx context.Context, chat *entity.ChatEntity) arg.IParam {
	//chat = receiver.PreloadSystemPrompt(chat)
	chatRequest := arg.NewChatRequest(receiver.PlatformConfig)
	chatRequest.Url = "/v1/chat/completions"
	chatRequest.Messages = chat.History
	chatRequest.History = chat.History
	chatRequest.MaxTokens = 8192
	if chat.Thinking {
		// 强制指定模型为 r1
		chatRequest.RequestParam.LLMPublicArgs.Model = "DeepSeek-R1"
	}
	return chatRequest
}

// ChatStream
// 获取流消息
func (receiver *GiteeAI) ChatStream(ctx context.Context, chat *entity.ChatEntity) {
	receiver.AgentStream(ctx, chat)
}

func (receiver *GiteeAI) AgentStream(ctx context.Context, chat *entity.ChatEntity) {
	star := &sync.Once{}
	end := &sync.Once{}
	// 处理图片数据
	imageSources := receiver.ReadImageSource(chat)
	for _, source := range imageSources {
		imageMessage := giteeaiutil.CreateImageMessage(source)
		chat.History = append(chat.History, imageMessage)
	}
	param := receiver.ChatRequest(ctx, chat)
	Type := vo.AgentMarkdown
	Status := vo.AgentItemStatusDefault
	endThinking := false
	// 当前会话是否在思考
	isThinking := false
	receiver.BaseBot.HandleAgent(ctx, chat, param, func(chatResponse *agent.Completion) (*agent.Item, bool, error) {
		content := ""
		content = chatResponse.GetContent()
		if chat.Thinking {
			if chatResponse.IsThinking() {
				if strings.TrimSpace(content) != "" {
					star.Do(func() {
						// 添加 thinking 渲染块
						content = ":::thinking\n\r" + content
						endThinking = false
						end = &sync.Once{}
						Type = vo.AgentThinking
					})
					isThinking = true
				}
			} else {
				if isThinking {
					end.Do(func() {
						content = "\n\n:::\n\r" + content
						// 重置思考模式输出
						star = &sync.Once{}
						endThinking = true
						Status = vo.AgentItemStatusStop
					})
				}
			}
		}
		agentItem := agent.NewAgentItem(Type, Status, content)
		if endThinking {
			// 思考模式结束了 切换 数据类型
			Type = vo.AgentMarkdown
			Status = vo.AgentItemStatusDefault
			endThinking = false
		}
		return agentItem, chatResponse.IsFinished(), nil
	})
}
