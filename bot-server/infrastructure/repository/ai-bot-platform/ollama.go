package ai_bot_platform

import (
	"bot/domain/entity"
	bot_context "bot/domain/service/bot-context"
	"bot/domain/service/bot/sdk/agent"
	"bot/domain/service/bot/sdk/arg"
	"bot/domain/service/bot/sdk/fun"
	"bot/domain/service/bot/sdk/handler"
	"bot/domain/vo"
	"bot/infrastructure/common/util/llmutils"
	"context"
	"strings"
	"sync"
)

type Ollama struct {
	*BaseBot
}

func NewLlama(
	botCtx *bot_context.Context,
	base *BaseRepository) (*Ollama, error) {
	llmHandler := handler.NewLLMHandler(botCtx)

	l := &Ollama{BaseBot: NewBaseBot(botCtx, llmHandler, base)}
	return l, nil
}

func (receiver *Ollama) ChatRequest(ctx context.Context, chat *entity.ChatEntity) arg.IParam {
	chatRequest := arg.NewChatRequest(receiver.PlatformConfig)
	chatRequest.Url = "/api/chat"
	chatRequest.Messages = chat.History
	return chatRequest
}

func (receiver *Ollama) CallingHandler(ctx context.Context, value string) fun.CallingResult {
	/*if value == "" {
		return nil
	}
	if isError, msg := giteeaiutil.CheckErrorMessage(value); isError {
		fmt.Println(msg)
		return nil
	}
	var data *giteeai.FuncCompletion
	if err := jsoniter.Unmarshal([]byte(value), &data); err != nil {
		return nil
	}
	return data*/
	return nil
}

// ChatStream
// 获取流消息
func (receiver *Ollama) ChatStream(ctx context.Context, chat *entity.ChatEntity) {
	// 处理图片消息
	/*	chat = llmutils.ReadSource(chat)
		param := receiver.ChatRequest(ctx, chat)

		receiver.BaseBot.ChatStream(ctx, chat, param, func(value string) (string, error) {
			if value == "" || value == "[DONE]" {
				return "", nil
			}
			var chatResponse *arg.OllamaChatResponse
			if err := jsoniter.Unmarshal([]byte(value), &chatResponse); err != nil {
				return "", sdk.LLMDataError.Instance().SetError(err)
			}
			content := ""
			content = chatResponse.GetContent()

			agentItem := agent.NewMarkdownAgentItem(content)

			llmData := sdk.NewLLMContent(sse.Data, agentItem.Json(), nil)
			if err := receiver.Writer(string(llmData.Byte())); err != nil {
				return content, sdk.SSEDisconnectError
			}
			return content, nil
		})*/

	receiver.AgentStream(ctx, chat)
}

func (receiver *Ollama) AgentStream(ctx context.Context, chat *entity.ChatEntity) {
	// 处理图片数据
	chat = llmutils.ReadSource(chat)
	param := receiver.ChatRequest(ctx, chat)

	star := &sync.Once{}
	endOnce := &sync.Once{}
	Type := vo.AgentMarkdown
	Status := vo.AgentItemStatusDefault
	endThinking := false

	// 用于缓存思考内容的变量
	var thinkBuffer strings.Builder
	inThinkMode := false

	receiver.BaseBot.HandleAgent(ctx, chat, param, func(chatResponse *agent.Completion) (*agent.Item, bool, error) {
		content := chatResponse.GetContent()

		if chat.Thinking {
			// 启用思考模式时的处理逻辑
			if strings.Contains(content, "<think>") {
				if strings.TrimSpace(content) != "" {
					star.Do(func() {
						// 添加 thinking 渲染块
						// 移除 <think> 标签并添加 thinking 标记
						content = strings.ReplaceAll(content, "<think>", "")
						content = ":::thinking\n\r" + content
						endThinking = false
						endOnce = &sync.Once{}
						Type = vo.AgentThinking
					})
				}
			} else if strings.Contains(content, "</think>") {
				// 检测 </think> 标签结束
				endOnce.Do(func() {
					// 移除 </think> 标签并添加结束标记
					content = strings.ReplaceAll(content, "</think>", "")
					content = content + "\n\n:::\n\r"
					// 重置思考模式输出
					star = &sync.Once{}
					endThinking = true
					Status = vo.AgentItemStatusStop
				})
			}
		} else {
			// 未启用思考模式时，过滤掉 <think> 标签内容
			if strings.Contains(content, "<think>") {
				// 进入思考模式缓存
				idx := strings.Index(content, "<think>")
				originalContent := content
				// 保留 <think> 标签前的内容
				if idx > 0 {
					content = originalContent[:idx]
				} else {
					content = ""
				}
				// 将 <think> 标签后的内容添加到缓存
				afterThink := originalContent[idx+7:] // 7 是 "<think>" 的长度
				thinkBuffer.WriteString(afterThink)
				inThinkMode = true
			} else if strings.Contains(content, "</think>") {
				// 退出思考模式缓存
				idx := strings.Index(content, "</think>")
				// 将 </think> 标签前的内容添加到缓存
				if idx > 0 {
					beforeEndThink := content[:idx]
					thinkBuffer.WriteString(beforeEndThink)
				}
				// 保留 </think> 标签后的内容
				afterEndThink := content[idx+8:] // 8 是 "</think>" 的长度
				content = afterEndThink
				// 清空缓存并退出思考模式
				thinkBuffer.Reset()
				inThinkMode = false
			} else if inThinkMode {
				// 在思考模式中，将内容缓存起来不返回
				thinkBuffer.WriteString(content)
				content = ""
			}
		}
		agentItem := agent.NewAgentItem(Type, Status, content)

		if endThinking {
			// 思考模式结束了 切换 数据类型
			Type = vo.AgentMarkdown
			Status = vo.AgentItemStatusDefault
			endThinking = false
		}

		return agentItem, chatResponse.IsFinished(), nil
	})
}
