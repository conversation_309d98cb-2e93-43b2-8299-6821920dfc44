package ai_bot_platform

import (
	"bot/domain/entity"
	botcontext "bot/domain/service/bot-context"
	"bot/domain/service/bot/sdk"
	"bot/domain/service/bot/sdk/fun"
	"bot/domain/service/bot/sdk/handler"
	"bot/domain/service/bot/sdk/sse"
	"bot/domain/vo"
	"bot/infrastructure/common/db"
	"bot/infrastructure/common/logs"

	"bot/infrastructure/common/util/apputil"
	"bot/infrastructure/common/util/uuidutils/uuid"
	"bot/infrastructure/po"
	"bot/infrastructure/repository/ai-bot-common/mcpclien"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"strings"
	"text/template"

	"github.com/mark3labs/mcp-go/mcp"
	"go.uber.org/zap"
)

type BaseBody struct {
	Tools []fun.FunctionSpec `json:"tools,omitempty"`
}

type BaseRepository struct {
	*db.ChatDB
	*logs.AppLog
	*uuid.Node
}

func NewBaseRepository(
	chatDB *db.ChatDB,
	log *logs.AppLog,
	node *uuid.Node,
) *BaseRepository {
	return &BaseRepository{
		ChatDB: chatDB,
		AppLog: log,
		Node:   node,
	}
}

// BaseBot 实现所有插件的默认功能
type BaseBot struct {
	*handler.LLMHandler
	// 基础库
	*BaseRepository
	// bot 上下文
	*botcontext.Context

	// 单个请求的全局 流数据通道
	Global chan string
}

// NewDefaultBot
// 基础 bot 实现，提供基础的功能
func NewDefaultBot(base *BaseRepository) *BaseBot {
	return &BaseBot{
		LLMHandler:     nil,
		BaseRepository: base,
		Global:         make(chan string, 10000),
	}
}

func NewBaseBot(
	ctx *botcontext.Context, lm *handler.LLMHandler, base *BaseRepository) *BaseBot {
	return &BaseBot{
		Context:        ctx,
		BaseRepository: base,
		LLMHandler:     lm,
		Global:         make(chan string, 10000),
	}
}

// Send
// 用户发送消息到服务器，存储消息，并返回当前消息
func (base *BaseBot) Send(ctx context.Context, chat *entity.ChatEntity) (*po.MessagePO, error) {
	messagePO := po.NewChatToMessagePO(chat)
	messagePO.ID = base.Node.String()
	if err := base.ChatDB.Create(messagePO).Error; err != nil {
		return nil, err
	}
	return messagePO, nil
}
func (base *BaseBot) Render(scene string) error {
	render := sdk.NewRender(scene)
	return base.Writer(render)
}

// SaveAgent
// 保存代理消息
func (base *BaseBot) SaveAgent(ctx context.Context, chat *entity.ChatEntity) (*po.MessagePO, error) {
	messagePO := po.NewChatToMessagePO(chat)
	messagePO.ID = base.Node.String()
	if err := base.ChatDB.Create(messagePO).Error; err != nil {
		return nil, err
	}
	return messagePO, nil
}

// Push
// 推送消息到 客户端 需要调用者手动提前开启
func (base *BaseBot) Push() {
	for data := range base.Global {
		agentContent := sdk.NewLLMContent(sse.Data, data, nil)
		base.Writer(agentContent)
	}
}

func (base *BaseBot) mcpCall(ctx context.Context, mcpEntity *entity.McpEntity, call mcpclien.McpCall) (any, error) {
	//var data []byte
	callToolResult := base.CallTool(mcpEntity, call)
	//data, _ = json.Marshal(callToolResult.Content)
	return callToolResult.Content, nil
}

// WriterStream
// 写作默认实现
func (base *BaseBot) WriterStream(ctx context.Context, writerEntity *entity.WriterEntity) {
	//TODO implement me
	panic("implement me")
}

// OcrStream
// ocr 图片文本提取默认实现
func (base *BaseBot) OcrStream(ctx context.Context, chat *entity.ChatEntity) {
	//TODO implement me
	panic("implement me")
}

// ImageStream
// 文生图默认实现
func (base *BaseBot) ImageStream(ctx context.Context, chat *entity.ChatEntity) {
	//TODO implement me
	panic("implement me")
}

func (base *BaseBot) FindMCP(ctx context.Context, ids []string) []*mcpclien.MCPClient {
	return base.FindMCPsById(ids)
}

// ReadImageSource
// 读取消息记录里面的所有图片数据
// 返回所有的 图片数据
func (base *BaseBot) ReadImageSource(chat *entity.ChatEntity) [][]byte {
	images := make([][]byte, 0)
	if len(chat.Images) > 0 {
		// 获取最后一条用户消息gite
		lastUserMsg := chat.History[len(chat.History)-1]
		if lastUserMsg.Role == vo.UserRoleType {
			for _, imagePath := range chat.Images {
				// 读取图片文件
				p := "/api/resource/"
				index := strings.Index(imagePath, p)
				// 从完整URL中提取资源路径
				resourcePath := imagePath[index+len(p):]
				// ./var/.. 地址是基于接口上传逻辑的存储路径 详细可以查看 bot-server/infrastructure/common/util/imageutil/imageutil.go:71
				imageData, err := os.ReadFile(fmt.Sprintf("./var/%s", resourcePath))
				if err != nil {
					zap.L().Error(err.Error())
					continue
				}
				images = append(images, imageData)
			}
		}
	}
	return images
}

// SaveBotMsg
// 公共存储bot消息
func (base *BaseBot) SaveBotMsg(msg *po.MessagePO) {
	// 消息入库
	msg.Role = vo.AssistantRoleType
	base.ChatDB.Create(msg)
}

// PreloadSystemPrompt
// 预加载 系统提示词
func (base *BaseBot) PreloadSystemPrompt(chat *entity.ChatEntity) *entity.ChatEntity {
	var list []*entity.HistoryEntity
	if chat.Thinking {
		prompt := `请你进行思考后,输出你的思考过程，并且把你的思考过程使用 markdown 的方式输出给我，如下示例格式
:::thinking

:::
中间填写思考内容，回答的主要内容不能出现在 thinking 块中
`
		list = append(list, &entity.HistoryEntity{
			Role:    vo.SystemRoleType,
			Content: prompt,
		})
		chat.History = append(list, chat.History...)
	}
	return chat
}

func (base *BaseBot) renderAgentPrompt(data map[string]any) (string, error) {
	var buf bytes.Buffer
	mcpTemplate, err := GetAgentTemplate()
	if err != nil {
		return "", err
	}
	if err = mcpTemplate.Execute(&buf, data); err != nil {
		return "", err
	}
	return buf.String(), err
}

func (base *BaseBot) McpTools(chat *entity.ChatEntity) map[string][]mcp.Tool {
	// 查询所有的工具信息
	mcpClients := base.FindMCP(context.Background(), chat.Mcp)
	// 读取 对应的 tools 组装为 对应的 提示词发送给 LLM
	mcpMap := make(map[string][]mcp.Tool)
	for _, client := range mcpClients {
		listToolsRequest := mcp.ListToolsRequest{}
		var list *mcp.ListToolsResult
		list, err := client.ListTools(context.Background(), listToolsRequest)
		if err != nil {
			continue
		}
		mcpMap[client.ID] = list.Tools
	}
	return mcpMap
}

// GetEnableMcpTools
// 获取 工具及其工具介绍
func (base *BaseBot) GetEnableMcpTools() (map[string][]mcp.Tool, map[string]string) {
	// 查询所有的工具信息
	mcpClients := base.GetAllClients()
	// 读取 对应的 tools 组装为 对应的 提示词发送给 LLM
	mcpMap := make(map[string][]mcp.Tool)
	mcpInfo := make(map[string]string)
	for _, client := range mcpClients {
		if client.Disabled == 1 {
			continue
		}
		listToolsRequest := mcp.ListToolsRequest{}
		var list *mcp.ListToolsResult
		list, err := client.ListTools(context.Background(), listToolsRequest)
		if err != nil {
			continue
		}
		mcpMap[client.ID] = list.Tools
		// todo 后续需要优化自定义工具的介绍让llm可以更加详细的判断
		mcpInfo[client.ID] = client.ServerInfo.Name
	}
	return mcpMap, mcpInfo
}

// McpInfo 整合获取所有的 mcp工具信息 用于组装提示词
func (base *BaseBot) McpInfo(chat *entity.ChatEntity) map[string]string {
	mcpClients := base.FindMCP(context.Background(), chat.Mcp)
	mcpMap := make(map[string]string)
	for _, client := range mcpClients {
		mcpMap[client.ID] = client.ServerInfo.Name
	}
	return mcpMap
}

func GetAgentTemplate() (*template.Template, error) {
	open, err := os.Open(apputil.AbsPath("template/agent/agent-base.txt"))
	if err != nil {
		return nil, err
	}
	all, err := io.ReadAll(open)
	if err != nil {
		return nil, err
	}
	buffer := bytes.NewBuffer(all)
	mcpTemp := template.New("mcp")
	mcpTemp = mcpTemp.Funcs(template.FuncMap{
		"json": Json,
	})
	parse, err := mcpTemp.Parse(buffer.String())
	return parse, err
}

func GetMcpTemplate() (*template.Template, error) {
	open, err := os.Open(apputil.AbsPath("template/mcp-prompt.txt"))
	if err != nil {
		return nil, err
	}
	all, err := io.ReadAll(open)
	if err != nil {
		return nil, err
	}
	buffer := bytes.NewBuffer(all)
	mcpTemp := template.New("mcp")
	mcpTemp = mcpTemp.Funcs(template.FuncMap{
		"json": Json,
	})
	parse, err := mcpTemp.Parse(buffer.String())
	return parse, err
}

func Json(data any) string {
	indent, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		return ""
	}
	return string(indent)
}
