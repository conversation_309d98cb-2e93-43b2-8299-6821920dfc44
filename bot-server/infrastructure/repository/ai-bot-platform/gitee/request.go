package gitee

import (
	"bot/domain/service/bot/sdk/arg"
	"bot/domain/service/bot/sdk/fun"
)

// Message 表示聊天消息
type Message struct {
	Content          string         `json:"content"`
	ReasoningContent string         `json:"reasoning_content"` // 适配 giteeai deepseek 的思考模式
	Role             string         `json:"role"`
	ToolCalls        []fun.ToolCall `json:"tool_calls,omitempty"`
}

// ChatRequest 表示聊天请求参数
type ChatRequest struct {
	arg.LLMReq
}

// NewChatRequest 创建一个新的聊天请求
func NewChatRequest(model string, messages arg.LLMMessage) *ChatRequest {
	return &ChatRequest{
		arg.LLMReq{
			LLMPublicArgs: arg.LLMPublicArgs{
				Messages: messages,
				Model:    model,
				Stream:   true, // 默认启用流式响应
			},
			History: messages,
		},
	}
}
