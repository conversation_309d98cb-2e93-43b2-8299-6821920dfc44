package gitee_ai_util

import (
	"bot/domain/entity"
	"bot/domain/vo"
	"bot/infrastructure/common/util/ossutil"
	"bot/infrastructure/repository/ai-bot-platform/gitee"
	"encoding/json"
	"fmt"
	"math/rand"
	"time"
)

var oss *ossutil.TxOSS

func init() {
	var err error
	oss, err = ossutil.NewTxOSS(ossutil.TxOssConfig{
		//SecretID:  os.Getenv("TX_OSS_SECRET_ID"),
		//SecretKey: os.Getenv("TX_OSS_SECRET_KEY"),
		SecretID:  "AKIDU7ZotlT1NXVIuJXbT9oLix1wAHCuVxDG",
		SecretKey: "whIhtulQuCJExSyU0Y3V0WzLex63tlZC",
		Region:    "ap-guangzhou",
		Bucket:    "ai-bot-1252940994",
	})
	if err != nil {
		panic(err)
	}
}

// 生成随机字符串
func generateRandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	seededRand := rand.New(rand.NewSource(time.Now().UnixNano()))
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[seededRand.Intn(len(charset))]
	}
	return string(b)
}

// ErrorResponse 错误响应结构
type ErrorResponse struct {
	Error struct {
		Code    string `json:"code"`
		Message string `json:"message"`
		Type    string `json:"type"`
	} `json:"error"`
}

// CheckErrorMessage 检查消息是否为错误消息
// 返回值：isError - 是否为错误消息，errorMsg - 错误信息（如果是错误消息）
func CheckErrorMessage(message string) (isError bool, errorMsg string) {
	var errResp ErrorResponse
	if err := json.Unmarshal([]byte(message), &errResp); err != nil {
		return false, ""
	}
	// 检查是否包含错误结构
	if errResp.Error.Code != "" && errResp.Error.Message != "" {
		return true, fmt.Sprintf("GiteeAI Code: %s, Message: %s, Type: %s",
			errResp.Error.Code,
			errResp.Error.Message,
			errResp.Error.Type)
	}

	return false, ""
}

func CreateImageMessage(data []byte) *entity.HistoryEntity {
	// 生成随机文件名：时间戳_随机字符串.png
	randomName := fmt.Sprintf("%d_%s.png", time.Now().Unix(), generateRandomString(8))

	// 上传图片
	result := oss.TxUploadImage(data, randomName)
	if result.Error != nil {
		return &entity.HistoryEntity{
			Role: vo.UserRoleType,
			Content: map[string]interface{}{
				"type": "text",
				"text": fmt.Sprintf("图片上传失败: %v", result.Error),
			},
		}
	}

	return &entity.HistoryEntity{
		Role: vo.UserRoleType,
		Content: map[string]interface{}{
			"type": "image_url",
			"image_url": map[string]interface{}{
				"url": result.URL,
			},
		},
	}
}

func IsThinking(data *gitee.ChatCompletion) bool {
	if len(data.Choices) > 0 {
		if data.Choices[0].Message.Content == data.Choices[0].Message.ReasoningContent {
			return true
		}
		return data.Choices[0].Message.ReasoningContent != ""
	}
	return false
}
