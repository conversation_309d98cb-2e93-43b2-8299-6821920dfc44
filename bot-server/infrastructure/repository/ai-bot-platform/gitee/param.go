package gitee

import (
	"bot/domain/entity"
	"bot/domain/service/bot/sdk/arg"
	"bot/infrastructure/common/errs"
	"bytes"
	"net/http"
	"net/url"
)

type ChatRequestParam struct {
	arg.RequestParam
	*entity.PlatformConfig
}

func (param *ChatRequestParam) Build() (*http.Request, error) {
	if param.PlatformConfig == nil {
		return nil, errs.ParamBuildError
	}
	if param.Key == "" {
		return nil, errs.ParamBuildError
	}
	if param.Api == "" {
		return nil, errs.ParamBuildError
	}

	urlValues := &url.Values{}
	for key, value := range param.Params {
		urlValues.Add(key, value)
	}

	u, err := url.Parse(param.Api)
	if err != nil {
		return nil, errs.ParamBuildError
	}

	u.Path = param.Url
	u.RawQuery = urlValues.Encode()

	body := param.Body()

	req, err := http.NewRequest(param.Method, u.String(), bytes.NewBuffer(body))
	if err != nil {
		return nil, errs.ParamBuildError
	}

	req.Header.Set("Authorization", "Bearer "+param.Key)
	if param.Method == http.MethodPost {
		req.Header.Set("Content-Type", "application/json")
		req.Header.Add("Accept", "application/json")
	}
	return req, nil
}

func (param *ChatRequestParam) Clone() arg.IParam {
	if param == nil {
		return nil
	}

	// 创建新的实例，浅拷贝基本字段
	clone := &ChatRequestParam{
		RequestParam:   param.RequestParam,
		PlatformConfig: param.PlatformConfig,
	}

	// 只对消息进行深度克隆
	if param.Messages != nil {
		if messages, ok := param.Messages.([]*entity.HistoryEntity); ok {
			cloneMessages := make([]*entity.HistoryEntity, len(messages))
			for i, msg := range messages {
				if msg != nil {
					cloneMsg := &entity.HistoryEntity{
						Role:    msg.Role,
						Content: msg.Content,
					}
					cloneMessages[i] = cloneMsg
				}
			}
			clone.Messages = cloneMessages
			clone.History = cloneMessages
		}
	}

	return clone
}

func (param *ChatRequestParam) String() string {
	if param == nil {
		return "{}"
	}
	baseStr := param.RequestParam.String()
	// 移除最后的 }
	baseStr = baseStr[:len(baseStr)-1]

	configInfo := "nil"
	if param.PlatformConfig != nil {
		configInfo = "{\"api\":\"" + param.Api + "\",\"key\":\"***\"}"
	}
	return baseStr + ",\"config\":" + configInfo + "}"
}
