package ai_bot_platform

import (
	"bot/domain/entity"
	bot_context "bot/domain/service/bot-context"
	"bot/domain/service/bot/sdk"
	"bot/domain/service/bot/sdk/arg"
	"bot/domain/service/bot/sdk/fun"
	"bot/domain/service/bot/sdk/handler"
	"bot/infrastructure/repository/ai-bot-platform/gitee"
	giteeaiutil "bot/infrastructure/repository/ai-bot-platform/gitee/gitee-ai-util"
	"context"
	"errors"
	"fmt"
	"net/http"

	jsoniter "github.com/json-iterator/go"
)

// GiteeAI
type Volcengine struct {
	*BaseBot
}

func NewVolcengine(
	botCtx *bot_context.Context,
	base *BaseRepository) (*Volcengine, error) {
	llmHandler := handler.NewLLMHandler(botCtx)
	d := &Volcengine{BaseBot: NewBaseBot(botCtx, llmHandler, base)}
	return d, nil
}

func (receiver *Volcengine) ChatRequest(ctx context.Context, chat *entity.ChatEntity) arg.IParam {
	request := gitee.NewChatRequest(receiver.CurrentModel, chat.History)
	param := &arg.ChatRequest{
		RequestParam: arg.RequestParam{
			Method: http.MethodPost,
			Url:    "/v1/chat/completions",
			LLMReq: request.LLMReq,
		},
		PlatformConfig: receiver.PlatformConfig,
	}
	return param
}
func (receiver *Volcengine) Handler(value string) (sdk.ILLMData, error) {
	if value == "" {
		return nil, nil
	}
	if isError, msg := giteeaiutil.CheckErrorMessage(value); isError {
		return nil, sdk.DeepSeekRequestError.Instance().SetError(errors.New(msg))
	}
	var data *gitee.ChatCompletion
	if err := jsoniter.Unmarshal([]byte(value), &data); err != nil {
		return nil, sdk.LLMDataError.Instance().SetError(err)
	}
	return data, nil
}
func (receiver *Volcengine) CallingHandler(ctx context.Context, value string) fun.CallingResult {
	if value == "" {
		return nil
	}
	if isError, msg := giteeaiutil.CheckErrorMessage(value); isError {
		fmt.Println(msg)
		return nil
	}
	var data *gitee.FuncCompletion
	if err := jsoniter.Unmarshal([]byte(value), &data); err != nil {
		return nil
	}
	return data
}

// ChatStream
// 获取流消息
func (receiver *Volcengine) AgentStream(context.Context, *entity.ChatEntity) {

	//messagePO := receiver.Handle.Chat(ctx, chat)
	receiver.SaveBotMsg(nil)
}
