package ai_bot_platform

import (
	"bot/domain/entity"
	"bot/domain/service/bot/sdk"
	"bot/domain/service/bot/sdk/agent"
	"bot/domain/vo"
	"bot/infrastructure/po"
	"bot/infrastructure/repository/ai-bot-platform/gitee"
	"bot/infrastructure/repository/ai-bot-platform/gitee/painting"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"net/http"
	"strings"
)

// NewStablediffusionRequest
// 创建 stablediffusion 绘画请求
func (receiver *GiteeAI) NewStableDiffusionRequest(ctx context.Context, painting *entity.PaintingEntity) (*http.Request, error) {
	body := map[string]any{
		"model":               "stable-diffusion-3.5-large-turbo",
		"prompt":              painting.Prompt,
		"negative_prompt":     painting.NegativePrompt,
		"guidance_scale":      painting.GuidanceScale,
		"num_inference_steps": painting.NumInferenceSteps,
		"steps":               painting.NumInferenceSteps,
		"size":                fmt.Sprintf("%dx%d", painting.Width, painting.Height),
		"n":                   1,
		"response_format":     "url",
	}
	jsonBody, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	request, err := http.NewRequest(http.MethodPost, "https://ai.gitee.com/v1/images/generations", bytes.NewBuffer(jsonBody))
	if err != nil {
		return nil, err
	}
	request.Header.Set("Content-Type", "application/json")
	request.Header.Set("Authorization", "Bearer "+receiver.PlatformConfig.Key)
	request.Header.Set("X-Gitee-AI-Model", "stablediffusion")
	return request, nil
}

// NewFluxRequest
// 创建 flux 绘画请求
func (receiver *GiteeAI) NewFluxRequest(ctx context.Context, painting *entity.PaintingEntity) (*http.Request, error) {
	body := map[string]any{
		"model":               "flux-1-schnell",
		"prompt":              painting.Prompt,
		"negative_prompt":     painting.NegativePrompt,
		"guidance_scale":      painting.GuidanceScale,
		"num_inference_steps": painting.NumInferenceSteps,
		"steps":               painting.NumInferenceSteps,
		"size":                fmt.Sprintf("%dx%d", painting.Width, painting.Height),
		"n":                   1,
		"response_format":     "url",
	}
	jsonBody, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	request, err := http.NewRequest(http.MethodPost, "https://ai.gitee.com/v1/images/generations", bytes.NewBuffer(jsonBody))
	if err != nil {
		return nil, err
	}
	request.Header.Set("Content-Type", "application/json")
	request.Header.Set("Authorization", "Bearer "+receiver.PlatformConfig.Key)
	request.Header.Set("X-Gitee-AI-Model", "stablediffusion")
	return request, nil
}

// SendPainting 通过 输入的提示词，生产一个绘画的标题等信息
func (receiver *GiteeAI) SendPainting(ctx context.Context, painting *entity.PaintingEntity) (*po.PaintingPO, error) {
	message := new(po.PaintingPO)
	message.ID = receiver.String()
	message.Status = vo.AgentItemStatusStart
	message.Prompt = receiver.HandlePaintingPrompt(painting)

	painting.ChatEntity.Thinking = false
	receiver.PlatformConfig.CurrentModel = "Kimi-K2-Instruct"
	painting.ChatEntity.History = append(painting.ChatEntity.History, &entity.HistoryEntity{
		Role:    vo.UserRoleType,
		Content: receiver.HandlePaintingPrompt(painting),
	})
	param := receiver.ChatRequest(ctx, painting.ChatEntity)
	// 绘画标题提示词
	titlePrompt := receiver.HandlePaintingTitlePrompt(painting)
	param.Prompt(titlePrompt)
	// 取消流式响应
	param.SetStream(false)
	httpClient := &http.Client{}
	var request *http.Request
	var err error
	request, err = param.Build()
	if err != nil {
		return nil, err
	}
	response, err := httpClient.Do(request)
	if err != nil {
		receiver.AppLog.Error("发送绘画请求失败", zap.Error(err))
		return nil, err
	}
	defer response.Body.Close()
	var data *agent.Completion
	if err = json.NewDecoder(response.Body).Decode(&data); err != nil {
		receiver.AppLog.Error("解析绘画响应失败", zap.Error(err))
		return nil, err
	}
	if data == nil || len(data.Choices) == 0 {
		receiver.AppLog.Error("绘画响应数据为空")
		message.Title = "AI 绘画"
	} else {
		message.Title = data.GetContent()
	}
	if err = receiver.ChatDB.Create(&message).Error; err != nil {
		receiver.AppLog.Error("保存绘画信息失败", zap.Error(err))
		return nil, err
	}
	return message, nil
}

func (receiver *GiteeAI) SavePainting(ctx context.Context, painting *entity.PaintingEntity) (*po.PaintingPO, error) {
	panic("implement me")
}

// HandlePaintingPrompt
// 处理绘画的提示词,用于请求大模型进行图片生产
func (receiver *GiteeAI) HandlePaintingPrompt(painting *entity.PaintingEntity) string {
	if painting.PromptTemplate != "" {
		if strings.Index(painting.PromptTemplate, "{prompt}") != -1 {
			painting.Prompt = strings.Replace(painting.PromptTemplate, "{prompt}", painting.Prompt, -1)
		} else {
			painting.Prompt += fmt.Sprintf(" \n\n %s", painting.PromptTemplate)
		}
	}
	return painting.Prompt
}

func (receiver *GiteeAI) HandlePaintingTitlePrompt(painting *entity.PaintingEntity) string {
	system := `你是一个内容总结大师,以下是一批图片的描述,请根据内容进行总结,给出一个图片内容的标题，不要有多余的内容，有以下几点要求
1.不能出现内容之外的词汇修饰
2.不能无中生有
3. 不能出现过度的重复
4. 图片内容不应包含过多的主观色
5. 语句通顺
`
	return system
}

// PaintingStream
// 绘画消息的推送
func (receiver *GiteeAI) PaintingStream(ctx context.Context, chat *entity.PaintingEntity) {
	var err error
	var request *http.Request
	// 初始化绘画消息
	var message *po.PaintingPO
	var imageUrls []string

	message = &po.PaintingPO{
		ID:     receiver.Node.String(),
		Prompt: chat.Prompt,
		Status: gitee.PaintingStatusDefault,
	}

	// 监听推送
	go receiver.Push()
	render := sdk.NewRender(vo.PaintingScene)
	receiver.Writer(render)

	request, err = receiver.NewStableDiffusionRequest(ctx, chat)
	if err != nil {
		receiver.AppLog.Error(err.Error(), zap.Error(err))
		message.Status = gitee.PaintingStatusFailed
		goto end
	}
	receiver.GenStableDiffusionImage(request, func(url string, err error) {
		if err != nil {
			receiver.AppLog.Error(err.Error(), zap.Error(err))
			return
		}
		if url == "" {
			return
		}
		agentItem := agent.NewAgentItem(vo.AgentPainting, vo.AgentItemStatusDefault, url)
		receiver.Global <- agentItem.Json()
		imageUrls = append(imageUrls, url)
		receiver.AppLog.Info("绘制图片", zap.String("url", url))
	})
	message.Status = gitee.PaintingStatusSuccess
	message.ImageUrls = strings.Join(imageUrls, ",")
end:
	// 保存绘画消息
	receiver.ChatDB.Create(message)
	// 下发结束
	finish := sdk.NewFinishLLMData()
	receiver.Writer(finish)
}

func (receiver *GiteeAI) GenStableDiffusionImage(request *http.Request, image painting.ImageCallback) {
	receiver.AppLog.Info("绘画请求", zap.Any("request", request))
	httpClient := &http.Client{}
	var paintingResponse gitee.PaintingResultResponse
	response, err := httpClient.Do(request)
	if err != nil {
		receiver.AppLog.Error(err.Error(), zap.Error(err))
		image("", err)
		return
	}
	defer response.Body.Close()
	decoder := json.NewDecoder(response.Body)
	err = decoder.Decode(&paintingResponse)
	if err != nil {
		receiver.AppLog.Error(err.Error(), zap.Error(err))
		image("", err)
		return
	}
	receiver.AppLog.Info("绘画响应", zap.Any("paintingResponse", paintingResponse))
	if len(paintingResponse.Data) == 0 {
		receiver.AppLog.Error("绘画响应为空", zap.Any("paintingResponse", paintingResponse))
		image("", err)
		return
	}
	for _, data := range paintingResponse.Data {
		image(data.Url, nil)
	}
}
