package ai_bot_platform

import (
	"bot/domain/entity"
	bot_context "bot/domain/service/bot-context"
	"bot/domain/service/bot/sdk/agent"
	"bot/domain/service/bot/sdk/arg"
	"bot/domain/service/bot/sdk/handler"
	"bot/domain/vo"
	"context"
	"strings"
	"sync"
)

type DeepSeek struct {
	*BaseBot
}

func NewDeepSeek(
	botCtx *bot_context.Context,
	base *BaseRepository) (*DeepSeek, error) {
	llmHandler := handler.NewLLMHandler(botCtx)
	d := &DeepSeek{BaseBot: NewBaseBot(botCtx, llmHandler, base)}
	return d, nil
}

func (receiver *DeepSeek) ChatRequest(ctx context.Context, chat *entity.ChatEntity) arg.IParam {
	chatRequest := arg.NewChatRequest(receiver.PlatformConfig)
	chatRequest.Url = "/chat/completions"
	chatRequest.Messages = chat.History
	if chat.Thinking {
		// 强制指定模型为 r1
		chatRequest.RequestParam.LLMPublicArgs.Model = "deepseek-reasoner"
	}
	return chatRequest
}

// ChatStream
// 获取流消息
func (receiver *DeepSeek) ChatStream(ctx context.Context, chat *entity.ChatEntity) {
	receiver.AgentStream(ctx, chat)
}

func (receiver *DeepSeek) AgentStream(ctx context.Context, chat *entity.ChatEntity) {
	param := receiver.ChatRequest(ctx, chat)
	star := &sync.Once{}
	end := &sync.Once{}
	Type := vo.AgentMarkdown
	Status := vo.AgentItemStatusDefault
	endThinking := false
	receiver.BaseBot.HandleAgent(ctx, chat, param, func(chatResponse *agent.Completion) (*agent.Item, bool, error) {
		content := ""
		content = chatResponse.GetContent()
		if chat.Thinking {
			if chatResponse.IsThinking() {
				if strings.TrimSpace(content) != "" {
					star.Do(func() {
						// 添加 thinking 渲染块
						content = ":::thinking\n\r" + content
						endThinking = false
						end = &sync.Once{}
						Type = vo.AgentThinking
					})
				}
			} else {
				end.Do(func() {
					content = "\n\n:::\n\r" + content
					// 重置思考模式输出
					endThinking = true
					Status = vo.AgentItemStatusStop
				})
			}
		}
		agentItem := agent.NewAgentItem(Type, Status, content)
		if endThinking {
			// 思考模式结束了 切换 数据类型
			Type = vo.AgentMarkdown
			Status = vo.AgentItemStatusDefault
			endThinking = false
		}
		return agentItem, chatResponse.IsFinished(), nil
	})
}
