package bot_file_impl

import (
	"bot/domain/entity"
	"bot/domain/repository"
	"bot/infrastructure/po"
	ai_bot_platform "bot/infrastructure/repository/ai-bot-platform"
	"context"
	"database/sql"
	"errors"
	"time"

	"gorm.io/gorm"
)

// FileImpl 文件仓储实现
type FileImpl struct {
	*ai_bot_platform.BaseRepository
}

// NewFileImpl 创建文件仓储实现
func NewFileImpl(baseRepository *ai_bot_platform.BaseRepository) repository.IFile {
	return &FileImpl{
		BaseRepository: baseRepository,
	}
}

// CreateFile 创建文件或目录
func (f *FileImpl) CreateFile(ctx context.Context, fileEntity *entity.FileEntity) (*po.FilePO, error) {
	if fileEntity == nil {
		return nil, errors.New("文件实体不能为空")
	}

	filePO := &po.FilePO{
		ID:        fileEntity.ID,
		PID:       fileEntity.PID,
		Name:      fileEntity.Name,
		Size:      fileEntity.Size,
		IsDir:     fileEntity.IsDir,
		CreatedAt: fileEntity.CreatedAt,
		UpdatedAt: fileEntity.UpdatedAt,
		DeletedAt: sql.NullTime{}, // 创建时设置为 NULL
	}

	if err := f.ChatDB.Create(filePO).Error; err != nil {
		return nil, err
	}

	return filePO, nil
}

// DeleteFile 软删除文件或目录
func (f *FileImpl) DeleteFile(ctx context.Context, fileEntity *entity.FileEntity) error {
	if fileEntity == nil || fileEntity.ID == "" {
		return errors.New("文件ID不能为空")
	}

	// 软删除：设置 DeletedAt 为当前时间
	now := time.Now()
	return f.ChatDB.Model(&po.FilePO{}).
		Where("id = ? AND deleted_at IS NULL", fileEntity.ID).
		Update("deleted_at", now).Error
}

// UpdateFile 更新文件信息
func (f *FileImpl) UpdateFile(ctx context.Context, fileEntity *entity.FileEntity) (*po.FilePO, error) {
	if fileEntity == nil || fileEntity.ID == "" {
		return nil, errors.New("文件ID不能为空")
	}

	// 构建更新数据
	updateData := make(map[string]interface{})
	if fileEntity.Name != "" {
		updateData["name"] = fileEntity.Name
	}
	if fileEntity.PID != "" {
		updateData["pid"] = fileEntity.PID
	}
	updateData["updated_at"] = fileEntity.UpdatedAt

	// 执行更新（只更新未删除的文件）
	if err := f.ChatDB.Model(&po.FilePO{}).
		Where("id = ? AND deleted_at IS NULL", fileEntity.ID).
		Updates(updateData).Error; err != nil {
		return nil, err
	}

	// 返回更新后的数据
	var filePO po.FilePO
	if err := f.ChatDB.Where("id = ? AND deleted_at IS NULL", fileEntity.ID).First(&filePO).Error; err != nil {
		return nil, err
	}

	return &filePO, nil
}

// QueryFile 查询单个文件信息（只查询未删除的文件）
func (f *FileImpl) QueryFile(ctx context.Context, fileEntity *entity.FileEntity) (*po.FilePO, error) {
	if fileEntity == nil {
		return nil, errors.New("查询参数不能为空")
	}

	var filePO po.FilePO
	query := f.ChatDB.Model(&po.FilePO{}).Where("deleted_at IS NULL")

	if fileEntity.ID != "" {
		query = query.Where("id = ?", fileEntity.ID)
	}
	if fileEntity.PID != "" {
		query = query.Where("pid = ?", fileEntity.PID)
	}
	if fileEntity.Name != "" {
		query = query.Where("name = ?", fileEntity.Name)
	}

	if err := query.First(&filePO).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("文件不存在")
		}
		return nil, err
	}

	return &filePO, nil
}

// QueryFiles 查询文件列表（只查询未删除的文件）
func (f *FileImpl) QueryFiles(ctx context.Context, fileEntity *entity.FileEntity) ([]*po.FilePO, error) {
	var files []*po.FilePO
	query := f.ChatDB.Model(&po.FilePO{}).Where("deleted_at IS NULL")

	if fileEntity != nil {
		if fileEntity.ID != "" {
			query = query.Where("id = ?", fileEntity.ID)
		}
		if fileEntity.PID != "" {
			query = query.Where("pid = ?", fileEntity.PID)
		}
		if fileEntity.Name != "" {
			query = query.Where("name LIKE ?", "%"+fileEntity.Name+"%")
		}
		// 根据IsDir字段过滤
		if fileEntity.IsDir {
			query = query.Where("is_dir = ?", true)
		}
	}

	if err := query.Order("is_dir DESC, name ASC").Find(&files).Error; err != nil {
		return nil, err
	}

	return files, nil
}

// QueryFilesByPID 根据父级ID查询文件列表（只查询未删除的文件）
func (f *FileImpl) QueryFilesByPID(ctx context.Context, pid string) ([]*po.FilePO, error) {
	var files []*po.FilePO

	query := f.ChatDB.Model(&po.FilePO{}).Where("deleted_at IS NULL")
	if pid == "" {
		// 查询根目录文件（pid为空或者为"0"）
		query = query.Where("pid = ? OR pid IS NULL", "")
	} else {
		query = query.Where("pid = ?", pid)
	}

	if err := query.Order("is_dir DESC, name ASC").Find(&files).Error; err != nil {
		return nil, err
	}

	return files, nil
}

// QueryDeletedFiles 查询已删除的文件列表
func (f *FileImpl) QueryDeletedFiles(ctx context.Context, fileEntity *entity.FileEntity) ([]*po.FilePO, error) {
	var files []*po.FilePO
	query := f.ChatDB.Model(&po.FilePO{}).Where("deleted_at IS NOT NULL")

	if fileEntity != nil {
		if fileEntity.ID != "" {
			query = query.Where("id = ?", fileEntity.ID)
		}
		if fileEntity.PID != "" {
			query = query.Where("pid = ?", fileEntity.PID)
		}
		if fileEntity.Name != "" {
			query = query.Where("name LIKE ?", "%"+fileEntity.Name+"%")
		}
		if fileEntity.IsDir {
			query = query.Where("is_dir = ?", true)
		}
	}

	if err := query.Order("deleted_at DESC, name ASC").Find(&files).Error; err != nil {
		return nil, err
	}

	return files, nil
}

// RestoreFile 恢复已删除的文件
func (f *FileImpl) RestoreFile(ctx context.Context, fileID string) error {
	if fileID == "" {
		return errors.New("文件ID不能为空")
	}

	// 恢复文件：将 DeletedAt 设置为 NULL
	return f.ChatDB.Model(&po.FilePO{}).
		Where("id = ? AND deleted_at IS NOT NULL", fileID).
		Update("deleted_at", nil).Error
}

// PermanentDeleteFile 彻底删除文件（物理删除）
func (f *FileImpl) PermanentDeleteFile(ctx context.Context, fileID string) error {
	if fileID == "" {
		return errors.New("文件ID不能为空")
	}

	// 物理删除：从数据库中彻底移除记录
	return f.ChatDB.Unscoped().Where("id = ?", fileID).Delete(&po.FilePO{}).Error
}

// CountFilesByStatus 统计文件数量（按删除状态）
func (f *FileImpl) CountFilesByStatus(ctx context.Context, includeDeleted bool) (int64, error) {
	var count int64
	query := f.ChatDB.Model(&po.FilePO{})

	if !includeDeleted {
		query = query.Where("deleted_at IS NULL")
	}

	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}

	return count, nil
}
