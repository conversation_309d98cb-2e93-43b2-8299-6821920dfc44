package gdmap

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"os"
	"strings"
)

type GDAdCodeCityCode struct {
	Name     string `json:"name"`
	AdCode   string `json:"adcode"`
	CityCode string `json:"citycode"`
}

type GDMap struct {
	Ak      string
	BaseURL string
	Version string

	// AdCodeCityCodeByAdCode 通过城市变吗查询对应城市信息
	AdCodeCityCodeByAdCode map[string]GDAdCodeCityCode
	// AdCodeCityCodeByName 通过城市名称查询对应城市信息
	AdCodeCityCodeByName map[string]GDAdCodeCityCode
}

func NewGDMap(ak string) *GDMap {
	// 读取 gdmap_adcode_citycode_by_adcode.json 和 gdmap_adcode_citycode_by_name.json
	gd := &GDMap{Ak: ak}
	file, err := os.ReadFile("dict/map_dict/gdmap_adcode_citycode_by_adcode.json")
	if err != nil {
		panic(err)
	}
	err = json.Unmarshal(file, &gd.AdCodeCityCodeByAdCode)
	if err != nil {
		panic(err)
	}
	file, err = os.ReadFile("dict/map_dict/gdmap_adcode_citycode_by_name.json")
	if err != nil {
		panic(err)
	}
	err = json.Unmarshal(file, &gd.AdCodeCityCodeByName)
	if err != nil {
		panic(err)
	}
	if ak == "" {
		return nil
	}
	return gd
}

type MapResult struct {
	Status   string
	Info     string
	Infocode string
}

type IpAddress struct {
	MapResult
	Province  string `json:"province"`
	City      string `json:"city"`
	Adcode    string `json:"adcode"`
	Rectangle string `json:"rectangle"`
}

func (receiver *IpAddress) Json() string {
	marshal, err := json.Marshal(receiver)
	if err != nil {
		return ""
	}
	return string(marshal)
}

type Weather struct {
	MapResult
	Lives    []Live   `json:"lives"`    //实况天气数据信息
	Forecast Forecast `json:"forecast"` // 预报天气信息数据
}

func (receiver *Weather) Json() string {
	marshal, err := json.Marshal(receiver)
	if err != nil {
		return ""
	}
	return string(marshal)
}

type Live struct {
	Province      string `json:"province,omitempty"`      //省份名
	City          string `json:"city,omitempty"`          //城市名
	Adcode        string `json:"adcode,omitempty"`        //区域编码
	Weather       string `json:"weather,omitempty"`       //天气现象（汉字描述）
	Temperature   string `json:"temperature,omitempty"`   //实时气温，单位：摄氏度
	Winddirection string `json:"winddirection,omitempty"` //风向描述
	Windpower     string `json:"windpower,omitempty"`     //风力级别，单位：级
	Humidity      string `json:"humidity,omitempty"`      // 空气湿度
	Reporttime    string `json:"reporttime,omitempty"`    //数据发布的时间
}

type Forecast struct {
	City       string `json:"city,omitempty"`       //城市名称
	Adcode     string `json:"adcode,omitempty"`     //城市编码
	Province   string `json:"province,omitempty"`   //省份名称
	Reporttime string `json:"reporttime,omitempty"` //预报发布时间
	Casts      []Cast `json:"casts,omitempty"`
}

type Cast struct {
	Date         string `json:"date,omitempty"`         //日期
	Week         string `json:"week,omitempty"`         //星期几
	Dayweather   string `json:"dayweather,omitempty"`   //白天天气现象
	Nightweather string `json:"nightweather,omitempty"` //晚上天气现象
	Daytemp      string `json:"daytemp,omitempty"`      //白天温度
	Nighttemp    string `json:"nighttemp,omitempty"`    //晚上温度
	Daywind      string `json:"daywind,omitempty"`      //白天风向
	Nightwind    string `json:"nightwind,omitempty"`    //晚上风向
	Daypower     string `json:"daypower,omitempty"`     //白天风力
	Nightpower   string `json:"nightpower,omitempty"`   // 晚上风力
}

type GeocodesResult struct {
	MapResult
	geocodes []Geocodes
}

type Geocodes struct {
	MapResult
	Country  string
	Province string
	City     string
	Citycode string
	District string
	Street   string
	Number   string
	Adcode   string
	Location string
	Level    string
}

// IpLocation
// 获取一个高德标准的城市名
func (gd *GDMap) IpLocation(ctx context.Context) (string, error) {
	address, err := gd.ipAddress()
	if err != nil {
		return "", err
	}
	return address.City, nil
}

func (gd *GDMap) IPGeo() string {
	address, err := gd.ipAddress()
	if err != nil {
		return ""
	}
	return address.Rectangle
}

func (gd *GDMap) CityWeather(ctx context.Context, city string) (string, error) {
	weather, err := gd.queryWeather(city)
	if err != nil {
		return "", err
	}
	// 生成简单的天气概括
	weatherSummary := ""
	if len(weather.Lives) > 0 {
		live := weather.Lives[0]
		weatherSummary = fmt.Sprintf(
			"现在%s%s的天气%s，气温%s度，%s风%s级，湿度%s%%。",
			live.Province, live.City,
			live.Weather,
			live.Temperature,
			live.Winddirection,
			live.Windpower,
			live.Humidity,
		)
	}
	return weatherSummary, nil
}

// ipAddress
// 根据当前ip进行定位
func (gd *GDMap) ipAddress() (*IpAddress, error) {
	url := fmt.Sprintf("https://restapi.amap.com/v3/ip?output=json&key=%s", gd.Ak)
	data, err := api[*IpAddress](http.MethodGet, url, nil)
	if err != nil {
		return nil, err
	}
	if data.Status != "1" {
		return nil, errors.New("Get IP address failed")
	}
	return data, nil
}

// QueryWeather
// 查询天气
// name 城市名称
func (gd *GDMap) queryWeather(name string) (*Weather, error) {
	adCodeCityCode := gd.QueryAdcode(name)
	url := fmt.Sprintf("https://restapi.amap.com/v3/weather/weatherInfo?city=%s&key=%s", adCodeCityCode, gd.Ak)
	var data *Weather
	var err error
	data, err = api[*Weather](http.MethodGet, url, nil)
	if err != nil {
		return nil, err
	}
	if data.Status != "1" {
		return nil, errors.New("Weather query failed")
	}
	return data, nil
}

// Geo
// 查询位置信息
// city 城市名称
// name 位置名称
func (gd *GDMap) Geo(city, name string) (*GeocodesResult, error) {
	url := fmt.Sprintf("https://restapi.amap.com/v3/geocode/geo?key=%s&city=%s&address=%s", gd.Ak, city, name)
	a, err := api[*GeocodesResult](http.MethodGet, url, nil)
	if err != nil {
		return nil, nil
	}
	return a, nil
}

func (gd *GDMap) QueryAdcode(city string) string {
	if adcode, ok := gd.AdCodeCityCodeByName[city]; ok {
		return adcode.AdCode
	}
	for key, code := range gd.AdCodeCityCodeByName {
		if strings.Contains(key, city) {
			return code.AdCode
		}
	}
	return ""
}

func api[T any](method, url string, body any) (T, error) {
	marshal, err := json.Marshal(body)
	var data T
	if err != nil {
		return data, err
	}
	request, err := http.NewRequest(method, url, bytes.NewBuffer(marshal))
	if err != nil {
		return data, err
	}
	response, err := http.DefaultClient.Do(request)

	if err != nil {
		return data, err
	}
	defer response.Body.Close()
	if err = json.NewDecoder(response.Body).Decode(&data); err != nil {
		return data, err
	}
	return data, nil
}
