package mcpclien

import (
	"bot/domain/entity"
	"github.com/mark3labs/mcp-go/client"
	"github.com/mark3labs/mcp-go/mcp"
)

// MCPClient MCP协议客户端
type MCPClient struct {
	*client.StdioMCPClient
	*mcp.InitializeResult
	*entity.McpEntity
}

type McpCall struct {
	Name      string         `json:"name"`
	Arguments map[string]any `json:"arguments"`
}

// NewMCPClient 创建新的MCP客户端
func NewMCPClient(mcpEntity *entity.McpEntity) *client.StdioMCPClient {
	// 把自定义环境信息转化为 k=v
	var envs []string
	for s, s2 := range mcpEntity.Env {
		envs = append(envs, s+"="+s2)
	}
	mcpClient, err := client.NewStdioMCPClient(mcpEntity.Command, envs, mcpEntity.Args...)
	if err != nil {
		panic(err.Error())
	}
	return mcpClient
}
