package ai_bot_mcp

import (
	"bot/domain/entity"
	"bot/domain/service/bot/sdk/fun"
	"bot/domain/vo"
	"bot/infrastructure/repository/ai-bot-common/mcpclien"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"github.com/mark3labs/mcp-go/mcp"
	"io"
	"os"
	"os/exec"
	"testing"
	"text/template"
	"time"
)

// TestRunExecutable 测试运行可执行程序并进行输入输出操作
func TestRunExecutable(t *testing.T) {
	// 可执行程序路径
	execPath := "./time"

	// 创建命令对象
	cmd := exec.Command(execPath)

	// 创建输入管道
	stdin, err := cmd.StdinPipe()
	if err != nil {
		t.Fatalf("无法创建标准输入管道: %v", err)
	}

	// 创建输出管道
	stdout, err := cmd.StdoutPipe()
	if err != nil {
		t.Fatalf("无法创建标准输出管道: %v", err)
	}

	// 收集错误输出
	var stderr bytes.Buffer
	cmd.Stderr = &stderr

	// 启动程序
	if err = cmd.Start(); err != nil {
		t.Fatalf("启动程序失败: %v", err)
	}

	jsonrpcRequest := mcp.JSONRPCRequest{}
	jsonrpcRequest.JSONRPC = mcp.JSONRPC_VERSION
	jsonrpcRequest.ID = 1
	jsonrpcRequest.Method = string(mcp.MethodToolsList)
	// 将输入数据编码为JSON
	inputData, err := json.Marshal(jsonrpcRequest)
	if err != nil {
		t.Fatalf("JSON编码失败: %v", err)
	}

	// 添加换行符，确保命令被处理
	inputData = append(inputData, '\n')

	t.Logf("发送的JSON数据: %s", string(inputData))

	// 向程序发送输入数据
	_, err = stdin.Write(inputData)
	if err != nil {
		t.Fatalf("写入输入数据失败: %v", err)
	}

	// 关闭输入，表示不再发送更多数据
	stdin.Close()

	// 设置读取输出的超时通道
	outputCh := make(chan string, 1)
	errorCh := make(chan error, 1)

	// 在goroutine中读取输出
	go func() {
		buf := new(bytes.Buffer)
		_, err := buf.ReadFrom(stdout)
		if err != nil {
			errorCh <- fmt.Errorf("读取输出失败: %v", err)
			return
		}
		outputCh <- buf.String()
	}()

	// 等待输出或超时
	var output string
	select {
	case output = <-outputCh:
		t.Logf("成功接收到输出")
	case err = <-errorCh:
		t.Fatalf("接收输出时出错: %v", err)
	case <-time.After(10 * time.Second):
		// 如果超时，尝试终止程序
		if err = cmd.Process.Kill(); err != nil {
			t.Logf("无法终止程序: %v", err)
		}
		t.Fatal("等待程序输出超时")
	}

	// 程序可能还在运行，尝试正常终止
	go func() {
		cmd.Wait()
	}()

	// 打印输出结果
	t.Logf("程序输出: %s", output)
	t.Logf("错误输出: %s", stderr.String())

	// 验证输出结果是合法的JSON
	if output == "" {
		t.Error("程序没有输出")
		return
	}

	var result map[string]interface{}
	if err := json.Unmarshal([]byte(output), &result); err != nil {
		t.Errorf("程序输出不是合法的JSON: %v, 输出: %s", err, output)
	} else {
		t.Logf("成功解析JSON响应")
	}

	// 确保程序终止
	if cmd.Process != nil {
		cmd.Process.Kill()
	}

	both, i, err := fun.ConvertMCPToolsResponseBoth(output)
	if err != nil {
		return
	}
	t.Log(both, i)
}

func TestClient(t *testing.T) {
	client := mcpclien.NewMCPClient(&entity.McpEntity{
		Command: "npx",
		Args:    []string{"-y", "@amap/amap-maps-mcp-server"},
		Env: map[string]string{
			"AMAP_MAPS_API_KEY": "05f0304a9f5ad3e34d522457fbd8ae7b",
		},
		Timeout: 10,
	})
	defer client.Close()

	request := mcp.InitializeRequest{}
	request.Params.ProtocolVersion = "1.0"
	request.Params.ClientInfo = mcp.Implementation{
		Name:    "test-client",
		Version: "1.0.0",
	}
	request.Params.Capabilities = mcp.ClientCapabilities{
		Roots: &struct {
			ListChanged bool `json:"listChanged,omitempty"`
		}{
			ListChanged: true,
		},
	}

	_, err := client.Initialize(context.Background(), request)
	if err != nil {
		t.Fatalf("Initialize failed: %v", err)
	}

	requestList := mcp.ListToolsRequest{}
	mcpMap := make(map[string][]mcp.Tool)
	callMCP, err := client.ListTools(context.Background(), requestList)
	if err != nil {
		t.Error(err.Error())
		return
	}
	t.Log(callMCP)
	mcpMap["test-client"] = callMCP.Tools
	marshal, err := json.Marshal(callMCP.Tools)
	if err != nil {
		t.Error(err.Error())
	}

	t.Log(string(marshal))
	j := `{
  "System": "系统提示文本",
  "Tools": [{
    "Name": "工具名",
    "Description": "功能描述",
    "InputSchema": {
      "Type": "object",
      "Properties": {"参数名": {"Description":"说明"}},
      "Required": ["必填参数"]
  }],
  "Messages": [{
    "Role": "user|assistant|tool",
    "Content": "消息内容",
    "ToolCalls": [{
      "Function": {"Name":"工具名", "Arguments":{}}
    }]
  },{
	"Role":"tool",
	"Content":"{
		  "maps_direction_transit_integrated" : "[{\"type\":\"text\",\"text\":\"Direction Transit Integrated failed: INVALID_PARAMS\"}]",
		  "maps_text_search" : "[{\"type\":\"text\",\"text\":\"{\\n  \\\"suggestion\\\": {\\n    \\\"keywords\\\": [],\\n    \\\"ciytes\\\": []\\n  },\\n  \\\"pois\\\": [\\n    {\\n      \\\"id\\\": \\\"B02F37T4NJ\\\",\\n      \\\"name\\\": \\\"深圳仙湖植物园\\\",\\n      \\\"address\\\": \\\"仙湖路160号\\\",\\n      \\\"typecode\\\": \\\"110103\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"B0FFGB242E\\\",\\n      \\\"name\\\": \\\"甘坑古镇\\\",\\n      \\\"address\\\": \\\"吉华街道甘坑社区甘李路18号\\\",\\n      \\\"typecode\\\": \\\"110200\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"B02F300115\\\",\\n      \\\"name\\\": \\\"广东梧桐山风景名胜区\\\",\\n      \\\"address\\\": \\\"莲塘街道罗沙路2076号\\\",\\n      \\\"typecode\\\": \\\"110202\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"B0FFFDUD6Q\\\",\\n      \\\"name\\\": \\\"凤谷鸣琴景区\\\",\\n      \\\"address\\\": \\\"望桐路梧桐山内\\\",\\n      \\\"typecode\\\": \\\"110000\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"B0H3DDR8Q3\\\",\\n      \\\"name\\\": \\\"恩上半山景区\\\",\\n      \\\"address\\\": \\\"莲塘街道罗沙路2076号广东梧桐山国家森林公园\\\",\\n      \\\"typecode\\\": \\\"110000\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"B0FFFDTVMV\\\",\\n      \\\"name\\\": \\\"梧桐烟云景区\\\",\\n      \\\"address\\\": \\\"望桐路梧桐山内\\\",\\n      \\\"typecode\\\": \\\"110000\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"B0GDFSX0T6\\\",\\n      \\\"name\\\": \\\"欢乐港湾\\\",\\n      \\\"address\\\": \\\"海天路与宝兴路交汇处\\\",\\n      \\\"typecode\\\": \\\"110200\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"B02F37U89L\\\",\\n      \\\"name\\\": \\\"南头古城\\\",\\n      \\\"address\\\": \\\"深南大道(南头古城地铁站C口步行147米)\\\",\\n      \\\"typecode\\\": \\\"110200\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"B02F38JKI1\\\",\\n      \\\"name\\\": \\\"深圳观澜湖旅游度假区\\\",\\n      \\\"address\\\": \\\"观澜高尔夫大道1号\\\",\\n      \\\"typecode\\\": \\\"110202\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"B02F302560\\\",\\n      \\\"name\\\": \\\"海上世界\\\",\\n      \\\"address\\\": \\\"望海路1128号\\\",\\n      \\\"typecode\\\": \\\"110200\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"B0FFFRCZ8O\\\",\\n      \\\"name\\\": \\\"杨梅坑\\\",\\n      \\\"address\\\": \\\"南澳镇桔钓沙\\\",\\n      \\\"typecode\\\": \\\"110200\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"B02F38J6TZ\\\",\\n      \\\"name\\\": \\\"华侨城欢乐海岸\\\",\\n      \\\"address\\\": \\\"滨海大道2008号\\\",\\n      \\\"typecode\\\": \\\"080501\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"B02F37UI6D\\\",\\n      \\\"name\\\": \\\"马峦山郊野公园\\\",\\n      \\\"address\\\": \\\"坪马线公路\\\",\\n      \\\"typecode\\\": \\\"110200\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"B02F37VHXJ\\\",\\n      \\\"name\\\": \\\"大鹏所城\\\",\\n      \\\"address\\\": \\\"大鹏新区南门东路\\\",\\n      \\\"typecode\\\": \\\"110200\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"B02F37V2O5\\\",\\n      \\\"name\\\": \\\"深圳世界之窗\\\",\\n      \\\"address\\\": \\\"深南大道9037号\\\",\\n      \\\"typecode\\\": \\\"110202\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"B02F37U4VE\\\",\\n      \\\"name\\\": \\\"深圳华侨城旅游度假区\\\",\\n      \\\"address\\\": \\\"杜鹃山\\\",\\n      \\\"typecode\\\": \\\"110202\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"B02F37VM2F\\\",\\n      \\\"name\\\": \\\"大芬油画村\\\",\\n      \\\"address\\\": \\\"大芬地铁站A1口步行460米\\\",\\n      \\\"typecode\\\": \\\"120302\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"B0FFHU49UZ\\\",\\n      \\\"name\\\": \\\"西涌旅游度假区\\\",\\n      \\\"address\\\": \\\"大鹏新区南澳街道西涌社区新屋村68号\\\",\\n      \\\"typecode\\\": \\\"110200|080300\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"B02F37V7IB\\\",\\n      \\\"name\\\": \\\"深圳野生动物园\\\",\\n      \\\"address\\\": \\\"西丽街道西丽路4066号\\\",\\n      \\\"typecode\\\": \\\"110102|110202\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"B02F38TG4Q\\\",\\n      \\\"name\\\": \\\"海上田园\\\",\\n      \\\"address\\\": \\\"沙井街道民主大道\\\",\\n      \\\"typecode\\\": \\\"110202\\\"\\n    }\\n  ]\\n}\"}]",
		  "maps_weather" : "[{\"type\":\"text\",\"text\":\"{\\n  \\\"city\\\": \\\"深圳市\\\",\\n  \\\"forecasts\\\": [\\n    {\\n      \\\"date\\\": \\\"2025-04-06\\\",\\n      \\\"week\\\": \\\"7\\\",\\n      \\\"dayweather\\\": \\\"多云\\\",\\n      \\\"nightweather\\\": \\\"多云\\\",\\n      \\\"daytemp\\\": \\\"26\\\",\\n      \\\"nighttemp\\\": \\\"18\\\",\\n      \\\"daywind\\\": \\\"北\\\",\\n      \\\"nightwind\\\": \\\"北\\\",\\n      \\\"daypower\\\": \\\"1-3\\\",\\n      \\\"nightpower\\\": \\\"1-3\\\",\\n      \\\"daytemp_float\\\": \\\"26.0\\\",\\n      \\\"nighttemp_float\\\": \\\"18.0\\\"\\n    },\\n    {\\n      \\\"date\\\": \\\"2025-04-07\\\",\\n      \\\"week\\\": \\\"1\\\",\\n      \\\"dayweather\\\": \\\"多云\\\",\\n      \\\"nightweather\\\": \\\"多云\\\",\\n      \\\"daytemp\\\": \\\"26\\\",\\n      \\\"nighttemp\\\": \\\"20\\\",\\n      \\\"daywind\\\": \\\"北\\\",\\n      \\\"nightwind\\\": \\\"北\\\",\\n      \\\"daypower\\\": \\\"1-3\\\",\\n      \\\"nightpower\\\": \\\"1-3\\\",\\n      \\\"daytemp_float\\\": \\\"26.0\\\",\\n      \\\"nighttemp_float\\\": \\\"20.0\\\"\\n    },\\n    {\\n      \\\"date\\\": \\\"2025-04-08\\\",\\n      \\\"week\\\": \\\"2\\\",\\n      \\\"dayweather\\\": \\\"多云\\\",\\n      \\\"nightweather\\\": \\\"阴\\\",\\n      \\\"daytemp\\\": \\\"26\\\",\\n      \\\"nighttemp\\\": \\\"21\\\",\\n      \\\"daywind\\\": \\\"北\\\",\\n      \\\"nightwind\\\": \\\"北\\\",\\n      \\\"daypower\\\": \\\"1-3\\\",\\n      \\\"nightpower\\\": \\\"1-3\\\",\\n      \\\"daytemp_float\\\": \\\"26.0\\\",\\n      \\\"nighttemp_float\\\": \\\"21.0\\\"\\n    },\\n    {\\n      \\\"date\\\": \\\"2025-04-09\\\",\\n      \\\"week\\\": \\\"3\\\",\\n      \\\"dayweather\\\": \\\"阴\\\",\\n      \\\"nightweather\\\": \\\"阵雨\\\",\\n      \\\"daytemp\\\": \\\"26\\\",\\n      \\\"nighttemp\\\": \\\"22\\\",\\n      \\\"daywind\\\": \\\"北\\\",\\n      \\\"nightwind\\\": \\\"北\\\",\\n      \\\"daypower\\\": \\\"1-3\\\",\\n      \\\"nightpower\\\": \\\"1-3\\\",\\n      \\\"daytemp_float\\\": \\\"26.0\\\",\\n      \\\"nighttemp_float\\\": \\\"22.0\\\"\\n    }\\n  ]\\n}\"}]"
		}"
	}
]
}`

	data := make(map[string]interface{})
	json.Unmarshal([]byte(j), &data)

	data["Tools"] = mcpMap
	data["Messages"] = []any{
		map[string]interface{}{
			"Role":    "tool",
			"Content": "{\n\t\t  \"maps_direction_transit_integrated\" : \"[{\\\"type\\\":\\\"text\\\",\\\"text\\\":\\\"Direction Transit Integrated failed: INVALID_PARAMS\\\"}]\",\n\t\t  \"maps_text_search\" : \"[{\\\"type\\\":\\\"text\\\",\\\"text\\\":\\\"{\\\\n  \\\\\\\"suggestion\\\\\\\": {\\\\n    \\\\\\\"keywords\\\\\\\": [],\\\\n    \\\\\\\"ciytes\\\\\\\": []\\\\n  },\\\\n  \\\\\\\"pois\\\\\\\": [\\\\n    {\\\\n      \\\\\\\"id\\\\\\\": \\\\\\\"B02F37T4NJ\\\\\\\",\\\\n      \\\\\\\"name\\\\\\\": \\\\\\\"深圳仙湖植物园\\\\\\\",\\\\n      \\\\\\\"address\\\\\\\": \\\\\\\"仙湖路160号\\\\\\\",\\\\n      \\\\\\\"typecode\\\\\\\": \\\\\\\"110103\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"id\\\\\\\": \\\\\\\"B0FFGB242E\\\\\\\",\\\\n      \\\\\\\"name\\\\\\\": \\\\\\\"甘坑古镇\\\\\\\",\\\\n      \\\\\\\"address\\\\\\\": \\\\\\\"吉华街道甘坑社区甘李路18号\\\\\\\",\\\\n      \\\\\\\"typecode\\\\\\\": \\\\\\\"110200\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"id\\\\\\\": \\\\\\\"B02F300115\\\\\\\",\\\\n      \\\\\\\"name\\\\\\\": \\\\\\\"广东梧桐山风景名胜区\\\\\\\",\\\\n      \\\\\\\"address\\\\\\\": \\\\\\\"莲塘街道罗沙路2076号\\\\\\\",\\\\n      \\\\\\\"typecode\\\\\\\": \\\\\\\"110202\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"id\\\\\\\": \\\\\\\"B0FFFDUD6Q\\\\\\\",\\\\n      \\\\\\\"name\\\\\\\": \\\\\\\"凤谷鸣琴景区\\\\\\\",\\\\n      \\\\\\\"address\\\\\\\": \\\\\\\"望桐路梧桐山内\\\\\\\",\\\\n      \\\\\\\"typecode\\\\\\\": \\\\\\\"110000\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"id\\\\\\\": \\\\\\\"B0H3DDR8Q3\\\\\\\",\\\\n      \\\\\\\"name\\\\\\\": \\\\\\\"恩上半山景区\\\\\\\",\\\\n      \\\\\\\"address\\\\\\\": \\\\\\\"莲塘街道罗沙路2076号广东梧桐山国家森林公园\\\\\\\",\\\\n      \\\\\\\"typecode\\\\\\\": \\\\\\\"110000\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"id\\\\\\\": \\\\\\\"B0FFFDTVMV\\\\\\\",\\\\n      \\\\\\\"name\\\\\\\": \\\\\\\"梧桐烟云景区\\\\\\\",\\\\n      \\\\\\\"address\\\\\\\": \\\\\\\"望桐路梧桐山内\\\\\\\",\\\\n      \\\\\\\"typecode\\\\\\\": \\\\\\\"110000\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"id\\\\\\\": \\\\\\\"B0GDFSX0T6\\\\\\\",\\\\n      \\\\\\\"name\\\\\\\": \\\\\\\"欢乐港湾\\\\\\\",\\\\n      \\\\\\\"address\\\\\\\": \\\\\\\"海天路与宝兴路交汇处\\\\\\\",\\\\n      \\\\\\\"typecode\\\\\\\": \\\\\\\"110200\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"id\\\\\\\": \\\\\\\"B02F37U89L\\\\\\\",\\\\n      \\\\\\\"name\\\\\\\": \\\\\\\"南头古城\\\\\\\",\\\\n      \\\\\\\"address\\\\\\\": \\\\\\\"深南大道(南头古城地铁站C口步行147米)\\\\\\\",\\\\n      \\\\\\\"typecode\\\\\\\": \\\\\\\"110200\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"id\\\\\\\": \\\\\\\"B02F38JKI1\\\\\\\",\\\\n      \\\\\\\"name\\\\\\\": \\\\\\\"深圳观澜湖旅游度假区\\\\\\\",\\\\n      \\\\\\\"address\\\\\\\": \\\\\\\"观澜高尔夫大道1号\\\\\\\",\\\\n      \\\\\\\"typecode\\\\\\\": \\\\\\\"110202\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"id\\\\\\\": \\\\\\\"B02F302560\\\\\\\",\\\\n      \\\\\\\"name\\\\\\\": \\\\\\\"海上世界\\\\\\\",\\\\n      \\\\\\\"address\\\\\\\": \\\\\\\"望海路1128号\\\\\\\",\\\\n      \\\\\\\"typecode\\\\\\\": \\\\\\\"110200\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"id\\\\\\\": \\\\\\\"B0FFFRCZ8O\\\\\\\",\\\\n      \\\\\\\"name\\\\\\\": \\\\\\\"杨梅坑\\\\\\\",\\\\n      \\\\\\\"address\\\\\\\": \\\\\\\"南澳镇桔钓沙\\\\\\\",\\\\n      \\\\\\\"typecode\\\\\\\": \\\\\\\"110200\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"id\\\\\\\": \\\\\\\"B02F38J6TZ\\\\\\\",\\\\n      \\\\\\\"name\\\\\\\": \\\\\\\"华侨城欢乐海岸\\\\\\\",\\\\n      \\\\\\\"address\\\\\\\": \\\\\\\"滨海大道2008号\\\\\\\",\\\\n      \\\\\\\"typecode\\\\\\\": \\\\\\\"080501\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"id\\\\\\\": \\\\\\\"B02F37UI6D\\\\\\\",\\\\n      \\\\\\\"name\\\\\\\": \\\\\\\"马峦山郊野公园\\\\\\\",\\\\n      \\\\\\\"address\\\\\\\": \\\\\\\"坪马线公路\\\\\\\",\\\\n      \\\\\\\"typecode\\\\\\\": \\\\\\\"110200\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"id\\\\\\\": \\\\\\\"B02F37VHXJ\\\\\\\",\\\\n      \\\\\\\"name\\\\\\\": \\\\\\\"大鹏所城\\\\\\\",\\\\n      \\\\\\\"address\\\\\\\": \\\\\\\"大鹏新区南门东路\\\\\\\",\\\\n      \\\\\\\"typecode\\\\\\\": \\\\\\\"110200\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"id\\\\\\\": \\\\\\\"B02F37V2O5\\\\\\\",\\\\n      \\\\\\\"name\\\\\\\": \\\\\\\"深圳世界之窗\\\\\\\",\\\\n      \\\\\\\"address\\\\\\\": \\\\\\\"深南大道9037号\\\\\\\",\\\\n      \\\\\\\"typecode\\\\\\\": \\\\\\\"110202\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"id\\\\\\\": \\\\\\\"B02F37U4VE\\\\\\\",\\\\n      \\\\\\\"name\\\\\\\": \\\\\\\"深圳华侨城旅游度假区\\\\\\\",\\\\n      \\\\\\\"address\\\\\\\": \\\\\\\"杜鹃山\\\\\\\",\\\\n      \\\\\\\"typecode\\\\\\\": \\\\\\\"110202\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"id\\\\\\\": \\\\\\\"B02F37VM2F\\\\\\\",\\\\n      \\\\\\\"name\\\\\\\": \\\\\\\"大芬油画村\\\\\\\",\\\\n      \\\\\\\"address\\\\\\\": \\\\\\\"大芬地铁站A1口步行460米\\\\\\\",\\\\n      \\\\\\\"typecode\\\\\\\": \\\\\\\"120302\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"id\\\\\\\": \\\\\\\"B0FFHU49UZ\\\\\\\",\\\\n      \\\\\\\"name\\\\\\\": \\\\\\\"西涌旅游度假区\\\\\\\",\\\\n      \\\\\\\"address\\\\\\\": \\\\\\\"大鹏新区南澳街道西涌社区新屋村68号\\\\\\\",\\\\n      \\\\\\\"typecode\\\\\\\": \\\\\\\"110200|080300\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"id\\\\\\\": \\\\\\\"B02F37V7IB\\\\\\\",\\\\n      \\\\\\\"name\\\\\\\": \\\\\\\"深圳野生动物园\\\\\\\",\\\\n      \\\\\\\"address\\\\\\\": \\\\\\\"西丽街道西丽路4066号\\\\\\\",\\\\n      \\\\\\\"typecode\\\\\\\": \\\\\\\"110102|110202\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"id\\\\\\\": \\\\\\\"B02F38TG4Q\\\\\\\",\\\\n      \\\\\\\"name\\\\\\\": \\\\\\\"海上田园\\\\\\\",\\\\n      \\\\\\\"address\\\\\\\": \\\\\\\"沙井街道民主大道\\\\\\\",\\\\n      \\\\\\\"typecode\\\\\\\": \\\\\\\"110202\\\\\\\"\\\\n    }\\\\n  ]\\\\n}\\\"}]\",\n\t\t  \"maps_weather\" : \"[{\\\"type\\\":\\\"text\\\",\\\"text\\\":\\\"{\\\\n  \\\\\\\"city\\\\\\\": \\\\\\\"深圳市\\\\\\\",\\\\n  \\\\\\\"forecasts\\\\\\\": [\\\\n    {\\\\n      \\\\\\\"date\\\\\\\": \\\\\\\"2025-04-06\\\\\\\",\\\\n      \\\\\\\"week\\\\\\\": \\\\\\\"7\\\\\\\",\\\\n      \\\\\\\"dayweather\\\\\\\": \\\\\\\"多云\\\\\\\",\\\\n      \\\\\\\"nightweather\\\\\\\": \\\\\\\"多云\\\\\\\",\\\\n      \\\\\\\"daytemp\\\\\\\": \\\\\\\"26\\\\\\\",\\\\n      \\\\\\\"nighttemp\\\\\\\": \\\\\\\"18\\\\\\\",\\\\n      \\\\\\\"daywind\\\\\\\": \\\\\\\"北\\\\\\\",\\\\n      \\\\\\\"nightwind\\\\\\\": \\\\\\\"北\\\\\\\",\\\\n      \\\\\\\"daypower\\\\\\\": \\\\\\\"1-3\\\\\\\",\\\\n      \\\\\\\"nightpower\\\\\\\": \\\\\\\"1-3\\\\\\\",\\\\n      \\\\\\\"daytemp_float\\\\\\\": \\\\\\\"26.0\\\\\\\",\\\\n      \\\\\\\"nighttemp_float\\\\\\\": \\\\\\\"18.0\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"date\\\\\\\": \\\\\\\"2025-04-07\\\\\\\",\\\\n      \\\\\\\"week\\\\\\\": \\\\\\\"1\\\\\\\",\\\\n      \\\\\\\"dayweather\\\\\\\": \\\\\\\"多云\\\\\\\",\\\\n      \\\\\\\"nightweather\\\\\\\": \\\\\\\"多云\\\\\\\",\\\\n      \\\\\\\"daytemp\\\\\\\": \\\\\\\"26\\\\\\\",\\\\n      \\\\\\\"nighttemp\\\\\\\": \\\\\\\"20\\\\\\\",\\\\n      \\\\\\\"daywind\\\\\\\": \\\\\\\"北\\\\\\\",\\\\n      \\\\\\\"nightwind\\\\\\\": \\\\\\\"北\\\\\\\",\\\\n      \\\\\\\"daypower\\\\\\\": \\\\\\\"1-3\\\\\\\",\\\\n      \\\\\\\"nightpower\\\\\\\": \\\\\\\"1-3\\\\\\\",\\\\n      \\\\\\\"daytemp_float\\\\\\\": \\\\\\\"26.0\\\\\\\",\\\\n      \\\\\\\"nighttemp_float\\\\\\\": \\\\\\\"20.0\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"date\\\\\\\": \\\\\\\"2025-04-08\\\\\\\",\\\\n      \\\\\\\"week\\\\\\\": \\\\\\\"2\\\\\\\",\\\\n      \\\\\\\"dayweather\\\\\\\": \\\\\\\"多云\\\\\\\",\\\\n      \\\\\\\"nightweather\\\\\\\": \\\\\\\"阴\\\\\\\",\\\\n      \\\\\\\"daytemp\\\\\\\": \\\\\\\"26\\\\\\\",\\\\n      \\\\\\\"nighttemp\\\\\\\": \\\\\\\"21\\\\\\\",\\\\n      \\\\\\\"daywind\\\\\\\": \\\\\\\"北\\\\\\\",\\\\n      \\\\\\\"nightwind\\\\\\\": \\\\\\\"北\\\\\\\",\\\\n      \\\\\\\"daypower\\\\\\\": \\\\\\\"1-3\\\\\\\",\\\\n      \\\\\\\"nightpower\\\\\\\": \\\\\\\"1-3\\\\\\\",\\\\n      \\\\\\\"daytemp_float\\\\\\\": \\\\\\\"26.0\\\\\\\",\\\\n      \\\\\\\"nighttemp_float\\\\\\\": \\\\\\\"21.0\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"date\\\\\\\": \\\\\\\"2025-04-09\\\\\\\",\\\\n      \\\\\\\"week\\\\\\\": \\\\\\\"3\\\\\\\",\\\\n      \\\\\\\"dayweather\\\\\\\": \\\\\\\"阴\\\\\\\",\\\\n      \\\\\\\"nightweather\\\\\\\": \\\\\\\"阵雨\\\\\\\",\\\\n      \\\\\\\"daytemp\\\\\\\": \\\\\\\"26\\\\\\\",\\\\n      \\\\\\\"nighttemp\\\\\\\": \\\\\\\"22\\\\\\\",\\\\n      \\\\\\\"daywind\\\\\\\": \\\\\\\"北\\\\\\\",\\\\n      \\\\\\\"nightwind\\\\\\\": \\\\\\\"北\\\\\\\",\\\\n      \\\\\\\"daypower\\\\\\\": \\\\\\\"1-3\\\\\\\",\\\\n      \\\\\\\"nightpower\\\\\\\": \\\\\\\"1-3\\\\\\\",\\\\n      \\\\\\\"daytemp_float\\\\\\\": \\\\\\\"26.0\\\\\\\",\\\\n      \\\\\\\"nighttemp_float\\\\\\\": \\\\\\\"22.0\\\\\\\"\\\\n    }\\\\n  ]\\\\n}\\\"}]\"\n\t\t}",
		},
	}

	v, _ := json.Marshal(data)
	t.Log(string(v))

	open, err := os.Open("D:\\workspace\\code\\go\\gpt-app-server\\bot-server\\infrastructure\\repository\\ai-bot-mcp\\mcp.txt")
	if err != nil {
		return
	}
	all, err := io.ReadAll(open)
	if err != nil {
		return
	}
	buffer := bytes.NewBuffer(all)
	mcpTemp := template.New("mcp")
	mcpTemp = mcpTemp.Funcs(template.FuncMap{
		"json": Json,
	})
	parse, err := mcpTemp.Parse(buffer.String())
	if err != nil {
		t.Fatalf("解析模板文件失败: %v", err)
	}

	var buf bytes.Buffer
	if err = parse.Execute(&buf, data); err != nil {
		t.Fatalf("执行模板失败: %v", err)
	}

	t.Log(buf.String())
}

func TestMcpTemplate(t *testing.T) {
	client := mcpclien.NewMCPClient(&entity.McpEntity{
		Command: "npx",
		Args:    []string{"-y", "@amap/amap-maps-mcp-server"},
		Env: map[string]string{
			"AMAP_MAPS_API_KEY": "05f0304a9f5ad3e34d522457fbd8ae7b",
		},
		Timeout: 10,
	})
	defer client.Close()

	request := mcp.InitializeRequest{}
	request.Params.ProtocolVersion = "1.0"
	request.Params.ClientInfo = mcp.Implementation{
		Name:    "test-client",
		Version: "1.0.0",
	}
	request.Params.Capabilities = mcp.ClientCapabilities{
		Roots: &struct {
			ListChanged bool `json:"listChanged,omitempty"`
		}{
			ListChanged: true,
		},
	}

	init, err := client.Initialize(context.Background(), request)
	if err != nil {
		t.Fatalf("Initialize failed: %v", err)
	}
	fmt.Println(init)
	requestList := mcp.ListToolsRequest{}
	callMCP, err := client.ListTools(context.Background(), requestList)
	if err != nil {
		t.Error(err.Error())
		return
	}
	t.Log(callMCP)

	data := make(map[string]interface{})
	data["McpTool"] = map[string]interface{}{
		"test": "Description",
	}
	data["Tools"] = map[string][]mcp.Tool{
		"test": callMCP.Tools,
	}

	data["Messages"] = []any{
		entity.HistoryEntity{
			Role:    vo.UserRoleType,
			Content: "sss",
		},
		entity.HistoryEntity{
			Role:      vo.AssistantRoleType,
			ToolCalls: "123",
		},
		entity.HistoryEntity{
			Role: vo.ToolRoleType,
			Tool: "sss",
		},
	}

	//json, err := json.MarshalIndent(data, "", "	")
	//if err != nil {
	//	t.Error(err.Error())
	//}
	//fmt.Println(string(json))
	absPath := "D:\\workspace\\code\\go\\gpt-app-server\\bot-server\\template\\agent\\model\\deepseek-call.txt"
	open, err := os.Open(absPath)
	if err != nil {
		t.Error(err.Error())
	}
	all, err := io.ReadAll(open)
	if err != nil {
		t.Error(err.Error())
	}
	buffer := bytes.NewBuffer(all)
	mcpTemp := template.New("mcp")
	mcpTemp = mcpTemp.Funcs(template.FuncMap{
		"json": Json,
	})

	parse, err := mcpTemp.Parse(buffer.String())
	if err != nil {
		t.Fatalf("解析模板文件失败: %v", err)
	}
	var buf bytes.Buffer
	if err = parse.Execute(&buf, data); err != nil {
		t.Fatalf("执行模板失败: %v", err)
		return
	}

	t.Log(buf.String())
}

func Json(data any) string {
	indent, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		return ""
	}
	return string(indent)
}
