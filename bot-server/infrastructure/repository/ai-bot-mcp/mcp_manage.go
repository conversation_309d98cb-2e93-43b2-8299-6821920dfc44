package ai_bot_mcp

import (
	"bot/domain/entity"
	"bot/domain/repository/mcp_repo"
	"bot/infrastructure/common/clean"
	"bot/infrastructure/po"
	"bot/infrastructure/repository/ai-bot-common/mcpclien"
	aibotplatform "bot/infrastructure/repository/ai-bot-platform"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"sync"

	"github.com/mark3labs/mcp-go/mcp"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

var once sync.Once
var clients map[string]*mcpclien.MCPClient

type MCPClientManage struct {
	*aibotplatform.BaseRepository
}

func NewMCPClientManage(repository *aibotplatform.BaseRepository) mcp_repo.IMcp {
	once.Do(func() {
		go func() {
			var datas []*po.McpPO
			if find := repository.ChatDB.Find(&datas); find.Error != nil {
				return
			}
			clients = make(map[string]*mcpclien.MCPClient)

			// 启动程序默认全打开 todo 后期优化 只运行启用的 mcp
			for _, data := range datas {
				if err := addCache(data); err != nil {
					// todo 给出日志 或者通知
					panic(err)
				}
			}

			fn := func() {
				for _, client := range clients {
					err := client.Close()
					if err != nil {
						fmt.Println("close client failed", zap.Error(err))
					}
				}
				fmt.Println("close all clients")
			}
			// 注册资源回收
			clean.Ch <- fn
		}()
	})
	return &MCPClientManage{
		BaseRepository: repository,
	}
}

func MCPPoToCMPEntity(data *po.McpPO) *entity.McpEntity {
	if data == nil {
		return nil
	}
	var args []string
	var envs map[string]string
	if data.Args != "" {
		args = strings.Split(data.Args, ",")
	}
	if data.Env != "" {
		json.Unmarshal([]byte(data.Env), &envs)
	}
	return &entity.McpEntity{
		ID:       data.ID,
		Name:     data.Name,
		Args:     args,
		Env:      envs,
		Command:  data.Command,
		Disabled: data.Disabled,
		Timeout:  data.Timeout,
	}
}

// Add 添加MCP
func (receiver *MCPClientManage) Add(mcp *entity.McpEntity) error {

	// 使用po包中定义的转换方法
	mcpPO := po.NewMcpPO(mcp)
	// 使用GORM创建记录
	if err := receiver.ChatDB.Create(mcpPO).Error; err != nil {
		return err
	}
	go func() {
		if err := addCache(mcpPO); err != nil {
			receiver.AppLog.Error("add cache failed", zap.Error(err))
		}
	}()
	// 成功添加以后 添加到全局缓存
	return nil
}

func (receiver *MCPClientManage) FindMCPsById(ids []string) []*mcpclien.MCPClient {
	var mcps []*mcpclien.MCPClient
	for _, id := range ids {
		if client, ok := clients[id]; ok {
			mcps = append(mcps, client)
		}
	}
	return mcps
}

// GetAllClients 获取所有MCP客户端
func (receiver *MCPClientManage) GetAllClients() []*mcpclien.MCPClient {
	var allClients []*mcpclien.MCPClient
	for _, client := range clients {
		allClients = append(allClients, client)
	}
	return allClients
}

// GetById 根据ID获取MCP
func (receiver *MCPClientManage) GetById(id string) (*entity.McpEntity, error) {
	var mcpPO po.McpPO

	// 使用GORM查询记录
	if err := receiver.ChatDB.Where("id = ?", id).First(&mcpPO).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("MCP不存在")
		}
		return nil, err
	}

	// 使用po包中定义的转换方法
	return po.McpPOToEntity(&mcpPO)
}

// GetAllPO 获取所有MCP的PO结构
func (receiver *MCPClientManage) GetAllPO() ([]*po.McpPO, error) {
	var mcpPOs []*po.McpPO

	// 使用GORM查询所有记录
	if err := receiver.ChatDB.Find(&mcpPOs).Error; err != nil {
		return nil, err
	}

	return mcpPOs, nil
}

// GetAll 获取所有MCP
func (receiver *MCPClientManage) GetAll() ([]*entity.McpEntity, error) {
	// 先获取所有PO结构
	mcpPOs, err := receiver.GetAllPO()
	if err != nil {
		return nil, err
	}

	// 转换为实体列表
	var results []*entity.McpEntity
	for _, mcpPO := range mcpPOs {
		entity, err := po.McpPOToEntity(mcpPO)
		if err != nil {
			continue
		}
		results = append(results, entity)
	}

	return results, nil
}

// Reload 重启 mcp 客户端
func (receiver *MCPClientManage) Reload(id string) error {
	mcpEntity, err := receiver.GetById(id)
	if err != nil {
		return err
	}
	if err = reload(mcpEntity); err != nil {
		return err
	}
	return nil
}

// Update 更新MCP
func (receiver *MCPClientManage) Update(mcp *entity.McpEntity) error {
	// 先检查记录是否存在
	var count int64
	if err := receiver.ChatDB.Model(&po.McpPO{}).Where("id = ?", mcp.ID).Count(&count).Error; err != nil {
		return err
	}

	if count == 0 {
		return errors.New("MCP不存在")
	}

	// 使用po包中定义的转换方法
	mcpPO := po.NewMcpPO(mcp)

	// 使用GORM更新记录
	if err := receiver.ChatDB.Save(mcpPO).Error; err != nil {
		return err
	}

	// 检查 mcp 运行情况

	return nil
}

// Delete 删除MCP
func (receiver *MCPClientManage) Delete(id string) error {
	// 使用GORM删除记录
	result := receiver.ChatDB.Delete(&po.McpPO{}, "id = ?", id)
	if result.Error != nil {
		return result.Error
	}

	// 检查是否找到并删除了记录
	if result.RowsAffected == 0 {
		return errors.New("MCP不存在")
	}

	// 更新对应缓存
	if _, ok := clients[id]; ok {
		err := clients[id].Close()
		if err != nil {
			return err
		}
	}
	delete(clients, id)
	return nil
}

// GetTools
// 获取指定 mcp 的可调用工具列表，以及定义列表
func (receiver *MCPClientManage) GetTools(data *entity.McpEntity) ([]mcp.Tool, error) {
	var client *mcpclien.MCPClient
	var ok bool
	if client, ok = clients[data.ID]; !ok {
		return nil, nil
	}
	request := mcp.ListToolsRequest{}
	tools, err := client.ListTools(context.Background(), request)
	if err != nil {
		return nil, err
	}
	return tools.Tools, nil
}

func (receiver *MCPClientManage) CallTool(data *entity.McpEntity, tool mcpclien.McpCall) *mcp.CallToolResult {
	var mcpClient *mcpclien.MCPClient
	var ok bool
	if mcpClient, ok = clients[data.ID]; !ok {
		return nil
	}
	request := mcp.CallToolRequest{}
	request.Params.Name = tool.Name
	request.Params.Arguments = tool.Arguments
	callTool, err := mcpClient.CallTool(context.Background(), request)
	if err != nil {
		return nil
	}
	return callTool
}

func (receiver *MCPClientManage) ClientInfo(id string) (*mcpclien.MCPClient, error) {
	if mcpClient, ok := clients[id]; ok {
		return mcpClient, nil
	}
	return nil, errors.New("not found")
}

func addCache(data *po.McpPO) error {
	var mcpEntity *entity.McpEntity
	if mcpEntity = MCPPoToCMPEntity(data); mcpEntity == nil {
		// 给出日志
		return nil
	}
	return add(mcpEntity)
}

func add(mcpEntity *entity.McpEntity) error {
	mcpClient := mcpclien.NewMCPClient(mcpEntity)
	request := mcp.InitializeRequest{}
	request.Params.ProtocolVersion = "1.0"
	request.Params.ClientInfo = mcp.Implementation{
		Name:    "client",
		Version: "1.0.0",
	}
	initialize, err := mcpClient.Initialize(context.Background(), request)
	if err != nil {
		return err
	}
	clients[mcpEntity.ID] = &mcpclien.MCPClient{
		StdioMCPClient:   mcpClient,
		InitializeResult: initialize,
		McpEntity:        mcpEntity,
	}
	return nil
}

func reload(mcpEntity *entity.McpEntity) error {

	// 检查当前的客户端是否存在
	if mcpClient, ok := clients[mcpEntity.ID]; ok {
		err := mcpClient.Close()
		if err != nil {
			return err
		}
	}

	// 创建新的客户端
	mcpClient := mcpclien.NewMCPClient(mcpEntity)
	request := mcp.InitializeRequest{}
	request.Params.ProtocolVersion = "1.0"
	request.Params.ClientInfo = mcp.Implementation{
		Name:    "client",
		Version: "1.0.0",
	}
	initialize, err := mcpClient.Initialize(context.Background(), request)
	if err != nil {
		return err
	}
	clients[mcpEntity.ID] = &mcpclien.MCPClient{
		StdioMCPClient:   mcpClient,
		InitializeResult: initialize,
		McpEntity:        mcpEntity,
	}
	return nil
}
