package po

import (
	"bot/domain/entity"
	"bot/domain/service/bot/sdk/sse"
	"database/sql"
	"encoding/json"
	"time"

	_ "gorm.io/gorm"
)

type ConversationPO struct {
	ID        string    `gorm:"primaryKey" json:"id"`
	Picture   string    `gorm:"column:picture" json:"picture"`
	Title     string    `gorm:"column:title" json:"title"`
	LastMsg   string    `gorm:"column:last_msg" json:"lastMsg"`
	UpdatedAt time.Time `gorm:"column:updated_at;autoUpdateTime" json:"updatedAt"`
}

func (receiver *ConversationPO) TableName() string {
	return "bot_conversation"
}

// MessagePO
// 所有回话的消息数据存储
type MessagePO struct {
	ID             string `gorm:"primaryKey" json:"id"`
	ConversationID string `gorm:"column:conversation_id" json:"conversationID"` // 所属会话ID
	Picture        string `gorm:"column:picture" json:"picture"`                // 消息头像，后续可能会用上
	ReplyMsgID     string `gorm:"column:reply_msg_id" json:"replyMsgID"`        // 回复消息ID
	Role           string `gorm:"column:role" json:"role"`                      // 消息角色 一般消息角色仅包含 user assistant 两种
	MessageType    string `gorm:"column:message_type" json:"messageType"`       // 消息类型
	Ex             string `gorm:"column:ex;default:{}" json:"ex"`               // 消息扩展字段，扩展信息字端应该更具需求，专门整理文档来记录
	Content        string `gorm:"column:content" json:"content"`                // 消息文本内容
}

func (receiver *MessagePO) TableName() string {
	return "bot_message"
}

func (receiver *MessagePO) SetEx(ex *entity.MessageExtendEntity) {
	if ex != nil {
		receiver.Ex = ex.Ex()
	}
}

func (receiver *MessagePO) GetEx() *entity.MessageExtendEntity {
	data := new(entity.MessageExtendEntity)
	if receiver.Ex != "" {
		if e := json.Unmarshal([]byte(receiver.Ex), data); e != nil {
			panic(e)
		}
	}
	return data
}

// SetError
// 设置消息的错误信息
func (receiver *MessagePO) SetError(err error) {
	if err == nil {
		return
	}
	data := receiver.GetEx()
	data.DebugINFO.Status = sse.Error
	data.DebugINFO.ErrMsg = err.Error()
	if receiver.Content == "" {
		receiver.Content = err.Error()
	}
	receiver.SetEx(data)
}

//	Plugin
//
// BOT 插件实体
type PluginPO struct {
	ID         string `gorm:"primaryKey" json:"id"`
	Name       string `gorm:"column:name" json:"name"`              // 插件名
	PlatformID string `gorm:"column:platform_id" json:"platformID"` // 插件平台
	Code       string `gorm:"column:code" json:"code"`              // 插件代码 应该更具插件代码实现客户端插件功能和服务端的借口调用对应，插件Code应该保持唯一
	Icon       string `gorm:"column:icon" json:"icon"`              // 插件图标
	ConfigView string `gorm:"column:config_view" json:"configView"` // 插件配置面板
	Props      string `gorm:"column:props" json:"props"`            // 插件配置面板属性
	Status     bool   `gorm:"column:status" json:"status"`          // 插件状态
}

func (receiver *PluginPO) TableName() string {
	return "bot_plugin"
}

// PlatformPO
// 存储 ai-bot 所有可调用平台
type PlatformPO struct {
	ID              string `gorm:"primaryKey" json:"id"`
	Icon            string `gorm:"column:icon" json:"icon"`                         // 平台图标 后续可能会用上
	Name            string `gorm:"column:name" json:"name"`                         // 平台自定义名称
	Type            string `gorm:"column:type" json:"type"`                         // 平台类型
	Config          string `gorm:"column:config" json:"config"`                     // 平台配置
	ConfigView      string `gorm:"column:config_view" json:"configView"`            // 平台配置面板
	QuickConfigView string `gorm:"column:quick_config_view" json:"quickConfigView"` // 平台快捷配置慢半
	Current         bool   `gorm:"column:current" json:"current"`                   // 系统当前默认基座平台
}

func (receiver *PlatformPO) TableName() string {
	return "bot_platform"
}

// SettingPO
// 系统设置表
// 系统设置表里面应该只维护一条数据记录，value 数据为json提供app运行期间的配置服务
// 后续可能通过多条数据实现历史配置操作
type SettingPO struct {
	ID    string `gorm:"primaryKey" json:"id"`
	Value string `gorm:"column:value" json:"value"`
}

func (receiver *SettingPO) TableName() string {
	return "bot_setting"
}

type McpPO struct {
	ID       string `gorm:"primaryKey" json:"id"`
	Type     string `gorm:"column:type" json:"type"`         // mcp 协议类型 stdio/sse
	Name     string `gorm:"column:name" json:"name"`         // mcp 名称
	Command  string `gorm:"column:command" json:"command"`   // 执行命令
	Args     string `gorm:"column:args" json:"args"`         // 命令参数
	Env      string `gorm:"column:env" json:"env"`           // 环境变量
	Disabled int    `gorm:"column:disabled" json:"disabled"` // 是否启用
	Timeout  int    `gorm:"column:timeout" json:"timeout"`   // 接口超时配置
	Tools    string `gorm:"column:tools" json:"tools"`       // mcp 可调用工具列表
}

func (receiver *McpPO) TableName() string {
	return "bot_mcp"
}

type FilePO struct {
	ID        string       `gorm:"primaryKey" json:"id"`                              // 文件ID
	PID       string       `gorm:"column:pid" json:"pid"`                             // 父级ID
	Name      string       `gorm:"column:name" json:"name"`                           // 文件名称
	Size      int64        `gorm:"column:size" json:"size"`                           // 文件大小
	IsDir     bool         `gorm:"column:is_dir" json:"isDir"`                        // 是否是目录
	CreatedAt time.Time    `gorm:"column:created_at;autoCreateTime" json:"createdAt"` // 创建时间
	UpdatedAt time.Time    `gorm:"column:updated_at;autoUpdateTime" json:"updatedAt"` // 更新时间
	DeletedAt sql.NullTime `gorm:"column:deleted_at" json:"deletedAt"`                // 删除时间
}

func (receiver *FilePO) TableName() string {
	return "bot_file"
}

func (receiver *FilePO) GetId() string {
	return receiver.ID
}

func (receiver *FilePO) GetPid() string {
	return receiver.PID
}

func (receiver *FilePO) GetName() string {
	return receiver.Name
}

// PaintingPO
// 绘画实体 记录绘画的提示词、图片URL、创建时间、更新时间、删除时间
type PaintingPO struct {
	ID        string       `gorm:"primaryKey" json:"id"`                              // 绘画ID
	Title     string       `gorm:"column:title" json:"title"`                         // 绘画名称
	Prompt    string       `gorm:"column:prompt" json:"prompt"`                       // 绘画提示词
	Status    string       `gorm:"column:status" json:"status"`                       // 绘画状态
	ImageUrls string       `gorm:"column:image_urls" json:"imageUrls"`                // 绘画图片URL 多个图片URL用逗号分隔
	CreatedAt time.Time    `gorm:"column:created_at;autoCreateTime" json:"createdAt"` // 创建时间
	UpdatedAt time.Time    `gorm:"column:updated_at;autoUpdateTime" json:"updatedAt"` // 更新时间
	DeletedAt sql.NullTime `gorm:"column:deleted_at" json:"deletedAt"`                // 删除时间
}

func (receiver *PaintingPO) TableName() string {
	return "bot_painting"
}

// PaintingNegativePromptPO
// 绘画负面提示词实体 记录绘画的负面提示词、创建时间、更新时间、删除时间
type PaintingNegativePromptPO struct {
	ID        string       `gorm:"primaryKey" json:"id"`                              // 绘画负面提示词ID
	Prompt    string       `gorm:"column:prompt" json:"prompt"`                       // 绘画负面提示词
	CreatedAt time.Time    `gorm:"column:created_at;autoCreateTime" json:"createdAt"` // 创建时间
	UpdatedAt time.Time    `gorm:"column:updated_at;autoUpdateTime" json:"updatedAt"` // 更新时间
	DeletedAt sql.NullTime `gorm:"column:deleted_at" json:"deletedAt"`                // 删除时间
}

func (receiver *PaintingNegativePromptPO) TableName() string {
	return "bot_painting_negative_prompt"
}

// PaintingConfigPO
// 绘画提示词配置实体 记录绘画的提示词配置、创建时间、更新时间、删除时间
type PaintingConfigPO struct {
	ID        string       `gorm:"primaryKey" json:"id"`
	Config    string       `gorm:"column:config" json:"config"`                       // 绘画提示词配置 json 格式
	CreatedAt time.Time    `gorm:"column:created_at;autoCreateTime" json:"createdAt"` // 创建时间
	UpdatedAt time.Time    `gorm:"column:updated_at;autoUpdateTime" json:"updatedAt"` // 更新时间
	DeletedAt sql.NullTime `gorm:"column:deleted_at" json:"deletedAt"`                // 删除时间
}

func (receiver *PaintingConfigPO) TableName() string {
	return "bot_painting_config"
}
