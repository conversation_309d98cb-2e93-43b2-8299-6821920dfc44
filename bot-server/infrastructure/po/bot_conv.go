package po

import (
	"bot/domain/entity"
	"encoding/json"
	"errors"
	"strings"
)

func NewConversationPO(cov *entity.ConversationEntity) *ConversationPO {
	return &ConversationPO{
		ID:      cov.ID,
		Picture: cov.Picture,
		LastMsg: cov.LastMsg,
		Title:   cov.Title,
	}
}

func NewPluginPO(cov *entity.PluginEntity) *PluginPO {
	return &PluginPO{
		ID:         cov.ID,
		Name:       cov.Name,
		Icon:       cov.Icon,
		Code:       cov.Code,
		PlatformID: cov.PlatformID,
		Props:      cov.Props,
		ConfigView: cov.ConfigView,
	}
}

func NewPlatformPO(cov *entity.PlatformEntity) *PlatformPO {
	return &PlatformPO{
		ID:              cov.ID,
		Name:            cov.Name,
		Icon:            cov.Icon,
		Type:            cov.Type,
		Config:          cov.Config,
		ConfigView:      cov.ConfigView,
		QuickConfigView: cov.QuickConfigView,
		Current:         cov.Current,
	}
}

func NewPlatformPOs(coves []*entity.PlatformEntity) []*PlatformPO {
	var poses []*PlatformPO
	for _, cov := range coves {
		poses = append(poses, NewPlatformPO(cov))
	}
	return poses
}

func NewChatToMessagePO(cov *entity.ChatEntity) *MessagePO {
	return &MessagePO{
		ID:             cov.ID,
		ConversationID: cov.ConversationID,
		ReplyMsgID:     cov.ReplyMsgID,
		Role:           cov.Role,
		MessageType:    cov.MessageType,
		Content:        cov.Message,
	}
}

func NewMessagePO(cov *entity.MessageEntity) *MessagePO {
	return &MessagePO{
		ID:             cov.ID,
		ConversationID: cov.ConversationID,
		ReplyMsgID:     cov.ReplyMsgID,
		Role:           cov.Role,
		MessageType:    cov.MessageType,
		Content:        cov.Content,
	}
}

func NewMcpPO(cov *entity.McpEntity) *McpPO {
	env, _ := json.Marshal(cov.Env)
	tols, _ := json.Marshal(cov.Tools)
	return &McpPO{
		ID:       cov.ID,
		Name:     cov.Name,
		Command:  cov.Command,
		Disabled: cov.Disabled,
		Timeout:  cov.Timeout,
		Env:      string(env),
		Args:     strings.Join(cov.Args, ","),
		Tools:    string(tols),
	}
}

// McpPOToEntity 将McpPO转换为McpEntity
func McpPOToEntity(po *McpPO) (*entity.McpEntity, error) {
	entity := &entity.McpEntity{
		ID:       po.ID,
		Name:     po.Name,
		Command:  po.Command,
		Disabled: po.Disabled,
		Timeout:  po.Timeout,
	}

	// 解析Args
	if po.Args != "" {
		entity.Args = strings.Split(po.Args, ",")
	}

	// 解析Env
	if po.Env != "" {
		if err := json.Unmarshal([]byte(po.Env), &entity.Env); err != nil {
			return nil, errors.New("解析Env字段失败")
		}
	}

	// 解析Tools
	if po.Tools != "" {
		if err := json.Unmarshal([]byte(po.Tools), &entity.Tools); err != nil {
			return nil, errors.New("解析Tools字段失败")
		}
	}

	return entity, nil
}
