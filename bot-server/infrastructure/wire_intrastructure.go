package infrastructure

import (
	"bot/infrastructure/common/db"
	ai_bot_mcp "bot/infrastructure/repository/ai-bot-mcp"
	ai_bot_platform "bot/infrastructure/repository/ai-bot-platform"
	bot_manmge_impl "bot/infrastructure/repository/bot-manmge-impl"
	bot_painting_impl "bot/infrastructure/repository/bot-painting-impl"

	"github.com/google/wire"
)

var Provider = wire.NewSet(
	db.NewChatDB,
	ai_bot_platform.NewBaseRepository,
	bot_manmge_impl.NewManageImpl,
	bot_painting_impl.NewPaintingConfigImpl,
	bot_painting_impl.NewPaintingNegativePromptImpl,
	bot_painting_impl.NewPaintingRecordImpl,
	ai_bot_mcp.NewMCPClientManage,
)
