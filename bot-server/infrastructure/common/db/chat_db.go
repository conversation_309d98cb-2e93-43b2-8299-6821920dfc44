package db

import (
	"bot/conf"
	"bot/infrastructure/common/logs"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"time"
)

type ChatDB struct {
	*gorm.DB
}

func NewChatDB(c *conf.Configuration, log *logs.AppLog) *ChatDB {
	var err error
	var DB *gorm.DB
	DB, err = gorm.Open(sqlite.Open(c.Database), Config(log))
	if err != nil {
		logs.Log.Panic(err.Error())
	}
	sqlDB, err := DB.DB()
	if err != nil {
		logs.Log.Panic(err.Error())
	}

	if err := sqlDB.Ping(); err != nil {
		logs.Log.Panic(err.Error())
	}
	// SetMaxIdleConns sets the maximum number of connections in the idle connection pool.
	sqlDB.SetMaxIdleConns(10)
	// SetMaxOpenConns sets the maximum number of open connections to the database.
	sqlDB.SetMaxOpenConns(100)
	// SetConnMaxLifetime sets the maximum amount of time a connection may be reused.
	sqlDB.SetConnMaxLifetime(time.Hour)
	return &ChatDB{DB: DB}
}
