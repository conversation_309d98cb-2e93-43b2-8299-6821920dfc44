package db

import (
	"bot/conf"
	"bot/infrastructure/common/logs"
	"fmt"
	"go.uber.org/zap"
	"gorm.io/driver/mysql"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"

	"time"
)

func NewMysqlGorm(cnf *conf.Configuration, log *logs.AppLog) *gorm.DB {
	var err error
	var DB *gorm.DB
	DB, err = gorm.Open(mysql.New(mysql.Config{
		DSN: cnf.Database,
	}), Config(log))
	sqlDB, err := DB.DB()
	if err != nil {
		zap.L().Error(err.Error())
		return nil
	}
	if err = sqlDB.Ping(); err != nil {
		zap.L().Error(err.Error())
		return nil
	}
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetConnMaxLifetime(time.Hour)
	return DB
}

func NewPostgresqlGorm(cnf *conf.Configuration, log *logs.AppLog) *gorm.DB {
	var err error
	var DB *gorm.DB
	DB, err = gorm.Open(postgres.New(
		postgres.Config{
			DSN:                  cnf.Database,
			PreferSimpleProtocol: true, // disables implicit prepared statement usage
		}), Config(log))
	sqlDB, err := DB.DB()
	if err != nil {
		zap.L().Error(err.Error())
		return nil
	}
	if err = sqlDB.Ping(); err != nil {
		zap.L().Error(err.Error())
		return nil
	}
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetConnMaxLifetime(time.Hour)
	return DB
}

func Config(log *logs.AppLog) *gorm.Config {
	newLogger := logger.New(
		NewGLog(log),
		logger.Config{
			SlowThreshold:             time.Millisecond, // Slow SQL threshold
			LogLevel:                  logger.Info,      // Log level
			IgnoreRecordNotFoundError: false,            // Ignore ErrRecordNotFound error for logger
			ParameterizedQueries:      false,            // Don't include params in the SQL log
			Colorful:                  false,            // Disable color
		},
	)
	return &gorm.Config{
		Logger:                                   newLogger,
		DisableAutomaticPing:                     false,
		DisableForeignKeyConstraintWhenMigrating: true,
		NamingStrategy: schema.NamingStrategy{
			SingularTable: true, // use singular table name, table for `User` would be `user` with this option enabled
			NoLowerCase:   true, // skip the snake_casing of names
		},
	}
}

type GormLog struct {
	*zap.Logger
}

func (l GormLog) Printf(format string, args ...interface{}) {
	sprintf := "\r" + fmt.Sprintf(format, args...)
	l.Info(sprintf)
}

func NewGLog(log *logs.AppLog) *GormLog {
	return &GormLog{log.WithOptions(zap.AddCallerSkip(4))}
}
