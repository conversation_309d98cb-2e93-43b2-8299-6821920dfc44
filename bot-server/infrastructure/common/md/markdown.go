package md

import (
	"github.com/<PERSON><PERSON><PERSON><PERSON><PERSON>/html-to-markdown/v2/converter"
	"github.com/<PERSON><PERSON><PERSON><PERSON>/html-to-markdown/v2/plugin/base"
	"github.com/<PERSON><PERSON><PERSON><PERSON><PERSON>/html-to-markdown/v2/plugin/commonmark"
)

// HTMLToMarkdown 将HTML字符串转换为Markdown
func HTMLToMarkdown(html string) (string, error) {
	// 创建converter并应用基础插件
	conv := converter.NewConverter(
		converter.WithPlugins(
			base.NewBasePlugin(),
			commonmark.NewCommonmarkPlugin(),
		),
	)
	// 转换HTML为Markdown
	return conv.ConvertString(html)
}
