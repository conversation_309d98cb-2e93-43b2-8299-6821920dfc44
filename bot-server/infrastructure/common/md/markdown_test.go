package md

import (
	"fmt"
	"strings"
	"testing"
)

// TestHTMLToMarkdown 测试将HTML转换为Markdown的基本功能
func TestHTMLToMarkdown(t *testing.T) {
	// 测试用例1: 基本HTML转Markdown
	t.Run("基本HTML转换", func(t *testing.T) {
		html := `<h1>标题</h1><p>这是<strong>加粗</strong>和<em>斜体</em>文本</p>`
		expected := "# 标题\n\n这是**加粗**和*斜体*文本"

		markdown, err := HTMLToMarkdown(html)
		if err != nil {
			t.Fatalf("HTML转Markdown失败: %v", err)
		}

		fmt.Printf("\n=== 基本HTML转换 ===\n")
		fmt.Printf("HTML 输入:\n%s\n", html)
		fmt.Printf("Markdown 输出:\n%s\n", markdown)
		fmt.Printf("期望输出:\n%s\n", expected)
		fmt.Printf("==================\n")

		// 去除所有空白字符进行比较
		cleanedExpected := strings.ReplaceAll(strings.ReplaceAll(expected, " ", ""), "\n", "")
		cleanedMarkdown := strings.ReplaceAll(strings.ReplaceAll(markdown, " ", ""), "\n", "")

		if cleanedMarkdown != cleanedExpected {
			t.Errorf("转换结果不匹配.\n期望: %s\n实际: %s", expected, markdown)
		}
	})

	// 测试用例2: 列表转换
	t.Run("列表转换", func(t *testing.T) {
		html := `<ul><li>项目1</li><li>项目2</li><li>项目3</li></ul>`

		markdown, err := HTMLToMarkdown(html)
		if err != nil {
			t.Fatalf("HTML转Markdown失败: %v", err)
		}

		fmt.Printf("\n=== 列表转换 ===\n")
		fmt.Printf("HTML 输入:\n%s\n", html)
		fmt.Printf("Markdown 输出:\n%s\n", markdown)
		fmt.Printf("==================\n")

		if !strings.Contains(markdown, "- 项目1") || !strings.Contains(markdown, "- 项目2") || !strings.Contains(markdown, "- 项目3") {
			t.Errorf("列表转换失败: %s", markdown)
		}
	})

	// 测试用例3: 链接转换
	t.Run("链接转换", func(t *testing.T) {
		html := `<a href="https://example.com">示例链接</a>`
		expected := "[示例链接](https://example.com)"

		markdown, err := HTMLToMarkdown(html)
		if err != nil {
			t.Fatalf("HTML转Markdown失败: %v", err)
		}

		fmt.Printf("\n=== 链接转换 ===\n")
		fmt.Printf("HTML 输入:\n%s\n", html)
		fmt.Printf("Markdown 输出:\n%s\n", markdown)
		fmt.Printf("期望输出:\n%s\n", expected)
		fmt.Printf("==================\n")

		if !strings.Contains(markdown, expected) {
			t.Errorf("链接转换失败.\n期望包含: %s\n实际: %s", expected, markdown)
		}
	})
}

// TestComplexHTMLToMarkdown 测试复杂HTML文档的转换
func TestComplexHTMLToMarkdown(t *testing.T) {
	htmlContent := `
	<html>
		<body>
			<h1>HTML转Markdown测试</h1>
			<p>这是一个<strong>加粗文本</strong>和<em>斜体文本</em>。</p>
			<ul>
				<li>列表项 1</li>
				<li>列表项 2</li>
				<li>列表项 3</li>
			</ul>
			<a href="https://github.com">GitHub链接</a>
		</body>
	</html>
	`

	t.Run("复杂HTML文档转换", func(t *testing.T) {
		markdown, err := HTMLToMarkdown(htmlContent)
		if err != nil {
			t.Fatalf("复杂HTML转Markdown失败: %v", err)
		}

		fmt.Printf("\n=== 复杂HTML文档转换 ===\n")
		fmt.Printf("HTML 输入:\n%s\n", htmlContent)
		fmt.Printf("Markdown 输出:\n%s\n", markdown)
		fmt.Printf("==================\n")

		// 检查关键元素是否存在
		mustContain := []string{
			"# HTML转Markdown测试",
			"**加粗文本**",
			"*斜体文本*",
			"- 列表项",
			"[GitHub链接](https://github.com)",
		}

		for _, text := range mustContain {
			if !strings.Contains(markdown, text) {
				t.Errorf("转换结果中缺少关键元素 '%s'\n实际结果:\n%s", text, markdown)
			}
		}
	})
}
