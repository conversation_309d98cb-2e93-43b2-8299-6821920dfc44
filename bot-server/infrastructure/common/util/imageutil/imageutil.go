package imageutil

import (
	"encoding/base64"
	"fmt"
	"mime/multipart"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// UploadResult 图片上传结果
type UploadResult struct {
	Filename string // 原始文件名
	SaveName string // 保存的文件名（带时间戳）
	Path     string // 完整的HTTP访问路径
	Error    error
}

// GetBaseURL 获取完整的基础URL
func GetBaseURL(c *gin.Context) string {
	scheme := "http"
	if c.Request.TLS != nil || c.<PERSON>Header("X-Forwarded-Proto") == "https" {
		scheme = "https"
	}
	host := c.Request.Host
	return fmt.Sprintf("%s://%s", scheme, host)
}

// GenerateFileName 生成带时间戳的文件名
func GenerateFileName(originalName string) string {
	// 获取文件扩展名
	ext := filepath.Ext(originalName)
	// 获取文件名（不含扩展名）
	nameWithoutExt := strings.TrimSuffix(originalName, ext)
	// 生成时间戳
	timestamp := time.Now().Format("20060102150405")
	// 组合新文件名：原文件名_时间戳.扩展名
	return fmt.Sprintf("%s_%s%s", nameWithoutExt, timestamp, ext)
}

// SaveImage 保存图片文件并返回结果
func SaveImage(c *gin.Context, imageData []byte, originalName string) UploadResult {
	// 生成带时间戳的文件名
	saveName := GenerateFileName(originalName)

	// 确保目录存在
	uploadDir := "var/images"
	if err := os.MkdirAll(uploadDir, 0755); err != nil {
		return UploadResult{Error: fmt.Errorf("创建目录失败: %v", err)}
	}

	// 保存文件
	filePath := filepath.Join(uploadDir, saveName)
	if err := os.WriteFile(filePath, imageData, 0644); err != nil {
		return UploadResult{Error: fmt.Errorf("保存文件失败: %v", err)}
	}

	// 获取完整的基础URL
	baseURL := GetBaseURL(c)
	return UploadResult{
		Filename: originalName,
		SaveName: saveName,
		Path:     fmt.Sprintf("%s/api/resource/images/%s", baseURL, saveName),
	}
}

// HandleFileUpload 处理文件上传
func HandleFileUpload(c *gin.Context, file *multipart.FileHeader) UploadResult {
	// 获取文件扩展名
	ext := strings.ToLower(filepath.Ext(file.Filename))

	// 检查是否是图片文件
	if !IsImageFile(ext) {
		return UploadResult{Error: fmt.Errorf("不支持的图片格式: %s", ext)}
	}

	// 读取文件内容
	src, err := file.Open()
	if err != nil {
		return UploadResult{Error: fmt.Errorf("读取文件失败: %v", err)}
	}
	defer src.Close()

	// 读取文件内容到内存
	imageData := make([]byte, file.Size)
	if _, err := src.Read(imageData); err != nil {
		return UploadResult{Error: fmt.Errorf("读取文件内容失败: %v", err)}
	}

	return SaveImage(c, imageData, file.Filename)
}

// HandleBase64Upload 处理base64图片上传
func HandleBase64Upload(c *gin.Context, base64Data string, filename string) UploadResult {
	var imageData []byte
	var ext string
	var err error

	// 处理可能包含的data:image/jpeg;base64,前缀
	if strings.Contains(base64Data, ",") {
		parts := strings.SplitN(base64Data, ",", 2)
		if len(parts) != 2 {
			return UploadResult{Error: fmt.Errorf("无效的base64数据格式")}
		}
		// 从前缀中提取图片格式
		if strings.Contains(parts[0], "image/") {
			mimeType := strings.TrimPrefix(strings.Split(parts[0], ";")[0], "data:image/")
			ext = "." + mimeType
		}
		base64Data = parts[1]
	}

	// 解码base64数据
	imageData, err = base64.StdEncoding.DecodeString(base64Data)
	if err != nil {
		return UploadResult{Error: fmt.Errorf("base64解码失败: %v", err)}
	}

	// 如果没有从base64数据中提取到格式，且没有提供文件名，默认使用png
	if ext == "" {
		if filename != "" {
			ext = filepath.Ext(filename)
		}
		if ext == "" {
			ext = ".png"
		}
	}

	// 检查文件类型
	if !IsImageFile(ext) {
		return UploadResult{Error: fmt.Errorf("不支持的图片格式: %s", ext)}
	}

	// 如果没有提供文件名，生成一个默认的
	if filename == "" {
		filename = fmt.Sprintf("image%s", ext)
	}

	return SaveImage(c, imageData, filename)
}

// IsImageFile 检查文件扩展名是否是图片
func IsImageFile(ext string) bool {
	validExts := map[string]bool{
		".jpg":  true,
		".jpeg": true,
		".png":  true,
		".gif":  true,
		".bmp":  true,
		".webp": true,
	}
	return validExts[strings.ToLower(ext)]
}

// ConvertToBase64URL 将二进制图片转换为base64 URL格式
func ConvertToBase64URL(imageData []byte, format string) string {
	if format == "" {
		format = "png"
	}
	// 将图片数据转换为base64
	base64Data := base64.StdEncoding.EncodeToString(imageData)
	// 返回完整的data URL
	return fmt.Sprintf("data:image/%s;base64,%s", format, base64Data)
}
