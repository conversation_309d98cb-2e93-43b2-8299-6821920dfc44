package llmutils

import (
	"bot/domain/entity"
	"bot/domain/vo"
	"errors"
	"fmt"
	"net"
	"os"
	"strings"

	"go.uber.org/zap"
)

// GetLocalIPv4 returns the non loopback local IPv4 address of the host
func GetLocalIPv4() (string, error) {
	addrs, err := net.InterfaceAddrs()
	if err != nil {
		return "", err
	}
	for _, address := range addrs {
		// check the address type and if it is not a loopback
		if ipnet, ok := address.(*net.IPNet); ok && !ipnet.IP.IsLoopback() {
			if ipv4 := ipnet.IP.To4(); ipv4 != nil {
				return ipv4.String(), nil
			}
		}
	}
	return "", errors.New("unable to determine local IPv4 address")
}

func ReadSource(chat *entity.ChatEntity) *entity.ChatEntity {
	if len(chat.Images) > 0 {
		// 获取最后一条用户消息
		lastUserMsg := chat.History[len(chat.History)-1]
		if lastUserMsg.Role == vo.UserRoleType {
			images := make([][]byte, 0)
			for _, imagePath := range chat.Images {
				// 读取图片文件
				p := "/api/resource/"
				index := strings.Index(imagePath, p)
				// 从完整URL中提取资源路径
				resourcePath := imagePath[index+len(p):]
				imageData, err := os.ReadFile(fmt.Sprintf("./var/%s", resourcePath))
				if err != nil {
					zap.L().Error(err.Error())
					continue
				}
				images = append(images, imageData)
			}
			// 将图片数据添加到用户消息中
			lastUserMsg.Images = images
		}
		chat.History[len(chat.History)-1] = lastUserMsg
	}
	return chat
}

// RemoveJSONComments 从JSON字符串中移除注释内容
// 支持两种注释格式：
// 1. 单行注释: // 注释内容
// 2. 多行注释: /* 注释内容 */
func RemoveJSONComments(jsonStr string) string {
	var result strings.Builder
	i := 0
	inString := false
	escapeNext := false

	for i < len(jsonStr) {
		// 处理字符串内容
		if jsonStr[i] == '"' && !escapeNext {
			inString = !inString
			result.WriteByte(jsonStr[i])
			i++
			continue
		}

		// 处理转义字符
		if inString && jsonStr[i] == '\\' {
			result.WriteByte(jsonStr[i])
			escapeNext = !escapeNext
			i++
			continue
		} else {
			escapeNext = false
		}

		// 只有在非字符串内容中，才处理注释
		if !inString {
			// 处理单行注释: //
			if i+1 < len(jsonStr) && jsonStr[i] == '/' && jsonStr[i+1] == '/' {
				i += 2 // 跳过 //
				// 跳过该行剩余内容
				for i < len(jsonStr) && jsonStr[i] != '\n' {
					i++
				}
				continue
			}

			// 处理多行注释: /* ... */
			if i+1 < len(jsonStr) && jsonStr[i] == '/' && jsonStr[i+1] == '*' {
				i += 2 // 跳过 /*
				// 查找 */
				for i+1 < len(jsonStr) && !(jsonStr[i] == '*' && jsonStr[i+1] == '/') {
					i++
				}
				if i+1 < len(jsonStr) {
					i += 2 // 跳过 */
				}
				continue
			}
		}

		// 保留普通字符
		result.WriteByte(jsonStr[i])
		i++
	}

	return result.String()
}
