package uuid

import (
	"bot/conf"
	"github.com/bwmarrin/snowflake"
)

type Node struct {
	*snowflake.Node
}

func NewUUID(node int64) *Node {
	var n *snowflake.Node
	var err error
	if n, err = snowflake.NewNode(0); err != nil {
		panic(err)
	}
	return &Node{n}
}

func NewUID(cnf *conf.Configuration) *Node {
	return NewUUID(cnf.Node)
}

// String 生成UUID字符串
func (n *Node) String() string {
	return n.Generate().String()
}

func (n *Node) Long() int64 {
	return n.Generate().Int64()
}

func (n *Node) Uint() uint64 {
	return uint64(n.Generate().Int64())
}
