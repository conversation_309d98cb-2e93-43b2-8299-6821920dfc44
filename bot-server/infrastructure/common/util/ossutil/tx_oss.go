package ossutil

import (
	"bytes"
	"context"
	"fmt"
	"net/http"
	"net/url"
	"path/filepath"
	"strings"
	"time"

	"github.com/tencentyun/cos-go-sdk-v5"
)

// TxOssConfig 腾讯云 COS 配置
type TxOssConfig struct {
	SecretID  string // 密钥ID
	SecretKey string // 密钥Key
	Region    string // 地域
	Bucket    string // 存储桶名称
}

// TxOSS 腾讯云对象存储客户端
type TxOSS struct {
	client *cos.Client
	config TxOssConfig
}

// NewTxOSS 创建腾讯云 COS 客户端
func NewTxOSS(config TxOssConfig) (*TxOSS, error) {
	// 构建访问URL
	bucketURL, err := url.Parse(fmt.Sprintf("https://%s.cos.%s.myqcloud.com", config.Bucket, config.Region))
	if err != nil {
		return nil, fmt.Errorf("解析存储桶URL失败: %v", err)
	}

	// 构建基础URL
	baseURL := &cos.BaseURL{BucketURL: bucketURL}

	// 创建客户端
	client := cos.NewClient(baseURL, &http.Client{
		Transport: &cos.AuthorizationTransport{
			SecretID:  config.SecretID,
			SecretKey: config.SecretKey,
		},
	})

	return &TxOSS{
		client: client,
		config: config,
	}, nil
}

// TxUploadResult 上传结果
type TxUploadResult struct {
	URL      string // 访问URL
	Filename string // 文件名
	Error    error  // 错误信息
}

// TxUploadImage 上传图片到腾讯云 COS
func (t *TxOSS) TxUploadImage(imageData []byte, filename string) TxUploadResult {
	// 生成带时间戳的文件名
	ext := filepath.Ext(filename)
	timestamp := time.Now().Format("20060102150405")
	objectKey := fmt.Sprintf("images/%s_%s%s", strings.TrimSuffix(filename, ext), timestamp, ext)

	// 上传对象
	_, err := t.client.Object.Put(context.Background(), objectKey, bytes.NewReader(imageData), nil)
	if err != nil {
		return TxUploadResult{Error: fmt.Errorf("上传文件失败: %v", err)}
	}

	// 生成访问URL
	url := fmt.Sprintf("https://%s.cos.%s.myqcloud.com/%s", t.config.Bucket, t.config.Region, objectKey)

	return TxUploadResult{
		URL:      url,
		Filename: objectKey,
	}
}

// TxDeleteImage 从腾讯云 COS 删除图片
func (t *TxOSS) TxDeleteImage(objectKey string) error {
	_, err := t.client.Object.Delete(context.Background(), objectKey)
	if err != nil {
		return fmt.Errorf("删除文件失败: %v", err)
	}
	return nil
}

// TxGetImageURL 获取图片访问URL
func (t *TxOSS) TxGetImageURL(objectKey string) string {
	return fmt.Sprintf("https://%s.cos.%s.myqcloud.com/%s", t.config.Bucket, t.config.Region, objectKey)
}
