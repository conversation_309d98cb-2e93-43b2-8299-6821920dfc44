package excel

import (
	"github.com/xuri/excelize/v2"
	"testing"
)

func TestToJson(t *testing.T) {
	file, err := excelize.OpenFile("税屋全部数据.xlsx")
	if err != nil {
		t.<PERSON>rror(err.<PERSON><PERSON>r())
		return
	}
	defer func() {
		err := file.Close()
		if err != nil {
			t.<PERSON><PERSON>r(err.<PERSON><PERSON>r())
			return
		}
	}()
	var rows [][]string
	if rows, err = file.GetRows("Sheet1"); err != nil {
		t.<PERSON>rror(err.<PERSON>rror())
		return
	}
	file.GetCols("Sheet1")
	t.Log(len(rows))
}
