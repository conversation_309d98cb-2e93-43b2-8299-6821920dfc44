package task

// Status 表示任务的状态
type Status string

const (
	Pending   Status = "pending"   // 等待执行
	Running   Status = "running"   // 正在执行
	Paused    Status = "paused"    // 暂停
	Completed Status = "completed" // 已完成
	Failed    Status = "failed"    // 失败
	Canceled  Status = "canceled"  // 已取消
	Retrying  Status = "retrying"  // 重试中
)

type ITask interface {
	Run() error
	Stop() error
	Delete() error
}
