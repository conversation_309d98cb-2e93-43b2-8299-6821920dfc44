package task

import (
	"context"
	"sync"
)

type Progress struct {
	Status Status
	Value  float64
	Msg    string
}

type FuncTask func(ctx context.Context, entity *Entity)

// Entity
// 任务实体
type Entity struct {
	// 任务id
	TaskID string
	// 进度任务调
	progressCh chan Progress
	ctx        context.Context
	cancel     context.CancelFunc
	funcTask   FuncTask

	mx *sync.Mutex
}

func New(ID string, task FuncTask) *Entity {
	ctx, cancel := context.WithCancel(context.Background())
	return &Entity{
		TaskID:     ID,
		ctx:        ctx,
		cancel:     cancel,
		progressCh: make(chan Progress, 10),
		funcTask:   task,
		mx:         &sync.Mutex{},
	}
}

func (e *Entity) Run() {
	go e.run()
}

func (e *Entity) Progress(value float64, msg string, status Status) {
	e.progressCh <- Progress{Status: status, Value: value, Msg: msg}
}

func (e *Entity) run() {
	defer close(e.progressCh)
	defer e.cancel()
	e.funcTask(e.ctx, e)
}
