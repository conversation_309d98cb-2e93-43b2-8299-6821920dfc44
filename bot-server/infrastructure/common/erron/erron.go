package erron

// ErrNoResult is returned when the query result is empty

type ErrorType string


type ErrNoResult struct {
	Code    ErrorType // 错误码
	Message string    // 错误信息
}

func (e *ErrNoResult) Error() string {
	return e.Message
}

func NewNoResult(code ErrorType, message string) error {
	return &ErrNoResult{
		Code:    code,
		Message: message,
	}
}

// Equal checks whether two errors are equal.
// 判断 错误类型是否一样
func Equal(a, b error) bool {
	if a == nil || b == nil {
		return true
	}
	var aNoResult, bNoResult *ErrNoResult
	var ok bool

	if aNoResult, ok = a.(*ErrNoResult); !ok {
		return ok
	}
	if bNoResult, ok = b.(*ErrNoResult); !ok {
		return ok
	}
	return aNoResult.Code == bNoResult.Code
}
