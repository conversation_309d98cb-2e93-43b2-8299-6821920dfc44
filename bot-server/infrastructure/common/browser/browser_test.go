package browser

import (
	"encoding/json"
	"fmt"
	"os"
	"testing"
	"time"
)

func TestBaiduSearch(t *testing.T) {
	// 从环境变量获取 SerpAPI 密钥
	apiKey := os.Getenv("SERPAPI_KEY")
	if apiKey == "" {
		t.<PERSON><PERSON>("跳过测试：请设置 SERPAPI_KEY 环境变量")
	}

	// 创建配置
	config := DefaultConfig()
	config.SerpAPIKey = apiKey
	config.Timeout = 30 * time.Second

	// 创建浏览器实例
	browser, err := NewWithConfig(config)
	if err != nil {
		t.Fatalf("创建浏览器失败: %v", err)
	}
	defer browser.Close()

	// 使用百度搜索
	query := "Go语言教程"
	results, err := browser.SearchAPI(query, Baidu, 5)
	if err != nil {
		t.Fatalf("百度搜索失败: %v", err)
	}

	// 解析结果
	var searchResults []SearchResult
	err = json.Unmarshal([]byte(results), &searchResults)
	if err != nil {
		t.Fatalf("解析搜索结果失败: %v", err)
	}

	// 验证结果
	if len(searchResults) == 0 {
		t.Error("搜索结果为空")
	}

	// 输出结果
	fmt.Printf("百度搜索 '%s' 的结果:\n", query)
	for _, result := range searchResults {
		fmt.Printf("位置: %d\n", result.Position)
		fmt.Printf("标题: %s\n", result.Title)
		fmt.Printf("URL: %s\n", result.URL)
		fmt.Printf("摘要: %s\n", result.Snippet)
		fmt.Println("---")
	}
}

func TestBaiduSearchWithOptions(t *testing.T) {
	// 从环境变量获取 SerpAPI 密钥
	apiKey := os.Getenv("SERPAPI_KEY")
	if apiKey == "" {
		t.Skip("跳过测试：请设置 SERPAPI_KEY 环境变量")
	}

	// 创建配置
	config := DefaultConfig()
	config.SerpAPIKey = apiKey

	// 创建浏览器实例
	browser, err := NewWithConfig(config)
	if err != nil {
		t.Fatalf("创建浏览器失败: %v", err)
	}
	defer browser.Close()

	// 使用便捷搜索方法
	options := &SearchOptions{
		Provider: Baidu,
		Limit:    3,
	}

	query := "人工智能"
	results, err := browser.WebSearch(query, options)
	if err != nil {
		t.Fatalf("百度搜索失败: %v", err)
	}

	// 验证结果
	if len(results) == 0 {
		t.Error("搜索结果为空")
	}

	// 输出结果
	fmt.Printf("百度搜索 '%s' 的结果 (使用选项):\n", query)
	for _, result := range results {
		fmt.Printf("位置: %d\n", result.Position)
		fmt.Printf("标题: %s\n", result.Title)
		fmt.Printf("URL: %s\n", result.URL)
		fmt.Printf("摘要: %s\n", result.Snippet)
		fmt.Println("---")
	}
}

func TestCompareSearchEngines(t *testing.T) {
	// 从环境变量获取 SerpAPI 密钥
	apiKey := os.Getenv("SERPAPI_KEY")
	if apiKey == "" {
		t.Skip("跳过测试：请设置 SERPAPI_KEY 环境变量")
	}

	// 创建配置
	config := DefaultConfig()
	config.SerpAPIKey = apiKey

	// 创建浏览器实例
	browser, err := NewWithConfig(config)
	if err != nil {
		t.Fatalf("创建浏览器失败: %v", err)
	}
	defer browser.Close()

	query := "golang"
	providers := []struct {
		name     string
		provider SearchProvider
	}{
		{"Google", Google},
		{"百度", Baidu},
		{"Bing", Bing},
	}

	for _, p := range providers {
		fmt.Printf("\n=== %s 搜索结果 ===\n", p.name)

		results, err := browser.SearchAPI(query, p.provider, 3)
		if err != nil {
			t.Logf("%s 搜索失败: %v", p.name, err)
			continue
		}

		var searchResults []SearchResult
		err = json.Unmarshal([]byte(results), &searchResults)
		if err != nil {
			t.Logf("%s 解析结果失败: %v", p.name, err)
			continue
		}

		if len(searchResults) == 0 {
			fmt.Printf("%s: 无搜索结果\n", p.name)
			continue
		}

		for _, result := range searchResults {
			fmt.Printf("位置: %d\n", result.Position)
			fmt.Printf("标题: %s\n", result.Title)
			fmt.Printf("URL: %s\n", result.URL)
			fmt.Printf("摘要: %s\n", result.Snippet)
			fmt.Println("---")
		}
	}
}

func TestBaiduSearchError(t *testing.T) {
	// 测试无效的API密钥
	config := DefaultConfig()
	config.SerpAPIKey = "invalid_key"

	browser, err := NewWithConfig(config)
	if err != nil {
		t.Fatalf("创建浏览器失败: %v", err)
	}
	defer browser.Close()

	// 应该返回错误
	_, err = browser.SearchAPI("测试", Baidu, 5)
	if err == nil {
		t.Error("期望搜索失败，但没有返回错误")
	}

	fmt.Printf("预期的错误: %v\n", err)
}

func TestBaiduSearchWithoutAPIKey(t *testing.T) {
	// 测试没有设置API密钥
	browser, err := New()
	if err != nil {
		t.Fatalf("创建浏览器失败: %v", err)
	}
	defer browser.Close()

	// 应该返回错误
	_, err = browser.SearchAPI("测试", Baidu, 5)
	if err == nil {
		t.Error("期望搜索失败，但没有返回错误")
	}

	expectedError := "SerpAPI密钥未设置"
	if err.Error() != expectedError {
		t.Errorf("期望错误 '%s'，但得到 '%s'", expectedError, err.Error())
	}
}

// 基准测试
func BenchmarkBaiduSearch(b *testing.B) {
	apiKey := os.Getenv("SERPAPI_KEY")
	if apiKey == "" {
		b.Skip("跳过基准测试：请设置 SERPAPI_KEY 环境变量")
	}

	config := DefaultConfig()
	config.SerpAPIKey = apiKey

	browser, err := NewWithConfig(config)
	if err != nil {
		b.Fatalf("创建浏览器失败: %v", err)
	}
	defer browser.Close()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := browser.SearchAPI("golang", Baidu, 5)
		if err != nil {
			b.Fatalf("搜索失败: %v", err)
		}
	}
}
