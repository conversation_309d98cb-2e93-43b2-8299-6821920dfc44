# 百度搜索测试使用说明

## 前置条件

1. 注册 SerpApi 账户：https://serpapi.com/
2. 获取 API 密钥
3. 设置环境变量

## 设置环境变量

### Windows (PowerShell)
```powershell
$env:SERPAPI_KEY = "your_serpapi_key_here"
```

### Linux/Mac
```bash
export SERPAPI_KEY="your_serpapi_key_here"
```

## 运行测试

### 1. 运行单个测试

```bash
# 运行百度搜索测试
go test -v -run TestBaiduSearch

# 运行百度搜索选项测试
go test -v -run TestBaiduSearchWithOptions

# 运行搜索引擎比较测试
go test -v -run TestCompareSearchEngines
```

### 2. 运行所有测试

```bash
go test -v
```

### 3. 运行基准测试

```bash
go test -bench=BenchmarkBaiduSearch -v
```

## 测试内容

### TestBaiduSearch
- 测试基本的百度搜索功能
- 搜索关键词："Go语言教程"
- 返回 5 个结果

### TestBaiduSearchWithOptions
- 测试使用选项的百度搜索
- 搜索关键词："人工智能"
- 返回 3 个结果

### TestCompareSearchEngines
- 比较 Google、百度、Bing 三个搜索引擎
- 搜索关键词："golang"
- 每个引擎返回 3 个结果

### TestBaiduSearchError
- 测试错误处理（无效API密钥）

### TestBaiduSearchWithoutAPIKey
- 测试没有API密钥的情况

### BenchmarkBaiduSearch
- 性能基准测试

## 预期输出

测试成功时，你会看到类似以下的输出：

```
=== RUN   TestBaiduSearch
百度搜索 'Go语言教程' 的结果:
位置: 1
标题: Go语言教程 - 官方文档
URL: https://example.com/go-tutorial
摘要: Go语言教程，从入门到精通...
---
位置: 2
标题: Go语言编程指南
URL: https://example.com/go-guide
摘要: 详细的Go语言编程指南...
---
--- PASS: TestBaiduSearch (2.45s)
```

## 注意事项

1. **API配额**：SerpApi 免费账户有搜索次数限制
2. **网络连接**：确保网络连接正常
3. **速率限制**：避免过于频繁的请求
4. **密钥安全**：不要在代码中硬编码API密钥

## 故障排除

### 常见错误

1. **"SerpAPI密钥未设置"**
   - 检查环境变量是否正确设置
   - 确认密钥拼写正确

2. **"搜索失败"**
   - 检查网络连接
   - 验证API密钥是否有效
   - 确认API配额是否已用完

3. **"解析搜索结果失败"**
   - 可能是API返回格式变化
   - 检查SerpApi文档是否有更新

## 示例用法

```go
// 设置API密钥
config := browser.DefaultConfig()
config.SerpAPIKey = "your_serpapi_key_here"

// 创建浏览器实例
browser, err := browser.NewWithConfig(config)
if err != nil {
    log.Fatal(err)
}
defer browser.Close()

// 百度搜索
results, err := browser.SearchAPI("Go语言教程", browser.Baidu, 5)
if err != nil {
    log.Fatal(err)
}

fmt.Println("搜索结果:", results)
``` 