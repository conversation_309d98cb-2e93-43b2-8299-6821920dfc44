# 浏览器模块 (Browser Module)

这个模块提供了一个功能丰富的浏览器客户端，支持传统的浏览器操作和基于 SerpApi 的搜索功能。

## 功能特性

- **页面导航**: 支持访问网页、获取页面内容、点击链接等
- **内容提取**: 提取页面标题、HTML内容、文本内容
- **搜索集成**: 通过 SerpApi 支持多种搜索引擎（Google、百度、Bing）
- **截图功能**: 支持页面截图
- **链接管理**: 自动提取并管理页面链接

## 使用示例

### 基本浏览器操作

```go
package main

import (
    "fmt"
    "bot-server/infrastructure/common/browser"
)

func main() {
    // 创建浏览器实例
    browser, err := browser.New()
    if err != nil {
        panic(err)
    }
    defer browser.Close()
    
    // 导航到网页
    err = browser.NavigateTo("https://example.com")
    if err != nil {
        panic(err)
    }
    
    // 获取页面内容
    content, err := browser.GetCurrentPageContent()
    if err != nil {
        panic(err)
    }
    
    fmt.Printf("标题: %s\n", content.Title)
    fmt.Printf("URL: %s\n", content.URL)
    
    // 获取可点击的链接
    links, err := browser.GetAvailableLinks()
    if err != nil {
        panic(err)
    }
    
    for _, link := range links {
        fmt.Printf("链接: %s\n", link)
    }
}
```

### 使用 SerpApi 搜索

```go
package main

import (
    "fmt"
    "bot-server/infrastructure/common/browser"
)

func main() {
    // 创建配置
    config := browser.DefaultConfig()
    config.SerpAPIKey = "your_serpapi_key_here"
    
    // 创建浏览器实例
    browser, err := browser.NewWithConfig(config)
    if err != nil {
        panic(err)
    }
    defer browser.Close()
    
    // 使用 Google 搜索
    results, err := browser.SearchAPI("Go语言教程", browser.Google, 10)
    if err != nil {
        panic(err)
    }
    
    fmt.Printf("搜索结果: %s\n", results)
    
    // 使用便捷的搜索方法
    options := &browser.SearchOptions{
        Provider: browser.Google,
        Limit:    5,
    }
    
    searchResults, err := browser.WebSearch("人工智能", options)
    if err != nil {
        panic(err)
    }
    
    for _, result := range searchResults {
        fmt.Printf("位置: %d\n", result.Position)
        fmt.Printf("标题: %s\n", result.Title)
        fmt.Printf("URL: %s\n", result.URL)
        fmt.Printf("摘要: %s\n", result.Snippet)
        fmt.Println("---")
    }
}
```

## 配置选项

### SerpAPI 配置

要使用搜索功能，您需要：

1. 注册 SerpApi 账户: https://serpapi.com/
2. 获取 API 密钥
3. 在配置中设置密钥

```go
config := browser.DefaultConfig()
config.SerpAPIKey = "your_serpapi_key_here"

browser, err := browser.NewWithConfig(config)
```

### 浏览器配置

```go
config := &browser.Config{
    Headless:             true,                    // 无头模式
    Timeout:              30 * time.Second,       // 请求超时
    UserAgent:            "Custom-Agent/1.0",     // 用户代理
    IgnoreImages:         true,                   // 忽略图片
    SerpAPIKey:          "your_key",              // SerpApi 密钥
}
```

## 支持的搜索引擎

- **Google**: `browser.Google` - 支持全球搜索
- **百度**: `browser.Baidu` - 支持中文搜索
- **Bing**: `browser.Bing` - 微软搜索引擎

## 搜索选项

```go
options := &browser.SearchOptions{
    Provider:   browser.Google,  // 搜索提供者
    Limit:      10,              // 结果数量限制
    SearchText: "关键词",         // 搜索文本（用于页面内搜索）
    Selector:   "div.result",    // CSS选择器（用于页面内搜索）
}
```

## 搜索结果结构

```go
type SearchResult struct {
    Text     string `json:"text"`     // 结果文本
    Title    string `json:"title"`    // 标题
    URL      string `json:"url"`      // 链接
    Snippet  string `json:"snippet"`  // 摘要
    Position int    `json:"position"` // 位置
}
```

## 错误处理

所有方法都返回错误，请务必检查：

```go
result, err := browser.SearchAPI("查询", browser.Google, 10)
if err != nil {
    log.Printf("搜索失败: %v", err)
    return
}
```

## 注意事项

1. **SerpApi 密钥**: 搜索功能需要有效的 SerpApi 密钥
2. **速率限制**: 请遵守 SerpApi 的速率限制
3. **资源管理**: 使用完毕后记得调用 `Close()` 方法
4. **错误处理**: 始终检查返回的错误

## 依赖

- `github.com/chromedp/chromedp`: 用于浏览器自动化
- `github.com/serpapi/serpapi-golang`: 用于 SerpApi 集成 