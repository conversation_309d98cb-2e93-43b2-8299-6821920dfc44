package browser

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/chromedp/chromedp"
	"github.com/serpapi/serpapi-golang"
)

var (
	defaultBrowser *Browser
	browserMutex   sync.Mutex
)

// PageContent 表示页面内容
type PageContent struct {
	Title   string
	Content string
	URL     string
}

// CachedPage 表示缓存的页面
type CachedPage struct {
	HTML    string
	Links   map[string]string // key: 链接文本, value: href
	Content PageContent
}

// Browser 浏览器结构体
type Browser struct {
	ctx         context.Context
	cancel      context.CancelFunc
	currentPage *CachedPage
	pageMutex   sync.RWMutex
	serpAPIKey  string
}

// SearchResult 表示搜索结果
type SearchResult struct {
	Text     string `json:"text"`
	Title    string `json:"title"`
	URL      string `json:"url"`
	Snippet  string `json:"snippet"`
	Position int    `json:"position"`
}

// SearchProvider 定义搜索提供者
type SearchProvider string

const (
	Google SearchProvider = "google"
	Baidu  SearchProvider = "baidu"
	Bing   SearchProvider = "bing"
)

// SearchOptions 搜索选项
type SearchOptions struct {
	Selector   string         // CSS选择器
	SearchText string         // 要搜索的文本
	Provider   SearchProvider // 搜索提供者
	Limit      int            // 搜索结果数量限制
}

// DefaultSearchOptions 返回默认搜索选项
func DefaultSearchOptions() *SearchOptions {
	return &SearchOptions{
		Provider: Google,
		Limit:    10,
	}
}

// Config 浏览器配置项
type Config struct {
	Headless      bool
	Timeout       time.Duration
	UserAgent     string
	IgnoreImages  bool
	ProxyServer   string
	ExecPath      string
	CustomOptions []chromedp.ExecAllocatorOption
	SerpAPIKey    string
}

// DefaultConfig 返回默认配置
func DefaultConfig() *Config {
	return &Config{
		Headless:     true,
		Timeout:      30 * time.Second,
		UserAgent:    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
		IgnoreImages: true,
	}
}

// New 创建一个新的浏览器实例
func New() (*Browser, error) {
	ctx, cancel := chromedp.NewContext(
		context.Background(),
	)

	ctx, cancel = context.WithTimeout(ctx, 30*time.Second)

	return &Browser{
		ctx:         ctx,
		cancel:      cancel,
		currentPage: nil,
		pageMutex:   sync.RWMutex{},
	}, nil
}

// NewWithConfig 使用配置创建浏览器实例
func NewWithConfig(config *Config) (*Browser, error) {
	ctx, cancel := chromedp.NewContext(
		context.Background(),
	)

	ctx, cancel = context.WithTimeout(ctx, config.Timeout)

	return &Browser{
		ctx:         ctx,
		cancel:      cancel,
		currentPage: nil,
		pageMutex:   sync.RWMutex{},
		serpAPIKey:  config.SerpAPIKey,
	}, nil
}

// GetDefaultBrowser 获取默认浏览器实例
func GetDefaultBrowser() (*Browser, error) {
	browserMutex.Lock()
	defer browserMutex.Unlock()

	if defaultBrowser == nil {
		browser, err := New()
		if err != nil {
			return nil, err
		}
		defaultBrowser = browser
	}
	return defaultBrowser, nil
}

// SetSerpAPIKey 设置SerpAPI密钥
func (b *Browser) SetSerpAPIKey(apiKey string) {
	b.serpAPIKey = apiKey
}

// Close 关闭浏览器
func (b *Browser) Close() {
	if b.cancel != nil {
		b.cancel()
	}
}

// Execute 执行浏览器操作
func (b *Browser) Execute(actions ...chromedp.Action) error {
	return chromedp.Run(b.ctx, actions...)
}

// NavigateTo 导航到指定URL并缓存页面内容
func (b *Browser) NavigateTo(url string) error {
	var html string
	var title string
	var content string

	err := chromedp.Run(b.ctx,
		chromedp.Navigate(url),
		chromedp.WaitReady("body"),
		chromedp.OuterHTML("html", &html),
		chromedp.Title(&title),
		chromedp.Text("body", &content),
	)

	if err != nil {
		return err
	}

	// 提取页面中的所有链接
	var links map[string]string
	err = chromedp.Run(b.ctx,
		chromedp.Evaluate(`
			(() => {
				const links = {};
				document.querySelectorAll('a').forEach(a => {
					if (a.textContent && a.href) {
						links[a.textContent.trim()] = a.href;
					}
				});
				return links;
			})()
		`, &links),
	)

	if err != nil {
		return err
	}

	b.pageMutex.Lock()
	b.currentPage = &CachedPage{
		HTML:  html,
		Links: links,
		Content: PageContent{
			Title:   title,
			Content: content,
			URL:     url,
		},
	}
	b.pageMutex.Unlock()

	return nil
}

// ClickLink 点击指定文本的链接并返回新页面的内容
func (b *Browser) ClickLink(linkText string) (*PageContent, error) {
	b.pageMutex.RLock()
	if b.currentPage == nil {
		b.pageMutex.RUnlock()
		return nil, fmt.Errorf("没有缓存的页面")
	}

	href, exists := b.currentPage.Links[linkText]
	b.pageMutex.RUnlock()

	if !exists {
		return nil, fmt.Errorf("未找到链接文本: %s", linkText)
	}

	// 导航到链接URL
	err := b.NavigateTo(href)
	if err != nil {
		return nil, err
	}

	b.pageMutex.RLock()
	defer b.pageMutex.RUnlock()
	return &b.currentPage.Content, nil
}

// GetCurrentPageContent 获取当前页面的内容
func (b *Browser) GetCurrentPageContent() (*PageContent, error) {
	b.pageMutex.RLock()
	defer b.pageMutex.RUnlock()

	if b.currentPage == nil {
		return nil, fmt.Errorf("没有缓存的页面")
	}

	return &b.currentPage.Content, nil
}

// GetAvailableLinks 获取当前页面上所有可点击的链接文本
func (b *Browser) GetAvailableLinks() ([]string, error) {
	b.pageMutex.RLock()
	defer b.pageMutex.RUnlock()

	if b.currentPage == nil {
		return nil, fmt.Errorf("没有缓存的页面")
	}

	links := make([]string, 0, len(b.currentPage.Links))
	for linkText := range b.currentPage.Links {
		links = append(links, linkText)
	}

	return links, nil
}

// GetText 获取指定选择器的文本内容
func (b *Browser) GetText(selector string) (string, error) {
	var text string
	err := chromedp.Run(b.ctx,
		chromedp.Text(selector, &text),
	)
	return text, err
}

// GetHTML 获取指定选择器的HTML内容
func (b *Browser) GetHTML(selector string) (string, error) {
	var html string
	err := chromedp.Run(b.ctx,
		chromedp.OuterHTML(selector, &html),
	)
	return html, err
}

// Search 在页面中搜索内容（仅限当前页面）
func (b *Browser) Search(selector, searchText string) ([]string, error) {
	var elements []string
	err := chromedp.Run(b.ctx,
		chromedp.Evaluate(fmt.Sprintf(`
			Array.from(document.querySelectorAll('%s')).filter(el => 
				el.textContent.toLowerCase().includes('%s'.toLowerCase())
			).map(el => el.textContent)
		`, selector, searchText), &elements),
	)
	return elements, err
}

// SearchWithOptions 使用选项在页面中搜索内容（仅限当前页面）
func (b *Browser) SearchWithOptions(options *SearchOptions) ([]SearchResult, error) {
	if options == nil {
		options = DefaultSearchOptions()
	}

	if options.Selector == "" {
		return nil, fmt.Errorf("搜索选择器不能为空")
	}

	texts, err := b.Search(options.Selector, options.SearchText)
	if err != nil {
		return nil, err
	}

	results := make([]SearchResult, len(texts))
	for i, text := range texts {
		results[i] = SearchResult{Text: text}
	}

	return results, nil
}

// Screenshot 截取页面截图
func (b *Browser) Screenshot(selector string) ([]byte, error) {
	var buf []byte
	err := chromedp.Run(b.ctx,
		chromedp.Screenshot(selector, &buf),
	)
	return buf, err
}

// SearchAPI 使用SerpApi进行搜索
func (b *Browser) SearchAPI(query string, provider SearchProvider, limit int) (string, error) {
	if b.serpAPIKey == "" {
		return "", fmt.Errorf("SerpAPI密钥未设置")
	}

	// 创建SerpAPI客户端设置
	setting := serpapi.NewSerpApiClientSetting(b.serpAPIKey)
	client := serpapi.NewClient(setting)

	// 构建搜索参数
	params := map[string]string{
		"q":   query,
		"num": fmt.Sprintf("%d", limit),
	}

	var results []SearchResult

	switch provider {
	case Google:
		// Google搜索
		params["engine"] = "google"
		response, err := client.Search(params)
		if err != nil {
			return "", fmt.Errorf("Google搜索失败: %w", err)
		}
		results = b.parseGoogleResults(response)

	case Baidu:
		// 百度搜索
		params["engine"] = "baidu"
		response, err := client.Search(params)
		if err != nil {
			return "", fmt.Errorf("百度搜索失败: %w", err)
		}
		results = b.parseBaiduResults(response)

	case Bing:
		// Bing搜索
		params["engine"] = "bing"
		response, err := client.Search(params)
		if err != nil {
			return "", fmt.Errorf("Bing搜索失败: %w", err)
		}
		results = b.parseBingResults(response)

	default:
		return "", fmt.Errorf("不支持的搜索提供者: %s", provider)
	}

	// 将结果转换为JSON
	jsonData, err := json.Marshal(results)
	if err != nil {
		return "", fmt.Errorf("JSON序列化失败: %w", err)
	}

	return string(jsonData), nil
}

// parseGoogleResults 解析Google搜索结果
func (b *Browser) parseGoogleResults(response map[string]interface{}) []SearchResult {
	var results []SearchResult

	if organicResults, ok := response["organic_results"].([]interface{}); ok {
		for i, result := range organicResults {
			if resultMap, ok := result.(map[string]interface{}); ok {
				searchResult := SearchResult{
					Position: i + 1,
				}

				if title, ok := resultMap["title"].(string); ok {
					searchResult.Title = title
				}

				if url, ok := resultMap["link"].(string); ok {
					searchResult.URL = url
				}

				if snippet, ok := resultMap["snippet"].(string); ok {
					searchResult.Snippet = snippet
					searchResult.Text = snippet
				}

				results = append(results, searchResult)
			}
		}
	}

	return results
}

// parseBaiduResults 解析百度搜索结果
func (b *Browser) parseBaiduResults(response map[string]interface{}) []SearchResult {
	var results []SearchResult

	if organicResults, ok := response["organic_results"].([]interface{}); ok {
		for i, result := range organicResults {
			if resultMap, ok := result.(map[string]interface{}); ok {
				searchResult := SearchResult{
					Position: i + 1,
				}

				if title, ok := resultMap["title"].(string); ok {
					searchResult.Title = title
				}

				if url, ok := resultMap["link"].(string); ok {
					searchResult.URL = url
				}

				if snippet, ok := resultMap["snippet"].(string); ok {
					searchResult.Snippet = snippet
					searchResult.Text = snippet
				}

				results = append(results, searchResult)
			}
		}
	}

	return results
}

// parseBingResults 解析Bing搜索结果
func (b *Browser) parseBingResults(response map[string]interface{}) []SearchResult {
	var results []SearchResult

	if organicResults, ok := response["organic_results"].([]interface{}); ok {
		for i, result := range organicResults {
			if resultMap, ok := result.(map[string]interface{}); ok {
				searchResult := SearchResult{
					Position: i + 1,
				}

				if title, ok := resultMap["title"].(string); ok {
					searchResult.Title = title
				}

				// Bing 使用 "url" 而不是 "link"
				if url, ok := resultMap["url"].(string); ok {
					searchResult.URL = url
				} else if url, ok := resultMap["link"].(string); ok {
					searchResult.URL = url
				}

				if snippet, ok := resultMap["snippet"].(string); ok {
					searchResult.Snippet = snippet
					searchResult.Text = snippet
				}

				results = append(results, searchResult)
			}
		}
	}

	return results
}

// WebSearch 使用SerpApi进行Web搜索的便捷方法
func (b *Browser) WebSearch(query string, options *SearchOptions) ([]SearchResult, error) {
	if options == nil {
		options = DefaultSearchOptions()
	}

	jsonStr, err := b.SearchAPI(query, options.Provider, options.Limit)
	if err != nil {
		return nil, err
	}

	var results []SearchResult
	err = json.Unmarshal([]byte(jsonStr), &results)
	if err != nil {
		return nil, fmt.Errorf("解析搜索结果失败: %w", err)
	}

	return results, nil
}
