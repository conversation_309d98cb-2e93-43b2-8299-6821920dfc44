package web

import (
	"bot/infrastructure/common/logs"
	"bot/infrastructure/common/resp"
	"github.com/gin-gonic/gin"
	"net/http"
	"runtime/debug"
)

// Cors 跨域处理
func Cors() gin.HandlerFunc {
	return func(c *gin.Context) {
		method := c.Request.Method
		origin := c.Request.Header.Get("Origin")
		if origin != "" {
			c.<PERSON><PERSON>("Access-Control-Allow-Origin", "*") // 可将将 * 替换为指定的域名
			c.Header("Access-Control-Allow-Methods", "POST, GET, OPTIONS, PUT, DELETE, UPDATE")
			c.<PERSON><PERSON>("Access-Control-Allow-Headers", "Origin, X-Requested-With, Content-Type, Accept, Authorization,Orgid")
			c.<PERSON>("Access-Control-Expose-Headers", "Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Cache-Control, Content-Language, Content-Type")
			c.<PERSON><PERSON>("Access-Control-Allow-Credentials", "true")
		}
		if method == "OPTIONS" {
			c.AbortWithStatus(http.StatusOK)
			return
		}
		c.Next()
	}
}

func GlobalException() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				switch e := err.(type) {
				case ArgsError:
					ctx.JSON(500, resp.Error(e, resp.Msg("参数错误 "), resp.Code(resp.WebArgsErr)))
				default:
					ctx.JSON(500, resp.Error(nil, resp.Msg("系统错误 ", err.(error).Error()), resp.Code(resp.WebErr)))
				}
				logs.Log.Error(string(debug.Stack()))
			}
		}()
		ctx.Next()
	}
}
