package web

import (
	"github.com/gin-gonic/gin"
)

func BindJSON[T any](ctx *gin.Context) T {
	var data T
	if err := ctx.BindJSON(&data); err != nil {
		panic(ArgsErr(err.<PERSON>rror()))
	}
	return data
}

func ShouldJSON[T any](ctx *gin.Context) T {
	var data T
	if err := ctx.ShouldBind(&data); err != nil {
		panic(ArgsErr(err.<PERSON>rror()))
	}
	return data
}
func ShouldBindUri[T any](ctx *gin.Context) T {
	var data T
	if err := ctx.ShouldBindUri(&data); err != nil {
		panic(ArgsErr(err.Error()))
	}
	return data
}
