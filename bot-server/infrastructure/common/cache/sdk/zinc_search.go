package sdk

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

const (
	// DefaultZincSearchURL 默认 ZincSearch 地址
	DefaultZincSearchURL = "http://localhost:4080"
	// DefaultZincSearchUsername 默认用户名
	DefaultZincSearchUsername = "admin"
	// DefaultZincSearchPassword 默认密码
	DefaultZincSearchPassword = "Complexpass#123"
	// DefaultTimeout 默认超时时间
	DefaultTimeout = 10 * time.Second
)

// ZincSearchClient ZincSearch 客户端
type ZincSearchClient struct {
	BaseURL  string
	Username string
	Password string
	client   *http.Client
}

// NewDefaultZincSearchClient 使用默认配置创建 ZincSearch 客户端
func NewDefaultZincSearchClient() *ZincSearchClient {
	return NewZincSearchClient(DefaultZincSearchURL, DefaultZincSearchUsername, DefaultZincSearchPassword)
}

// NewZincSearchClient 创建新的 ZincSearch 客户端
func NewZincSearchClient(baseURL, username, password string) *ZincSearchClient {
	return &ZincSearchClient{
		BaseURL:  baseURL,
		Username: username,
		Password: password,
		client: &http.Client{
			Timeout: DefaultTimeout,
		},
	}
}

// IndexConfig 索引配置
type IndexConfig struct {
	Name     string                 `json:"name"`
	Settings map[string]interface{} `json:"settings,omitempty"`
	Mappings map[string]interface{} `json:"mappings,omitempty"`
}

// SearchRequest 搜索请求
type SearchRequest struct {
	SearchType string                 `json:"search_type"`
	Query      map[string]interface{} `json:"query"`
	From       int                    `json:"from"`
	MaxResults int                    `json:"max_results"`
	Source     []string               `json:"_source,omitempty"`
}

// doRequest 执行 HTTP 请求
func (c *ZincSearchClient) doRequest(method, path string, body interface{}) ([]byte, error) {
	var reqBody io.Reader
	if body != nil {
		jsonData, err := json.Marshal(body)
		if err != nil {
			return nil, err
		}
		reqBody = bytes.NewBuffer(jsonData)
	}

	req, err := http.NewRequest(method, c.BaseURL+path, reqBody)
	if err != nil {
		return nil, err
	}

	req.SetBasicAuth(c.Username, c.Password)
	req.Header.Set("Content-Type", "application/json")

	resp, err := c.client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	if resp.StatusCode >= 400 {
		return nil, fmt.Errorf("request failed with status %d: %s", resp.StatusCode, string(respBody))
	}

	return respBody, nil
}

// CreateIndex 创建索引
func (c *ZincSearchClient) CreateIndex(config IndexConfig) error {
	_, err := c.doRequest(http.MethodPost, "/api/index", config)
	return err
}

// DeleteIndex 删除索引
func (c *ZincSearchClient) DeleteIndex(indexName string) error {
	_, err := c.doRequest(http.MethodDelete, "/api/index/"+indexName, nil)
	return err
}

// IndexDocument 索引文档
func (c *ZincSearchClient) IndexDocument(indexName string, id string, document interface{}) error {
	path := fmt.Sprintf("/api/%s/_doc/%s", indexName, id)
	_, err := c.doRequest(http.MethodPut, path, document)
	return err
}

// UpdateDocument 更新文档
func (c *ZincSearchClient) UpdateDocument(indexName string, id string, document interface{}) error {
	path := fmt.Sprintf("/api/%s/_update/%s", indexName, id)
	_, err := c.doRequest(http.MethodPost, path, document)
	return err
}

// DeleteDocument 删除文档
func (c *ZincSearchClient) DeleteDocument(indexName string, id string) error {
	path := fmt.Sprintf("/api/%s/_doc/%s", indexName, id)
	_, err := c.doRequest(http.MethodDelete, path, nil)
	return err
}

// Search 搜索文档
func (c *ZincSearchClient) Search(indexName string, request SearchRequest) ([]byte, error) {
	path := fmt.Sprintf("/api/%s/_search", indexName)
	return c.doRequest(http.MethodPost, path, request)
}

// BulkIndex 批量索引文档
func (c *ZincSearchClient) BulkIndex(indexName string, documents []map[string]interface{}) error {
	path := fmt.Sprintf("/api/%s/_bulk", indexName)
	var bulkData []map[string]interface{}

	for _, doc := range documents {
		action := map[string]interface{}{
			"index": map[string]string{
				"_index": indexName,
			},
		}
		bulkData = append(bulkData, action, doc)
	}

	_, err := c.doRequest(http.MethodPost, path, bulkData)
	return err
}

// CreateDocument 创建文档
func (c *ZincSearchClient) CreateDocument(indexName string, document map[string]interface{}) error {
	path := fmt.Sprintf("/api/%s/_doc", indexName)
	_, err := c.doRequest(http.MethodPost, path, document)
	return err
}
