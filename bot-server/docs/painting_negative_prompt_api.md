# 负面提示词管理接口文档

## 概述

本文档描述了AI绘画负面提示词管理接口的使用方法。负面提示词用于存储和管理AI绘画中不希望出现的内容描述，帮助生成更高质量的图像。

## 数据模型

### PaintingNegativePromptPO (持久化对象)
```go
type PaintingNegativePromptPO struct {
    ID        string       `gorm:"primaryKey" json:"id"`                              // 负面提示词ID
    Prompt    string       `gorm:"column:prompt" json:"prompt"`                       // 负面提示词内容
    CreatedAt time.Time    `gorm:"column:created_at;autoCreateTime" json:"createdAt"` // 创建时间
    UpdatedAt time.Time    `gorm:"column:updated_at;autoUpdateTime" json:"updatedAt"` // 更新时间
    DeletedAt sql.NullTime `gorm:"column:deleted_at" json:"deletedAt"`                // 删除时间
}
```

### PaintingNegativePromptDTO (数据传输对象)
```go
type PaintingNegativePromptDTO struct {
    ID        string       `json:"id"`        // 负面提示词ID
    Prompt    string       `json:"prompt"`    // 负面提示词内容
    CreatedAt time.Time    `json:"createdAt"` // 创建时间
    UpdatedAt time.Time    `json:"updatedAt"` // 更新时间
    DeletedAt sql.NullTime `json:"deletedAt"` // 删除时间
}
```

## API接口

### 1. 查询负面提示词列表

**接口地址：** `GET /api/bot/painting-negative-prompts`

**功能描述：** 获取所有负面提示词列表

**请求参数：** 无

**响应示例：**
```json
{
    "code": 200,
    "data": [
        {
            "id": "prompt-001",
            "prompt": "模糊、低质量、变形、不清晰",
            "createdAt": "2024-01-01T00:00:00Z",
            "updatedAt": "2024-01-01T00:00:00Z",
            "deletedAt": null
        }
    ],
    "msg": "success"
}
```

### 2. 根据条件查询负面提示词

**接口地址：** `POST /api/bot/painting-negative-prompts/query`

**功能描述：** 根据查询条件获取负面提示词列表

**请求参数：**
```json
{
    "id": "prompt-001"
}
```

**响应示例：**
```json
{
    "code": 200,
    "data": [
        {
            "id": "prompt-001",
            "prompt": "模糊、低质量、变形、不清晰",
            "createdAt": "2024-01-01T00:00:00Z",
            "updatedAt": "2024-01-01T00:00:00Z",
            "deletedAt": null
        }
    ],
    "msg": "success"
}
```

### 3. 创建负面提示词

**接口地址：** `POST /api/bot/painting-negative-prompts/create`

**功能描述：** 创建新的负面提示词

**请求参数：**
```json
{
    "prompt": "模糊、低质量、变形、不清晰、噪点、水印"
}
```

**响应示例：**
```json
{
    "code": 200,
    "data": {
        "id": "prompt-002",
        "prompt": "模糊、低质量、变形、不清晰、噪点、水印",
        "createdAt": "2024-01-01T00:00:00Z",
        "updatedAt": "2024-01-01T00:00:00Z",
        "deletedAt": null
    },
    "msg": "success"
}
```

### 4. 更新负面提示词

**接口地址：** `POST /api/bot/painting-negative-prompts/update`

**功能描述：** 更新指定的负面提示词

**请求参数：**
```json
{
    "id": "prompt-001",
    "prompt": "模糊、低质量、变形、不清晰、噪点、水印、文字"
}
```

**响应示例：**
```json
{
    "code": 200,
    "data": {
        "id": "prompt-001",
        "prompt": "模糊、低质量、变形、不清晰、噪点、水印、文字",
        "createdAt": "2024-01-01T00:00:00Z",
        "updatedAt": "2024-01-01T00:00:00Z",
        "deletedAt": null
    },
    "msg": "success"
}
```

### 5. 删除负面提示词

**接口地址：** `POST /api/bot/painting-negative-prompts/delete`

**功能描述：** 删除指定的负面提示词

**请求参数：**
```json
{
    "id": "prompt-001"
}
```

**响应示例：**
```json
{
    "code": 200,
    "data": null,
    "msg": "success"
}
```

### 6. 获取单个负面提示词

**接口地址：** `POST /api/bot/painting-negative-prompts/get`

**功能描述：** 根据ID获取指定的负面提示词

**请求参数：**
```json
{
    "id": "prompt-001"
}
```

**响应示例：**
```json
{
    "code": 200,
    "data": {
        "id": "prompt-001",
        "prompt": "模糊、低质量、变形、不清晰",
        "createdAt": "2024-01-01T00:00:00Z",
        "updatedAt": "2024-01-01T00:00:00Z",
        "deletedAt": null
    },
    "msg": "success"
}
```

## 使用说明

### 负面提示词的作用

负面提示词用于告诉AI绘画模型不希望生成的内容，常见的负面提示词包括：

- **质量问题：** 模糊、低质量、像素化、噪点
- **内容问题：** 变形、不清晰、水印、文字
- **风格问题：** 不自然的颜色、过度饱和、过度锐化

### 最佳实践

1. **简洁明了：** 负面提示词应该简洁明了，避免过于复杂的描述
2. **针对性强：** 根据具体的绘画需求选择合适的负面提示词
3. **适度使用：** 不要过度使用负面提示词，以免影响生成效果
4. **定期更新：** 根据使用效果定期更新和优化负面提示词

### 示例负面提示词

```json
{
    "prompt": "模糊、低质量、变形、不清晰、噪点、水印、文字、过度饱和、过度锐化、不自然的颜色"
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 | 