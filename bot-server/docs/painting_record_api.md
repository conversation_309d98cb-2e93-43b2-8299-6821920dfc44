# 绘画记录管理接口文档

## 概述

本文档描述了AI绘画记录管理接口的使用方法。绘画记录用于存储和管理AI绘画的历史记录，包括绘画的提示词、图片URL、状态等信息。

## 数据模型

### PaintingPO (持久化对象)
```go
type PaintingPO struct {
    ID        string       `gorm:"primaryKey" json:"id"`                              // 绘画ID
    Title     string       `gorm:"column:title" json:"title"`                         // 绘画名称
    Prompt    string       `gorm:"column:prompt" json:"prompt"`                       // 绘画提示词
    Status    string       `gorm:"column:status" json:"status"`                       // 绘画状态
    ImageUrls string       `gorm:"column:image_urls" json:"imageUrls"`                // 绘画图片URL 多个图片URL用逗号分隔
    CreatedAt time.Time    `gorm:"column:created_at;autoCreateTime" json:"createdAt"` // 创建时间
    UpdatedAt time.Time    `gorm:"column:updated_at;autoUpdateTime" json:"updatedAt"` // 更新时间
    DeletedAt sql.NullTime `gorm:"column:deleted_at" json:"deletedAt"`                // 删除时间
}
```

### PaintingRecordDTO (数据传输对象)
```go
type PaintingRecordDTO struct {
    ID        string       `json:"id"`        // 绘画ID
    Title     string       `json:"title"`     // 绘画名称
    Prompt    string       `json:"prompt"`    // 绘画提示词
    Status    string       `json:"status"`    // 绘画状态
    ImageUrls string       `json:"imageUrls"` // 绘画图片URL 多个图片URL用逗号分隔
    CreatedAt time.Time    `json:"createdAt"` // 创建时间
    UpdatedAt time.Time    `json:"updatedAt"` // 更新时间
    DeletedAt sql.NullTime `json:"deletedAt"` // 删除时间
}
```

### PaintingRecordQueryDTO (查询对象)
```go
type PaintingRecordQueryDTO struct {
    ID     string `json:"id,omitempty"`     // 绘画ID - 用于精确查询
    Title  string `json:"title,omitempty"`  // 绘画名称 - 用于模糊查询
    Status string `json:"status,omitempty"` // 绘画状态 - 用于状态筛选
}
```

## API接口

### 1. 查询绘画记录列表

**接口地址：** `GET /api/bot/painting-records`

**功能描述：** 获取所有绘画记录列表

**请求参数：** 无

**响应示例：**
```json
{
    "code": 200,
    "data": [
        {
            "id": "painting-001",
            "title": "可爱的小猫",
            "prompt": "一只可爱的小猫",
            "status": "completed",
            "imageUrls": "http://example.com/image1.jpg,http://example.com/image2.jpg",
            "createdAt": "2024-01-01T00:00:00Z",
            "updatedAt": "2024-01-01T00:00:00Z",
            "deletedAt": null
        }
    ],
    "msg": "success"
}
```

### 2. 根据条件查询绘画记录

**接口地址：** `POST /api/bot/painting-records/query`

**功能描述：** 根据查询条件获取绘画记录列表

**请求参数：**
```json
{
    "id": "painting-001",
    "title": "小猫",
    "status": "completed"
}
```

**响应示例：**
```json
{
    "code": 200,
    "data": [
        {
            "id": "painting-001",
            "title": "可爱的小猫",
            "prompt": "一只可爱的小猫",
            "status": "completed",
            "imageUrls": "http://example.com/image1.jpg",
            "createdAt": "2024-01-01T00:00:00Z",
            "updatedAt": "2024-01-01T00:00:00Z",
            "deletedAt": null
        }
    ],
    "msg": "success"
}
```

### 3. 创建绘画记录

**接口地址：** `POST /api/bot/painting-records/create`

**功能描述：** 创建新的绘画记录

**请求参数：**
```json
{
    "title": "可爱的小猫",
    "prompt": "一只可爱的小猫",
    "status": "completed",
    "imageUrls": "http://example.com/image1.jpg,http://example.com/image2.jpg"
}
```

**响应示例：**
```json
{
    "code": 200,
    "data": {
        "id": "painting-001",
        "title": "可爱的小猫",
        "prompt": "一只可爱的小猫",
        "status": "completed",
        "imageUrls": "http://example.com/image1.jpg,http://example.com/image2.jpg",
        "createdAt": "2024-01-01T00:00:00Z",
        "updatedAt": "2024-01-01T00:00:00Z",
        "deletedAt": null
    },
    "msg": "success"
}
```

### 4. 更新绘画记录

**接口地址：** `POST /api/bot/painting-records/update`

**功能描述：** 更新指定的绘画记录

**请求参数：**
```json
{
    "id": "painting-001",
    "title": "可爱的小猫（更新）",
    "prompt": "一只可爱的小猫",
    "status": "completed",
    "imageUrls": "http://example.com/image1.jpg,http://example.com/image2.jpg"
}
```

**响应示例：**
```json
{
    "code": 200,
    "data": {
        "id": "painting-001",
        "title": "可爱的小猫（更新）",
        "prompt": "一只可爱的小猫",
        "status": "completed",
        "imageUrls": "http://example.com/image1.jpg,http://example.com/image2.jpg",
        "createdAt": "2024-01-01T00:00:00Z",
        "updatedAt": "2024-01-01T00:00:00Z",
        "deletedAt": null
    },
    "msg": "success"
}
```

### 5. 删除绘画记录

**接口地址：** `POST /api/bot/painting-records/delete`

**功能描述：** 删除指定的绘画记录

**请求参数：**
```json
{
    "id": "painting-001"
}
```

**响应示例：**
```json
{
    "code": 200,
    "data": null,
    "msg": "success"
}
```

### 6. 获取单个绘画记录

**接口地址：** `POST /api/bot/painting-records/get`

**功能描述：** 根据ID获取指定的绘画记录

**请求参数：**
```json
{
    "id": "painting-001"
}
```

**响应示例：**
```json
{
    "code": 200,
    "data": {
        "id": "painting-001",
        "title": "可爱的小猫",
        "prompt": "一只可爱的小猫",
        "status": "completed",
        "imageUrls": "http://example.com/image1.jpg,http://example.com/image2.jpg",
        "createdAt": "2024-01-01T00:00:00Z",
        "updatedAt": "2024-01-01T00:00:00Z",
        "deletedAt": null
    },
    "msg": "success"
}
```

## 状态说明

绘画记录的状态包括：
- `pending`: 等待中
- `processing`: 处理中
- `completed`: 已完成
- `failed`: 失败

## 错误码说明

- `200`: 成功
- `500`: 服务器内部错误
- `400`: 请求参数错误

## 注意事项

1. 所有时间字段使用ISO 8601格式
2. ImageUrls字段使用逗号分隔多个图片URL
3. 查询接口支持模糊匹配和精确匹配
4. 删除操作为软删除，不会物理删除数据 