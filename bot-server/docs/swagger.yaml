definitions:
  dto.ConversationDTO:
    properties:
      id:
        type: string
      lastMsg:
        type: string
      lastTime:
        type: string
      picture:
        type: string
      title:
        type: string
    type: object
  dto.CrudDTO:
    type: object
  dto.PlatformDTO:
    properties:
      config:
        type: string
      configView:
        type: string
      id:
        type: string
      name:
        type: string
      quickConfigView:
        type: string
      type:
        type: string
    type: object
  resp.Response:
    properties:
      code:
        description: 业务状态码
        example: 200
        type: integer
      data:
        description: 响应数据
      msg:
        description: 附加消息
        example: success
        type: string
    type: object
info:
  contact: {}
paths:
  /api/bot/conversations:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/resp.Response'
            - properties:
                code:
                  type: integer
                data: {}
                msg:
                  type: string
              type: object
        "500":
          description: Internal Server Error
          schema:
            allOf:
            - $ref: '#/definitions/resp.Response'
            - properties:
                code:
                  type: integer
                data: {}
                msg:
                  type: string
              type: object
      tags:
      - 会话
  /api/bot/conversations/create:
    post:
      consumes:
      - application/json
      parameters:
      - description: 会话实体
        in: body
        name: args
        required: true
        schema:
          $ref: '#/definitions/dto.ConversationDTO'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/resp.Response'
            - properties:
                code:
                  type: integer
                data: {}
                msg:
                  type: string
              type: object
        "500":
          description: Internal Server Error
          schema:
            allOf:
            - $ref: '#/definitions/resp.Response'
            - properties:
                code:
                  type: integer
                data: {}
                msg:
                  type: string
              type: object
      tags:
      - 会话
  /api/bot/conversations/delete:
    post:
      consumes:
      - application/json
      parameters:
      - description: 会话实体
        in: body
        name: args
        required: true
        schema:
          $ref: '#/definitions/dto.CrudDTO'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/resp.Response'
            - properties:
                code:
                  type: integer
                data: {}
                msg:
                  type: string
              type: object
        "500":
          description: Internal Server Error
          schema:
            allOf:
            - $ref: '#/definitions/resp.Response'
            - properties:
                code:
                  type: integer
                data: {}
                msg:
                  type: string
              type: object
      tags:
      - 会话
  /api/bot/conversations/update:
    post:
      consumes:
      - application/json
      parameters:
      - description: 会话实体
        in: body
        name: args
        required: true
        schema:
          $ref: '#/definitions/dto.CrudDTO'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/resp.Response'
            - properties:
                code:
                  type: integer
                data: {}
                msg:
                  type: string
              type: object
        "500":
          description: Internal Server Error
          schema:
            allOf:
            - $ref: '#/definitions/resp.Response'
            - properties:
                code:
                  type: integer
                data: {}
                msg:
                  type: string
              type: object
      tags:
      - 会话
  /api/bot/platforms:
    get:
      consumes:
      - application/json
      description: 获取系统内的所有平台信息
      parameters:
      - description: 会话实体
        in: body
        name: args
        required: true
        schema:
          $ref: '#/definitions/dto.ConversationDTO'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/resp.Response'
            - properties:
                code:
                  type: integer
                data: {}
                msg:
                  type: string
              type: object
        "500":
          description: Internal Server Error
          schema:
            allOf:
            - $ref: '#/definitions/resp.Response'
            - properties:
                code:
                  type: integer
                data: {}
                msg:
                  type: string
              type: object
      tags:
      - 平台
  /api/bot/platforms/create:
    post:
      consumes:
      - application/json
      description: 创建添加平台
      parameters:
      - description: 平台实体
        in: body
        name: args
        required: true
        schema:
          $ref: '#/definitions/dto.PlatformDTO'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/resp.Response'
            - properties:
                code:
                  type: integer
                data: {}
                msg:
                  type: string
              type: object
        "500":
          description: Internal Server Error
          schema:
            allOf:
            - $ref: '#/definitions/resp.Response'
            - properties:
                code:
                  type: integer
                data: {}
                msg:
                  type: string
              type: object
      tags:
      - 平台
  /api/bot/platforms/delete:
    post:
      consumes:
      - application/json
      description: 删除平台
      parameters:
      - description: 会话实体
        in: body
        name: args
        required: true
        schema:
          $ref: '#/definitions/dto.ConversationDTO'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/resp.Response'
            - properties:
                code:
                  type: integer
                data: {}
                msg:
                  type: string
              type: object
        "500":
          description: Internal Server Error
          schema:
            allOf:
            - $ref: '#/definitions/resp.Response'
            - properties:
                code:
                  type: integer
                data: {}
                msg:
                  type: string
              type: object
      tags:
      - 平台
  /api/bot/platforms/update:
    post:
      consumes:
      - application/json
      description: 修改平台配置
      parameters:
      - description: 会话实体
        in: body
        name: args
        required: true
        schema:
          $ref: '#/definitions/dto.PlatformDTO'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/resp.Response'
            - properties:
                code:
                  type: integer
                data: {}
                msg:
                  type: string
              type: object
        "500":
          description: Internal Server Error
          schema:
            allOf:
            - $ref: '#/definitions/resp.Response'
            - properties:
                code:
                  type: integer
                data: {}
                msg:
                  type: string
              type: object
      tags:
      - 平台
swagger: "2.0"
