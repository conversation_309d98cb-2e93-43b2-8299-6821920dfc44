# 绘画配置管理接口文档

## 概述

本文档描述了AI绘画配置管理接口的使用方法。绘画配置用于存储和管理AI绘画的各种参数设置，包括提示词模板、尺寸、引导强度等配置信息。

## 数据模型

### PaintingConfigPO (持久化对象)
```go
type PaintingConfigPO struct {
    ID        string       `gorm:"primaryKey" json:"id"`                              // 绘画配置ID
    Config    string       `gorm:"column:config" json:"config"`                       // 绘画配置 json 格式
    CreatedAt time.Time    `gorm:"column:created_at;autoCreateTime" json:"createdAt"` // 创建时间
    UpdatedAt time.Time    `gorm:"column:updated_at;autoUpdateTime" json:"updatedAt"` // 更新时间
    DeletedAt sql.NullTime `gorm:"column:deleted_at" json:"deletedAt"`                // 删除时间
}
```

### PaintingConfigDTO (数据传输对象)
```go
type PaintingConfigDTO struct {
    ID        string       `json:"id"`        // 绘画配置ID
    Config    string       `json:"config"`    // 绘画配置 json 格式
    CreatedAt time.Time    `json:"createdAt"` // 创建时间
    UpdatedAt time.Time    `json:"updatedAt"` // 更新时间
    DeletedAt sql.NullTime `json:"deletedAt"` // 删除时间
}
```

### PaintingConfigQueryDTO (查询对象)
```go
type PaintingConfigQueryDTO struct {
    ID string `json:"id,omitempty"` // 绘画配置ID - 用于精确查询
}
```

## 配置JSON格式

配置字段存储为JSON字符串，包含以下参数：

```json
{
    "name": "配置名称",
    "promptTemplate": "提示词模板",
    "size": "尺寸规格",
    "guidanceScale": 7.5,
    "negativePrompt": "负面提示词",
    "width": 1024,
    "height": 1024,
    "useCustomSize": false,
    "imageCount": 1,
    "numInferenceSteps": 20
}
```

## API接口

### 1. 查询绘画配置列表

**接口地址：** `GET /api/bot/painting-configs`

**功能描述：** 获取所有绘画配置列表

**请求参数：** 无

**响应示例：**
```json
{
    "code": 200,
    "data": [
        {
            "id": "config-001",
            "config": "{\"name\":\"默认配置\",\"promptTemplate\":\"一只可爱的小猫\"}",
            "createdAt": "2024-01-01T00:00:00Z",
            "updatedAt": "2024-01-01T00:00:00Z",
            "deletedAt": null
        }
    ],
    "msg": "success"
}
```

### 2. 根据条件查询绘画配置

**接口地址：** `POST /api/bot/painting-configs/query`

**功能描述：** 根据查询条件获取绘画配置列表

**请求参数：**
```json
{
    "id": "config-001"
}
```

**响应示例：**
```json
{
    "code": 200,
    "data": [
        {
            "id": "config-001",
            "config": "{\"name\":\"默认配置\",\"promptTemplate\":\"一只可爱的小猫\"}",
            "createdAt": "2024-01-01T00:00:00Z",
            "updatedAt": "2024-01-01T00:00:00Z",
            "deletedAt": null
        }
    ],
    "msg": "success"
}
```

### 3. 创建绘画配置

**接口地址：** `POST /api/bot/painting-configs/create`

**功能描述：** 创建新的绘画配置

**请求参数：**
```json
{
    "config": "{\"name\":\"新配置\",\"promptTemplate\":\"美丽的风景画\",\"size\":\"16:9 (1024*576)\",\"guidanceScale\":8.0,\"negativePrompt\":\"模糊、变形\",\"width\":1024,\"height\":576,\"useCustomSize\":false,\"imageCount\":2,\"numInferenceSteps\":25}"
}
```

**响应示例：**
```json
{
    "code": 200,
    "data": {
        "id": "config-002",
        "config": "{\"name\":\"新配置\",\"promptTemplate\":\"美丽的风景画\"}",
        "createdAt": "2024-01-01T00:00:00Z",
        "updatedAt": "2024-01-01T00:00:00Z",
        "deletedAt": null
    },
    "msg": "success"
}
```

### 4. 更新绘画配置

**接口地址：** `POST /api/bot/painting-configs/update`

**功能描述：** 更新指定的绘画配置

**请求参数：**
```json
{
    "id": "config-002",
    "config": "{\"name\":\"更新后的配置\",\"promptTemplate\":\"更新后的提示词\",\"size\":\"1:1 (1024*1024)\",\"guidanceScale\":7.5,\"negativePrompt\":\"模糊、低质量\",\"width\":1024,\"height\":1024,\"useCustomSize\":false,\"imageCount\":1,\"numInferenceSteps\":20}"
}
```

**响应示例：**
```json
{
    "code": 200,
    "data": {
        "id": "config-002",
        "config": "{\"name\":\"更新后的配置\",\"promptTemplate\":\"更新后的提示词\"}",
        "createdAt": "2024-01-01T00:00:00Z",
        "updatedAt": "2024-01-01T01:00:00Z",
        "deletedAt": null
    },
    "msg": "success"
}
```

### 5. 删除绘画配置

**接口地址：** `POST /api/bot/painting-configs/delete`

**功能描述：** 删除指定的绘画配置

**请求参数：**
```json
{
    "id": "config-002"
}
```

**响应示例：**
```json
{
    "code": 200,
    "data": null,
    "msg": "success"
}
```

### 6. 获取单个绘画配置

**接口地址：** `POST /api/bot/painting-configs/get`

**功能描述：** 根据ID获取指定的绘画配置

**请求参数：**
```json
{
    "id": "config-001"
}
```

**响应示例：**
```json
{
    "code": 200,
    "data": {
        "id": "config-001",
        "config": "{\"name\":\"默认配置\",\"promptTemplate\":\"一只可爱的小猫\"}",
        "createdAt": "2024-01-01T00:00:00Z",
        "updatedAt": "2024-01-01T00:00:00Z",
        "deletedAt": null
    },
    "msg": "success"
}
```

## 错误处理

所有接口在发生错误时会返回以下格式：

```json
{
    "code": 500,
    "data": null,
    "msg": "错误描述信息"
}
```

常见错误码：
- 400: 请求参数错误
- 404: 资源不存在
- 500: 服务器内部错误

## 使用示例

### 创建配置示例

```javascript
// 创建绘画配置
const configData = {
    name: "风景画配置",
    promptTemplate: "壮丽的自然风景，山川河流，晨光熹微，写实摄影风格",
    size: "16:9 (1024*576)",
    guidanceScale: 8.0,
    negativePrompt: "模糊、低质量、变形、不自然的色彩",
    width: 1024,
    height: 576,
    useCustomSize: false,
    imageCount: 1,
    numInferenceSteps: 25
};

const response = await fetch('/api/bot/painting-configs/create', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        config: JSON.stringify(configData)
    })
});

const result = await response.json();
console.log('创建的配置:', result.data);
```

### 查询配置示例

```javascript
// 查询所有配置
const response = await fetch('/api/bot/painting-configs', {
    method: 'GET'
});

const result = await response.json();
const configs = result.data;

// 解析配置JSON
configs.forEach(config => {
    const configData = JSON.parse(config.config);
    console.log('配置名称:', configData.name);
    console.log('提示词模板:', configData.promptTemplate);
});
```

## 注意事项

1. 配置字段必须为有效的JSON字符串
2. 创建配置时不需要提供ID，系统会自动生成
3. 更新配置时必须提供完整的配置信息
4. 删除操作是物理删除，请谨慎操作
5. 时间字段会自动处理，创建时自动设置创建时间，更新时自动更新修改时间 