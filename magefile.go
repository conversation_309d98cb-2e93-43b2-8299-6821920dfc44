//go:build mage
// +build mage

package main

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"

	"github.com/magefile/mage/mg"
)

var Default = Build

func Build() error {
	mg.Deps(InstallDeps)
	fmt.Println("Building bot-server...")

	outputName := "bot"
	if runtime.GOOS == "windows" {
		outputName += ".exe"
	}

	cmd := exec.Command("go", "build", "-o", outputName)
	cmd.Dir = "bot-server"
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("build failed: %v", err)
	}

	fmt.Printf("Successfully built %s\n", outputName)
	return nil
}

// 复制文件或目录的辅助函数
func copyFileOrDir(src, dst string) error {
	srcInfo, err := os.Stat(src)
	if err != nil {
		return fmt.Errorf("failed to get source info: %v", err)
	}

	if srcInfo.IsDir() {
		// 复制目录
		if err := os.MkdirAll(dst, srcInfo.Mode()); err != nil {
			return fmt.Errorf("failed to create destination directory: %v", err)
		}

		entries, err := os.ReadDir(src)
		if err != nil {
			return fmt.Errorf("failed to read source directory: %v", err)
		}

		for _, entry := range entries {
			srcPath := filepath.Join(src, entry.Name())
			dstPath := filepath.Join(dst, entry.Name())
			if err := copyFileOrDir(srcPath, dstPath); err != nil {
				return err
			}
		}
	} else {
		// 复制文件
		data, err := os.ReadFile(src)
		if err != nil {
			return fmt.Errorf("failed to read source file: %v", err)
		}

		if err := os.MkdirAll(filepath.Dir(dst), 0755); err != nil {
			return fmt.Errorf("failed to create destination directory: %v", err)
		}

		if err := os.WriteFile(dst, data, srcInfo.Mode()); err != nil {
			return fmt.Errorf("failed to write destination file: %v", err)
		}
	}
	return nil
}

// 执行UI构建命令的辅助函数
func buildUI() error {
	uiDir := "bot-ui"

	// 安装依赖
	fmt.Println("Installing UI dependencies...")
	cmd := exec.Command("yarn")
	cmd.Dir = uiDir
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("failed to install UI dependencies: %v", err)
	}

	// 生成图标
	fmt.Println("Generating icons...")
	cmd = exec.Command("yarn", "run", "electron:generate-icons")
	cmd.Dir = uiDir
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("failed to generate icons: %v", err)
	}

	// 构建Windows版本
	fmt.Println("Building Windows application...")
	cmd = exec.Command("yarn", "run", "electron:product:build:windows")
	cmd.Dir = uiDir
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("failed to build Windows application: %v", err)
	}

	fmt.Println("UI build completed successfully")
	return nil
}

func Install() error {
	mg.Deps(Build)
	fmt.Println("Installing...")

	outputName := "bot"
	if runtime.GOOS == "windows" {
		outputName += ".exe"
	}

	// 创建目标根目录
	serverDir := filepath.Join("bot-ui", "server")
	if err := os.MkdirAll(serverDir, 0755); err != nil {
		return fmt.Errorf("failed to create server directory: %v", err)
	}

	// 复制可执行文件
	source := filepath.Join("bot-server", outputName)
	dest := filepath.Join(serverDir, outputName)

	// 如果目标文件存在，先删除
	if _, err := os.Stat(dest); err == nil {
		if err := os.Remove(dest); err != nil {
			return fmt.Errorf("failed to remove existing file: %v", err)
		}
	}

	// 复制可执行文件
	sourceFile, err := os.ReadFile(source)
	if err != nil {
		return fmt.Errorf("failed to read source file: %v", err)
	}

	if err := os.WriteFile(dest, sourceFile, 0755); err != nil {
		return fmt.Errorf("failed to write destination file: %v", err)
	}

	// 复制额外的目录和文件
	dirsToCopy := []string{"dict", "template"}
	for _, dir := range dirsToCopy {
		src := filepath.Join("bot-server", dir)
		dst := filepath.Join(serverDir, dir)
		if err := copyFileOrDir(src, dst); err != nil {
			return fmt.Errorf("failed to copy %s: %v", dir, err)
		}
		fmt.Printf("Copied %s directory\n", dir)
	}

	// 复制配置文件
	filesToCopy := []string{"config-dev.yaml", "config-pro.yaml", "config-test.yaml", "setting_init.json"}
	for _, file := range filesToCopy {
		src := filepath.Join("bot-server", file)
		dst := filepath.Join(serverDir, file)
		if err := copyFileOrDir(src, dst); err != nil {
			return fmt.Errorf("failed to copy %s: %v", file, err)
		}
		fmt.Printf("Copied %s\n", file)
	}

	fmt.Printf("Successfully installed all files to %s\n", serverDir)

	// 构建UI
	if err := buildUI(); err != nil {
		return fmt.Errorf("UI build failed: %v", err)
	}

	return nil
}

func InstallDeps() error {
	fmt.Println("Installing Dependencies...")

	cmd := exec.Command("go", "mod", "tidy")
	cmd.Dir = "bot-server"
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("failed to tidy dependencies: %v", err)
	}

	cmd = exec.Command("go", "mod", "download")
	cmd.Dir = "bot-server"
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("failed to download dependencies: %v", err)
	}

	fmt.Println("Dependencies installed successfully")
	return nil
}

func Clean() error {
	fmt.Println("Cleaning...")

	outputName := "bot"
	if runtime.GOOS == "windows" {
		outputName += ".exe"
	}

	return os.RemoveAll(filepath.Join("bot-server", outputName))
}
