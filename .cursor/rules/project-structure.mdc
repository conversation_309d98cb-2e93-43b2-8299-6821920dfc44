---
description: 
globs: 
alwaysApply: false
---
# 项目结构指南

## 整体架构
该项目采用前后端分离架构:
- `bot-server`: Go 语言实现的后端服务器
- `bot-ui`: 基于 Electron 实现的 PC 客户端

## 后端服务器 (bot-server)
后端采用分层架构:

### 应用层 (Application)
- `bot-server/application/service/ai-bot`: 业务逻辑定义
- `bot-server/application/service/wire_as.go`: 依赖注入配置
- `bot-server/application/dto`: 数据传输对象
  - `*_dto.go`: 前后端数据传输协议定义
  - `*_conv.go`: DTO 与实体对象转换逻辑

### 领域层 (Domain)
- `bot-server/domain/repository`: 仓储接口定义
- `bot-server/domain/service`: 领域服务实现

### 基础设施层 (Infrastructure)
- `bot-server/infrastructure/repository`: 仓储接口实现

## 前端客户端 (bot-ui)
- 基于 Electron 实现
- 使用第三方组件库(如 Ant Vue)时的规范:
  - 不允许直接修改第三方组件的 CSS
  - 如需样式调整，必须在指令中明确指定修改范围

## 重要约定
1. 新增服务配置参数时，必须在 bot-server 目录下运行 wire 重新生成依赖文件
2. 所有客户端相关修改必须在 bot-ui 目录下进行
3. 遵循依赖注入原则，通过 domain/service 向应用层提供服务

