---
description: 
globs: 
alwaysApply: false
---
# 开发规范指南

## 后端开发规范 (bot-server)

### 分层原则
1. 应用层 (Application)
   - 负责协调领域对象和领域服务
   - 不包含业务规则
   - 通过 DTO 对象处理数据传输

2. 领域层 (Domain)
   - 包含所有业务规则和业务逻辑
   - 定义领域模型和接口
   - 不依赖其他层

3. 基础设施层 (Infrastructure)
   - 实现领域层定义的接口
   - 处理技术细节（数据库访问、外部服务调用等）

### 依赖注入
- 新服务必须在 `wire_as.go` 中注册
- 遵循依赖倒置原则
- 使用 wire 工具生成依赖注入代码

### DTO 规范
- `*_dto.go` 文件仅包含数据传输结构定义
- `*_conv.go` 文件处理所有数据转换逻辑
- 保持 DTO 结构简单，只包含必要字段

## 前端开发规范 (bot-ui)

### Electron 应用开发
- 严格分离主进程和渲染进程代码
- 使用 IPC 通信处理进程间通信
- 遵循 Electron 安全最佳实践

### UI 组件规范
- 优先使用 Ant Vue 组件库
- 不直接修改第三方组件样式
- 组件封装需考虑可重用性

### 样式管理
- 使用 CSS Module 或 Scoped CSS
- 遵循 BEM 命名规范
- 避免全局样式污染

## 通用规范
1. 代码提交前必须通过所有测试
2. 保持代码格式一致性
3. 编写清晰的注释和文档
4. 遵循 Git 分支管理规范

