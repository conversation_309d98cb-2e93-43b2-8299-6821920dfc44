---
description: 
globs: 
alwaysApply: true
---
# Your rule content

- bot-server 文件夹下是服务器相关的代码
- bot-server\application\service\ai-bot 业务定义相关文件
- bot-server\application\service\wire_as.go 存储 业务依赖的依赖器，主要是把 bot-server\application\service\ai-bot 下面定义的服务 new 函数注册到其中，有新的参数配置，项目就必须在 bot-server下运行 wire 进行依赖文件生成
- bot-server\application\dto 下面的\_dto 结尾的文件 存储前端到服务器的数据传输协议
- bot-server\application\dto 下面的\_conv 结尾相关的文件 存储 dto 数据到 中间 entity 数据的转换逻辑
- bot-server\domain\repository 是存储 接口定义的位置，一般是定义 bot-server\infrastructure\repository 需要实现的业务接口，最终提供一个 bot-server\domain\service domain实例 给 bot-server\application\service 层调用 实现业务逻辑

- bot-ui 是 pc 客户端实现,客户端是通过 electron 来实现的 任何 客户端相关的修改都应该在 bot-ui 下进行
- 在使用第三方组件库的时候 比如 ant vue 不允许添加改变第三方组件库的而外 css 操作，除非指令只中明确说出需要改动那个地方
