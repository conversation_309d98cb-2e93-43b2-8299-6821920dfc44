---
description: 
globs: 
alwaysApply: false
---
# 前端技术栈指南

## 核心框架和工具
- 主框架：[Vue 3](mdc:bot-ui/package.json) (^3.5.12)
- 构建工具：[Vite](mdc:bot-ui/package.json) (^5.4.10)
- 桌面应用框架：[Electron](mdc:bot-ui/package.json) (^35.0.1)
- 类型支持：[TypeScript](mdc:bot-ui/package.json) (~5.6.0)

## 状态管理和路由
- 状态管理：[Pinia](mdc:bot-ui/package.json) (^2.2.4)
  - 持久化插件：pinia-plugin-persistedstate
- 路由管理：[Vue Router](mdc:bot-ui/package.json) (^4.4.5)

## UI 框架和组件
- 主要 UI 框架：
  - [Ant Design Vue](mdc:bot-ui/package.json) (^4.2.6)
  - [Quasar](mdc:bot-ui/package.json) (^2.13.0)
- 富文本编辑器：CKEditor 5 系列
- 样式支持：
  - [TailwindCSS](mdc:bot-ui/package.json) (^3.4.15)
  - SASS
  - Animate.css

## 功能组件和工具库
### 拖拽相关
- vue3-draggable-resizable
- vuedraggable
- gridstack

### 编辑器和展示
- Markdown 支持：
  - markdown-it 及其插件系列
  - KaTeX 数学公式支持
- 代码相关：
  - Prismjs 代码高亮
  - CodeMirror 代码编辑器

### 数据处理和工具
- HTTP 请求：Axios
- 工具库：
  - Lodash
  - UUID
  - Crypto-js
- 文件处理：
  - file-saver
  - html2canvas
  - jspdf

### 国际化和动画
- 国际化：vue-i18n
- 动画效果：
  - animate.css
  - hover.css
  - typed.js

## 开发工具和优化
- 自动导入：
  - unplugin-auto-import
  - unplugin-vue-components
- Vite 插件：
  - @vitejs/plugin-vue
  - vite-plugin-electron
  - vite-plugin-electron-renderer
  - @quasar/vite-plugin

## 项目规范
1. 使用 TypeScript 进行开发
2. 组件库样式修改限制：
   - 不直接修改 Ant Design Vue 组件样式
   - 使用主题定制或局部样式覆盖
3. 状态管理：
   - 使用 Pinia 进行状态管理
   - 需要持久化的状态使用 pinia-plugin-persistedstate
4. 样式开发：
   - 优先使用 TailwindCSS
   - 复杂样式使用 SASS
   - 保持 BEM 命名规范

