# ai-bot

## 部署安装

go version

```text
go 1.23
```

安装部署工具

```text
go install github.com/magefile/mage@latest
```

执行构建`mage build`,编译 `bot-server` 生成构建产物

```text
mage build
```

运行安装命令 `mage install` ，安装命令会打包`bot-server`内的构建产物和必需的构建资源到 `bot-ui`的
`server`文件夹中

```text
mage install
```

清空构建

```text
mage clear
```

## api 文档

```text
go install github.com/swaggo/swag/cmd/swag@latest
```

`bot-server` 根路径下执行文档生成命令

```text
swag init
```

# road map

- [x] LLM 流式对话问答
- [x] 消息记录内容本地存储
- [x] 多会话列表支持
- [x] 支持平台配置
    - [x] Ollama
    - [x] GiteeAI
        - [x] 支持 Function Call
    - [x] DeepSeek
- [x] 编辑器功能
    - [x] 文件
        - [x] 图片
    - [x] 代码块
- [x] 插件支持
    - [x] AI 聊天
    - [ ] AI 写作
- [x] 联网支持
    - [x] 时间查询
    - [x] 实时天气
    - [ ] 网络查询
    - [ ] POI 查询
- [x] 消息管理
    - [x] 消息删除
    - [x] 消息晴空
    - [x] 消息快捷复制
    - [x] 消息超长图片导出
    - [x] 消息导出pdf文件



